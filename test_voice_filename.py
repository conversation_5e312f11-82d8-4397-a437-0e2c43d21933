#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新的语音文件名生成逻辑
"""

import hashlib
import time
from pathlib import Path

def test_new_filename_format():
    """测试新的文件名格式：哈希值+时间戳"""
    
    # 测试数据
    test_text = "这是一个测试语音文件"
    speaker_id = 5
    speed = 110
    
    print("🔥 测试新的语音文件名生成格式")
    print("=" * 50)
    
    # 旧格式（原来的）
    old_text_hash = hashlib.md5(test_text.encode('utf-8')).hexdigest()[:8]
    old_filename = f"{old_text_hash}_{speaker_id}_{speed}.wav"
    print(f"旧格式: {old_filename}")
    
    # 新格式（哈希值+时间戳）
    content_for_hash = f"{test_text}_{speaker_id}_{speed}"
    new_text_hash = hashlib.md5(content_for_hash.encode('utf-8')).hexdigest()[:16]
    timestamp = int(time.time() * 1000)  # 毫秒级时间戳
    new_filename = f"{new_text_hash}_{timestamp}.wav"
    print(f"新格式: {new_filename}")
    
    print("\n🔍 格式对比:")
    print(f"旧格式长度: {len(old_filename)} 字符")
    print(f"新格式长度: {len(new_filename)} 字符")
    print(f"哈希长度: 旧={len(old_text_hash)} vs 新={len(new_text_hash)}")
    print(f"时间戳: {timestamp}")
    
    # 测试多个文件名生成
    print("\n🔄 生成多个文件名测试:")
    for i in range(3):
        time.sleep(0.001)  # 确保时间戳不同
        timestamp = int(time.time() * 1000)
        filename = f"{new_text_hash}_{timestamp}.wav"
        print(f"  {i+1}. {filename}")
    
    # 测试不同内容的哈希
    print("\n📝 不同内容的哈希测试:")
    test_texts = [
        "你好世界",
        "Hello World", 
        "测试语音文件生成",
        "AI语音合成系统"
    ]
    
    for text in test_texts:
        content_for_hash = f"{text}_{speaker_id}_{speed}"
        text_hash = hashlib.md5(content_for_hash.encode('utf-8')).hexdigest()[:16]
        timestamp = int(time.time() * 1000)
        filename = f"{text_hash}_{timestamp}.wav"
        print(f"  '{text}' -> {filename}")
        time.sleep(0.001)

def test_voice_service_format():
    """测试VoiceService中的文件名格式"""
    print("\n🎤 VoiceService格式测试:")
    print("=" * 50)
    
    text = "测试VoiceService格式"
    speaker_id = 0
    speed = 1.0
    
    # 模拟VoiceService中的逻辑
    content_for_hash = f"{text}_{speaker_id}_{speed}"
    text_hash = hashlib.md5(content_for_hash.encode()).hexdigest()[:16]
    timestamp = int(time.time() * 1000)
    filename = f"{text_hash}_{timestamp}.wav"
    
    print(f"文本: {text}")
    print(f"主播ID: {speaker_id}")
    print(f"语速: {speed}")
    print(f"生成的文件名: {filename}")

if __name__ == "__main__":
    test_new_filename_format()
    test_voice_service_format()
    
    print("\n✅ 测试完成！")
    print("\n📋 新文件名格式说明:")
    print("  - 格式: {16位哈希}_{毫秒时间戳}.wav")
    print("  - 哈希基于: 文本内容_主播ID_语速")
    print("  - 时间戳: 毫秒级，确保文件名唯一性")
    print("  - 优势: 避免文件名冲突，包含时间信息")
