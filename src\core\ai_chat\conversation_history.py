"""
AI Broadcaster v2 - 对话历史管理器
管理AI对话的历史记录
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class ConversationHistory:
    """对话历史管理器"""
    
    def __init__(self, db_manager):
        self.logger = create_logger("conversation_history")
        self.db_manager = db_manager
        
        # 历史记录配置
        self.max_history_days = 30  # 保留30天的历史
        self.max_records_per_session = 100  # 每个会话最多100条记录
        
    @handle_exceptions("ConversationHistory")
    def add_conversation(self, user_input: str, ai_response: str, 
                        context: Optional[Dict[str, Any]] = None,
                        session_id: Optional[str] = None,
                        user_id: Optional[int] = None) -> Optional[int]:
        """
        添加对话记录
        
        Args:
            user_input: 用户输入
            ai_response: AI回复
            context: 上下文信息
            session_id: 会话ID
            user_id: 用户ID
            
        Returns:
            对话记录ID，失败返回None
        """
        try:
            context_json = json.dumps(context, ensure_ascii=False) if context else None
            
            conversation_id = self.db_manager.execute_insert(
                """INSERT INTO conversations 
                   (user_input, ai_response, context, session_id, user_id, created_at)
                   VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)""",
                (user_input, ai_response, context_json, session_id, user_id)
            )
            
            if conversation_id:
                self.logger.debug(f"添加对话记录成功: ID {conversation_id}")
                
                # 清理过期记录
                self._cleanup_old_conversations()
                
                return conversation_id
            else:
                self.logger.error("添加对话记录失败")
                return None
                
        except Exception as e:
            self.logger.error(f"添加对话记录异常: {e}")
            return None
            
    @handle_exceptions("ConversationHistory")
    def get_recent_conversations(self, limit: int = 10, 
                                session_id: Optional[str] = None,
                                user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取最近的对话记录"""
        try:
            query = "SELECT * FROM conversations WHERE 1=1"
            params = []
            
            if session_id:
                query += " AND session_id = ?"
                params.append(session_id)
                
            if user_id:
                query += " AND user_id = ?"
                params.append(user_id)
                
            query += " ORDER BY created_at DESC LIMIT ?"
            params.append(limit)
            
            conversations = self.db_manager.execute_query(query, params)
            
            # 解析context JSON
            for conv in conversations:
                if conv['context']:
                    try:
                        conv['context'] = json.loads(conv['context'])
                    except json.JSONDecodeError:
                        conv['context'] = {}
                else:
                    conv['context'] = {}
                    
            # 按时间正序返回（最早的在前面）
            return list(reversed(conversations))
            
        except Exception as e:
            self.logger.error(f"获取最近对话记录失败: {e}")
            return []
            
    @handle_exceptions("ConversationHistory")
    def get_conversation_by_id(self, conversation_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取对话记录"""
        try:
            conversations = self.db_manager.execute_query(
                "SELECT * FROM conversations WHERE id = ?",
                (conversation_id,)
            )
            
            if conversations:
                conv = conversations[0]
                if conv['context']:
                    try:
                        conv['context'] = json.loads(conv['context'])
                    except json.JSONDecodeError:
                        conv['context'] = {}
                else:
                    conv['context'] = {}
                    
                return conv
            return None
            
        except Exception as e:
            self.logger.error(f"获取对话记录失败: {e}")
            return None
            
    @handle_exceptions("ConversationHistory")
    def search_conversations(self, keyword: str, user_id: Optional[int] = None,
                           days: int = 7) -> List[Dict[str, Any]]:
        """搜索对话记录"""
        try:
            # 计算搜索时间范围
            start_date = datetime.now() - timedelta(days=days)
            start_date_str = start_date.strftime("%Y-%m-%d %H:%M:%S")
            
            query = """
                SELECT * FROM conversations 
                WHERE created_at >= ?
                AND (user_input LIKE ? OR ai_response LIKE ?)
            """
            params = [start_date_str, f"%{keyword}%", f"%{keyword}%"]
            
            if user_id:
                query += " AND user_id = ?"
                params.append(user_id)
                
            query += " ORDER BY created_at DESC LIMIT 50"
            
            conversations = self.db_manager.execute_query(query, params)
            
            # 解析context JSON
            for conv in conversations:
                if conv['context']:
                    try:
                        conv['context'] = json.loads(conv['context'])
                    except json.JSONDecodeError:
                        conv['context'] = {}
                else:
                    conv['context'] = {}
                    
            return conversations
            
        except Exception as e:
            self.logger.error(f"搜索对话记录失败: {e}")
            return []
            
    @handle_exceptions("ConversationHistory")
    def get_conversation_stats(self, user_id: Optional[int] = None,
                              days: int = 7) -> Dict[str, Any]:
        """获取对话统计信息"""
        try:
            # 计算统计时间范围
            start_date = datetime.now() - timedelta(days=days)
            start_date_str = start_date.strftime("%Y-%m-%d %H:%M:%S")
            
            # 总对话数
            total_query = "SELECT COUNT(*) as total FROM conversations WHERE created_at >= ?"
            total_params = [start_date_str]
            
            if user_id:
                total_query += " AND user_id = ?"
                total_params.append(user_id)
                
            total_result = self.db_manager.execute_query(total_query, total_params)
            total_count = total_result[0]['total'] if total_result else 0
            
            # 平均输入长度
            avg_input_query = """
                SELECT AVG(LENGTH(user_input)) as avg_input_length 
                FROM conversations WHERE created_at >= ?
            """
            avg_input_params = [start_date_str]
            
            if user_id:
                avg_input_query += " AND user_id = ?"
                avg_input_params.append(user_id)
                
            avg_input_result = self.db_manager.execute_query(avg_input_query, avg_input_params)
            avg_input_length = avg_input_result[0]['avg_input_length'] if avg_input_result else 0
            
            # 平均回复长度
            avg_response_query = """
                SELECT AVG(LENGTH(ai_response)) as avg_response_length 
                FROM conversations WHERE created_at >= ?
            """
            avg_response_params = [start_date_str]
            
            if user_id:
                avg_response_query += " AND user_id = ?"
                avg_response_params.append(user_id)
                
            avg_response_result = self.db_manager.execute_query(avg_response_query, avg_response_params)
            avg_response_length = avg_response_result[0]['avg_response_length'] if avg_response_result else 0
            
            # 每日对话数
            daily_query = """
                SELECT DATE(created_at) as date, COUNT(*) as count 
                FROM conversations WHERE created_at >= ?
            """
            daily_params = [start_date_str]
            
            if user_id:
                daily_query += " AND user_id = ?"
                daily_params.append(user_id)
                
            daily_query += " GROUP BY DATE(created_at) ORDER BY date"
            daily_results = self.db_manager.execute_query(daily_query, daily_params)
            
            return {
                'total_conversations': total_count,
                'avg_input_length': round(avg_input_length or 0, 2),
                'avg_response_length': round(avg_response_length or 0, 2),
                'daily_counts': daily_results or [],
                'period_days': days
            }
            
        except Exception as e:
            self.logger.error(f"获取对话统计失败: {e}")
            return {
                'total_conversations': 0,
                'avg_input_length': 0,
                'avg_response_length': 0,
                'daily_counts': [],
                'period_days': days
            }
            
    @handle_exceptions("ConversationHistory")
    def delete_conversation(self, conversation_id: int) -> bool:
        """删除对话记录"""
        try:
            rows_affected = self.db_manager.execute_update(
                "DELETE FROM conversations WHERE id = ?",
                (conversation_id,)
            )
            
            if rows_affected > 0:
                self.logger.info(f"删除对话记录成功: ID {conversation_id}")
                return True
            else:
                self.logger.warning(f"对话记录不存在: ID {conversation_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除对话记录失败: {e}")
            return False
            
    @handle_exceptions("ConversationHistory")
    def clear_history(self, user_id: Optional[int] = None, 
                     session_id: Optional[str] = None):
        """清空对话历史"""
        try:
            query = "DELETE FROM conversations WHERE 1=1"
            params = []
            
            if user_id:
                query += " AND user_id = ?"
                params.append(user_id)
                
            if session_id:
                query += " AND session_id = ?"
                params.append(session_id)
                
            rows_affected = self.db_manager.execute_update(query, params)
            
            self.logger.info(f"清空对话历史完成: 删除 {rows_affected} 条记录")
            
        except Exception as e:
            self.logger.error(f"清空对话历史失败: {e}")
            
    def _cleanup_old_conversations(self):
        """清理过期的对话记录"""
        try:
            # 删除超过保留期限的记录
            cutoff_date = datetime.now() - timedelta(days=self.max_history_days)
            cutoff_date_str = cutoff_date.strftime("%Y-%m-%d %H:%M:%S")
            
            rows_affected = self.db_manager.execute_update(
                "DELETE FROM conversations WHERE created_at < ?",
                (cutoff_date_str,)
            )
            
            if rows_affected > 0:
                self.logger.debug(f"清理过期对话记录: 删除 {rows_affected} 条")
                
        except Exception as e:
            self.logger.error(f"清理过期对话记录失败: {e}")
            
    @handle_exceptions("ConversationHistory")
    def export_conversations(self, user_id: Optional[int] = None,
                           session_id: Optional[str] = None,
                           days: int = 30) -> List[Dict[str, Any]]:
        """导出对话记录"""
        try:
            # 计算导出时间范围
            start_date = datetime.now() - timedelta(days=days)
            start_date_str = start_date.strftime("%Y-%m-%d %H:%M:%S")
            
            query = "SELECT * FROM conversations WHERE created_at >= ?"
            params = [start_date_str]
            
            if user_id:
                query += " AND user_id = ?"
                params.append(user_id)
                
            if session_id:
                query += " AND session_id = ?"
                params.append(session_id)
                
            query += " ORDER BY created_at"
            
            conversations = self.db_manager.execute_query(query, params)
            
            # 处理导出数据
            export_data = []
            for conv in conversations:
                export_item = {
                    'id': conv['id'],
                    'user_input': conv['user_input'],
                    'ai_response': conv['ai_response'],
                    'created_at': conv['created_at'],
                    'session_id': conv['session_id']
                }
                
                # 解析context
                if conv['context']:
                    try:
                        export_item['context'] = json.loads(conv['context'])
                    except json.JSONDecodeError:
                        export_item['context'] = {}
                else:
                    export_item['context'] = {}
                    
                export_data.append(export_item)
                
            self.logger.info(f"导出对话记录: {len(export_data)} 条")
            return export_data
            
        except Exception as e:
            self.logger.error(f"导出对话记录失败: {e}")
            return []
