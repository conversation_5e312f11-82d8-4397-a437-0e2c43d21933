"""
AI直播系统 v2 - 弹幕处理器
弹幕内容分析和处理
"""

import re
import json
from typing import Dict, Any, List, Optional, Callable, Set
from datetime import datetime, timedelta

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class DanmakuProcessor:
    """弹幕处理器"""
    
    def __init__(self, db_manager):
        self.logger = create_logger("danmaku_processor")
        self.db_manager = db_manager
        
        # 关键词配置
        self.keywords = {
            'greetings': ['你好', '大家好', '主播好', 'hello', 'hi'],
            'questions': ['什么', '怎么', '为什么', '如何', '?', '？'],
            'praise': ['厉害', '牛逼', '666', '赞', '棒', '好看', '漂亮'],
            'requests': ['唱歌', '跳舞', '表演', '来一个', '再来'],
            'gifts': ['礼物', '打赏', '支持', '刷礼物'],
            'interactive': ['互动', '聊天', '说话', '回复']
        }
        
        # 敏感词过滤
        self.sensitive_words = set()
        self.load_sensitive_words()
        
        # 用户管理
        self.user_cache = {}  # 用户信息缓存
        self.user_cooldown = {}  # 用户冷却时间
        self.default_cooldown = 30  # 默认冷却时间（秒）
        
        # 处理规则
        self.processing_rules = []
        self.load_processing_rules()
        
        # 回调函数
        self.on_keyword_matched: Optional[Callable] = None
        self.on_sensitive_word_detected: Optional[Callable] = None
        self.on_user_interaction: Optional[Callable] = None
        
        # 统计信息
        self.stats = {
            'total_processed': 0,
            'keyword_matches': {},
            'sensitive_word_blocks': 0,
            'user_interactions': 0,
            'processing_errors': 0
        }
        
    def load_sensitive_words(self):
        """加载敏感词列表"""
        try:
            # 使用默认敏感词列表（实际项目中可以从数据库加载）
            default_words = [
                '垃圾', '傻逼', '白痴', '脑残', '智障', '死', '滚',
                '操', '草', '艹', '妈的', '他妈', '你妈', '卧槽'
            ]
            self.sensitive_words = set(default_words)
            self.logger.info(f"加载敏感词: {len(self.sensitive_words)} 个")
            
        except Exception as e:
            self.logger.error(f"加载敏感词失败: {e}")
            self.sensitive_words = set()
            
    def load_processing_rules(self):
        """加载处理规则"""
        try:
            # 默认处理规则
            default_rules = [
                {
                    'id': 1,
                    'name': '问候回复',
                    'pattern': r'(你好|大家好|主播好|hello|hi)',
                    'action': 'respond',
                    'response': '你好！欢迎来到直播间！',
                    'priority': 1,
                    'cooldown': 30
                },
                {
                    'id': 2,
                    'name': '提问回复',
                    'pattern': r'(什么|怎么|为什么|如何).*[?？]',
                    'action': 'respond',
                    'response': '这是个好问题，让我想想...',
                    'priority': 2,
                    'cooldown': 60
                }
            ]
            
            self.processing_rules = default_rules
            self.logger.info(f"加载处理规则: {len(self.processing_rules)} 条")
            
        except Exception as e:
            self.logger.error(f"加载处理规则失败: {e}")
            self.processing_rules = []
            
    @handle_exceptions("DanmakuProcessor")
    def process_danmaku(self, danmaku_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理弹幕消息
        
        Args:
            danmaku_info: 弹幕信息
            
        Returns:
            处理结果
        """
        try:
            self.stats['total_processed'] += 1
            
            username = danmaku_info.get('username', '')
            content = danmaku_info.get('content', '')
            user_id = danmaku_info.get('user_id', '')
            
            # 处理结果
            result = {
                'original': danmaku_info,
                'processed': True,
                'blocked': False,
                'keywords_matched': [],
                'sensitive_words_found': [],
                'response_needed': False,
                'suggested_response': '',
                'action': 'none',
                'user_info': {}
            }
            
            # 检查用户冷却时间
            if self._is_user_in_cooldown(user_id):
                result['action'] = 'cooldown'
                return result
                
            # 敏感词检测
            sensitive_words = self._detect_sensitive_words(content)
            if sensitive_words:
                result['sensitive_words_found'] = sensitive_words
                result['blocked'] = True
                result['action'] = 'block'
                self.stats['sensitive_word_blocks'] += 1
                
                if self.on_sensitive_word_detected:
                    self.on_sensitive_word_detected(danmaku_info, sensitive_words)
                    
                return result
                
            # 关键词匹配
            matched_keywords = self._match_keywords(content)
            result['keywords_matched'] = matched_keywords
            
            # 更新关键词统计
            for category in matched_keywords:
                if category not in self.stats['keyword_matches']:
                    self.stats['keyword_matches'][category] = 0
                self.stats['keyword_matches'][category] += 1
                
            # 应用处理规则
            rule_result = self._apply_processing_rules(content, danmaku_info)
            if rule_result:
                result.update(rule_result)
                
            # 用户信息处理
            user_info = self._process_user_info(danmaku_info)
            result['user_info'] = user_info
            
            # 设置用户冷却时间
            if result['response_needed']:
                self._set_user_cooldown(user_id)
                self.stats['user_interactions'] += 1
                
                if self.on_user_interaction:
                    self.on_user_interaction(danmaku_info, result)
                    
            # 触发关键词匹配回调
            if matched_keywords and self.on_keyword_matched:
                self.on_keyword_matched(danmaku_info, matched_keywords)
                
            return result
            
        except Exception as e:
            self.logger.error(f"处理弹幕失败: {e}")
            self.stats['processing_errors'] += 1
            return {
                'original': danmaku_info,
                'processed': False,
                'error': str(e)
            }
            
    def _detect_sensitive_words(self, content: str) -> List[str]:
        """检测敏感词"""
        found_words = []
        content_lower = content.lower()
        
        for word in self.sensitive_words:
            if word.lower() in content_lower:
                found_words.append(word)
                
        return found_words
        
    def _match_keywords(self, content: str) -> List[str]:
        """匹配关键词"""
        matched_categories = []
        content_lower = content.lower()
        
        for category, keywords in self.keywords.items():
            for keyword in keywords:
                if keyword.lower() in content_lower:
                    if category not in matched_categories:
                        matched_categories.append(category)
                    break
                    
        return matched_categories
        
    def _apply_processing_rules(self, content: str, danmaku_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """应用处理规则"""
        try:
            for rule in self.processing_rules:
                pattern = rule['pattern']
                
                # 正则匹配
                if re.search(pattern, content, re.IGNORECASE):
                    action = rule['action']
                    response = rule.get('response', '')
                    
                    result = {
                        'rule_matched': rule['name'],
                        'action': action
                    }
                    
                    if action == 'respond':
                        result['response_needed'] = True
                        result['suggested_response'] = response
                    elif action == 'block':
                        result['blocked'] = True
                    elif action == 'highlight':
                        result['highlighted'] = True
                        
                    return result
                    
            return None
            
        except Exception as e:
            self.logger.error(f"应用处理规则失败: {e}")
            return None
            
    def _process_user_info(self, danmaku_info: Dict[str, Any]) -> Dict[str, Any]:
        """处理用户信息"""
        try:
            user_id = danmaku_info.get('user_id', '')
            username = danmaku_info.get('username', '')
            
            # 从缓存获取用户信息
            if user_id in self.user_cache:
                user_info = self.user_cache[user_id]
                user_info['last_seen'] = datetime.now()
                user_info['message_count'] += 1
            else:
                # 新用户
                user_info = {
                    'user_id': user_id,
                    'username': username,
                    'first_seen': datetime.now(),
                    'last_seen': datetime.now(),
                    'message_count': 1,
                    'is_vip': danmaku_info.get('is_vip', False),
                    'is_admin': danmaku_info.get('is_admin', False),
                    'user_level': danmaku_info.get('user_level', 0)
                }
                self.user_cache[user_id] = user_info
                
            return user_info
            
        except Exception as e:
            self.logger.error(f"处理用户信息失败: {e}")
            return {}
            
    def _is_user_in_cooldown(self, user_id: str) -> bool:
        """检查用户是否在冷却时间内"""
        if not user_id or user_id not in self.user_cooldown:
            return False
            
        cooldown_end = self.user_cooldown[user_id]
        return datetime.now() < cooldown_end
        
    def _set_user_cooldown(self, user_id: str, cooldown_seconds: Optional[int] = None):
        """设置用户冷却时间"""
        if not user_id:
            return
            
        cooldown = cooldown_seconds or self.default_cooldown
        self.user_cooldown[user_id] = datetime.now() + timedelta(seconds=cooldown)
        
    @handle_exceptions("DanmakuProcessor")
    def add_keyword(self, category: str, keyword: str):
        """添加关键词"""
        if category not in self.keywords:
            self.keywords[category] = []
            
        if keyword not in self.keywords[category]:
            self.keywords[category].append(keyword)
            self.logger.info(f"添加关键词: {category} -> {keyword}")
            
    @handle_exceptions("DanmakuProcessor")
    def remove_keyword(self, category: str, keyword: str):
        """移除关键词"""
        if category in self.keywords and keyword in self.keywords[category]:
            self.keywords[category].remove(keyword)
            self.logger.info(f"移除关键词: {category} -> {keyword}")
            
    @handle_exceptions("DanmakuProcessor")
    def add_sensitive_word(self, word: str):
        """添加敏感词"""
        self.sensitive_words.add(word)
        self.logger.info(f"添加敏感词: {word}")
            
    @handle_exceptions("DanmakuProcessor")
    def remove_sensitive_word(self, word: str):
        """移除敏感词"""
        self.sensitive_words.discard(word)
        self.logger.info(f"移除敏感词: {word}")
            
    @handle_exceptions("DanmakuProcessor")
    def get_user_stats(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取用户统计"""
        try:
            # 按消息数量排序
            sorted_users = sorted(
                self.user_cache.values(),
                key=lambda x: x['message_count'],
                reverse=True
            )
            
            return sorted_users[:limit]
            
        except Exception as e:
            self.logger.error(f"获取用户统计失败: {e}")
            return []
            
    @handle_exceptions("DanmakuProcessor")
    def get_keyword_stats(self) -> Dict[str, Any]:
        """获取关键词统计"""
        return {
            'categories': list(self.keywords.keys()),
            'total_keywords': sum(len(words) for words in self.keywords.values()),
            'matches': self.stats['keyword_matches'].copy()
        }
        
    @handle_exceptions("DanmakuProcessor")
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计"""
        stats = self.stats.copy()
        stats['sensitive_words_count'] = len(self.sensitive_words)
        stats['processing_rules_count'] = len(self.processing_rules)
        stats['cached_users'] = len(self.user_cache)
        stats['users_in_cooldown'] = len([
            user_id for user_id, cooldown_end in self.user_cooldown.items()
            if datetime.now() < cooldown_end
        ])
        
        return stats
