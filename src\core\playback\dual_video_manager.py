"""
AI Broadcaster v2 - 双主视频管理器
管理两个主视频源的无缝切换，避免黑屏
"""

import threading
import time
from typing import Optional, Dict, Any, Callable, List
from enum import Enum

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class VideoSourceState(Enum):
    """视频源状态"""
    HIDDEN = "hidden"           # 隐藏
    VISIBLE = "visible"         # 显示
    PLAYING = "playing"         # 播放中
    PAUSED = "paused"          # 暂停
    ENDED = "ended"            # 播放结束
    PREPARING = "preparing"     # 准备中


class DualVideoManager:
    """双主视频管理器"""
    
    def __init__(self, obs_controller):
        self.logger = create_logger("dual_video_manager")
        self.obs_controller = obs_controller
        
        # 视频源配置
        self.video_source_a: Optional[str] = None
        self.video_source_b: Optional[str] = None
        
        # 当前状态
        self.current_active_source: Optional[str] = None
        self.next_source: Optional[str] = None
        
        # 状态监控
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        
        # 切换配置
        self.switch_threshold_seconds = 5  # 提前5秒准备下一个视频
        self.speed_range = (0.5, 2.0)     # 变速范围
        self.next_source_speed = 1.0  # 下一个视频源的预设播放速度
        
        # 回调函数
        self.on_source_switched: Optional[Callable] = None
        self.on_source_prepared: Optional[Callable] = None
        self.on_switch_error: Optional[Callable] = None
        
        # 状态缓存
        self._source_states = {}
        self._last_check_time = 0
        self._check_interval = 0.5  # 减少检查间隔到0.5秒，提高响应速度

        # 防止频繁切换的冷却时间
        self._last_switch_time = 0
        self._switch_cooldown = 3.0  # 切换冷却时间（秒）

        # 精确切换控制（根据播放速度动态调整）
        self._base_preparation_threshold = 1.5  # 基础准备时间1.5秒
        self._next_source_prepared = False  # 下一个源是否已准备好
        self._preparation_start_time = 0    # 开始准备的时间
        self._current_playback_speed = 1.0  # 当前播放速度
        
        self.logger.info("双主视频管理器初始化完成")
    
    @handle_exceptions("DualVideoManager")
    def set_video_sources(self, source_a: str, source_b: str):
        """设置双主视频源"""
        try:
            self.video_source_a = source_a
            self.video_source_b = source_b
            
            # 初始化状态
            self._source_states[source_a] = VideoSourceState.HIDDEN
            self._source_states[source_b] = VideoSourceState.HIDDEN
            
            self.logger.info(f"设置双主视频源: A={source_a}, B={source_b}")
            
            # 检查初始状态并设置默认激活源
            self._initialize_active_source()
            
        except Exception as e:
            self.logger.error(f"设置双主视频源失败: {e}")
    
    @handle_exceptions("DualVideoManager")
    def _initialize_active_source(self):
        """初始化激活的视频源"""
        try:
            if not self.video_source_a or not self.video_source_b:
                return

            # 首先确保只有一个视频源显示
            self._ensure_single_source_visible()

            # 检查两个视频源的显示状态
            source_a_visible = self._is_source_visible(self.video_source_a)
            source_b_visible = self._is_source_visible(self.video_source_b)

            if source_a_visible and source_b_visible:
                # 两个都显示，选择最上面的并隐藏另一个
                top_source = self._get_top_visible_source()
                other_source = self.video_source_b if top_source == self.video_source_a else self.video_source_a
                self._hide_source(other_source)
                self.current_active_source = top_source
                self.logger.info(f"两个视频源都显示，选择最上面的: {top_source}，隐藏: {other_source}")

            elif source_a_visible:
                self.current_active_source = self.video_source_a
                self.logger.info(f"视频源A显示，设为激活源: {self.video_source_a}")

            elif source_b_visible:
                self.current_active_source = self.video_source_b
                self.logger.info(f"视频源B显示，设为激活源: {self.video_source_b}")

            else:
                # 两个都隐藏，自动开启视频源A
                self.current_active_source = self.video_source_a
                self._show_source(self.video_source_a)
                self.logger.info(f"两个视频源都隐藏，自动开启视频源A: {self.video_source_a}")

            # 设置下一个源
            self.next_source = self.video_source_b if self.current_active_source == self.video_source_a else self.video_source_a

            # 为下一个视频源生成初始随机速度
            self._generate_random_speed_for_next_source()

            self.logger.info(f"初始化完成 - 当前激活源: {self.current_active_source}, 下一个源: {self.next_source} (预设速度: {self.next_source_speed}x)")

        except Exception as e:
            self.logger.error(f"初始化激活视频源失败: {e}")

    @handle_exceptions("DualVideoManager")
    def _ensure_single_source_visible(self):
        """确保只有一个视频源处于显示状态"""
        try:
            # 获取当前场景中所有的媒体源
            current_scene = self._get_current_scene()
            if not current_scene:
                return

            scene_items = self._get_scene_items(current_scene)
            visible_media_sources = []

            # 找出所有可见的媒体源
            for item in scene_items:
                source_name = item.get("sourceName")
                is_enabled = item.get("sceneItemEnabled", False)

                # 只处理媒体源（通过源名称判断）
                if is_enabled and self._is_media_source_name(source_name):
                    visible_media_sources.append({
                        'name': source_name,
                        'id': item.get("sceneItemId"),
                        'index': item.get("sceneItemIndex", 0)
                    })

            # 如果有多个媒体源可见，只保留一个
            if len(visible_media_sources) > 1:
                self.logger.info(f"发现 {len(visible_media_sources)} 个可见媒体源，确保只显示一个")

                # 优先保留双主视频源中的一个（如果已设置）
                dual_sources_visible = []
                if self.video_source_a and self.video_source_b:
                    dual_sources_visible = [s for s in visible_media_sources
                                          if s['name'] in [self.video_source_a, self.video_source_b]]

                if dual_sources_visible:
                    # 如果双主视频源中有可见的，保留z-order最高的
                    dual_sources_visible.sort(key=lambda x: x['index'], reverse=True)
                    keep_source = dual_sources_visible[0]

                    # 隐藏其他所有媒体源
                    for source in visible_media_sources:
                        if source['name'] != keep_source['name']:
                            self._hide_source_by_id(current_scene, source['id'])
                            self.logger.info(f"隐藏媒体源: {source['name']}")

                    self.logger.info(f"保留双主视频源: {keep_source['name']}")
                else:
                    # 如果双主视频源都不可见或未设置，保留z-order最高的媒体源，隐藏其他
                    visible_media_sources.sort(key=lambda x: x['index'], reverse=True)
                    keep_source = visible_media_sources[0]

                    for source in visible_media_sources[1:]:
                        self._hide_source_by_id(current_scene, source['id'])
                        self.logger.info(f"隐藏媒体源: {source['name']}")

                    self.logger.info(f"保留媒体源: {keep_source['name']}")
            elif len(visible_media_sources) == 1:
                self.logger.info(f"只有一个媒体源可见: {visible_media_sources[0]['name']}")
            else:
                self.logger.info("没有可见的媒体源")

        except Exception as e:
            self.logger.error(f"确保单一视频源显示失败: {e}")

    def _is_media_source_name(self, source_name: str) -> bool:
        """判断是否为媒体源（通过源名称）"""
        try:
            # 使用OBS控制器中已经过滤好的媒体源列表
            if hasattr(self.obs_controller, 'media_sources'):
                return source_name in self.obs_controller.media_sources

            # 如果没有媒体源列表，返回False
            return False
        except Exception as e:
            self.logger.error(f"检查媒体源名称失败: {e}")
            return False

    def _hide_source_by_id(self, scene_name: str, source_id: int):
        """通过ID隐藏源"""
        try:
            if not getattr(self.obs_controller, 'is_connected', getattr(self.obs_controller, 'connected', False)):
                return

            result = self.obs_controller.send_request_sync("SetSceneItemEnabled", {
                "sceneName": scene_name,
                "sceneItemId": source_id,
                "sceneItemEnabled": False
            })

            if result is not None:
                self.logger.debug(f"隐藏源ID {source_id}")
            else:
                self.logger.error(f"隐藏源ID {source_id} 失败")

        except Exception as e:
            self.logger.error(f"通过ID隐藏源失败: {e}")

    @handle_exceptions("DualVideoManager")
    def _ensure_only_current_source_visible(self):
        """确保只有当前激活的视频源显示，隐藏所有其他媒体源"""
        try:
            if not self.current_active_source:
                return

            # 获取当前场景中所有的媒体源
            current_scene = self._get_current_scene()
            if not current_scene:
                return

            scene_items = self._get_scene_items(current_scene)

            # 隐藏所有其他媒体源
            for item in scene_items:
                source_name = item.get("sourceName")
                is_enabled = item.get("sceneItemEnabled", False)
                source_id = item.get("sceneItemId")

                # 只处理媒体源（通过源名称判断）
                if self._is_media_source_name(source_name):
                    if source_name != self.current_active_source and is_enabled:
                        # 隐藏非当前激活的媒体源
                        self._hide_source_by_id(current_scene, source_id)
                        self.logger.info(f"隐藏非激活媒体源: {source_name}")
                    elif source_name == self.current_active_source and not is_enabled:
                        # 确保当前激活源是显示的
                        self._show_source_by_id(current_scene, source_id)
                        self.logger.info(f"显示当前激活源: {source_name}")

        except Exception as e:
            self.logger.error(f"确保只有当前源显示失败: {e}")

    def _show_source_by_id(self, scene_name: str, source_id: int):
        """通过ID显示源"""
        try:
            if not getattr(self.obs_controller, 'is_connected', getattr(self.obs_controller, 'connected', False)):
                return

            result = self.obs_controller.send_request_sync("SetSceneItemEnabled", {
                "sceneName": scene_name,
                "sceneItemId": source_id,
                "sceneItemEnabled": True
            })

            if result is not None:
                self.logger.debug(f"显示源ID {source_id}")
            else:
                self.logger.error(f"显示源ID {source_id} 失败")

        except Exception as e:
            self.logger.error(f"通过ID显示源失败: {e}")

    @handle_exceptions("DualVideoManager")
    def start_monitoring(self):
        """开始监控视频播放状态"""
        try:
            if self.monitoring:
                self.logger.warning("视频监控已在运行")
                return
            
            if not self.video_source_a or not self.video_source_b:
                self.logger.error("未设置双主视频源，无法开始监控")
                return
            
            self.monitoring = True
            self.stop_event.clear()
            
            self.monitor_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True,
                name="DualVideoMonitor"
            )
            self.monitor_thread.start()

            self.logger.info(f"🚀 双主视频监控已开始")
            self.logger.info(f"📊 监控配置: 当前激活={self.current_active_source}, 下一个={self.next_source}")
            self.logger.info(f"📊 监控配置: 检查间隔={self._check_interval}秒, 切换阈值={self.switch_threshold_seconds}秒")
            self.logger.info(f"📊 监控配置: 变速范围={self.speed_range}, 预设速度={self.next_source_speed}x")
            
        except Exception as e:
            self.logger.error(f"开始视频监控失败: {e}")
            self.monitoring = False
    
    @handle_exceptions("DualVideoManager")
    def stop_monitoring(self):
        """停止监控"""
        try:
            if not self.monitoring:
                return

            self.monitoring = False
            self.stop_event.set()

            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5.0)

            self.logger.info("双主视频监控已停止")

        except Exception as e:
            self.logger.error(f"停止视频监控失败: {e}")

    @handle_exceptions("DualVideoManager")
    def pause_monitoring(self):
        """暂停监控（用于副视频播放期间）"""
        try:
            if not self.monitoring:
                return

            # 设置暂停标志
            self._monitoring_paused = True
            self.logger.info("双主视频监控已暂停（副视频播放期间）")

        except Exception as e:
            self.logger.error(f"暂停视频监控失败: {e}")

    @handle_exceptions("DualVideoManager")
    def resume_monitoring(self):
        """恢复监控（副视频播放完成后）"""
        try:
            if not self.monitoring:
                return

            # 清除暂停标志
            self._monitoring_paused = False
            self.logger.info("双主视频监控已恢复（副视频播放完成）")

        except Exception as e:
            self.logger.error(f"恢复视频监控失败: {e}")
    
    def _monitoring_loop(self):
        """监控循环"""
        monitor_cycle_count = 0
        self.logger.info(f"🔄 监控循环开始运行...")

        while not self.stop_event.is_set():
            try:
                # 🔥 新增：检查是否暂停监控（副视频播放期间）
                if getattr(self, '_monitoring_paused', False):
                    time.sleep(0.5)  # 暂停期间减少检查频率
                    continue

                current_time = time.time()

                # 控制检查频率
                if current_time - self._last_check_time < self._check_interval:
                    time.sleep(0.1)
                    continue

                self._last_check_time = current_time
                monitor_cycle_count += 1

                # 每30个周期输出一次详细状态（约30秒）
                if monitor_cycle_count % 30 == 1:
                    self.logger.info(f"📊 监控周期 #{monitor_cycle_count}: 当前激活={self.current_active_source}, 下一个={self.next_source} (预设速度: {self.next_source_speed}x)")

                # 第一个周期输出详细信息
                if monitor_cycle_count == 1:
                    self.logger.info(f"🔍 开始第一次状态检查...")
                    self.logger.info(f"🔍 OBS连接状态: {getattr(self.obs_controller, 'is_connected', getattr(self.obs_controller, 'connected', False))}")
                    self.logger.info(f"🔍 当前激活源: {self.current_active_source}")
                    self.logger.info(f"🔍 下一个源: {self.next_source}")

                # 检查当前激活源的状态
                if self.current_active_source:
                    # 使用ui_design.py风格的状态检查
                    try:
                        self._check_active_source_status_ui_design_style()
                    except Exception as e:
                        self.logger.error(f"ui_design风格状态检查失败: {e}")
                        # 如果ui_design风格失败，回退到原始方法
                        self._check_active_source_status()
                else:
                    self.logger.warning("⚠️ 当前激活源为空，监控无法进行")

                # 检查是否需要准备下一个源
                self._check_next_source_preparation()

            except Exception as e:
                self.logger.error(f"视频监控循环异常: {e}")
                if self.on_switch_error:
                    self.on_switch_error(str(e))

                # 出错后等待一段时间
                time.sleep(5.0)
    
    def _check_active_source_status(self):
        """检查激活源状态"""
        try:
            if not self.current_active_source:
                return
            
            # 获取媒体状态
            if hasattr(self.obs_controller, 'get_media_status_sync'):
                status = self.obs_controller.get_media_status_sync(self.current_active_source)
            else:
                status = self.obs_controller.get_media_status(self.current_active_source)
            if not status:
                return

            media_state = status.get('media_state', '')
            media_duration = status.get('media_duration', 0)
            media_cursor = status.get('media_cursor', 0)

            # 增强日志：每5秒输出一次详细状态，便于调试自动切换问题
            current_time = time.time()
            if not hasattr(self, '_last_status_log_time'):
                self._last_status_log_time = 0

            if current_time - self._last_status_log_time > 5:
                self._last_status_log_time = current_time
                remaining_time = (media_duration - media_cursor) / 1000.0 if media_duration > 0 and media_cursor > 0 else 0
                progress_percent = (media_cursor / media_duration * 100) if media_duration > 0 else 0
                self.logger.info(f"📊 视频状态监控: {self.current_active_source} | 状态: {media_state} | 进度: {progress_percent:.1f}% | 剩余: {remaining_time:.1f}秒 | 下一个: {self.next_source}")

                # 关键修复：检测视频是否卡在接近结束的位置
                if (media_duration > 0 and media_cursor > 0 and
                    remaining_time < 2.0 and media_state in ['OBS_MEDIA_STATE_PLAYING', 'OBS_MEDIA_STATE_PAUSED']):
                    self.logger.warning(f"⚠️ 视频接近结束但未自动切换，剩余时间: {remaining_time:.1f}秒")

                    # 如果剩余时间小于1秒，强制切换
                    if remaining_time < 1.0:
                        self.logger.info(f"🚨 强制切换：视频剩余时间不足1秒")
                        self._generate_random_speed_for_next_source()
                        switch_success = self._switch_to_next_source()
                        if switch_success:
                            self.logger.info(f"✅ 强制切换成功: {self.current_active_source}")
                        else:
                            self.logger.error(f"❌ 强制切换失败")
                        return
            
            # 更新状态缓存
            if media_state == 'OBS_MEDIA_STATE_PLAYING':
                self._source_states[self.current_active_source] = VideoSourceState.PLAYING
            elif media_state == 'OBS_MEDIA_STATE_PAUSED':
                self._source_states[self.current_active_source] = VideoSourceState.PAUSED
            elif media_state == 'OBS_MEDIA_STATE_ENDED':
                self._source_states[self.current_active_source] = VideoSourceState.ENDED
                # 视频播放结束，立即切换并生成新的随机速度
                self.logger.info(f"🎬 检测到视频播放结束(ENDED): {self.current_active_source}")
                self.logger.info(f"🔄 准备切换到下一个视频源: {self.next_source}")

                # 确保下一个视频源存在
                if not self.next_source:
                    self.logger.error("❌ 下一个视频源未设置，无法自动切换")
                    return

                # 为下一个视频源生成新的随机速度
                self._generate_random_speed_for_next_source()

                # 立即执行切换
                switch_success = self._switch_to_next_source()

                if switch_success:
                    self.logger.info(f"✅ 视频播放结束(ENDED)后自动切换完成: {self.current_active_source}")
                else:
                    self.logger.error(f"❌ 视频播放结束(ENDED)后自动切换失败")

                return
            elif media_state == 'OBS_MEDIA_STATE_STOPPED':
                # 也检测停止状态，有时候播放结束会显示为停止
                self.logger.info(f"🛑 检测到视频停止(STOPPED): {self.current_active_source}")
                # 检查是否是因为播放完成而停止（光标接近结束）
                if media_duration > 0 and media_cursor > 0:
                    progress = media_cursor / media_duration
                    self.logger.info(f"📊 停止时播放进度: {progress:.1%} ({media_cursor}/{media_duration})")

                    # 降低阈值到90%，更容易触发切换
                    if progress > 0.90:  # 播放进度超过90%认为是播放完成
                        self.logger.info(f"🎬 视频停止且进度{progress:.1%}，判断为播放完成")
                        self._source_states[self.current_active_source] = VideoSourceState.ENDED

                        # 执行自动切换
                        if self.next_source:
                            self._generate_random_speed_for_next_source()
                            switch_success = self._switch_to_next_source()
                            if switch_success:
                                self.logger.info(f"✅ 视频停止(STOPPED)后自动切换完成: {self.current_active_source}")
                            else:
                                self.logger.error(f"❌ 视频停止(STOPPED)后自动切换失败")
                        return
                    else:
                        self.logger.info(f"📊 视频停止但进度仅{progress:.1%}，不执行切换")
                else:
                    self.logger.warning(f"⚠️ 视频停止但无法获取播放进度信息")
            elif media_state == 'OBS_MEDIA_STATE_NONE' or media_state == '':
                # 检测到无状态，可能是播放完成后的状态
                self.logger.info(f"❓ 检测到无媒体状态: {self.current_active_source}")
                if media_duration > 0 and media_cursor > 0:
                    progress = media_cursor / media_duration
                    if progress > 0.90:  # 如果进度超过90%且无状态，也认为是播放完成
                        self.logger.info(f"🎬 无媒体状态且进度{progress:.1%}，判断为播放完成")
                        self._source_states[self.current_active_source] = VideoSourceState.ENDED

                        # 执行自动切换
                        if self.next_source:
                            self._generate_random_speed_for_next_source()
                            switch_success = self._switch_to_next_source()
                            if switch_success:
                                self.logger.info(f"✅ 无媒体状态后自动切换完成: {self.current_active_source}")
                            else:
                                self.logger.error(f"❌ 无媒体状态后自动切换失败")
                        return
            
            # 检查是否接近结束，需要准备切换
            if media_duration > 0 and media_cursor > 0:
                remaining_time = (media_duration - media_cursor) / 1000.0  # 转换为秒

                if remaining_time <= self.switch_threshold_seconds:
                    # 提前准备下一个源并生成随机速度
                    self._generate_random_speed_for_next_source()
                    self._prepare_next_source()
            
        except Exception as e:
            self.logger.error(f"检查激活源状态失败: {e}")

    def _generate_random_speed_for_next_source(self):
        """为下一个视频源生成随机播放速度"""
        try:
            import random
            min_speed, max_speed = self.speed_range

            # 生成随机速度（保留1位小数）
            random_speed = round(random.uniform(min_speed, max_speed), 1)

            # 设置下一个源的速度
            self.next_source_speed = random_speed
            self.logger.info(f"为下一个视频源生成随机速度: {self.next_source} = {random_speed}x")

        except Exception as e:
            self.logger.error(f"生成随机速度失败: {e}")
            # 如果生成失败，使用默认速度
            self.next_source_speed = 1.0

    def _check_next_source_preparation(self):
        """检查下一个源的准备状态"""
        try:
            if not self.next_source:
                return
            
            # 检查下一个源是否已准备好
            if hasattr(self.obs_controller, 'get_media_status_sync'):
                next_status = self.obs_controller.get_media_status_sync(self.next_source)
            else:
                next_status = self.obs_controller.get_media_status(self.next_source)
            if next_status:
                media_state = next_status.get('media_state', '')
                if media_state in ['OBS_MEDIA_STATE_PLAYING', 'OBS_MEDIA_STATE_PAUSED']:
                    self._source_states[self.next_source] = VideoSourceState.PREPARING
            
        except Exception as e:
            self.logger.error(f"检查下一个源准备状态失败: {e}")
    
    @handle_exceptions("DualVideoManager")
    def _prepare_next_source(self):
        """准备下一个视频源"""
        try:
            if not self.next_source:
                return

            # 检查下一个源是否已经在准备中
            if self._source_states.get(self.next_source) == VideoSourceState.PREPARING:
                return

            self.logger.info(f"准备下一个视频源: {self.next_source}")

            # 关键修复1: 提前设置下一个源的播放速度（使用预设的变速范围速度）
            self._set_source_speed(self.next_source, self.next_source_speed)
            self.logger.info(f"预设下一个视频源速度: {self.next_source} = {self.next_source_speed}x")

            # 确保下一个源处于隐藏状态但已加载
            self._hide_source(self.next_source)

            # 标记为准备中
            self._source_states[self.next_source] = VideoSourceState.PREPARING

            if self.on_source_prepared:
                self.on_source_prepared(self.next_source)

        except Exception as e:
            self.logger.error(f"准备下一个视频源失败: {e}")
    
    @handle_exceptions("DualVideoManager")
    def _switch_to_next_source(self):
        """切换到下一个视频源（优化无黑屏切换）"""
        try:
            if not self.next_source:
                self.logger.error("未设置下一个视频源")
                return False

            # 检查切换冷却时间，防止频繁切换
            current_time = time.time()
            if current_time - self._last_switch_time < self._switch_cooldown:
                remaining_cooldown = self._switch_cooldown - (current_time - self._last_switch_time)
                self.logger.warning(f"⏳ 切换冷却中，剩余 {remaining_cooldown:.1f}秒")
                return False

            old_active = self.current_active_source
            new_active = self.next_source

            self.logger.info(f"🔄 开始无黑屏切换视频源: {old_active} -> {new_active}")

            # 优化切换顺序，确保无黑屏切换

            # 步骤1: 设置新激活源的播放速度（在隐藏状态下设置）
            self._set_source_speed(new_active, self.next_source_speed)
            self.logger.info(f"🎛️ 设置新源速度: {new_active} = {self.next_source_speed}x")

            # 步骤2: 在隐藏状态下启动新源播放（预加载）
            self._play_source(new_active)
            self.logger.info(f"▶️ 预加载新源: {new_active}")

            # 步骤3: 等待新源启动（减少等待时间）
            time.sleep(0.1)  # 减少等待时间从0.2秒到0.1秒

            # 步骤4: 同时显示新源和隐藏旧源（原子操作）
            self._show_source(new_active)
            if old_active:
                self._hide_source(old_active)

            self._source_states[new_active] = VideoSourceState.VISIBLE
            if old_active:
                self._source_states[old_active] = VideoSourceState.HIDDEN

            self.logger.info(f"🔄 原子切换: 显示 {new_active}, 隐藏 {old_active}")

            # 步骤5: 更新当前激活源状态
            self.current_active_source = new_active
            self.next_source = old_active

            # 步骤6: 停止旧源播放（在隐藏后）
            if old_active:
                self._stop_source(old_active)
                self.logger.info(f"🛑 停止旧源播放: {old_active}")

            # 步骤7: 确保只有当前激活源显示
            self._ensure_only_current_source_visible()

            # 步骤8: 为新的下一个视频源生成随机速度（用于下次切换）
            self._generate_random_speed_for_next_source()

            # 步骤9: 更新切换时间
            self._last_switch_time = current_time

            self.logger.info(f"✅ 无黑屏切换完成，当前激活: {self.current_active_source}, 下一个: {self.next_source} (预设速度: {self.next_source_speed}x)")

            if self.on_source_switched:
                self.on_source_switched(self.current_active_source, old_active)

            return True

        except Exception as e:
            self.logger.error(f"❌ 切换视频源失败: {e}")
            if self.on_switch_error:
                self.on_switch_error(str(e))
            return False

    def _prepare_next_source_for_seamless_switch(self):
        """
        精确切换第一阶段：提前1秒准备新源
        按照您的要求：新源变速 → 播放新源
        """
        try:
            if not self.next_source:
                self.logger.error("❌ 下一个视频源未设置，无法准备")
                return False

            self.logger.info(f"🎬 开始精确切换第一阶段：准备新源 {self.next_source}")

            # 步骤1: 为新源生成随机速度并设置
            self._generate_random_speed_for_next_source()
            self._set_source_speed(self.next_source, self.next_source_speed)
            self.logger.info(f"🎛️ 新源变速完成: {self.next_source} = {self.next_source_speed}x")

            # 步骤2: 在隐藏状态下播放新源（预加载）
            self._play_source(self.next_source)
            self.logger.info(f"▶️ 新源播放启动: {self.next_source} (隐藏状态)")

            return True

        except Exception as e:
            self.logger.error(f"❌ 准备新源失败: {e}")
            return False

    def _execute_final_switch(self):
        """
        精确切换第二阶段：最终切换
        按照您的要求：显示新源 → 隐藏旧源 → 停止旧源
        """
        try:
            if not self.next_source:
                self.logger.error("❌ 下一个视频源未设置，无法执行最终切换")
                return False

            old_active = self.current_active_source
            new_active = self.next_source

            self.logger.info(f"🎬 开始精确切换第二阶段：最终切换 {old_active} -> {new_active}")

            # 步骤3a: 确保新源正在播放（防止黑屏）
            self._ensure_source_playing(new_active)
            self.logger.info(f"🔄 确保新源播放: {new_active}")

            # 步骤3b: 显示新源（新源已经在播放）
            self._show_source(new_active)
            self._source_states[new_active] = VideoSourceState.VISIBLE
            self.logger.info(f"👁️ 显示新源: {new_active}")

            # 步骤4: 隐藏旧源
            if old_active:
                self._hide_source(old_active)
                self._source_states[old_active] = VideoSourceState.HIDDEN
                self.logger.info(f"🙈 隐藏旧源: {old_active}")

            # 步骤5: 停止旧源播放
            if old_active:
                self._stop_source(old_active)
                self.logger.info(f"🛑 停止旧源: {old_active}")

            # 更新状态
            self.current_active_source = new_active
            self.next_source = old_active

            # 确保只有当前激活源显示
            self._ensure_only_current_source_visible()

            # 更新切换时间
            self._last_switch_time = time.time()

            self.logger.info(f"✅ 精确切换完成，当前激活: {self.current_active_source}, 下一个: {self.next_source}")

            if self.on_source_switched:
                self.on_source_switched(self.current_active_source, old_active)

            return True

        except Exception as e:
            self.logger.error(f"❌ 执行最终切换失败: {e}")
            return False

    def _ensure_source_playing(self, source_name: str):
        """确保视频源正在播放（防止黑屏）"""
        try:
            if not source_name or not getattr(self.obs_controller, 'is_connected', getattr(self.obs_controller, 'connected', False)):
                return

            # 获取当前媒体状态
            if hasattr(self.obs_controller, 'get_media_status_sync'):
                status = self.obs_controller.get_media_status_sync(source_name)
            else:
                status = self.obs_controller.get_media_status(source_name)

            if not status:
                self.logger.warning(f"⚠️ 无法获取源状态，强制播放: {source_name}")
                self._play_source(source_name)
                return

            media_state = status.get('media_state', '')

            # 检查是否需要启动播放
            if media_state not in ['OBS_MEDIA_STATE_PLAYING']:
                self.logger.warning(f"⚠️ 源未播放(状态: {media_state})，强制播放: {source_name}")
                self._play_source(source_name)

                # 等待一小段时间让播放启动
                time.sleep(0.05)

                # 再次检查状态
                if hasattr(self.obs_controller, 'get_media_status_sync'):
                    new_status = self.obs_controller.get_media_status_sync(source_name)
                else:
                    new_status = self.obs_controller.get_media_status(source_name)

                if new_status:
                    new_state = new_status.get('media_state', '')
                    self.logger.info(f"🔄 播放后状态: {source_name} = {new_state}")
                else:
                    self.logger.warning(f"⚠️ 无法确认播放状态: {source_name}")
            else:
                self.logger.info(f"✅ 源已在播放: {source_name} (状态: {media_state})")

        except Exception as e:
            self.logger.error(f"❌ 确保源播放失败: {e}")
            # 出错时强制播放
            self._play_source(source_name)

    def _get_dynamic_preparation_threshold(self) -> float:
        """根据当前播放速度动态计算准备时间阈值"""
        try:
            # 基础准备时间 / 播放速度 = 实际需要的准备时间
            # 例如：1.5秒基础时间，2x速度 = 需要提前3秒准备
            dynamic_threshold = self._base_preparation_threshold * self._current_playback_speed

            # 限制在合理范围内（0.5秒到5秒）
            dynamic_threshold = max(0.5, min(5.0, dynamic_threshold))

            return dynamic_threshold
        except Exception as e:
            self.logger.error(f"计算动态准备时间失败: {e}")
            return self._base_preparation_threshold

    def _update_current_playback_speed(self, source_name: str):
        """更新当前播放速度（从OBS获取实际播放速度）"""
        try:
            if not source_name or not getattr(self.obs_controller, 'is_connected', getattr(self.obs_controller, 'connected', False)):
                return

            # 尝试获取当前源的播放速度设置
            # 注意：这需要OBS控制器支持获取播放速度
            if hasattr(self.obs_controller, 'get_source_speed'):
                speed = self.obs_controller.get_source_speed(source_name)
                if speed and speed > 0:
                    self._current_playback_speed = speed
                    return

            # 如果无法获取实际速度，使用预设速度
            if source_name == self.current_active_source:
                # 当前激活源可能使用之前设置的速度
                self._current_playback_speed = getattr(self, 'current_source_speed', 1.0)
            elif source_name == self.next_source:
                # 下一个源使用预设速度
                self._current_playback_speed = self.next_source_speed

        except Exception as e:
            self.logger.error(f"更新播放速度失败: {e}")
            self._current_playback_speed = 1.0

    def _is_source_visible(self, source_name: str) -> bool:
        """检查视频源是否可见"""
        try:
            if not getattr(self.obs_controller, 'is_connected', getattr(self.obs_controller, 'connected', False)):
                return False

            # 获取当前场景
            current_scene = self._get_current_scene()
            if not current_scene:
                return False

            # 获取场景中的源列表
            scene_items = self._get_scene_items(current_scene)

            # 查找指定源并检查其可见性
            for item in scene_items:
                if item.get("sourceName") == source_name:
                    return item.get("sceneItemEnabled", False)

            return False

        except Exception as e:
            self.logger.error(f"检查源可见性失败: {e}")
            return False

    def _get_top_visible_source(self) -> Optional[str]:
        """获取最上面显示的视频源"""
        try:
            if not getattr(self.obs_controller, 'is_connected', getattr(self.obs_controller, 'connected', False)):
                return self.video_source_a

            # 获取当前场景
            current_scene = self._get_current_scene()
            if not current_scene:
                return self.video_source_a

            # 获取场景中的源列表（按z-order排序）
            scene_items = self._get_scene_items(current_scene)

            # 查找可见的视频源，返回z-order最高的
            visible_sources = []
            for item in scene_items:
                source_name = item.get("sourceName")
                if (source_name in [self.video_source_a, self.video_source_b] and
                    item.get("sceneItemEnabled", False)):
                    visible_sources.append({
                        'name': source_name,
                        'index': item.get("sceneItemIndex", 0)
                    })

            if not visible_sources:
                return self.video_source_a

            # 按索引排序，返回最高的（索引最大的）
            visible_sources.sort(key=lambda x: x['index'], reverse=True)
            return visible_sources[0]['name']

        except Exception as e:
            self.logger.error(f"获取最上面视频源失败: {e}")
            return self.video_source_a

    def _show_source(self, source_name: str):
        """显示视频源"""
        try:
            if not source_name or not getattr(self.obs_controller, 'is_connected', getattr(self.obs_controller, 'connected', False)):
                return

            # 获取当前场景
            current_scene = self._get_current_scene()
            if not current_scene:
                self.logger.error("无法获取当前场景")
                return

            # 获取源ID
            source_id = self._get_source_id(current_scene, source_name)
            if source_id is None:
                self.logger.error(f"在场景 {current_scene} 中找不到源 {source_name}")
                return

            # 显示源
            result = self.obs_controller.send_request_sync("SetSceneItemEnabled", {
                "sceneName": current_scene,
                "sceneItemId": source_id,
                "sceneItemEnabled": True
            })

            if result is not None:
                self.logger.info(f"显示视频源: {source_name}")
            else:
                self.logger.error(f"显示视频源失败: {source_name}")

        except Exception as e:
            self.logger.error(f"显示视频源失败: {e}")

    def _hide_source(self, source_name: str):
        """隐藏视频源"""
        try:
            if not source_name or not getattr(self.obs_controller, 'is_connected', getattr(self.obs_controller, 'connected', False)):
                return

            # 获取当前场景
            current_scene = self._get_current_scene()
            if not current_scene:
                self.logger.error("无法获取当前场景")
                return

            # 获取源ID
            source_id = self._get_source_id(current_scene, source_name)
            if source_id is None:
                self.logger.error(f"在场景 {current_scene} 中找不到源 {source_name}")
                return

            # 隐藏源
            result = self.obs_controller.send_request_sync("SetSceneItemEnabled", {
                "sceneName": current_scene,
                "sceneItemId": source_id,
                "sceneItemEnabled": False
            })

            if result is not None:
                self.logger.info(f"隐藏视频源: {source_name}")
            else:
                self.logger.error(f"隐藏视频源失败: {source_name}")

        except Exception as e:
            self.logger.error(f"隐藏视频源失败: {e}")

    def _play_source(self, source_name: str):
        """播放视频源（修复黑屏问题）"""
        try:
            if not source_name or not getattr(self.obs_controller, 'is_connected', getattr(self.obs_controller, 'connected', False)):
                return

            # 修复黑屏问题：直接重新开始播放，不要先停止
            # 步骤1: 直接重新开始播放视频源
            restart_result = self.obs_controller.send_request_sync("TriggerMediaInputAction", {
                "inputName": source_name,
                "mediaAction": "OBS_WEBSOCKET_MEDIA_INPUT_ACTION_RESTART"
            })

            if restart_result is not None:
                self.logger.info(f"🔄 重新开始播放视频源: {source_name}")
            else:
                # 如果重新开始失败，尝试普通播放
                play_result = self.obs_controller.send_request_sync("TriggerMediaInputAction", {
                    "inputName": source_name,
                    "mediaAction": "OBS_WEBSOCKET_MEDIA_INPUT_ACTION_PLAY"
                })

                if play_result is not None:
                    self.logger.info(f"▶️ 播放视频源: {source_name}")
                else:
                    self.logger.error(f"❌ 播放视频源失败: {source_name}")

        except Exception as e:
            self.logger.error(f"播放视频源失败: {e}")

    def _stop_source(self, source_name: str):
        """停止视频源播放"""
        try:
            if not source_name or not getattr(self.obs_controller, 'is_connected', getattr(self.obs_controller, 'connected', False)):
                return

            # 停止视频源播放
            result = self.obs_controller.send_request_sync("TriggerMediaInputAction", {
                "inputName": source_name,
                "mediaAction": "OBS_WEBSOCKET_MEDIA_INPUT_ACTION_STOP"
            })

            if result is not None:
                self.logger.info(f"🛑 停止视频源播放: {source_name}")
            else:
                self.logger.error(f"❌ 停止视频源播放失败: {source_name}")

        except Exception as e:
            self.logger.error(f"停止视频源播放失败: {e}")

    @handle_exceptions("DualVideoManager")
    def get_current_video_status(self) -> Dict[str, Any]:
        """获取当前激活视频的状态"""
        try:
            if not self.current_active_source:
                return {}

            # 获取当前激活源的状态
            if hasattr(self.obs_controller, 'get_media_status_sync'):
                status = self.obs_controller.get_media_status_sync(self.current_active_source)
            else:
                status = self.obs_controller.get_media_status(self.current_active_source)
            if status:
                status['active_source'] = self.current_active_source
                status['next_source'] = self.next_source
                status['source_state'] = self._source_states.get(self.current_active_source, VideoSourceState.HIDDEN).value

            return status

        except Exception as e:
            self.logger.error(f"获取当前视频状态失败: {e}")
            return {}

    @handle_exceptions("DualVideoManager")
    def manual_switch(self):
        """手动切换视频源"""
        try:
            if not self.next_source:
                self.logger.warning("未设置下一个视频源，无法手动切换")
                return False

            self.logger.info("执行手动视频源切换")
            self._switch_to_next_source()
            return True

        except Exception as e:
            self.logger.error(f"手动切换视频源失败: {e}")
            return False

    @handle_exceptions("DualVideoManager")
    def set_switch_threshold(self, seconds: float):
        """设置切换阈值（提前多少秒准备下一个视频）"""
        try:
            if seconds < 0:
                seconds = 0
            elif seconds > 30:
                seconds = 30

            self.switch_threshold_seconds = seconds
            self.logger.info(f"设置切换阈值: {seconds}秒")

        except Exception as e:
            self.logger.error(f"设置切换阈值失败: {e}")

    @handle_exceptions("DualVideoManager")
    def set_speed_range(self, min_speed: float, max_speed: float):
        """设置变速范围"""
        try:
            if min_speed <= 0 or max_speed <= 0 or min_speed >= max_speed:
                self.logger.error("无效的变速范围")
                return

            self.speed_range = (min_speed, max_speed)
            self.logger.info(f"设置变速范围: {min_speed} - {max_speed}")

        except Exception as e:
            self.logger.error(f"设置变速范围失败: {e}")

    @handle_exceptions("DualVideoManager")
    def set_next_source_speed(self, speed: float):
        """设置下一个视频源的预设播放速度"""
        try:
            min_speed, max_speed = self.speed_range
            if speed < min_speed or speed > max_speed:
                self.logger.error(f"速度 {speed} 超出范围 [{min_speed}, {max_speed}]")
                return

            self.next_source_speed = speed
            self.logger.info(f"设置下一个视频源预设速度: {speed}x")

            # 如果下一个源已经在准备中，立即应用新的速度
            if (self.next_source and
                self._source_states.get(self.next_source) == VideoSourceState.PREPARING):
                self._set_source_speed(self.next_source, speed)
                self.logger.info(f"立即应用新速度到准备中的源: {self.next_source} = {speed}x")

        except Exception as e:
            self.logger.error(f"设置下一个视频源速度失败: {e}")

    @handle_exceptions("DualVideoManager")
    def prepare_next_source_with_speed(self, speed: float):
        """手动准备下一个视频源并设置指定速度"""
        try:
            if not self.next_source:
                self.logger.warning("未设置下一个视频源")
                return False

            # 设置速度
            self.set_next_source_speed(speed)

            # 强制准备下一个源
            self._source_states[self.next_source] = VideoSourceState.HIDDEN  # 重置状态
            self._prepare_next_source()

            return True

        except Exception as e:
            self.logger.error(f"手动准备下一个视频源失败: {e}")
            return False

    @handle_exceptions("DualVideoManager")
    def get_manager_status(self) -> Dict[str, Any]:
        """获取管理器状态"""
        try:
            return {
                'video_source_a': self.video_source_a,
                'video_source_b': self.video_source_b,
                'current_active_source': self.current_active_source,
                'next_source': self.next_source,
                'monitoring': self.monitoring,
                'switch_threshold_seconds': self.switch_threshold_seconds,
                'speed_range': self.speed_range,
                'next_source_speed': self.next_source_speed,
                'source_states': {k: v.value for k, v in self._source_states.items()},
                'obs_connected': getattr(self.obs_controller, 'is_connected', getattr(self.obs_controller, 'connected', False))
            }

        except Exception as e:
            self.logger.error(f"获取管理器状态失败: {e}")
            return {}

    def _get_current_scene(self) -> Optional[str]:
        """获取当前场景名称"""
        try:
            if not getattr(self.obs_controller, 'is_connected', getattr(self.obs_controller, 'connected', False)):
                return None

            result = self.obs_controller.send_request_sync("GetCurrentProgramScene")
            if result:
                return result.get("currentProgramSceneName")
            return None

        except Exception as e:
            self.logger.error(f"获取当前场景失败: {e}")
            return None

    def _get_scene_items(self, scene_name: str) -> List[Dict[str, Any]]:
        """获取场景中的源列表"""
        try:
            if not getattr(self.obs_controller, 'is_connected', getattr(self.obs_controller, 'connected', False)):
                return []

            result = self.obs_controller.send_request_sync("GetSceneItemList", {
                "sceneName": scene_name
            })

            if result and "sceneItems" in result:
                return result["sceneItems"]
            return []

        except Exception as e:
            self.logger.error(f"获取场景源列表失败: {e}")
            return []

    def _get_source_id(self, scene_name: str, source_name: str) -> Optional[int]:
        """获取源在场景中的ID"""
        try:
            scene_items = self._get_scene_items(scene_name)

            for item in scene_items:
                if item.get("sourceName") == source_name:
                    return item.get("sceneItemId")

            return None

        except Exception as e:
            self.logger.error(f"获取源ID失败: {e}")
            return None

    def _set_source_speed(self, source_name: str, speed: float):
        """设置视频源播放速度"""
        try:
            if not source_name or not getattr(self.obs_controller, 'is_connected', getattr(self.obs_controller, 'connected', False)):
                return

            # 限制速度范围
            min_speed, max_speed = self.speed_range
            speed = max(min_speed, min(max_speed, speed))

            # 设置媒体源播放速度
            result = self.obs_controller.send_request_sync("SetInputSettings", {
                "inputName": source_name,
                "inputSettings": {
                    "speed_percent": int(speed * 100)
                }
            })

            if result is not None:
                self.logger.info(f"设置视频源速度: {source_name} = {speed}x")
            else:
                self.logger.error(f"设置视频源速度失败: {source_name}")

        except Exception as e:
            self.logger.error(f"设置视频源速度失败: {e}")

    @handle_exceptions("DualVideoManager")
    def set_source_speed(self, source_name: str, speed: float):
        """公开方法：设置指定源的播放速度"""
        try:
            if source_name not in [self.video_source_a, self.video_source_b]:
                self.logger.error(f"无效的视频源: {source_name}")
                return

            self._set_source_speed(source_name, speed)

        except Exception as e:
            self.logger.error(f"设置源播放速度失败: {e}")

    @handle_exceptions("DualVideoManager")
    def force_switch_to_source(self, source_name: str):
        """强制切换到指定视频源"""
        try:
            if source_name not in [self.video_source_a, self.video_source_b]:
                self.logger.error(f"无效的视频源: {source_name}")
                return False

            if source_name == self.current_active_source:
                self.logger.info(f"已经是当前激活源: {source_name}")
                # 即使是当前源，也要确保只有它显示
                self._ensure_only_current_source_visible()
                return True

            old_active = self.current_active_source

            self.logger.info(f"无缝强制切换: {old_active} -> {source_name}")

            # 关键修复3: 强制切换也采用无缝逻辑

            # 步骤1: 预设目标视频源的播放速度（如果还没有预设）
            self._set_source_speed(source_name, self.next_source_speed)
            self.logger.info(f"确保目标源速度: {source_name} = {self.next_source_speed}x")

            # 步骤2: 显示目标源
            self._show_source(source_name)
            self._source_states[source_name] = VideoSourceState.VISIBLE
            self.logger.info(f"显示目标源: {source_name}")

            # 步骤3: 播放目标源
            self._play_source(source_name)

            # 步骤4: 更新当前激活源状态
            self.current_active_source = source_name
            self.next_source = self.video_source_b if source_name == self.video_source_a else self.video_source_a

            # 步骤5: 隐藏旧的激活源（在新源已显示后）
            if old_active:
                self._hide_source(old_active)
                self._source_states[old_active] = VideoSourceState.HIDDEN
                self.logger.info(f"隐藏旧源: {old_active}")

            # 步骤6: 最终确保只有当前激活源显示
            self._ensure_only_current_source_visible()

            self.logger.info(f"强制切换到视频源: {source_name}")

            if self.on_source_switched:
                self.on_source_switched(source_name, old_active)

            return True

        except Exception as e:
            self.logger.error(f"强制切换视频源失败: {e}")
            return False

    @handle_exceptions("DualVideoManager")
    def ensure_single_source_display(self):
        """公开方法：确保只有一个媒体源处于显示状态"""
        try:
            self._ensure_single_source_visible()
            if self.current_active_source:
                self._ensure_only_current_source_visible()
            self.logger.info("已确保只有一个媒体源显示")
            return True
        except Exception as e:
            self.logger.error(f"确保单一媒体源显示失败: {e}")
            return False

    @handle_exceptions("DualVideoManager")
    def get_all_visible_media_sources(self) -> List[str]:
        """获取所有可见的媒体源列表"""
        try:
            current_scene = self._get_current_scene()
            if not current_scene:
                return []

            scene_items = self._get_scene_items(current_scene)
            visible_sources = []

            for item in scene_items:
                source_name = item.get("sourceName")
                is_enabled = item.get("sceneItemEnabled", False)

                if is_enabled and self._is_media_source_name(source_name):
                    visible_sources.append(source_name)

            return visible_sources

        except Exception as e:
            self.logger.error(f"获取可见媒体源列表失败: {e}")
            return []

    def _check_active_source_status_ui_design_style(self):
        """检查当前激活源的播放状态（参考ui_design.py的实现）"""
        if not self.current_active_source:
            return

        try:
            # 获取当前状态（参考ui_design.py第3164行）
            if hasattr(self.obs_controller, 'get_media_status_sync'):
                status = self.obs_controller.get_media_status_sync(self.current_active_source)
            else:
                status = self.obs_controller.get_media_status(self.current_active_source)

            if status:
                # 记录当前状态
                current_state = status.get('media_state', 'OBS_MEDIA_STATE_NONE')
                progress_percent = status.get('progress_percent', 0.0)
                progress = progress_percent / 100.0  # 转换为0-1范围
                duration = status.get('media_duration', 0)  # 总时长（毫秒）
                position = status.get('media_cursor', 0)  # 当前位置（毫秒）

                # 保存上一次的进度和状态，用于检测循环完成（参考ui_design.py第3173行）
                if not hasattr(self, "last_video_progress"):
                    self.last_video_progress = progress
                    self.last_video_state = current_state

                # 如果进度从高变低，说明循环了一次（参考ui_design.py第3179行）
                progress_reset = (self.last_video_progress > 0.9 and progress < 0.1)

                # 记录日志，但不要太频繁（参考ui_design.py第3182行）
                current_time = time.time()
                if not hasattr(self, '_last_status_log_time_ui'):
                    self._last_status_log_time_ui = 0

                if current_time - self._last_status_log_time_ui >= 5.0:
                    remaining_time = (duration - position) / 1000.0 if duration > 0 else 0.0
                    self.logger.info(f"📊 主视频状态: {current_state}, 进度: {progress*100:.2f}%, 位置: {position/1000:.2f}秒, 总时长: {duration/1000:.2f}秒, 剩余: {remaining_time:.1f}秒")
                    self._last_status_log_time_ui = current_time

                # 检测播放结束的多种状态（优化检测逻辑，减少频繁切换）
                should_switch = False
                switch_reason = ""

                # 检查切换冷却时间
                current_time = time.time()
                if current_time - self._last_switch_time < self._switch_cooldown:
                    # 在冷却期间，不进行任何切换检测
                    pass
                else:
                    # 更新当前播放速度
                    self._update_current_playback_speed(self.current_active_source)

                    # 获取动态准备时间阈值
                    dynamic_threshold = self._get_dynamic_preparation_threshold()

                    # 新的精确切换逻辑：根据播放速度动态调整准备时间
                    if duration > 0 and position > 0:
                        remaining_time = (duration - position) / 1000.0

                        # 阶段1: 动态提前时间开始准备新源（变速+播放）
                        if remaining_time <= dynamic_threshold and remaining_time > 0.1 and not self._next_source_prepared:
                            self.logger.info(f"🎬 剩余时间{remaining_time:.1f}秒（阈值{dynamic_threshold:.1f}秒，速度{self._current_playback_speed}x），开始准备新源: {self.next_source}")
                            self._prepare_next_source_for_seamless_switch()
                            self._next_source_prepared = True
                            self._preparation_start_time = current_time

                        # 阶段2: 剩余时间很少时执行最终切换（显示新源+隐藏旧源+停止旧源）
                        elif remaining_time <= 0.1 and self._next_source_prepared:
                            should_switch = True
                            switch_reason = f"🎬 精确切换：剩余时间{remaining_time:.1f}秒，执行最终切换: {self.current_active_source}"

                    # 检测1: ENDED状态（最准确，优先级最高）
                    if current_state == "OBS_MEDIA_STATE_ENDED":
                        should_switch = True
                        switch_reason = f"🎬 检测到视频播放结束(ENDED): {self.current_active_source}"

                    # 检测2: STOPPED状态且进度>95%（提高阈值，减少误触发）
                    elif current_state == "OBS_MEDIA_STATE_STOPPED":
                        if progress > 0.95:  # 提高阈值到95%
                            should_switch = True
                            switch_reason = f"🎬 视频停止且进度{progress*100:.1f}%，判断为播放完成: {self.current_active_source}"

                    # 检测3: 无状态且进度>95%（提高阈值，减少误触发）
                    elif current_state == "OBS_MEDIA_STATE_NONE" or current_state == "":
                        if progress > 0.95:  # 提高阈值到95%
                            should_switch = True
                            switch_reason = f"🎬 无媒体状态且进度{progress*100:.1f}%，判断为播放完成: {self.current_active_source}"

                    # 检测4: 进度重置（循环完成）- 添加更严格的条件
                    elif progress_reset and self.last_video_progress > 0.95:  # 只有上次进度>95%才认为是真正的循环完成
                        should_switch = True
                        switch_reason = f"🔄 检测到主视频进度重置，循环完成一次: {self.current_active_source} (上次进度: {self.last_video_progress*100:.1f}%)"

                # 执行切换
                if should_switch:
                    self.logger.info(switch_reason)

                    # 确保下一个视频源存在
                    if not self.next_source:
                        self.logger.error("❌ 下一个视频源未设置，无法自动切换")
                        return False

                    # 如果新源已经准备好，执行最终切换（显示+隐藏+停止）
                    if self._next_source_prepared:
                        switch_success = self._execute_final_switch()
                    else:
                        # 如果新源未准备，使用传统切换方法
                        old_speed = f"{self.next_source_speed:.2f}x"
                        self._generate_random_speed_for_next_source()
                        new_speed = f"{self.next_source_speed:.2f}x"
                        switch_success = self._switch_to_next_source()

                    if switch_success:
                        self.logger.info(f"✅ 自动切换完成: {self.current_active_source}")
                        # 重置准备状态
                        self._next_source_prepared = False
                        self._preparation_start_time = 0
                        if hasattr(self, 'on_switch_success') and self.on_switch_success:
                            self.on_switch_success(self.current_active_source, self.next_source)
                        return True
                    else:
                        self.logger.error(f"❌ 自动切换失败")
                        if hasattr(self, 'on_switch_error') and self.on_switch_error:
                            self.on_switch_error("自动切换失败")
                        return False

                # 更新上一次的进度和状态（参考ui_design.py第3416行）
                self.last_video_progress = progress
                self.last_video_state = current_state

        except Exception as e:
            self.logger.error(f"❌ 检查主视频状态出错: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())

        return False

    def __del__(self):
        """析构函数"""
        try:
            self.stop_monitoring()
        except:
            pass
