#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
播放完成处理测试程序
用于测试音频播放完成回调和删除逻辑是否正常工作
"""

import sys
import os
import time
import threading
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QTextEdit, QWidget
from PyQt5.QtCore import QTimer, pyqtSignal, QObject

class TestAudioPlayer:
    """测试用的简单音频播放器"""
    def __init__(self):
        self.is_playing = False
        self.current_file = None
        self.on_playback_end = None
        self.play_thread = None
        self.main_window = None

    def play_file(self, file_path):
        """播放音频文件（模拟）"""
        try:
            if self.is_playing:
                print(f"⚠️ 已有音频在播放，停止当前播放")
                self.stop()

            self.current_file = file_path
            self.is_playing = True
            
            print(f"🎵 开始播放: {file_path}")
            
            # 启动播放线程
            self.play_thread = threading.Thread(target=self._play_thread, args=(file_path,))
            self.play_thread.daemon = True
            self.play_thread.start()
            
            return True
            
        except Exception as e:
            print(f"❌ 播放失败: {e}")
            return False

    def _play_thread(self, file_path):
        """播放线程（模拟播放3秒）"""
        try:
            print(f"🎵 播放线程开始: {file_path}")
            
            # 模拟播放3秒
            for i in range(30):  # 3秒，每100ms检查一次
                if not self.is_playing:
                    print(f"⏹️ 播放被中断: {file_path}")
                    break
                time.sleep(0.1)
            
            if self.is_playing:
                print(f"✅ 播放完成: {file_path}")
            
        except Exception as e:
            print(f"❌ 播放线程异常: {e}")
        finally:
            # 🔥 关键：无论如何都要触发播放完成回调
            file_to_callback = self.current_file
            self.is_playing = False
            print(f"🎵🎵🎵 音频播放线程结束: {file_to_callback}")

            # 🔥 关键修复：确保回调被触发
            if self.on_playback_end:
                print(f"🔄🔄🔄 触发播放完成回调: {file_to_callback}")
                try:
                    # 🔥 修复：直接在当前线程调用回调，不使用QTimer
                    # 因为QTimer在非Qt线程中无法正常工作
                    self.on_playback_end(file_to_callback)
                    print(f"✅ 播放完成回调已执行")
                except Exception as callback_error:
                    print(f"❌ 播放完成回调异常: {callback_error}")
                    import traceback
                    traceback.print_exc()
            else:
                print(f"⚠️⚠️⚠️ 没有设置播放完成回调")

    def stop(self):
        """停止播放"""
        self.is_playing = False
        print(f"⏹️ 停止播放")

class PlaybackTestWindow(QMainWindow):
    """播放完成测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("播放完成处理测试程序")
        self.setGeometry(100, 100, 800, 600)
        
        # 测试数据
        self.playlist_items = []
        self.current_playing_item = None
        
        # 创建音频播放器
        self.audio_player = TestAudioPlayer()
        self.audio_player.main_window = self
        self.audio_player.on_playback_end = self.on_audio_finished
        
        self.init_ui()
        self.init_test_data()
        
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("播放完成处理测试程序")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.add_time_report_btn = QPushButton("添加报时话术")
        self.add_time_report_btn.clicked.connect(self.add_time_report)
        button_layout.addWidget(self.add_time_report_btn)
        
        self.add_main_video_btn = QPushButton("添加主视频话术")
        self.add_main_video_btn.clicked.connect(self.add_main_video)
        button_layout.addWidget(self.add_main_video_btn)
        
        self.play_btn = QPushButton("开始播放")
        self.play_btn.clicked.connect(self.start_playback)
        button_layout.addWidget(self.play_btn)
        
        self.stop_btn = QPushButton("停止播放")
        self.stop_btn.clicked.connect(self.stop_playback)
        button_layout.addWidget(self.stop_btn)
        
        self.clear_btn = QPushButton("清空列表")
        self.clear_btn.clicked.connect(self.clear_playlist)
        button_layout.addWidget(self.clear_btn)
        
        layout.addLayout(button_layout)
        
        # 状态显示
        self.status_label = QLabel("状态: 就绪")
        self.status_label.setStyleSheet("font-weight: bold; margin: 5px;")
        layout.addWidget(self.status_label)
        
        # 播放列表显示
        playlist_label = QLabel("播放列表:")
        layout.addWidget(playlist_label)
        
        self.playlist_text = QTextEdit()
        self.playlist_text.setMaximumHeight(200)
        layout.addWidget(self.playlist_text)
        
        # 日志显示
        log_label = QLabel("测试日志:")
        layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        layout.addWidget(self.log_text)
        
    def init_test_data(self):
        """初始化测试数据"""
        self.log("🚀 播放完成处理测试程序启动")
        self.update_playlist_display()
        
    def log(self, message):
        """添加日志"""
        self.log_text.append(message)
        print(message)
        
    def add_time_report(self):
        """添加报时话术"""
        import time
        current_time = time.strftime("%H:%M:%S")
        
        item = {
            'id': len(self.playlist_items) + 1,
            'voice_type': '报时话术',
            'content': f'现在时间是{current_time}',
            'status': '已下载',
            'filename': f'time_report_{len(self.playlist_items)}.wav'
        }
        
        self.playlist_items.append(item)
        self.log(f"➕ 添加报时话术: {item['content']}")
        self.update_playlist_display()
        
    def add_main_video(self):
        """添加主视频话术"""
        item = {
            'id': len(self.playlist_items) + 1,
            'voice_type': '主视频话术',
            'content': f'这是第{len(self.playlist_items) + 1}个主视频话术',
            'status': '已下载',
            'filename': f'main_video_{len(self.playlist_items)}.wav'
        }
        
        self.playlist_items.append(item)
        self.log(f"➕ 添加主视频话术: {item['content']}")
        self.update_playlist_display()
        
    def update_playlist_display(self):
        """更新播放列表显示"""
        text = f"播放列表 (共{len(self.playlist_items)}项):\n"
        for i, item in enumerate(self.playlist_items):
            status_icon = "▶️" if item == self.current_playing_item else "⏸️"
            text += f"{i+1}. {status_icon} [{item['voice_type']}] {item['content']} (状态: {item['status']})\n"
        
        self.playlist_text.setPlainText(text)
        
    def start_playback(self):
        """开始播放"""
        if self.current_playing_item:
            self.log("⚠️ 已有项目在播放中")
            return
            
        # 查找下一个要播放的项目
        next_item = None
        for item in self.playlist_items:
            if item['status'] == '已下载':
                next_item = item
                break
                
        if not next_item:
            self.log("📋 没有可播放的项目")
            return
            
        # 开始播放
        self.current_playing_item = next_item
        next_item['status'] = '播放中'
        
        self.log(f"🎵 开始播放: {next_item['voice_type']} - {next_item['content']}")
        self.status_label.setText(f"状态: 播放中 - {next_item['content'][:20]}...")
        
        # 模拟播放
        success = self.audio_player.play_file(next_item['filename'])
        
        if not success:
            self.current_playing_item = None
            next_item['status'] = '播放失败'
            self.log("❌ 播放启动失败")
            
        self.update_playlist_display()
        
    def stop_playback(self):
        """停止播放"""
        self.audio_player.stop()
        if self.current_playing_item:
            self.current_playing_item['status'] = '已下载'
            self.current_playing_item = None
        self.status_label.setText("状态: 已停止")
        self.log("⏹️ 停止播放")
        self.update_playlist_display()
        
    def clear_playlist(self):
        """清空播放列表"""
        self.stop_playback()
        self.playlist_items.clear()
        self.log("🗑️ 清空播放列表")
        self.update_playlist_display()
        
    def on_audio_finished(self, file_path):
        """音频播放完成回调"""
        self.log(f"🎵🎵🎵 播放完成回调触发: {file_path}")
        
        if not self.current_playing_item:
            self.log("⚠️ 播放完成但没有当前播放项目")
            return
            
        # 保存播放完成的项目信息
        completed_item = self.current_playing_item.copy()
        completed_content = completed_item['content']
        completed_voice_type = completed_item['voice_type']
        completed_filename = completed_item.get('filename', '')
        
        self.log(f"🔍 播放完成处理:")
        self.log(f"  - 类型: {completed_voice_type}")
        self.log(f"  - 内容: {completed_content}")
        self.log(f"  - 文件名: {completed_filename}")
        
        # 清空当前播放项目
        self.current_playing_item = None
        
        # 🔥 关键测试：删除已播放的项目
        original_count = len(self.playlist_items)
        new_playlist_items = []
        deleted_count = 0
        
        for item in self.playlist_items:
            item_content = item.get('content', '')
            item_voice_type = item.get('voice_type', '')
            item_filename = item.get('filename', '')
            
            # 使用多种匹配方式
            should_delete = False
            
            # 方式1：精确匹配内容和类型
            if item_content == completed_content and item_voice_type == completed_voice_type:
                should_delete = True
                self.log(f"    ✅ 精确匹配成功")
            
            # 方式2：如果有文件名，通过文件名匹配
            elif completed_filename and item_filename and completed_filename == item_filename:
                should_delete = True
                self.log(f"    ✅ 文件名匹配成功: {completed_filename}")
            
            # 方式3：对于报时话术，只要类型匹配且状态为播放中就删除
            elif (completed_voice_type == '报时话术' and 
                  item_voice_type == '报时话术' and 
                  item.get('status') == '播放中'):
                should_delete = True
                self.log(f"    ✅ 报时话术状态匹配")

            if should_delete:
                deleted_count += 1
                self.log(f"🗑️🗑️🗑️ 删除已播放项目: {item_voice_type} - {item_content}")
            else:
                new_playlist_items.append(item)
        
        # 更新播放列表
        self.playlist_items = new_playlist_items
        
        if deleted_count > 0:
            self.log(f"✅ 成功删除 {deleted_count} 个已播放项目")
            self.log(f"🔍 删除后播放列表项目数: {len(self.playlist_items)}")
        else:
            self.log(f"⚠️ 未找到要删除的项目")
            
        self.status_label.setText("状态: 播放完成")
        self.update_playlist_display()
        
        # 自动播放下一个项目
        QTimer.singleShot(1000, self.start_playback)

def main():
    app = QApplication(sys.argv)
    window = PlaybackTestWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
