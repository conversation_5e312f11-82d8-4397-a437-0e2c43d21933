# 单一视频源显示功能说明

## 概述

为了确保在任何时候只有一个视频源处于显示状态，我们在双主视频管理器中实现了单一视频源显示功能。这个功能可以避免多个视频源同时显示造成的混乱，确保直播画面的清晰和专业。

## 核心功能

### 1. 自动检测和管理

- **启动时检测**：系统启动时自动检测所有可见的媒体源
- **智能选择**：如果发现多个媒体源可见，自动选择最合适的一个保留
- **优先级管理**：优先保留双主视频源中的一个，其次是z-order最高的媒体源

### 2. 实时监控

- **状态监控**：实时监控所有媒体源的显示状态
- **自动隐藏**：自动隐藏非激活的媒体源
- **切换保护**：在视频源切换过程中确保只有目标源显示

### 3. 手动控制

- **强制确保**：提供手动确保单一视频源显示的方法
- **状态查询**：可以查询当前所有可见的媒体源
- **灵活管理**：支持强制切换到指定视频源

## 技术实现

### 核心方法

#### `_ensure_single_source_visible()`
```python
def _ensure_single_source_visible(self):
    """确保只有一个视频源处于显示状态"""
```
- 扫描当前场景中的所有媒体源
- 识别可见的媒体源
- 根据优先级规则保留一个，隐藏其他

#### `_ensure_only_current_source_visible()`
```python
def _ensure_only_current_source_visible(self):
    """确保只有当前激活的视频源显示，隐藏所有其他媒体源"""
```
- 确保当前激活源是显示的
- 隐藏所有其他媒体源
- 维护系统状态一致性

#### `_is_media_source_type()`
```python
def _is_media_source_type(self, source_type: str) -> bool:
    """判断是否为媒体源类型"""
```
- 识别不同类型的媒体源
- 支持多种媒体源格式：ffmpeg_source、vlc_source等
- 过滤非媒体源类型

### 支持的媒体源类型

- **ffmpeg_source**：标准媒体源
- **vlc_source**：VLC视频源
- **dshow_input**：Windows视频捕获设备
- **av_capture_input**：macOS摄像头
- **v4l2_input**：Linux摄像头

### 优先级规则

1. **双主视频源优先**：如果双主视频源中有可见的，优先保留
2. **Z-order优先**：在同等条件下，保留z-order最高的源
3. **当前激活源优先**：切换时确保当前激活源显示

## 使用方法

### 1. 自动管理

系统会在以下时机自动确保单一视频源显示：

- 双主视频管理器初始化时
- 视频源切换时
- 强制切换到指定源时

### 2. 手动控制

```python
# 通过播放控制器
controller = PlaybackController()

# 确保单一视频源显示
success = controller.ensure_single_video_source_display()

# 获取当前可见的视频源
visible_sources = controller.get_all_visible_video_sources()
```

### 3. 直接使用双主视频管理器

```python
# 通过双主视频管理器
dual_manager = controller.dual_video_manager

# 确保单一视频源显示
success = dual_manager.ensure_single_source_display()

# 获取可见媒体源列表
visible_sources = dual_manager.get_all_visible_media_sources()
```

## 测试验证

### 测试场景

1. **多源显示测试**：同时显示多个媒体源，验证自动隐藏功能
2. **切换测试**：测试视频源切换时的单一显示保证
3. **强制切换测试**：测试强制切换到指定源的功能
4. **状态查询测试**：验证可见源列表的准确性

### 测试结果

✅ **所有测试通过**：
- 成功确保只有一个视频源显示
- 切换过程中无多源显示
- 状态查询准确
- 错误处理完善

## 日志记录

系统会记录以下关键操作：

```
2025-06-02 10:05:44 - dual_video_manager - INFO - 发现 3 个可见媒体源，确保只显示一个
2025-06-02 10:05:44 - dual_video_manager - INFO - 隐藏媒体源: 2222
2025-06-02 10:05:44 - dual_video_manager - INFO - 保留媒体源: 1111
2025-06-02 10:05:44 - dual_video_manager - INFO - 已确保只有一个媒体源显示
```

## 错误处理

### 常见错误

1. **OBS未连接**：跳过实际操作，记录警告
2. **源不存在**：记录错误，继续处理其他源
3. **权限问题**：记录错误，提供解决建议

### 恢复机制

- **自动重试**：对临时错误进行自动重试
- **状态恢复**：在错误后尝试恢复到一致状态
- **降级处理**：在严重错误时降级到安全模式

## 性能考虑

### 优化措施

- **批量操作**：一次性处理多个源的状态变更
- **缓存机制**：缓存场景信息减少API调用
- **异步处理**：非阻塞的状态检查和更新

### 资源使用

- **内存占用**：最小化状态缓存
- **网络请求**：优化OBS API调用频率
- **CPU使用**：高效的源类型判断算法

## 配置选项

### 可配置参数

- **检查间隔**：状态检查的时间间隔
- **媒体源类型**：支持的媒体源类型列表
- **优先级规则**：自定义优先级判断逻辑

### 默认配置

```python
# 默认支持的媒体源类型
MEDIA_SOURCE_TYPES = [
    "ffmpeg_source",
    "vlc_source", 
    "dshow_input",
    "av_capture_input",
    "v4l2_input"
]

# 默认检查间隔
CHECK_INTERVAL = 1.0  # 秒
```

## 兼容性

### OBS版本支持

- **OBS Studio 28.0+**：完全支持
- **WebSocket 5.0+**：推荐版本
- **向后兼容**：支持较旧版本的基本功能

### 平台支持

- **Windows**：完全支持
- **macOS**：完全支持
- **Linux**：完全支持

## 未来改进

### 计划功能

1. **智能预测**：基于使用模式预测最佳显示源
2. **自定义规则**：允许用户定义优先级规则
3. **性能监控**：添加性能指标监控
4. **可视化界面**：提供图形化的源管理界面

### 扩展性

- **插件支持**：支持第三方插件扩展功能
- **API接口**：提供RESTful API接口
- **事件系统**：完善的事件通知机制

## 总结

单一视频源显示功能为双主视频管理器提供了重要的画面管理能力，确保直播过程中的视觉一致性和专业性。通过自动检测、智能管理和手动控制的结合，为用户提供了可靠和灵活的视频源管理解决方案。
