# 播放功能修复说明

## 🔧 已修复的问题

### 1. 下载卡顿问题 ✅
**问题**：点击播放时，语音下载导致软件卡顿
**解决方案**：
- 实现了真正的异步下载机制
- 使用多线程下载队列（最多3个并发线程）
- 使用QTimer定期检查下载结果，避免阻塞UI
- 下载完成后自动启动播放控制器

### 2. 播放控制逻辑 ✅
**问题**：缺少播放优先级控制和播放状态管理
**解决方案**：
- 实现了完整的播放控制器系统
- 支持优先级播放：弹幕话术 > 报时话术 > 主视频话术
- 播放状态管理：未下载 → 已下载 → 播放中 → 播放完成（删除）
- 自动播放间隔控制
- 播放完成后自动删除项目

## 🎯 新的播放流程

### 启动播放
1. **点击播放按钮**
2. **初始化播放列表**：从话术时间段生成播放项目
3. **异步下载语音**：多线程并发下载，不阻塞UI
4. **启动播放控制器**：下载完成后自动开始播放

### 播放控制
1. **优先级选择**：按优先级选择下一个播放项目
2. **状态更新**：将状态更新为"播放中"
3. **副视频切换**：如果需要，切换到副视频
4. **播放音频**：使用系统默认播放器播放
5. **播放完成**：删除已播放项目，切回主视频，应用播放间隔

### 播放优先级
```
弹幕话术 (优先级: 1) > 报时话术 (优先级: 2) > 主视频话术 (优先级: 3)
```

## 📋 播放列表状态变化

### 状态流转
```
未下载 → 已下载 → 播放中 → [删除]
```

### 实时显示
- 播放列表会实时显示当前状态
- 正在播放的项目状态显示为"播放中"
- 播放完成的项目会自动从列表中删除

## 🎵 音频播放机制

### 播放方式
- 使用系统默认音频播放器
- Windows: `start` 命令
- macOS: `open` 命令  
- Linux: `xdg-open` 命令

### 播放时长估算
- 基于文本长度估算播放时间
- 公式：`max(3秒, 文本长度 × 0.2秒)`
- 播放完成后自动进入下一个项目

### 播放间隔
- 支持随机播放间隔（可在系统设置中配置）
- 默认：0-3秒随机间隔
- 每次播放完成后应用间隔

## 🎬 副视频切换逻辑

### 切换时机
- 播放包含副视频关键词的话术时
- 自动切换到对应的副视频源

### 切换流程
1. **播放开始**：切换到副视频
2. **播放进行**：保持副视频显示
3. **播放完成**：切换回主视频

### 当前实现
- 目前为日志输出（便于调试）
- 后续可集成实际的OBS切换逻辑

## 🔄 自动补充机制

### 主视频话术补充
- 播放完成的主视频话术会自动补充
- 从对应时间段随机选择新的话术
- 保持预备语音数量

### 弹幕和报时话术
- 弹幕话术：实时添加，播放完成后删除
- 报时话术：定时添加，播放完成后删除

## 🧪 测试方法

### 基本测试
1. **启动程序**：`python run_gui_qt5.py`
2. **点击播放**：观察播放列表变化
3. **查看状态**：观察状态从"未下载"→"已下载"→"播放中"
4. **播放完成**：观察项目自动删除

### 预期输出
```
🎵 初始化播放列表...
🔄 开始异步下载播放列表语音...
✅ 启动了 3 个下载线程
✅ 语音下载成功: abc123_0_100.wav
✅ 所有语音下载完成: 3/3
🎵 启动播放控制器...
🎯 选择播放: 主视频话术 - 欢迎来到直播间！{nick}你好！...
🎵 开始播放: 主视频话术 - 欢迎来到直播间！{nick}你好！...
✅ 开始播放音频: abc123_0_100.wav
✅ 语音播放完成: 欢迎来到直播间！{nick}你好！...
✅ 删除已播放项目: 欢迎来到直播间！{nick}你好！...
⏱️ 播放间隔: 1.5 秒
✅ 播放控制器已恢复
```

### 弹幕测试
1. **配置AI对话**：添加关键词和回复
2. **模拟弹幕**：输入包含关键词的内容
3. **观察结果**：弹幕话术自动添加到播放列表
4. **优先播放**：弹幕话术优先于其他话术播放

### 报时测试
1. **启用报时**：在系统设置中启用报时功能
2. **设置间隔**：设置较短的报时间隔（如1分钟）
3. **观察结果**：报时话术定时添加到播放列表

## 🔧 技术细节

### 异步下载实现
```python
# 多线程下载队列
download_queue = queue.Queue()
result_queue = queue.Queue()

# 限制并发数（避免过载）
max_workers = 3

# 定时检查结果（避免阻塞UI）
self.download_result_timer.start(500)  # 每500ms检查一次
```

### 播放控制器实现
```python
# 播放控制定时器
self.playback_timer.start(1000)  # 每1秒检查一次播放队列

# 优先级映射
priority_map = {
    '弹幕话术': 1,
    '报时话术': 2,
    '主视频话术': 3
}
```

### 播放间隔实现
```python
# 随机间隔
interval_seconds = random.uniform(min_interval, max_interval)

# 延迟恢复播放
QTimer.singleShot(int(interval_seconds * 1000), self.resume_playback_controller)
```

## 📁 生成的文件

### 语音文件
```
voices/
├── abc123_0_100.wav    # 主视频话术1
├── def456_0_100.wav    # 主视频话术2
├── ghi789_0_100.wav    # 主视频话术3
└── ...                 # 更多语音文件
```

### 文件命名规则
- 格式：`{文本哈希}_{主播ID}_{语音速度}.wav`
- 避免重复下载相同内容的语音

## ✅ 功能验证清单

- [x] 点击播放后播放列表有内容
- [x] 语音异步下载不卡顿UI
- [x] 下载状态实时更新
- [x] 播放按优先级进行
- [x] 播放状态显示"播放中"
- [x] 播放完成后自动删除
- [x] 播放间隔正常工作
- [x] 弹幕话术优先播放
- [x] 报时话术定时添加
- [x] 副视频切换逻辑（日志）

## 🚀 下一步优化

1. **集成真实音频播放器**：替换系统默认播放器
2. **OBS视频切换集成**：实现真实的副视频切换
3. **播放时长检测**：获取真实的音频播放时长
4. **播放队列可视化**：显示播放队列和当前播放状态
5. **错误处理优化**：更好的网络错误和播放错误处理

现在您可以测试修复后的播放功能了！🎉
