#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查主程序中的副视频功能
直接验证主程序播放列表中的副视频是否正常工作
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_sub_video_in_main():
    """检查主程序中的副视频功能"""
    print("🔍 检查主程序中的副视频功能...")
    print("=" * 50)
    
    # 1. 检查副视频配置文件
    print("\n📋 步骤1：检查副视频配置文件")
    sub_video_file = Path("data/sub_videos.json")
    if sub_video_file.exists():
        try:
            with open(sub_video_file, 'r', encoding='utf-8') as f:
                sub_videos = json.load(f)
            print(f"✅ 副视频配置文件存在，包含 {len(sub_videos)} 个配置")
            
            for keyword, config in sub_videos.items():
                video_source = config.get('video_source', '未知')
                print(f"  • {keyword} → {video_source}")
        except Exception as e:
            print(f"❌ 读取副视频配置失败: {e}")
    else:
        print("❌ 副视频配置文件不存在")
    
    # 2. 检查播放列表文件
    print("\n📋 步骤2：检查播放列表中的副视频")
    playlist_file = Path("data/playlist.json")
    if playlist_file.exists():
        try:
            with open(playlist_file, 'r', encoding='utf-8') as f:
                playlist = json.load(f)
            
            print(f"✅ 播放列表文件存在，包含 {len(playlist)} 个项目")
            
            sub_video_count = 0
            for i, item in enumerate(playlist):
                sub_video = item.get('sub_video', '无')
                voice_type = item.get('voice_type', '未知')
                content = item.get('content', '')[:30]
                status = item.get('status', '未知')
                
                print(f"  项目 {i+1}: {voice_type}")
                print(f"    内容: {content}...")
                print(f"    状态: {status}")
                print(f"    副视频: {sub_video}")
                
                if sub_video and sub_video != '无':
                    sub_video_count += 1
                    print(f"    ✅ 检测到副视频需求")
                else:
                    print(f"    ⚪ 无副视频需求")
                print()
            
            if sub_video_count > 0:
                print(f"✅ 播放列表中有 {sub_video_count} 个项目需要副视频")
            else:
                print("⚠️ 播放列表中没有项目需要副视频")
                
        except Exception as e:
            print(f"❌ 读取播放列表失败: {e}")
    else:
        print("⚠️ 播放列表文件不存在")
    
    # 3. 模拟副视频触发测试
    print("\n🔍 步骤3：模拟副视频触发测试")
    
    try:
        # 导入主程序的副视频管理器
        from src.managers.sub_video_manager import SubVideoManager
        
        # 创建副视频管理器实例
        sub_video_manager = SubVideoManager()
        
        # 测试弹幕
        test_messages = [
            "感谢老板的火箭，太给力了！",
            "谢谢大家的礼物支持",
            "主播666，继续加油！",
            "普通弹幕，没有关键词"
        ]
        
        print("测试弹幕副视频触发:")
        for msg in test_messages:
            # 检查是否触发副视频
            triggered = False
            triggered_keyword = None
            video_source = None
            
            if hasattr(sub_video_manager, 'sub_videos'):
                sub_videos = sub_video_manager.sub_videos
                if isinstance(sub_videos, dict):
                    for keyword, config in sub_videos.items():
                        if keyword and keyword.lower() in msg.lower():
                            triggered = True
                            triggered_keyword = keyword
                            video_source = config.get('video_source', '未知')
                            break
            
            print(f"  弹幕: {msg}")
            if triggered:
                print(f"    ✅ 触发副视频: {triggered_keyword} → {video_source}")
            else:
                print(f"    ⚪ 无副视频触发")
            print()
            
    except Exception as e:
        print(f"❌ 副视频触发测试失败: {e}")
    
    # 4. 检查主程序中的副视频方法
    print("\n🔧 步骤4：检查主程序中的副视频方法")
    
    try:
        from run_gui_qt5 import MainWindow
        
        # 检查关键方法是否存在
        methods_to_check = [
            'check_sub_video_trigger',
            'handle_sub_video_playback',
            'switch_to_sub_video_with_obs',
            'switch_back_to_main_video_with_obs',
            'add_danmaku_to_playlist'
        ]
        
        for method_name in methods_to_check:
            if hasattr(MainWindow, method_name):
                print(f"  ✅ {method_name} 方法存在")
            else:
                print(f"  ❌ {method_name} 方法缺失")
                
    except Exception as e:
        print(f"❌ 检查主程序方法失败: {e}")
    
    # 5. 生成测试建议
    print("\n💡 步骤5：测试建议")
    print("要测试副视频功能，请按以下步骤操作：")
    print()
    print("1. 启动主程序 (python run_gui_qt5.py)")
    print("2. 确保OBS已连接并配置了视频源")
    print("3. 在弹幕设置中添加包含关键词的测试弹幕，例如：")
    print("   • '感谢老板的火箭，太给力了！'")
    print("   • '谢谢大家的礼物支持'")
    print("4. 观察播放列表中是否显示副视频标识")
    print("5. 点击播放按钮，观察是否触发副视频切换")
    print()
    print("🎯 预期效果：")
    print("• 播放列表中显示副视频源名称")
    print("• 播放时自动切换到副视频")
    print("• 播放完成后自动切回主视频")
    
    print("\n🏁 副视频功能检查完成")

if __name__ == "__main__":
    check_sub_video_in_main()
