#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的话术解析功能
验证时间段数据解析是否正常工作
"""

import json
import re

def test_parse_time_segment_json_format():
    """测试修复后的解析函数"""
    
    # 模拟测试数据（与你提供的错误日志中的数据一致）
    test_data = {
        "0秒 - 10秒": "1***哈喽，大家好，首先感谢【大哥|哥哥|宝子】们的捧场。\n2***稍作停留也是爱，所能接触的都是这四个大字，随便那个都是这四个大字。\n3***没有花里胡哨，没有弯弯绕绕。\n4***有啥就问，新来的家人们，不要藏着掖着。\n5***云想衣裳花想容，春风拂槛露华浓，仙侠界的白莲花，花容月貌顶呱呱。",
        "10秒 - 20秒": "6***自古套路得人心，但唯有真心得天下，咱们心不要慌，手不要抖，跟着感觉走。\n7***新来的家人们，相遇就是缘分，不要犹豫不要徘徊。\n8***你们的到来蓬荜生辉，老妹我深感荣幸\n9***随便什么数字都是这四个大字，目光所及都是这四个大字。\n10***俗话说的好，大哥们，人生不主动，快乐少一半",
        "20秒 - 30秒": "11***相信自己的眼睛，相信自己的耳朵\n12***斩青丝斩难过斩断红尘不为过，建模美姿势帅，呼风唤雨惹人爱\n13***不穿摸，不贴片，仙侠界的彭于晏，（千人捧万人追）手游界的刘亦菲\n14***播播间人很多，主播一个人一张嘴，忙不过来，理解下，大哥们\n15***万水千山总是情，今天主播最热情",
        "30秒 - 40秒": "16***今天只为做人气，玩归玩，闹归闹，不拿真诚开玩笑\n17***我一说，你一笑，【大哥|哥哥|宝子】们开心比啥都重要。\n18***落地人上人，只管开心，只管快乐。\n19***千里姻缘一线牵，欢迎大哥们来到播播间\n20***山外青山楼外楼，播播间里无忧愁。",
        "40秒 - 50秒": "21***只管开心只管爽，都是这四个大字都是这四个大字\n22***走过南呀闯过北，主播认识大家不后悔。\n23***有啥就说有啥就问，相遇就是缘分。人生何处不相逢啊。\n24***给个面子捧个场，大哥们，不要到处乱跑了\n25***真真实实，实实在在，简简又单单。",
        "50秒 - 60秒": "26***没有花里胡哨，没有弯弯绕绕，啥啥都是这四个大字。\n27***相信自己的眼睛，相信自己的耳朵\n28***老弟正儿八经，诚意满满，爱意满满（今天呢，老弟也是带着诚意来的）\n29***新来的大哥们，心别慌手别抖，不要着急离开\n30***万水千山总是情，今天主播最热情"
    }
    
    script_name = "xy-kaer"
    
    print("=" * 80)
    print("🧪 测试修复后的话术解析功能")
    print("=" * 80)
    
    # 模拟解析函数
    def parse_time_segment_json_format_fixed(script_name, content):
        """修复后的解析函数"""
        try:
            print(f"[SEARCH] ⭐ 开始解析时间段JSON格式，话术: {script_name}")
            
            # 支持多种输入格式：字符串、字典对象
            time_segments_data = None
            
            if isinstance(content, dict):
                # 如果输入已经是字典对象，直接使用
                time_segments_data = content
                print(f"[INFO] 输入为字典对象，包含 {len(content)} 个键")
            elif isinstance(content, str):
                # 如果输入是字符串，尝试解析JSON
                print(f"[INFO] JSON字符串长度: {len(content)} 字符")
                
                # 检查内容是否为空，避免JSON解析错误
                if not content or not content.strip():
                    print(f"[WARNING] JSON内容为空，跳过解析: {script_name}")
                    return False
                
                print(f"[INFO] JSON内容预览: {content[:200]}...")
                
                # 尝试解析JSON
                time_segments_data = json.loads(content)
            else:
                print(f"[WARNING] 不支持的输入类型: {type(content)}")
                return False
            
            if not isinstance(time_segments_data, dict):
                print(f"[WARNING] ❌ JSON数据不是字典格式，类型: {type(time_segments_data)}")
                return False
            
            print(f"[OK] ✅ JSON解析成功，包含 {len(time_segments_data)} 个顶级键")
            print(f"[DEBUG] 顶级键列表: {list(time_segments_data.keys())}")
            
            # 检查是否为新格式：所有值都是字符串
            is_new_format = all(isinstance(v, str) for v in time_segments_data.values())
            print(f"[INFO] 🔍 检测格式类型: {'新格式' if is_new_format else '旧格式'}")
            
            # 详细检查每个值的类型
            for key, value in time_segments_data.items():
                print(f"[DEBUG] 键 '{key}': 类型={type(value).__name__}, 长度={len(str(value))}")
            
            if not is_new_format:
                print(f"[WARNING] ❌ 不是新的时间段格式，跳过解析")
                return False
            
            # 初始化时间段数据结构
            script_time_segments = {script_name: {}}
            
            # 解析每个时间段（只处理新格式）
            parsed_count = 0
            for time_key, time_data in time_segments_data.items():
                print(f"[SEARCH] 处理时间段: '{time_key}', 数据类型: {type(time_data)}")
                
                # 新格式：时间段名称 -> 话术内容字符串
                if isinstance(time_data, str):
                    segment_content = time_data
                    
                    # 从时间段名称中提取时间信息
                    pattern = r'(\d+)秒\s*-\s*(\d+)秒'
                    match = re.search(pattern, time_key)
                    
                    if match:
                        start_time = int(match.group(1))
                        end_time = int(match.group(2))
                    else:
                        # 如果无法解析时间，使用默认值
                        start_time = parsed_count * 10
                        end_time = (parsed_count + 1) * 10
                    
                    print(f"   [DATA] 新格式 - 提取时间信息: start={start_time}, end={end_time}")
                    print(f"   [EDIT] 内容长度: {len(segment_content)} 字符")
                    
                    # 智能检测换行符格式并统计话术行数
                    if '\\n' in segment_content:
                        # 包含转义的换行符 \\n
                        content_lines = segment_content.split('\\n')
                        print(f"   [FORMAT] 检测到转义换行符格式 (\\n)")
                    else:
                        # 包含实际的换行符 \n
                        content_lines = segment_content.split('\n')
                        print(f"   [FORMAT] 检测到实际换行符格式 (\\n)")
                    
                    script_lines = [line for line in content_lines if line.strip() and '***' in line]
                    print(f"   [INFO] 话术行数: {len(script_lines)}/{len(content_lines)}")
                    
                    print(f"   [EDIT] 内容预览: {segment_content[:100]}...")
                    
                    # 存储到时间段数据结构（保存原始内容）
                    script_time_segments[script_name][time_key] = {
                        'start': start_time,
                        'end': end_time,
                        'content': segment_content  # 保存原始内容
                    }
                    
                    print(f"[OK] 解析时间段: '{time_key}' ({start_time}秒-{end_time}秒)")
                    print(f"   [INFO] 总行数: {len(content_lines)}, 话术行数: {len(script_lines)}")
                    
                    parsed_count += 1
                else:
                    print(f"[WARNING] 时间段 '{time_key}' 的数据格式不支持: {type(time_data)}")
                    continue
            
            print(f"[DART] ⭐ 成功解析 {parsed_count}/{len(time_segments_data)} 个时间段数据")
            print(f"[DATA] 📊 最终数据结构: {script_name} -> {list(script_time_segments[script_name].keys())}")
            
            # 验证数据结构
            if script_name in script_time_segments:
                segments = script_time_segments[script_name]
                print(f"[VERIFY] ✅ 验证数据结构: 共 {len(segments)} 个时间段")
                for seg_name, seg_data in segments.items():
                    content_preview = seg_data.get('content', '')[:50]
                    print(f"[VERIFY]   - {seg_name}: {seg_data['start']}-{seg_data['end']}秒, 内容: {content_preview}...")
            else:
                print(f"[VERIFY] ❌ 数据结构验证失败: {script_name} 不在 script_time_segments 中")
            
            result = parsed_count > 0
            print(f"[RESULT] 🎯 解析函数返回: {result}")
            return result, script_time_segments
            
        except json.JSONDecodeError as e:
            print(f"[WARNING] JSON解析失败: {e}")
            return False, {}
        except Exception as e:
            print(f"[ERROR] 解析时间段JSON格式异常: {e}")
            import traceback
            print(f"[INFO] 详细错误: {traceback.format_exc()}")
            return False, {}
    
    # 测试1：使用字典对象输入
    print("\n📋 测试1：字典对象输入")
    print("-" * 50)
    success1, segments1 = parse_time_segment_json_format_fixed(script_name, test_data)
    print(f"✅ 测试1结果: {'成功' if success1 else '失败'}")
    
    # 测试2：使用JSON字符串输入
    print("\n📋 测试2：JSON字符串输入")
    print("-" * 50)
    json_string = json.dumps(test_data, ensure_ascii=False, indent=2)
    success2, segments2 = parse_time_segment_json_format_fixed(script_name, json_string)
    print(f"✅ 测试2结果: {'成功' if success2 else '失败'}")
    
    # 测试3：模拟空内容（应该会导致错误的情况）
    print("\n📋 测试3：空内容输入")
    print("-" * 50)
    try:
        success3, segments3 = parse_time_segment_json_format_fixed(script_name, "")
        print(f"✅ 测试3结果: {'成功' if success3 else '失败'} (预期失败)")
    except Exception as e:
        success3 = False
        print(f"✅ 测试3结果: 失败 (预期失败) - {e}")
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 测试总结")
    print("=" * 80)
    print(f"✅ 字典对象输入: {'通过' if success1 else '失败'}")
    print(f"✅ JSON字符串输入: {'通过' if success2 else '失败'}")
    print(f"✅ 空内容处理: {'通过' if not success3 else '失败'} (预期失败)")
    
    if success1 and success2:
        print("\n🎉 所有核心测试通过！修复成功！")
        print(f"📈 解析出的时间段数量: {len(segments1[script_name])}")
        print("📝 时间段列表:")
        for seg_name in segments1[script_name].keys():
            print(f"   - {seg_name}")
    else:
        print("\n❌ 测试失败，需要进一步检查")
    
    return success1 and success2

if __name__ == "__main__":
    test_parse_time_segment_json_format()
