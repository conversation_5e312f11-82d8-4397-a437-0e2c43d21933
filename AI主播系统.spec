# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 添加数据文件 - 只包含必要的资源文件，不包含源码和配置文件
added_files = [
    ('简生活图标.ico', '.'),  # [CONFIG] 图标文件作为资源内嵌到exe中
    # [SECURE] 不包含以下文件，避免源码泄露和配置冲突：
    # - src目录（源码）
    # - config.json（配置文件）
    # - *.db（数据库文件）
    # - *.log（日志文件）
    # - __pycache__（缓存目录）
    # - *.pyc（编译缓存）
]

# 隐藏导入的模块
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui',
    'PyQt5.QtWidgets',
    'PyQt5.QtMultimedia',
    'PyQt5.QtNetwork',
    'requests',
    'json',
    'sqlite3',
    'threading',
    'queue',
    'time',
    'datetime',
    'random',
    'os',
    'sys',
    'pathlib',
    'urllib3',
    'certifi',
    'charset_normalizer',
    'idna',
    'websocket',
    'websocket-client',
    'obswebsocket',
    'cv2',
    'numpy',
    'PIL',
    'Pillow',
    # 修复缺失的模块
    'email',
    'email.mime',
    'email.mime.text',
    'email.mime.multipart',
    'email.utils',
    'hashlib',
    'base64',
    'uuid',
    'logging',
    'logging.handlers',
    'configparser',
    'urllib',
    'urllib.parse',
    'urllib.request',
    'http',
    'http.client',
    'ssl',
    'socket',
    'select',
    'platform',
    'subprocess',
    'shutil',
    'tempfile',
    'glob',
    'fnmatch',
    'collections',
    'collections.abc',
    'itertools',
    'functools',
    'operator',
    'copy',
    'pickle',
    'struct',
    'zlib',
    'gzip',
    'tarfile',
    'zipfile',
    'io',
    'codecs',
    'locale',
    'gettext',
    'weakref',
    'gc',
    'traceback',
    'inspect',
    'types',
    'importlib',
    'importlib.util',
    'pkg_resources',
]

a = Analysis(
    ['run_gui_qt5.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # [SECURE] 排除不需要的模块，减少文件大小
        'tkinter',
        'matplotlib',
        'scipy',
        'pandas',
        'jupyter',
        'IPython',
        'notebook',
        'test',
        'tests',
        'unittest',
        'doctest',
        'pydoc',
        # 'email',  # 已移除，因为程序需要使用email模块
        'xml.etree',
        'xml.dom',
        'xml.sax',
        # 'distutils',  # 已移除，因为程序可能需要使用
        # 'setuptools',  # 已移除，因为程序可能需要使用
        'pip',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AI主播系统',
    debug=False,  # [SECURE] 关闭调试模式，避免泄露信息
    bootloader_ignore_signals=False,
    strip=True,  # [SECURE] 启用strip，移除调试信息
    upx=True,  # [SECURE] 启用UPX压缩，进一步保护代码
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # [DEBUG] 启用控制台窗口，用于调试
    disable_windowed_traceback=False,  # [DEBUG] 启用窗口化回溯，用于调试
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='简生活图标.ico'  # 设置程序图标
)
