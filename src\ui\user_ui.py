"""
AI直播系统 v2 - 用户界面
登录、注册、充值界面
"""

import sys
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QTabWidget, QTextEdit,
                            QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox,
                            QMessageBox, QTableWidget, QTableWidgetItem,
                            QHeaderView, QFrame, QGridLayout, QGroupBox,
                            QDialog, QScrollArea)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QPalette, QColor

from ..core.user import UserManager, AuthManager, PaymentManager
from ..services.logging_service import create_logger


class UserLoginWidget(QWidget):
    """用户登录界面"""

    login_success = pyqtSignal(dict)  # 登录成功信号

    def __init__(self, db_manager):
        super().__init__()
        self.logger = create_logger("user_login_ui")
        self.db_manager = db_manager
        self.user_manager = UserManager(db_manager)
        self.auth_manager = AuthManager(db_manager)

        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 标题
        title_label = QLabel("AI直播系统 - 用户登录")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # 登录表单
        form_layout = QVBoxLayout()

        # 用户名
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("请输入用户名或邮箱")
        form_layout.addWidget(QLabel("用户名:"))
        form_layout.addWidget(self.username_input)

        # 密码
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        form_layout.addWidget(QLabel("密码:"))
        form_layout.addWidget(self.password_input)

        # 记住密码
        self.remember_checkbox = QCheckBox("记住密码")
        form_layout.addWidget(self.remember_checkbox)

        # 登录按钮
        self.login_button = QPushButton("登录")
        self.login_button.clicked.connect(self.login)
        form_layout.addWidget(self.login_button)

        # 注册按钮
        self.register_button = QPushButton("注册新账户")
        self.register_button.clicked.connect(self.show_register)
        form_layout.addWidget(self.register_button)

        layout.addLayout(form_layout)

        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)

        self.setLayout(layout)
        self.setWindowTitle("用户登录")
        self.resize(400, 300)

        # 回车键登录
        self.password_input.returnPressed.connect(self.login)

    def login(self):
        """登录"""
        username = self.username_input.text().strip()
        password = self.password_input.text()

        if not username or not password:
            self.show_message("请输入用户名和密码", "warning")
            return

        # 禁用登录按钮
        self.login_button.setEnabled(False)
        self.login_button.setText("登录中...")

        try:
            # 执行登录
            result = self.user_manager.login_user(username, password)

            if result['success']:
                # 生成令牌
                tokens = self.auth_manager.generate_tokens(result['user_info'])

                self.show_message("登录成功！", "success")
                self.login_success.emit({
                    'user_info': result['user_info'],
                    'tokens': tokens
                })

                # 清空密码
                if not self.remember_checkbox.isChecked():
                    self.password_input.clear()

            else:
                self.show_message(result['message'], "error")

        except Exception as e:
            self.logger.error(f"登录异常: {e}")
            self.show_message(f"登录失败: {str(e)}", "error")

        finally:
            # 恢复登录按钮
            self.login_button.setEnabled(True)
            self.login_button.setText("登录")

    def show_register(self):
        """显示注册界面"""
        self.register_widget = UserRegisterWidget(self.db_manager)
        self.register_widget.register_success.connect(self.on_register_success)
        self.register_widget.show()

    def on_register_success(self, user_info):
        """注册成功回调"""
        self.username_input.setText(user_info['username'])
        self.show_message("注册成功！请登录", "success")

    def show_message(self, message: str, msg_type: str = "info"):
        """显示消息"""
        self.status_label.setText(message)

        if msg_type == "success":
            self.status_label.setStyleSheet("color: green;")
        elif msg_type == "error":
            self.status_label.setStyleSheet("color: red;")
        elif msg_type == "warning":
            self.status_label.setStyleSheet("color: orange;")
        else:
            self.status_label.setStyleSheet("color: black;")

        # 3秒后清空消息
        QTimer.singleShot(3000, lambda: self.status_label.setText(""))


class UserRegisterWidget(QWidget):
    """用户注册界面"""

    register_success = pyqtSignal(dict)  # 注册成功信号

    def __init__(self, db_manager):
        super().__init__()
        self.logger = create_logger("user_register_ui")
        self.db_manager = db_manager
        self.user_manager = UserManager(db_manager)

        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 标题
        title_label = QLabel("用户注册")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # 注册表单
        form_layout = QVBoxLayout()

        # 用户名
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("请输入用户名（3-20个字符）")
        form_layout.addWidget(QLabel("用户名:"))
        form_layout.addWidget(self.username_input)

        # 密码
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码（至少6个字符）")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        form_layout.addWidget(QLabel("密码:"))
        form_layout.addWidget(self.password_input)

        # 确认密码
        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setPlaceholderText("请再次输入密码")
        self.confirm_password_input.setEchoMode(QLineEdit.EchoMode.Password)
        form_layout.addWidget(QLabel("确认密码:"))
        form_layout.addWidget(self.confirm_password_input)

        # 邮箱
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("请输入邮箱（可选）")
        form_layout.addWidget(QLabel("邮箱:"))
        form_layout.addWidget(self.email_input)

        # 手机号
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("请输入手机号（可选）")
        form_layout.addWidget(QLabel("手机号:"))
        form_layout.addWidget(self.phone_input)

        # 注册按钮
        self.register_button = QPushButton("注册")
        self.register_button.clicked.connect(self.register)
        form_layout.addWidget(self.register_button)

        layout.addLayout(form_layout)

        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)

        self.setLayout(layout)
        self.setWindowTitle("用户注册")
        self.resize(400, 400)

    def register(self):
        """注册"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        confirm_password = self.confirm_password_input.text()
        email = self.email_input.text().strip()
        phone = self.phone_input.text().strip()

        # 验证输入
        if not username:
            self.show_message("请输入用户名", "error")
            return

        if len(username) < 3 or len(username) > 20:
            self.show_message("用户名长度应为3-20个字符", "error")
            return

        if not password:
            self.show_message("请输入密码", "error")
            return

        if len(password) < 6:
            self.show_message("密码长度至少6个字符", "error")
            return

        if password != confirm_password:
            self.show_message("两次输入的密码不一致", "error")
            return

        # 禁用注册按钮
        self.register_button.setEnabled(False)
        self.register_button.setText("注册中...")

        try:
            # 执行注册
            result = self.user_manager.register_user(username, password, email, phone)

            if result['success']:
                self.show_message("注册成功！", "success")
                self.register_success.emit(result['user_info'])

                # 延迟关闭窗口
                QTimer.singleShot(2000, self.close)

            else:
                self.show_message(result['message'], "error")

        except Exception as e:
            self.logger.error(f"注册异常: {e}")
            self.show_message(f"注册失败: {str(e)}", "error")

        finally:
            # 恢复注册按钮
            self.register_button.setEnabled(True)
            self.register_button.setText("注册")

    def show_message(self, message: str, msg_type: str = "info"):
        """显示消息"""
        self.status_label.setText(message)

        if msg_type == "success":
            self.status_label.setStyleSheet("color: green;")
        elif msg_type == "error":
            self.status_label.setStyleSheet("color: red;")
        elif msg_type == "warning":
            self.status_label.setStyleSheet("color: orange;")
        else:
            self.status_label.setStyleSheet("color: black;")


class UserRechargeWidget(QWidget):
    """用户充值界面"""

    def __init__(self, db_manager, user_info):
        super().__init__()
        self.logger = create_logger("user_recharge_ui")
        self.db_manager = db_manager
        self.user_info = user_info
        self.payment_manager = PaymentManager(db_manager)

        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 标题
        title_label = QLabel("用户充值")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # 用户信息
        user_info_layout = QHBoxLayout()
        user_info_layout.addWidget(QLabel(f"当前用户: {self.user_info['username']}"))
        user_info_layout.addWidget(QLabel(f"用户类型: {self.get_user_type_name(self.user_info['user_type'])}"))
        layout.addLayout(user_info_layout)

        # 充值套餐
        packages_group = QGroupBox("充值套餐")
        packages_layout = QVBoxLayout()

        self.packages = self.payment_manager.get_recharge_packages()
        self.package_buttons = {}

        for package_id, package_info in self.packages.items():
            package_widget = self.create_package_widget(package_id, package_info)
            packages_layout.addWidget(package_widget)

        packages_group.setLayout(packages_layout)
        layout.addWidget(packages_group)

        # 支付方式
        payment_group = QGroupBox("支付方式")
        payment_layout = QVBoxLayout()

        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItem("支付宝", "alipay")
        self.payment_method_combo.addItem("微信支付", "wechat")
        self.payment_method_combo.addItem("银行卡", "bank")
        payment_layout.addWidget(self.payment_method_combo)

        payment_group.setLayout(payment_layout)
        layout.addWidget(payment_group)

        # 充值按钮
        self.recharge_button = QPushButton("立即充值")
        self.recharge_button.clicked.connect(self.start_recharge)
        self.recharge_button.setEnabled(False)
        layout.addWidget(self.recharge_button)

        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)

        self.setLayout(layout)
        self.setWindowTitle("用户充值")
        self.resize(500, 600)

        self.selected_package = None

    def create_package_widget(self, package_id: str, package_info: dict) -> QWidget:
        """创建套餐选择控件"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.Shape.Box)
        layout = QVBoxLayout()

        # 套餐名称
        name_label = QLabel(package_info['name'])
        name_font = QFont()
        name_font.setBold(True)
        name_label.setFont(name_font)
        layout.addWidget(name_label)

        # 套餐描述
        desc_label = QLabel(package_info['description'])
        layout.addWidget(desc_label)

        # 价格和天数
        price_label = QLabel(f"价格: ¥{package_info['amount']}")
        days_label = QLabel(f"有效期: {package_info['days']}天")
        layout.addWidget(price_label)
        layout.addWidget(days_label)

        # 选择按钮
        select_button = QPushButton("选择此套餐")
        select_button.clicked.connect(lambda: self.select_package(package_id))
        layout.addWidget(select_button)

        widget.setLayout(layout)
        self.package_buttons[package_id] = select_button

        return widget

    def select_package(self, package_id: str):
        """选择套餐"""
        self.selected_package = package_id

        # 更新按钮状态
        for pid, button in self.package_buttons.items():
            if pid == package_id:
                button.setText("已选择")
                button.setEnabled(False)
            else:
                button.setText("选择此套餐")
                button.setEnabled(True)

        self.recharge_button.setEnabled(True)
        self.show_message(f"已选择套餐: {self.packages[package_id]['name']}", "success")

    def start_recharge(self):
        """开始充值"""
        if not self.selected_package:
            self.show_message("请选择充值套餐", "error")
            return

        payment_method = self.payment_method_combo.currentData()

        # 禁用充值按钮
        self.recharge_button.setEnabled(False)
        self.recharge_button.setText("创建订单中...")

        try:
            # 创建充值订单
            result = self.payment_manager.create_recharge_order(
                self.user_info['id'],
                self.selected_package,
                payment_method
            )

            if result['success']:
                # 显示支付信息
                self.show_payment_info(result)
            else:
                self.show_message(result['message'], "error")

        except Exception as e:
            self.logger.error(f"创建充值订单异常: {e}")
            self.show_message(f"创建订单失败: {str(e)}", "error")

        finally:
            # 恢复充值按钮
            self.recharge_button.setEnabled(True)
            self.recharge_button.setText("立即充值")

    def show_payment_info(self, order_info: dict):
        """显示支付信息"""
        payment_info = order_info['payment_info']

        # 创建支付信息窗口
        payment_dialog = PaymentInfoDialog(order_info, self.payment_manager)
        payment_dialog.payment_success.connect(self.on_payment_success)
        payment_dialog.exec()

    def on_payment_success(self, order_info: dict):
        """支付成功回调"""
        self.show_message("充值成功！", "success")
        QTimer.singleShot(2000, self.close)

    def get_user_type_name(self, user_type: int) -> str:
        """获取用户类型名称"""
        type_names = {
            0: "免费用户",
            1: "VIP用户",
            2: "高级用户",
            9: "管理员"
        }
        return type_names.get(user_type, "未知")

    def show_message(self, message: str, msg_type: str = "info"):
        """显示消息"""
        self.status_label.setText(message)

        if msg_type == "success":
            self.status_label.setStyleSheet("color: green;")
        elif msg_type == "error":
            self.status_label.setStyleSheet("color: red;")
        elif msg_type == "warning":
            self.status_label.setStyleSheet("color: orange;")
        else:
            self.status_label.setStyleSheet("color: black;")


class PaymentInfoDialog(QDialog):
    """支付信息对话框"""

    payment_success = pyqtSignal(dict)  # 支付成功信号

    def __init__(self, order_info: dict, payment_manager):
        super().__init__()
        self.order_info = order_info
        self.payment_manager = payment_manager
        self.logger = create_logger("payment_info_dialog")

        self.init_ui()

        # 定时检查支付状态
        self.check_timer = QTimer()
        self.check_timer.timeout.connect(self.check_payment_status)
        self.check_timer.start(5000)  # 每5秒检查一次

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 标题
        title_label = QLabel("支付信息")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # 订单信息
        order_group = QGroupBox("订单信息")
        order_layout = QVBoxLayout()

        order_layout.addWidget(QLabel(f"订单号: {self.order_info['order_no']}"))
        order_layout.addWidget(QLabel(f"套餐: {self.order_info['package_info']['name']}"))
        order_layout.addWidget(QLabel(f"金额: ¥{self.order_info['amount']}"))

        order_group.setLayout(order_layout)
        layout.addWidget(order_group)

        # 支付信息
        payment_info = self.order_info['payment_info']
        payment_method = payment_info['payment_method']

        payment_group = QGroupBox("支付信息")
        payment_layout = QVBoxLayout()

        if payment_method == 'alipay':
            payment_layout.addWidget(QLabel("支付宝支付"))
            payment_layout.addWidget(QLabel("请使用支付宝扫描二维码支付"))
            payment_layout.addWidget(QLabel(f"支付链接: {payment_info.get('pay_url', '')}"))

        elif payment_method == 'wechat':
            payment_layout.addWidget(QLabel("微信支付"))
            payment_layout.addWidget(QLabel("请使用微信扫描二维码支付"))
            payment_layout.addWidget(QLabel(f"支付链接: {payment_info.get('pay_url', '')}"))

        elif payment_method == 'bank':
            payment_layout.addWidget(QLabel("银行卡支付"))
            bank_info = payment_info.get('bank_info', {})
            payment_layout.addWidget(QLabel(f"收款账户: {bank_info.get('account_name', '')}"))
            payment_layout.addWidget(QLabel(f"账号: {bank_info.get('account_number', '')}"))
            payment_layout.addWidget(QLabel(f"开户行: {bank_info.get('bank_name', '')}"))
            payment_layout.addWidget(QLabel(f"备注: {bank_info.get('remark', '')}"))

        payment_group.setLayout(payment_layout)
        layout.addWidget(payment_group)

        # 模拟支付按钮（仅用于测试）
        self.simulate_payment_button = QPushButton("模拟支付成功（测试用）")
        self.simulate_payment_button.clicked.connect(self.simulate_payment)
        layout.addWidget(self.simulate_payment_button)

        # 状态标签
        self.status_label = QLabel("等待支付...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)

        # 关闭按钮
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.close)
        layout.addWidget(close_button)

        self.setLayout(layout)
        self.setWindowTitle("支付信息")
        self.resize(400, 500)

    def check_payment_status(self):
        """检查支付状态"""
        try:
            result = self.payment_manager.get_order_status(self.order_info['order_no'])

            if result['success']:
                order = result['order']
                if order['status'] == 1:  # 支付成功
                    self.check_timer.stop()
                    self.status_label.setText("支付成功！")
                    self.status_label.setStyleSheet("color: green;")
                    self.payment_success.emit(order)
                    QTimer.singleShot(2000, self.close)

                elif order['status'] == 2:  # 支付失败
                    self.check_timer.stop()
                    self.status_label.setText("支付失败")
                    self.status_label.setStyleSheet("color: red;")

        except Exception as e:
            self.logger.error(f"检查支付状态失败: {e}")

    def simulate_payment(self):
        """模拟支付（仅用于测试）"""
        try:
            result = self.payment_manager.process_payment(self.order_info['order_no'])

            if result['success']:
                self.check_timer.stop()
                self.status_label.setText("支付成功！")
                self.status_label.setStyleSheet("color: green;")
                self.payment_success.emit(result['order_info'])
                QTimer.singleShot(2000, self.close)
            else:
                self.status_label.setText(f"支付失败: {result['message']}")
                self.status_label.setStyleSheet("color: red;")

        except Exception as e:
            self.logger.error(f"模拟支付失败: {e}")
            self.status_label.setText(f"支付失败: {str(e)}")
            self.status_label.setStyleSheet("color: red;")

    def closeEvent(self, event):
        """关闭事件"""
        if hasattr(self, 'check_timer'):
            self.check_timer.stop()
        super().closeEvent(event)