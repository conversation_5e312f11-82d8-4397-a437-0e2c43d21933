"""
AI Broadcaster v2 - 优先级播放列表管理器
支持不同类型语音的优先级播放
"""

import time
from enum import IntEnum
from typing import List, Optional, Dict, Any, Callable
from collections import deque
import threading

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class VoiceType(IntEnum):
    """语音类型优先级枚举"""
    SUB_VIDEO = 1      # 副视频语音 - 最高优先级
    AI_DIALOGUE = 2    # AI对话语音
    TIME_REPORT = 3    # 报时语音
    MAIN_VIDEO = 4     # 主视频语音 - 最低优先级


class PriorityPlaylistItem:
    """优先级播放列表项"""

    def __init__(self, file_path: str, voice_type: VoiceType, title: str = "",
                 metadata: Optional[Dict[str, Any]] = None):
        self.file_path = file_path
        self.voice_type = voice_type
        self.title = title or f"{voice_type.name}_{int(time.time())}"
        self.metadata = metadata or {}
        self.created_at = time.time()
        self.played_at = None
        self.play_count = 0

        # 副视频相关属性
        self.sub_video_source = metadata.get('sub_video_source') if metadata else None
        self.trigger_keyword = metadata.get('trigger_keyword') if metadata else None

        # 时间段相关属性
        self.time_segment = metadata.get('time_segment') if metadata else None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'file_path': self.file_path,
            'voice_type': self.voice_type.name,
            'title': self.title,
            'metadata': self.metadata,
            'created_at': self.created_at,
            'played_at': self.played_at,
            'play_count': self.play_count,
            'sub_video_source': self.sub_video_source,
            'trigger_keyword': self.trigger_keyword,
            'time_segment': self.time_segment
        }


class PriorityPlaylistManager:
    """优先级播放列表管理器"""

    def __init__(self, max_size: int = 1000):
        self.logger = create_logger("priority_playlist_manager")
        self.max_size = max_size

        # 按优先级分组的播放队列
        self._queues = {
            VoiceType.SUB_VIDEO: deque(maxlen=max_size),
            VoiceType.AI_DIALOGUE: deque(maxlen=max_size),
            VoiceType.TIME_REPORT: deque(maxlen=max_size),
            VoiceType.MAIN_VIDEO: deque(maxlen=max_size)
        }

        self._lock = threading.Lock()
        self._current_item: Optional[PriorityPlaylistItem] = None

        # 播放模式设置
        self.main_video_loop_mode = True  # 主视频循环播放

        # 回调函数
        self.on_playlist_change: Optional[Callable] = None
        self.on_current_change: Optional[Callable] = None
        self.on_sub_video_start: Optional[Callable] = None  # 副视频开始播放
        self.on_sub_video_end: Optional[Callable] = None    # 副视频播放结束

        # 播放历史
        self._play_history = deque(maxlen=100)

        self.logger.info("优先级播放列表管理器初始化完成")

    @handle_exceptions("PriorityPlaylistManager")
    def add_item(self, file_path: str, voice_type: VoiceType, title: str = "",
                 metadata: Optional[Dict[str, Any]] = None) -> bool:
        """添加播放项到对应优先级队列"""
        try:
            with self._lock:
                item = PriorityPlaylistItem(file_path, voice_type, title, metadata)
                self._queues[voice_type].append(item)

                self.logger.info(f"添加{voice_type.name}语音: {item.title}")

                # 触发回调
                if self.on_playlist_change:
                    self.on_playlist_change()

                return True

        except Exception as e:
            self.logger.error(f"添加播放项失败: {e}")
            return False

    def add_sub_video_item(self, file_path: str, sub_video_source: str,
                          trigger_keyword: str, title: str = "") -> bool:
        """添加副视频语音项"""
        metadata = {
            'sub_video_source': sub_video_source,
            'trigger_keyword': trigger_keyword
        }
        return self.add_item(file_path, VoiceType.SUB_VIDEO, title, metadata)

    def add_ai_dialogue_item(self, file_path: str, keyword: str, title: str = "") -> bool:
        """添加AI对话语音项"""
        metadata = {'keyword': keyword}
        return self.add_item(file_path, VoiceType.AI_DIALOGUE, title, metadata)

    def add_time_report_item(self, file_path: str, title: str = "") -> bool:
        """添加报时语音项"""
        return self.add_item(file_path, VoiceType.TIME_REPORT, title)

    def add_main_video_item(self, file_path: str, time_segment: str, title: str = "") -> bool:
        """添加主视频语音项"""
        metadata = {'time_segment': time_segment}
        return self.add_item(file_path, VoiceType.MAIN_VIDEO, title, metadata)

    def get_next_item(self) -> Optional[PriorityPlaylistItem]:
        """按优先级获取下一个播放项"""
        with self._lock:
            # 按优先级顺序检查队列
            for voice_type in [VoiceType.SUB_VIDEO, VoiceType.AI_DIALOGUE,
                              VoiceType.TIME_REPORT, VoiceType.MAIN_VIDEO]:
                queue = self._queues[voice_type]

                if queue:
                    if voice_type == VoiceType.MAIN_VIDEO and self.main_video_loop_mode:
                        # 主视频循环模式：取出后重新放回队列末尾
                        item = queue.popleft()
                        queue.append(item)
                    else:
                        # 其他类型：按下载顺序播放，播放后移除
                        item = queue.popleft()

                    # 更新播放统计
                    item.play_count += 1
                    item.played_at = time.time()

                    # 设置当前播放项
                    self._current_item = item

                    # 添加到历史记录
                    self._play_history.append(item.to_dict())

                    self.logger.info(f"下一个播放项: {item.voice_type.name} - {item.title}")

                    # 触发回调
                    if self.on_current_change:
                        self.on_current_change(item)

                    # 副视频特殊处理
                    if voice_type == VoiceType.SUB_VIDEO and self.on_sub_video_start:
                        self.on_sub_video_start(item)

                    return item

            return None

    def get_current_item(self) -> Optional[PriorityPlaylistItem]:
        """获取当前播放项"""
        return self._current_item

    def clear_queue(self, voice_type: Optional[VoiceType] = None):
        """清空指定类型的队列，如果不指定则清空所有队列"""
        with self._lock:
            if voice_type:
                self._queues[voice_type].clear()
                self.logger.info(f"清空{voice_type.name}队列")
            else:
                for queue in self._queues.values():
                    queue.clear()
                self._current_item = None
                self.logger.info("清空所有播放队列")

            # 触发回调
            if self.on_playlist_change:
                self.on_playlist_change()

    def get_queue_info(self) -> Dict[str, Any]:
        """获取所有队列信息"""
        with self._lock:
            queue_info = {}
            total_items = 0

            for voice_type, queue in self._queues.items():
                count = len(queue)
                total_items += count
                queue_info[voice_type.name] = {
                    'count': count,
                    'items': [item.to_dict() for item in list(queue)[:5]]  # 只返回前5个
                }

            return {
                'total_items': total_items,
                'queues': queue_info,
                'current_item': self._current_item.to_dict() if self._current_item else None,
                'main_video_loop_mode': self.main_video_loop_mode
            }

    def set_main_video_loop_mode(self, enabled: bool):
        """设置主视频循环播放模式"""
        self.main_video_loop_mode = enabled
        self.logger.info(f"主视频循环播放模式: {'开启' if enabled else '关闭'}")

    def on_item_finished(self, item: PriorityPlaylistItem):
        """播放项完成回调"""
        if item.voice_type == VoiceType.SUB_VIDEO and self.on_sub_video_end:
            self.on_sub_video_end(item)

        self.logger.debug(f"播放完成: {item.voice_type.name} - {item.title}")

    def get_play_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取播放历史"""
        with self._lock:
            history_list = list(self._play_history)
            return history_list[-limit:] if limit > 0 else history_list

    def has_items(self, voice_type: Optional[VoiceType] = None) -> bool:
        """检查是否有待播放项"""
        with self._lock:
            if voice_type:
                return len(self._queues[voice_type]) > 0
            else:
                return any(len(queue) > 0 for queue in self._queues.values())

    def get_next_voice_type(self) -> Optional[VoiceType]:
        """获取下一个要播放的语音类型"""
        with self._lock:
            for voice_type in [VoiceType.SUB_VIDEO, VoiceType.AI_DIALOGUE,
                              VoiceType.TIME_REPORT, VoiceType.MAIN_VIDEO]:
                if len(self._queues[voice_type]) > 0:
                    return voice_type
            return None

    def remove_time_segment(self, time_segment_text: str):
        """删除时间段管理器方法（兼容性）"""
        # 这里可以添加删除逻辑，目前为空实现
        pass

    def update_time_segment(self, time_segment_text: str, content: str):
        """更新时间段管理器方法（兼容性）"""
        # 这里可以添加更新逻辑，目前为空实现
        pass
