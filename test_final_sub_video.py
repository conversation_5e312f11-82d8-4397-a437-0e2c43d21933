#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终副视频功能测试
验证弹幕回复中的副视频检测是否正常工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_final_sub_video():
    """最终副视频功能测试"""
    print("🎉 最终副视频功能测试")
    print("=" * 50)
    
    try:
        # 导入主程序
        from run_gui_qt5 import MainWindow
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建主窗口
        user_info = {
            'username': 'test_user',
            'user_id': 1,
            'expire_time': '2025-12-31'
        }
        window = MainWindow(user_info)
        
        def final_test():
            """最终测试"""
            print("\n🔍 开始最终副视频功能测试...")
            
            # 测试多种弹幕内容
            test_danmaku_list = [
                "感谢老板的火箭，太给力了！",
                "谢谢大家的礼物支持",
                "主播666，继续加油！",
                "送个火箭给主播",
                "这个礼物不错",
                "666666666",
                "普通弹幕，没有关键词"
            ]
            
            print(f"📋 测试弹幕列表: {len(test_danmaku_list)} 条")
            
            for i, danmaku_content in enumerate(test_danmaku_list):
                print(f"\n🎬 测试弹幕 {i+1}: {danmaku_content}")
                
                # 检测副视频
                sub_video_result = None
                if hasattr(window, 'check_sub_video_trigger'):
                    sub_video_result = window.check_sub_video_trigger(danmaku_content)
                
                print(f"🔍 副视频检测结果: {sub_video_result if sub_video_result else '无'}")
                
                # 添加到播放列表
                if hasattr(window, 'add_danmaku_to_playlist'):
                    window.add_danmaku_to_playlist(danmaku_content, sub_video_result)
                    print(f"✅ 已添加到播放列表")
                
                # 检查播放列表中的副视频显示
                if hasattr(window, 'playlist_items'):
                    for item in reversed(window.playlist_items):
                        if item.get('content') == danmaku_content:
                            sub_video = item.get('sub_video', '无')
                            print(f"📋 播放列表中的副视频: {sub_video}")
                            break
            
            # 总结测试结果
            print(f"\n📊 测试结果总结:")
            if hasattr(window, 'playlist_items'):
                total_items = len(window.playlist_items)
                sub_video_items = [item for item in window.playlist_items if item.get('sub_video') and item.get('sub_video') != '无']
                
                print(f"  总播放列表项目: {total_items}")
                print(f"  包含副视频的项目: {len(sub_video_items)}")
                
                if sub_video_items:
                    print(f"  副视频项目详情:")
                    for item in sub_video_items:
                        content = item.get('content', '')[:30]
                        sub_video = item.get('sub_video', '无')
                        print(f"    • {content}... → {sub_video}")
                
                print(f"\n✅ 副视频功能测试完成！")
                print(f"🎯 现在当弹幕包含关键词时，播放列表会正确显示副视频源。")
            
            # 退出测试
            def exit_test():
                print(f"\n🏁 最终测试完成，副视频功能正常工作！")
                app.quit()
            
            QTimer.singleShot(3000, exit_test)
        
        # 等待界面初始化完成后开始测试
        QTimer.singleShot(3000, final_test)
        
        # 显示窗口
        window.show()
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_final_sub_video()
