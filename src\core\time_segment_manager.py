#!/usr/bin/env python3
"""
时间段话术管理器
负责管理时间段话术的随机不重复选择
"""

import random
import re
from typing import Dict, List, Optional, Any
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.services.logging_service import create_logger


class TimeSegmentManager:
    """时间段话术管理器"""
    
    def __init__(self):
        self.logger = create_logger("TimeSegmentManager")
        
        # 时间段话术数据
        self.time_segments = {}  # {时间段: {'start': 开始时间, 'end': 结束时间, 'content': 话术内容, 'scripts': 话术列表, 'used_scripts': 已使用话术, 'current_index': 当前索引}}
        
        self.logger.info("时间段话术管理器初始化完成")
    
    def add_time_segment(self, start_time: int, end_time: int, content: str) -> str:
        """
        添加时间段话术
        
        Args:
            start_time: 开始时间（秒）
            end_time: 结束时间（秒）
            content: 话术内容
            
        Returns:
            时间段标识符
        """
        segment_key = f"{start_time}秒 - {end_time}秒"
        
        # 解析话术内容
        scripts = self._parse_scripts(content)
        
        self.time_segments[segment_key] = {
            'start': start_time,
            'end': end_time,
            'content': content,
            'scripts': scripts,
            'used_scripts': set(),
            'current_index': 0
        }
        
        self.logger.info(f"添加时间段话术: {segment_key}, 话术数量: {len(scripts)}")
        return segment_key
    
    def update_time_segment(self, segment_key: str, content: str):
        """
        更新时间段话术内容
        
        Args:
            segment_key: 时间段标识符
            content: 新的话术内容
        """
        if segment_key in self.time_segments:
            # 重新解析话术内容
            scripts = self._parse_scripts(content)
            
            self.time_segments[segment_key]['content'] = content
            self.time_segments[segment_key]['scripts'] = scripts
            # 重置使用状态
            self.time_segments[segment_key]['used_scripts'] = set()
            self.time_segments[segment_key]['current_index'] = 0
            
            self.logger.info(f"更新时间段话术: {segment_key}, 话术数量: {len(scripts)}")
    
    def get_random_script(self, segment_key: str, current_time: int) -> Optional[str]:
        """
        获取随机不重复的话术
        
        Args:
            segment_key: 时间段标识符
            current_time: 当前时间（秒）
            
        Returns:
            话术内容，如果没有可用话术则返回None
        """
        if segment_key not in self.time_segments:
            return None
        
        segment = self.time_segments[segment_key]
        
        # 检查时间是否在范围内
        if not (segment['start'] <= current_time <= segment['end']):
            return None
        
        scripts = segment['scripts']
        if not scripts:
            return None
        
        used_scripts = segment['used_scripts']
        
        # 如果所有话术都用完了，重置
        if len(used_scripts) >= len(scripts):
            used_scripts.clear()
            self.logger.info(f"时间段话术已全部使用完毕，重置: {segment_key}")
        
        # 获取未使用的话术
        available_scripts = [script for i, script in enumerate(scripts) if i not in used_scripts]
        
        if not available_scripts:
            return None
        
        # 随机选择一个话术
        selected_script = random.choice(available_scripts)
        
        # 标记为已使用
        script_index = scripts.index(selected_script)
        used_scripts.add(script_index)
        
        # 处理变量词和随机选择
        processed_script = self._process_script_variables(selected_script)
        
        self.logger.info(f"获取时间段话术: {segment_key}, 剩余未使用: {len(scripts) - len(used_scripts)}")
        return processed_script
    
    def get_time_segments_for_time(self, current_time: int) -> List[str]:
        """
        获取当前时间适用的时间段列表
        
        Args:
            current_time: 当前时间（秒）
            
        Returns:
            适用的时间段标识符列表
        """
        applicable_segments = []
        
        for segment_key, segment in self.time_segments.items():
            if segment['start'] <= current_time <= segment['end']:
                applicable_segments.append(segment_key)
        
        return applicable_segments
    
    def get_segment_info(self, segment_key: str) -> Optional[Dict[str, Any]]:
        """
        获取时间段信息
        
        Args:
            segment_key: 时间段标识符
            
        Returns:
            时间段信息字典
        """
        if segment_key in self.time_segments:
            segment = self.time_segments[segment_key]
            return {
                'start_time': segment['start'],
                'end_time': segment['end'],
                'total_scripts': len(segment['scripts']),
                'used_scripts': len(segment['used_scripts']),
                'remaining_scripts': len(segment['scripts']) - len(segment['used_scripts'])
            }
        return None
    
    def remove_time_segment(self, segment_key: str) -> bool:
        """
        删除时间段话术
        
        Args:
            segment_key: 时间段标识符
            
        Returns:
            是否删除成功
        """
        if segment_key in self.time_segments:
            del self.time_segments[segment_key]
            self.logger.info(f"删除时间段话术: {segment_key}")
            return True
        return False
    
    def _parse_scripts(self, content: str) -> List[str]:
        """
        解析话术内容，提取话术列表
        
        Args:
            content: 话术内容
            
        Returns:
            话术列表
        """
        scripts = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            # 跳过注释行和空行
            if not line or line.startswith('#'):
                continue
            
            # 匹配话术格式：数字***内容
            match = re.match(r'^\d+\*\*\*(.+)$', line)
            if match:
                script_content = match.group(1).strip()
                scripts.append(script_content)
        
        return scripts
    
    def _process_script_variables(self, script: str) -> str:
        """
        处理话术中的变量词和随机选择
        
        Args:
            script: 原始话术
            
        Returns:
            处理后的话术
        """
        processed = script
        
        # 处理随机选择：【选项1|选项2|选项3】
        def replace_random_choice(match):
            choices = match.group(1).split('|')
            return random.choice(choices)
        
        processed = re.sub(r'【([^】]+)】', replace_random_choice, processed)
        
        # 这里可以添加更多变量词处理
        # 例如：{nick}, {gametype}, {gamename} 等
        # 实际使用时需要传入具体的变量值
        
        return processed
    
    def get_all_segments(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有时间段信息
        
        Returns:
            所有时间段的信息字典
        """
        return {
            segment_key: {
                'start_time': segment['start'],
                'end_time': segment['end'],
                'total_scripts': len(segment['scripts']),
                'used_scripts': len(segment['used_scripts']),
                'content': segment['content']
            }
            for segment_key, segment in self.time_segments.items()
        }


def main():
    """测试函数"""
    manager = TimeSegmentManager()
    
    # 测试添加时间段话术
    content = """# 测试时间段话术
1***欢迎{nick}进入直播间！
2***【大家好|各位好|hello】，感谢观看！
3***现在是测试时间段
4***请多多支持！
"""
    
    segment_key = manager.add_time_segment(0, 60, content)
    print(f"添加时间段: {segment_key}")
    
    # 测试获取随机话术
    for i in range(8):  # 测试8次，应该会重置一次
        script = manager.get_random_script(segment_key, 30)
        print(f"第{i+1}次获取: {script}")
    
    # 测试时间段信息
    info = manager.get_segment_info(segment_key)
    print(f"时间段信息: {info}")


if __name__ == "__main__":
    main()
