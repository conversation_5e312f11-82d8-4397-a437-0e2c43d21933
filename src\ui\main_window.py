"""
AI Broadcaster v2 - 主界面窗口
应用程序主界面
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTabWidget, QStatusBar, QMenuBar, QMenu,
    QMessageBox, QSplitter, QFrame, QTextEdit, QProgressBar, QAction
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, pyqtSlot
from PyQt5.QtGui import QFont, QIcon

from ..services.logging_service import create_logger
from ..data.database_manager import DatabaseManager
from ..core.auth.user_manager import UserManager
from ..core.voice.voice_manager import VoiceManager
from ..core.playback_controller import PlaybackController
from .components.ai_speaker_panel import AISpeakerPanel
from .components.script_panel import ScriptPanel
from .components.system_panel import SystemPanel
from .components.video_control_panel import VideoControlPanel


class MainWindow(QMainWindow):
    """主界面窗口"""

    def __init__(self, user_info=None):
        super().__init__()
        self.logger = create_logger("main_window")
        self.user_info = user_info

        # 初始化核心组件
        self.db_manager = DatabaseManager()
        self.user_manager = UserManager(self.db_manager)
        self.voice_manager = VoiceManager(self.db_manager)
        self.playback_controller = PlaybackController()

        # 设置当前用户
        if user_info:
            self.user_manager.current_user = user_info

        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # 每秒更新一次

        self.init_ui()
        self.setup_connections()

        self.logger.info("主界面初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("AI Broadcaster v2 - 智能AI广播系统")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # 设置窗口居中
        self.center_window()

        # 创建菜单栏
        self.create_menu_bar()

        # 创建中央部件
        self.create_central_widget()

        # 创建状态栏
        self.create_status_bar()

    def center_window(self):
        """窗口居中"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')

        # 导入配置
        import_action = QAction('导入配置...', self)
        import_action.setShortcut('Ctrl+I')
        import_action.triggered.connect(self.import_config)
        file_menu.addAction(import_action)

        # 导出配置
        export_action = QAction('导出配置...', self)
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.export_config)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 工具菜单
        tools_menu = menubar.addMenu('工具(&T)')

        # 清理缓存
        clear_cache_action = QAction('清理语音缓存', self)
        clear_cache_action.triggered.connect(self.clear_voice_cache)
        tools_menu.addAction(clear_cache_action)

        # 数据库备份
        backup_action = QAction('备份数据库', self)
        backup_action.triggered.connect(self.backup_database)
        tools_menu.addAction(backup_action)

        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')

        # 关于
        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_central_widget(self):
        """创建中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # 左侧面板
        self.create_left_panel(splitter)

        # 右侧面板
        self.create_right_panel(splitter)

        # 设置分割器比例
        splitter.setSizes([800, 400])

    def create_left_panel(self, parent):
        """创建左侧面板"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)

        # 用户信息栏
        self.create_user_info_bar(left_layout)

        # 功能选项卡
        self.create_function_tabs(left_layout)

        parent.addWidget(left_widget)

    def create_user_info_bar(self, layout):
        """创建用户信息栏"""
        user_frame = QFrame()
        user_frame.setFrameStyle(QFrame.StyledPanel)
        user_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
            }
        """)

        user_layout = QHBoxLayout(user_frame)

        # 用户信息
        if self.user_info:
            user_label = QLabel(f"欢迎，{self.user_info['username']}")
            user_label.setFont(QFont("", 12, QFont.Bold))
        else:
            user_label = QLabel("未登录用户")

        user_layout.addWidget(user_label)

        # 弹性空间
        user_layout.addStretch()

        # 登出按钮
        logout_button = QPushButton("登出")
        logout_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        logout_button.clicked.connect(self.logout)
        user_layout.addWidget(logout_button)

        layout.addWidget(user_frame)

    def create_function_tabs(self, layout):
        """创建功能选项卡"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 5px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                padding: 10px 15px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                min-width: 80px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #007bff;
            }
            QTabBar::tab:hover {
                background-color: #e9ecef;
            }
        """)

        # AI主播面板
        self.ai_speaker_panel = AISpeakerPanel(self.voice_manager)
        self.tab_widget.addTab(self.ai_speaker_panel, "AI主播")

        # 话术管理面板
        self.script_panel = ScriptPanel(self.db_manager, self.user_info)
        self.tab_widget.addTab(self.script_panel, "话术管理")

        # 视频控制面板
        self.video_control_panel = VideoControlPanel(self.playback_controller)
        self.tab_widget.addTab(self.video_control_panel, "视频控制")

        # 系统设置面板
        self.system_panel = SystemPanel(self.db_manager, self.user_info)
        self.tab_widget.addTab(self.system_panel, "系统设置")

        # TODO: 添加其他功能面板
        # self.tab_widget.addTab(QWidget(), "AI对话管理")
        # self.tab_widget.addTab(QWidget(), "副视频设置")
        # self.tab_widget.addTab(QWidget(), "OBS控制")
        # self.tab_widget.addTab(QWidget(), "报时设置")
        # self.tab_widget.addTab(QWidget(), "弹幕设置")

        layout.addWidget(self.tab_widget)

    def create_right_panel(self, parent):
        """创建右侧面板"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)

        # 控制面板
        self.create_control_panel(right_layout)

        # 日志面板
        self.create_log_panel(right_layout)

        parent.addWidget(right_widget)

    def create_control_panel(self, layout):
        """创建控制面板"""
        control_frame = QFrame()
        control_frame.setFrameStyle(QFrame.StyledPanel)
        control_frame.setMaximumHeight(200)
        control_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)

        control_layout = QVBoxLayout(control_frame)

        # 标题
        title_label = QLabel("播放控制")
        title_label.setFont(QFont("", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        control_layout.addWidget(title_label)

        # 控制按钮
        button_layout = QHBoxLayout()

        self.play_button = QPushButton("开始播放")
        self.play_button.setStyleSheet(self.get_control_button_style("#28a745"))
        self.play_button.clicked.connect(self.toggle_playback)
        button_layout.addWidget(self.play_button)

        self.stop_button = QPushButton("停止")
        self.stop_button.setStyleSheet(self.get_control_button_style("#dc3545"))
        self.stop_button.clicked.connect(self.stop_playback)
        button_layout.addWidget(self.stop_button)

        control_layout.addLayout(button_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        control_layout.addWidget(self.progress_bar)

        # 状态信息
        self.status_label = QLabel("就绪")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        control_layout.addWidget(self.status_label)

        layout.addWidget(control_frame)

    def create_log_panel(self, layout):
        """创建日志面板"""
        log_frame = QFrame()
        log_frame.setFrameStyle(QFrame.StyledPanel)
        log_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)

        log_layout = QVBoxLayout(log_frame)

        # 标题
        log_title = QLabel("系统日志")
        log_title.setFont(QFont("", 12, QFont.Bold))
        log_layout.addWidget(log_title)

        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
            }
        """)
        log_layout.addWidget(self.log_text)

        # 清空日志按钮
        clear_log_button = QPushButton("清空日志")
        clear_log_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        clear_log_button.clicked.connect(self.clear_log)
        log_layout.addWidget(clear_log_button)

        layout.addWidget(log_frame)

    def get_control_button_style(self, color):
        """获取控制按钮样式"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
            QPushButton:disabled {{
                background-color: #6c757d;
                color: #adb5bd;
            }}
        """

    def darken_color(self, color, factor=0.9):
        """使颜色变暗"""
        color_map = {
            "#28a745": "#218838",
            "#dc3545": "#c82333",
            "#007bff": "#0056b3"
        }
        return color_map.get(color, color)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 状态信息
        self.status_bar.showMessage("就绪")

        # 右侧状态信息
        self.connection_label = QLabel("数据库: 已连接")
        self.connection_label.setStyleSheet("color: #28a745;")
        self.status_bar.addPermanentWidget(self.connection_label)

    def setup_connections(self):
        """设置信号连接"""
        # 选项卡切换事件
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

    def add_log_message(self, message):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)

        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    @pyqtSlot()
    def update_status(self):
        """更新状态信息"""
        # 更新状态栏信息
        if self.voice_manager:
            current_speaker = self.voice_manager.get_current_speaker()
            if current_speaker:
                self.status_bar.showMessage(f"当前主播: {current_speaker['name']}")

    @pyqtSlot(int)
    def on_tab_changed(self, index):
        """选项卡切换事件"""
        tab_names = ["AI主播", "话术管理", "视频控制", "系统设置"]
        if 0 <= index < len(tab_names):
            self.add_log_message(f"切换到 {tab_names[index]} 面板")

    @pyqtSlot()
    def toggle_playback(self):
        """切换播放状态"""
        if self.play_button.text() == "开始播放":
            self.play_button.setText("暂停播放")
            self.play_button.setStyleSheet(self.get_control_button_style("#ffc107"))
            self.status_label.setText("播放中...")
            self.progress_bar.setVisible(True)
            self.add_log_message("开始播放")
        else:
            self.play_button.setText("开始播放")
            self.play_button.setStyleSheet(self.get_control_button_style("#28a745"))
            self.status_label.setText("已暂停")
            self.add_log_message("暂停播放")

    @pyqtSlot()
    def stop_playback(self):
        """停止播放"""
        self.play_button.setText("开始播放")
        self.play_button.setStyleSheet(self.get_control_button_style("#28a745"))
        self.status_label.setText("已停止")
        self.progress_bar.setVisible(False)
        self.add_log_message("停止播放")

    @pyqtSlot()
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.add_log_message("日志已清空")

    @pyqtSlot()
    def logout(self):
        """登出"""
        reply = QMessageBox.question(
            self, "确认", "确定要登出吗？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.logger.info("用户登出")
            self.close()

    @pyqtSlot()
    def import_config(self):
        """导入配置"""
        QMessageBox.information(self, "提示", "导入配置功能待实现")

    @pyqtSlot()
    def export_config(self):
        """导出配置"""
        QMessageBox.information(self, "提示", "导出配置功能待实现")

    @pyqtSlot()
    def clear_voice_cache(self):
        """清理语音缓存"""
        reply = QMessageBox.question(
            self, "确认", "确定要清理语音缓存吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                count = self.voice_manager.clear_voice_cache()
                QMessageBox.information(self, "成功", f"已清理 {count} 个缓存文件")
                self.add_log_message(f"清理语音缓存: {count} 个文件")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"清理缓存失败: {str(e)}")

    @pyqtSlot()
    def backup_database(self):
        """备份数据库"""
        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"data/backup/ai_broadcaster_{timestamp}.db"

            success = self.db_manager.backup_database(backup_path)
            if success:
                QMessageBox.information(self, "成功", f"数据库备份完成\n路径: {backup_path}")
                self.add_log_message(f"数据库备份完成: {backup_path}")
            else:
                QMessageBox.warning(self, "错误", "数据库备份失败")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"备份失败: {str(e)}")

    @pyqtSlot()
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于",
                         "AI Broadcaster v2\n"
                         "智能AI广播系统\n\n"
                         "版本: 2.0.0\n"
                         "基于PyQt6开发")

    def closeEvent(self, event):
        """窗口关闭事件"""
        reply = QMessageBox.question(
            self, "确认退出", "确定要退出AI Broadcaster吗？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 停止定时器
            if hasattr(self, 'status_timer') and self.status_timer:
                self.status_timer.stop()

            # 清理播放控制器资源
            if hasattr(self, 'playback_controller') and self.playback_controller:
                try:
                    self.playback_controller.close()
                except Exception as e:
                    self.logger.error(f"清理播放控制器失败: {e}")

            # 清理数据库资源
            if hasattr(self, 'db_manager') and self.db_manager:
                try:
                    self.db_manager.close()
                except Exception as e:
                    self.logger.error(f"清理数据库失败: {e}")

            self.logger.info("主界面关闭")
            event.accept()
        else:
            event.ignore()
