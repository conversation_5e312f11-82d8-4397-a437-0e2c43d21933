"""
AI主播系统 v2 - 弹幕WebSocket服务
弹幕消息的接收和处理
"""

import asyncio
import json
import websockets
from typing import Dict, Any, Callable, Optional
from datetime import datetime

from .logging_service import create_logger
from .error_handler import handle_exceptions


class DanmakuService:
    """弹幕WebSocket服务"""
    
    def __init__(self, websocket_url: str = "ws://127.0.0.1:9999"):
        self.logger = create_logger("danmaku_service")
        self.websocket_url = websocket_url
        self.websocket = None
        self.is_connected = False
        self.is_running = False
        
        # 消息处理回调
        self.message_handlers = []
        
        # 连接状态回调
        self.connection_handlers = []
        
    def add_message_handler(self, handler: Callable[[Dict[str, Any]], None]):
        """添加消息处理器"""
        self.message_handlers.append(handler)
        
    def add_connection_handler(self, handler: Callable[[bool], None]):
        """添加连接状态处理器"""
        self.connection_handlers.append(handler)
        
    def remove_message_handler(self, handler: Callable[[Dict[str, Any]], None]):
        """移除消息处理器"""
        if handler in self.message_handlers:
            self.message_handlers.remove(handler)
            
    def remove_connection_handler(self, handler: Callable[[bool], None]):
        """移除连接状态处理器"""
        if handler in self.connection_handlers:
            self.connection_handlers.remove(handler)
            
    @handle_exceptions("DanmakuService")
    async def connect(self) -> bool:
        """
        连接到弹幕WebSocket服务器
        
        Returns:
            是否连接成功
        """
        try:
            self.logger.info(f"连接弹幕服务器: {self.websocket_url}")
            
            # 建立WebSocket连接
            self.websocket = await websockets.connect(
                self.websocket_url,
                ping_interval=30,
                ping_timeout=10
            )
            
            self.is_connected = True
            self.logger.info("弹幕服务器连接成功")
            
            # 通知连接状态变化
            self._notify_connection_status(True)
            
            return True
            
        except Exception as e:
            self.logger.error(f"连接弹幕服务器失败: {e}")
            self.is_connected = False
            self._notify_connection_status(False)
            return False
            
    @handle_exceptions("DanmakuService")
    async def disconnect(self):
        """断开WebSocket连接"""
        try:
            self.is_running = False
            
            if self.websocket:
                await self.websocket.close()
                self.websocket = None
                
            self.is_connected = False
            self.logger.info("弹幕服务器连接已断开")
            
            # 通知连接状态变化
            self._notify_connection_status(False)
            
        except Exception as e:
            self.logger.error(f"断开连接失败: {e}")
            
    @handle_exceptions("DanmakuService")
    async def start_listening(self):
        """开始监听弹幕消息"""
        if not self.is_connected or not self.websocket:
            self.logger.error("未连接到弹幕服务器")
            return
            
        self.is_running = True
        self.logger.info("开始监听弹幕消息...")
        
        try:
            while self.is_running and self.is_connected:
                try:
                    # 接收消息
                    message = await asyncio.wait_for(
                        self.websocket.recv(),
                        timeout=1.0
                    )
                    
                    # 解析消息
                    await self._handle_message(message)
                    
                except asyncio.TimeoutError:
                    # 超时继续循环
                    continue
                except websockets.exceptions.ConnectionClosed:
                    self.logger.warning("弹幕服务器连接已关闭")
                    self.is_connected = False
                    self._notify_connection_status(False)
                    break
                    
        except Exception as e:
            self.logger.error(f"监听弹幕消息失败: {e}")
        finally:
            self.is_running = False
            
    @handle_exceptions("DanmakuService")
    async def _handle_message(self, raw_message: str):
        """处理接收到的消息"""
        try:
            # 解析JSON消息
            message_data = json.loads(raw_message)
            
            # 标准化消息格式
            standardized_message = self._standardize_message(message_data)
            
            # 记录消息
            self.logger.debug(f"收到弹幕: {standardized_message.get('user', 'Unknown')} - {standardized_message.get('message', '')}")
            
            # 调用所有消息处理器
            for handler in self.message_handlers:
                try:
                    handler(standardized_message)
                except Exception as e:
                    self.logger.error(f"消息处理器执行失败: {e}")
                    
        except json.JSONDecodeError:
            self.logger.error(f"无法解析消息: {raw_message}")
        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")
            
    def _standardize_message(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化消息格式"""
        # 根据API文档标准化消息格式
        standardized = {
            'type': message_data.get('type', 'ChatMessage'),
            'user': message_data.get('user', 'Unknown'),
            'message': message_data.get('message', ''),
            'color': message_data.get('color', '#FFFFFF'),
            'timestamp': message_data.get('timestamp', int(datetime.now().timestamp()))
        }
        
        # 如果没有时间戳，使用当前时间
        if not standardized['timestamp']:
            standardized['timestamp'] = int(datetime.now().timestamp())
            
        return standardized
        
    def _notify_connection_status(self, connected: bool):
        """通知连接状态变化"""
        for handler in self.connection_handlers:
            try:
                handler(connected)
            except Exception as e:
                self.logger.error(f"连接状态处理器执行失败: {e}")
                
    @handle_exceptions("DanmakuService")
    async def send_message(self, message: Dict[str, Any]) -> bool:
        """
        发送消息到弹幕服务器
        
        Args:
            message: 要发送的消息
            
        Returns:
            是否发送成功
        """
        if not self.is_connected or not self.websocket:
            self.logger.error("未连接到弹幕服务器")
            return False
            
        try:
            # 转换为JSON并发送
            json_message = json.dumps(message, ensure_ascii=False)
            await self.websocket.send(json_message)
            
            self.logger.debug(f"发送消息成功: {message}")
            return True
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False
            
    def get_connection_status(self) -> Dict[str, Any]:
        """获取连接状态信息"""
        return {
            'connected': self.is_connected,
            'running': self.is_running,
            'websocket_url': self.websocket_url,
            'message_handlers_count': len(self.message_handlers),
            'connection_handlers_count': len(self.connection_handlers)
        }
        
    def set_websocket_url(self, url: str):
        """设置WebSocket地址"""
        self.websocket_url = url
        self.logger.info(f"设置弹幕WebSocket地址: {self.websocket_url}")
        
    async def reconnect(self) -> bool:
        """重新连接"""
        self.logger.info("尝试重新连接弹幕服务器...")
        
        # 先断开现有连接
        await self.disconnect()
        
        # 等待一秒后重新连接
        await asyncio.sleep(1)
        
        # 重新连接
        return await self.connect()
