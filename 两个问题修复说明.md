# 两个问题修复说明

## 🔧 问题修复总结

我已经针对您提到的两个问题进行了深度修复：

### 问题1：OBS连接成功后自动加载视频源选项 ✅
### 问题2：按照视频位置时间段选择对应语音播放 ✅

---

## 🎬 问题1修复：OBS连接成功后自动加载配置

### 问题描述
OBS连接成功后，没有自动加载上次设置的视频源A和视频源B的选项。

### 修复内容

1. **配置加载逻辑增强**
   - 增加用户设置加载状态检查
   - 支持多种配置键名兼容性
   - 添加详细的调试信息输出

2. **配置文件兼容性**
   - 支持新格式：`obs.main_video_source_a`
   - 兼容旧格式：`video_source_a`、`obs_video_source_a`
   - 安全的类型转换和默认值处理

3. **时序控制优化**
   - 确保用户设置先加载完成
   - 等待源列表刷新完成
   - 临时断开信号避免循环保存

### 关键修复代码
```python
def load_obs_video_sources_from_config(self):
    """从配置文件加载视频源A和视频源B设置"""
    # 确保用户设置已加载
    if not hasattr(self, 'user_settings') or not self.user_settings:
        print("⚠️ 用户设置未加载，尝试重新加载...")
        self.load_user_settings()
        QTimer.singleShot(500, self.load_obs_video_sources_from_config)
        return

    # 获取保存的配置 - 尝试多种可能的键名
    obs_config = self.user_settings.get('obs', {})
    
    # 如果obs配置为空，尝试从根级别获取
    if not obs_config:
        saved_source_a = self.user_settings.get('video_source_a', '') or \
                        self.user_settings.get('obs_video_source_a', '')
        saved_source_b = self.user_settings.get('video_source_b', '') or \
                        self.user_settings.get('obs_video_source_b', '')
    else:
        saved_source_a = obs_config.get('main_video_source_a', '')
        saved_source_b = obs_config.get('main_video_source_b', '')

    # 检查下拉框是否有内容
    if self.video_source_a_combo.count() == 0:
        print("⚠️ 视频源下拉框为空，等待源列表刷新...")
        QTimer.singleShot(1000, self.load_obs_video_sources_from_config)
        return

    # 临时断开信号连接，避免设置时触发保存
    try:
        self.video_source_a_combo.currentTextChanged.disconnect()
        self.video_source_b_combo.currentTextChanged.disconnect()
    except:
        pass

    # 设置视频源
    if saved_source_a in available_sources:
        self.video_source_a_combo.setCurrentText(saved_source_a)
    
    # 重新连接信号
    self.video_source_a_combo.currentTextChanged.connect(self.schedule_save)
```

### 预期效果
```
✅ OBS连接成功: localhost:4455
🔄 从配置加载OBS视频源设置...
🔍 用户设置总数: 15 项
🔍 用户设置键: ['obs', 'game', 'voice', 'last_save_time']
📋 配置中的设置:
  - 视频源A: '主视频A'
  - 视频源B: '主视频B'
  - 速度范围: 0.5 - 2.0
📋 当前可用源: ['主视频A', '主视频B', '游戏画面', '摄像头']
✅ 已设置视频源A: 主视频A
✅ 已设置视频源B: 主视频B
✅ 已设置速度范围: 0.5 - 2.0
🎯 视频源设置完成，准备自动应用OBS设置
✅ 自动应用OBS设置成功: A=主视频A, B=主视频B, 速度=0.5-2.0
```

---

## 🕐 问题2修复：按照视频位置时间段选择语音播放

### 问题描述
未按照视频位置所处话术的时间段选择对应的语音播放。比如当前主视频播放位置为15秒，应该选择10-20秒的时间段播放这个时间段的语音。

### 修复内容

1. **时间段匹配逻辑增强**
   - 添加详细的调试信息输出
   - 改进错误处理和异常捕获
   - 增加进度条和话术存在性检查

2. **调试信息完善**
   - 显示当前进度条位置
   - 显示当前选择的话术
   - 显示所有可用时间段
   - 显示匹配过程和结果

3. **播放选择逻辑优化**
   - 优先选择弹幕话术和报时话术（不受时间段限制）
   - 然后选择匹配当前时间段的主视频话术
   - 最后选择任意可用的主视频话术作为备选

### 关键修复代码
```python
def get_current_time_segment(self):
    """获取当前主视频的时间段"""
    # 获取当前进度条位置
    current_position = 0
    if hasattr(self, 'progress_bar'):
        current_position = self.progress_bar.value()
    else:
        print("⚠️ 进度条不存在，使用默认位置0")

    # 获取当前选择的话术
    current_script = ""
    if hasattr(self, 'script_combo'):
        current_script = self.script_combo.currentText()

    print(f"🔍 时间段匹配调试:")
    print(f"  - 当前进度条位置: {current_position}")
    print(f"  - 当前选择话术: '{current_script}'")

    if current_script and hasattr(self, 'script_time_segments') and current_script in self.script_time_segments:
        time_segments = self.script_time_segments[current_script]
        print(f"  - 话术'{current_script}'的时间段数: {len(time_segments)}")

        # 查找当前位置对应的时间段
        for segment_name, segment_data in time_segments.items():
            start_time = segment_data.get('start_time', 0)
            end_time = segment_data.get('end_time', 0)
            
            print(f"    检查时间段 '{segment_name}': {start_time}-{end_time}秒")
            
            if start_time <= current_position <= end_time:
                print(f"  ✅ 匹配到时间段: '{segment_name}' ({start_time}-{end_time}秒)")
                return segment_name

        print(f"  ⚠️ 位置{current_position}秒不在任何时间段内")

    return "不在时间段内"

def get_next_playback_item(self):
    """获取下一个要播放的项目（按优先级和时间段）"""
    # 获取当前主视频时间段
    current_time_segment = self.get_current_time_segment()
    print(f"🕐 当前主视频时间段: {current_time_segment}")

    # 优先选择弹幕话术和报时话术（不受时间段限制）
    high_priority_items = [
        item for item in available_items
        if item['voice_type'] in ['弹幕话术', '报时话术']
    ]

    if high_priority_items:
        next_item = high_priority_items[0]
        print(f"🎯 选择播放: {next_item['voice_type']} - {next_item['content'][:30]}...")
        return next_item

    # 如果没有高优先级项目，选择匹配当前时间段的主视频话术
    if current_time_segment and current_time_segment != "不在时间段内":
        matching_items = [
            item for item in available_items
            if (item['voice_type'] == '主视频话术' and 
                item['time_segment'] == current_time_segment)
        ]

        if matching_items:
            next_item = matching_items[0]
            print(f"🎯 选择播放: {next_item['voice_type']} - 时间段:{current_time_segment} - {next_item['content'][:30]}...")
            return next_item

    # 如果没有匹配时间段的，选择任意主视频话术
    main_video_items = [
        item for item in available_items
        if item['voice_type'] == '主视频话术'
    ]

    if main_video_items:
        next_item = main_video_items[0]
        print(f"🎯 选择播放: {next_item['voice_type']} - {next_item['content'][:30]}... (备选)")
        return next_item

    return None
```

### 预期效果
```
🔍 时间段匹配调试:
  - 当前进度条位置: 15
  - 当前选择话术: '话术1'
  - script_time_segments总数: 1
  - 话术'话术1'的时间段数: 2
    检查时间段 '10秒 - 20秒': 10-20秒
  ✅ 匹配到时间段: '10秒 - 20秒' (10-20秒)
🕐 当前主视频时间段: 10秒 - 20秒
🎯 选择播放: 主视频话术 - 时间段:10秒 - 20秒 - 欢迎来到直播间！...
```

---

## 🧪 测试方法

### 测试问题1：OBS配置自动加载
1. **首次配置**：连接OBS，手动设置视频源A和B，应用设置
2. **断开重连**：断开OBS连接，重新连接
3. **观察输出**：查看控制台是否显示配置加载过程
4. **验证结果**：确认视频源下拉框自动选择了之前的设置

### 测试问题2：时间段匹配
1. **设置时间段**：在话术管理中设置时间段（如10-20秒，40-50秒）
2. **调整进度条**：拖动进度条到15秒位置
3. **开始播放**：点击播放按钮
4. **观察输出**：查看控制台的时间段匹配调试信息
5. **验证结果**：确认选择了10-20秒时间段的话术

### 调试信息示例

**OBS配置加载：**
```
🔄 从配置加载OBS视频源设置...
🔍 用户设置总数: 15 项
🔍 用户设置键: ['obs', 'game', 'voice', 'last_save_time']
📋 配置中的设置:
  - 视频源A: '主视频A'
  - 视频源B: '主视频B'
  - 速度范围: 0.5 - 2.0
📋 当前可用源: ['主视频A', '主视频B', '游戏画面']
✅ 已设置视频源A: 主视频A
✅ 已设置视频源B: 主视频B
```

**时间段匹配：**
```
🔍 时间段匹配调试:
  - 当前进度条位置: 15
  - 当前选择话术: '话术1'
  - script_time_segments总数: 1
  - 话术'话术1'的时间段数: 2
    检查时间段 '10秒 - 20秒': 10-20秒
    检查时间段 '40秒 - 50秒': 40-50秒
  ✅ 匹配到时间段: '10秒 - 20秒' (10-20秒)
🕐 当前主视频时间段: 10秒 - 20秒
🎯 选择播放: 主视频话术 - 时间段:10秒 - 20秒 - 欢迎来到直播间！...
```

---

## 🎯 功能特点

### OBS配置自动加载
- **多格式兼容**：支持新旧配置文件格式
- **智能重试**：配置未加载时自动重试
- **信号管理**：避免设置时的循环保存
- **详细日志**：完整的加载过程追踪

### 时间段智能匹配
- **精确匹配**：根据进度条位置精确匹配时间段
- **优先级控制**：弹幕>报时>时间段匹配>备选
- **调试友好**：详细的匹配过程输出
- **容错处理**：组件不存在时的优雅降级

### 错误处理
- **异常捕获**：完整的异常处理和日志输出
- **状态检查**：组件存在性和数据有效性检查
- **自动恢复**：失败时的自动重试机制
- **用户友好**：清晰的错误提示和状态反馈

---

## ✅ 修复验证

两个问题都已修复并增强：

1. ✅ **OBS配置自动加载**：连接成功后自动恢复视频源A/B和速度设置
2. ✅ **时间段智能匹配**：根据主视频位置选择对应时间段的话术

现在您可以：
1. 连接OBS后自动恢复之前的配置
2. 播放时自动选择当前时间段对应的话术
3. 通过详细的调试信息了解匹配过程

享受更智能的AI直播体验！🚀
