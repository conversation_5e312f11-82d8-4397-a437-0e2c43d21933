"""
视频控制面板
提供双主视频无缝切换和速度控制功能
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel,
    QPushButton, QComboBox, QSlider, QSpinBox, QDoubleSpinBox,
    QCheckBox, QTextEdit, QMessageBox, QProgressBar, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot, QTimer
from PyQt5.QtGui import QFont

from ...services.logging_service import create_logger


class VideoControlPanel(QWidget):
    """视频控制面板"""
    
    # 信号定义
    video_switched = pyqtSignal(str, str)  # 旧源, 新源
    speed_changed = pyqtSignal(float)      # 新速度
    
    def __init__(self, playback_controller):
        super().__init__()
        self.logger = create_logger("video_control_panel")
        self.playback_controller = playback_controller
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # 每秒更新一次
        
        self.init_ui()
        self.setup_connections()
        self.load_video_sources()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 创建视频源设置组
        self.create_video_source_group(layout)
        
        # 创建速度控制组
        self.create_speed_control_group(layout)
        
        # 创建切换控制组
        self.create_switch_control_group(layout)
        
        # 创建状态显示组
        self.create_status_group(layout)
        
    def create_video_source_group(self, parent_layout):
        """创建视频源设置组"""
        source_group = QGroupBox("双主视频源设置")
        source_group.setStyleSheet(self.get_group_style())
        source_layout = QVBoxLayout(source_group)
        
        # 主视频源A
        source_a_layout = QHBoxLayout()
        source_a_layout.addWidget(QLabel("主视频源A:"))
        self.source_a_combo = QComboBox()
        self.source_a_combo.setStyleSheet(self.get_input_style())
        self.source_a_combo.setMinimumWidth(200)
        source_a_layout.addWidget(self.source_a_combo)
        source_a_layout.addStretch()
        source_layout.addLayout(source_a_layout)
        
        # 主视频源B
        source_b_layout = QHBoxLayout()
        source_b_layout.addWidget(QLabel("主视频源B:"))
        self.source_b_combo = QComboBox()
        self.source_b_combo.setStyleSheet(self.get_input_style())
        self.source_b_combo.setMinimumWidth(200)
        source_b_layout.addWidget(self.source_b_combo)
        source_b_layout.addStretch()
        source_layout.addLayout(source_b_layout)
        
        # 设置按钮
        button_layout = QHBoxLayout()
        self.set_sources_button = QPushButton("设置双主视频源")
        self.set_sources_button.setStyleSheet(self.get_button_style("#007bff"))
        button_layout.addWidget(self.set_sources_button)
        
        self.refresh_sources_button = QPushButton("刷新源列表")
        self.refresh_sources_button.setStyleSheet(self.get_button_style("#6c757d"))
        button_layout.addWidget(self.refresh_sources_button)
        
        button_layout.addStretch()
        source_layout.addLayout(button_layout)
        
        parent_layout.addWidget(source_group)
        
    def create_speed_control_group(self, parent_layout):
        """创建速度控制组"""
        speed_group = QGroupBox("视频速度控制")
        speed_group.setStyleSheet(self.get_group_style())
        speed_layout = QVBoxLayout(speed_group)
        
        # 下一个视频源速度预设
        next_speed_layout = QHBoxLayout()
        next_speed_layout.addWidget(QLabel("下一个视频源速度:"))
        
        self.next_speed_slider = QSlider(Qt.Horizontal)
        self.next_speed_slider.setRange(50, 200)  # 0.5x 到 2.0x
        self.next_speed_slider.setValue(100)      # 默认1.0x
        self.next_speed_slider.setStyleSheet(self.get_slider_style())
        next_speed_layout.addWidget(self.next_speed_slider)
        
        self.next_speed_label = QLabel("1.0x")
        self.next_speed_label.setMinimumWidth(50)
        self.next_speed_label.setStyleSheet("font-weight: bold; color: #007bff;")
        next_speed_layout.addWidget(self.next_speed_label)
        
        speed_layout.addLayout(next_speed_layout)
        
        # 快速预设按钮
        preset_layout = QHBoxLayout()
        preset_layout.addWidget(QLabel("快速预设:"))
        
        preset_speeds = [0.5, 0.8, 1.0, 1.2, 1.5, 2.0]
        for speed in preset_speeds:
            button = QPushButton(f"{speed}x")
            button.setStyleSheet(self.get_button_style("#28a745"))
            button.clicked.connect(lambda checked, s=speed: self.set_preset_speed(s))
            preset_layout.addWidget(button)
        
        preset_layout.addStretch()
        speed_layout.addLayout(preset_layout)
        
        # 准备下一个视频源
        prepare_layout = QHBoxLayout()
        self.prepare_button = QPushButton("准备下一个视频源")
        self.prepare_button.setStyleSheet(self.get_button_style("#ffc107", "#212529"))
        prepare_layout.addWidget(self.prepare_button)
        prepare_layout.addStretch()
        speed_layout.addLayout(prepare_layout)
        
        parent_layout.addWidget(speed_group)
        
    def create_switch_control_group(self, parent_layout):
        """创建切换控制组"""
        switch_group = QGroupBox("视频切换控制")
        switch_group.setStyleSheet(self.get_group_style())
        switch_layout = QVBoxLayout(switch_group)
        
        # 手动切换按钮
        manual_layout = QHBoxLayout()
        self.manual_switch_button = QPushButton("手动切换视频")
        self.manual_switch_button.setStyleSheet(self.get_button_style("#dc3545"))
        self.manual_switch_button.setMinimumHeight(40)
        manual_layout.addWidget(self.manual_switch_button)
        
        # 强制切换到指定源
        self.force_switch_a_button = QPushButton("强制切换到A")
        self.force_switch_a_button.setStyleSheet(self.get_button_style("#17a2b8"))
        manual_layout.addWidget(self.force_switch_a_button)
        
        self.force_switch_b_button = QPushButton("强制切换到B")
        self.force_switch_b_button.setStyleSheet(self.get_button_style("#17a2b8"))
        manual_layout.addWidget(self.force_switch_b_button)
        
        switch_layout.addLayout(manual_layout)
        
        # 自动监控控制
        monitor_layout = QHBoxLayout()
        self.start_monitor_button = QPushButton("开始自动监控")
        self.start_monitor_button.setStyleSheet(self.get_button_style("#28a745"))
        monitor_layout.addWidget(self.start_monitor_button)
        
        self.stop_monitor_button = QPushButton("停止自动监控")
        self.stop_monitor_button.setStyleSheet(self.get_button_style("#6c757d"))
        self.stop_monitor_button.setEnabled(False)
        monitor_layout.addWidget(self.stop_monitor_button)
        
        monitor_layout.addStretch()
        switch_layout.addLayout(monitor_layout)
        
        # 单一显示确保
        ensure_layout = QHBoxLayout()
        self.ensure_single_button = QPushButton("确保单一显示")
        self.ensure_single_button.setStyleSheet(self.get_button_style("#fd7e14"))
        ensure_layout.addWidget(self.ensure_single_button)
        ensure_layout.addStretch()
        switch_layout.addLayout(ensure_layout)
        
        parent_layout.addWidget(switch_group)
        
    def create_status_group(self, parent_layout):
        """创建状态显示组"""
        status_group = QGroupBox("视频状态")
        status_group.setStyleSheet(self.get_group_style())
        status_layout = QVBoxLayout(status_group)
        
        # 状态信息显示
        self.status_text = QTextEdit()
        self.status_text.setReadOnly(True)
        self.status_text.setMaximumHeight(120)
        self.status_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                padding: 5px;
            }
        """)
        status_layout.addWidget(self.status_text)
        
        parent_layout.addWidget(status_group)
        
    def setup_connections(self):
        """设置信号连接"""
        # 速度滑块变化
        self.next_speed_slider.valueChanged.connect(self.on_speed_slider_changed)
        
        # 按钮点击事件
        self.set_sources_button.clicked.connect(self.on_set_sources_clicked)
        self.refresh_sources_button.clicked.connect(self.load_video_sources)
        self.prepare_button.clicked.connect(self.on_prepare_clicked)
        self.manual_switch_button.clicked.connect(self.on_manual_switch_clicked)
        self.force_switch_a_button.clicked.connect(self.on_force_switch_a_clicked)
        self.force_switch_b_button.clicked.connect(self.on_force_switch_b_clicked)
        self.start_monitor_button.clicked.connect(self.on_start_monitor_clicked)
        self.stop_monitor_button.clicked.connect(self.on_stop_monitor_clicked)
        self.ensure_single_button.clicked.connect(self.on_ensure_single_clicked)
        
    def load_video_sources(self):
        """加载视频源列表"""
        try:
            # 获取OBS媒体源列表
            sources = self.playback_controller.obs_controller.get_source_list()
            
            # 清空并重新填充下拉框
            self.source_a_combo.clear()
            self.source_b_combo.clear()
            
            for source in sources:
                self.source_a_combo.addItem(source)
                self.source_b_combo.addItem(source)
            
            self.logger.info(f"加载了 {len(sources)} 个视频源")
            
        except Exception as e:
            self.logger.error(f"加载视频源失败: {e}")
            QMessageBox.warning(self, "错误", f"加载视频源失败: {str(e)}")
            
    def set_preset_speed(self, speed: float):
        """设置预设速度"""
        slider_value = int(speed * 100)
        self.next_speed_slider.setValue(slider_value)
        
    @pyqtSlot(int)
    def on_speed_slider_changed(self, value):
        """速度滑块变化事件"""
        speed = value / 100.0
        self.next_speed_label.setText(f"{speed:.1f}x")
        
        # 设置下一个视频源的速度
        success = self.playback_controller.set_next_video_speed(speed)
        if success:
            self.speed_changed.emit(speed)
            
    @pyqtSlot()
    def on_set_sources_clicked(self):
        """设置双主视频源按钮点击事件"""
        source_a = self.source_a_combo.currentText()
        source_b = self.source_b_combo.currentText()
        
        if not source_a or not source_b:
            QMessageBox.warning(self, "提示", "请选择两个不同的视频源")
            return
            
        if source_a == source_b:
            QMessageBox.warning(self, "提示", "请选择两个不同的视频源")
            return
            
        success = self.playback_controller.set_dual_video_sources(source_a, source_b)
        if success:
            QMessageBox.information(self, "成功", f"双主视频源设置成功\nA: {source_a}\nB: {source_b}")
            self.logger.info(f"设置双主视频源: A={source_a}, B={source_b}")
        else:
            QMessageBox.warning(self, "失败", "双主视频源设置失败")
            
    @pyqtSlot()
    def on_prepare_clicked(self):
        """准备下一个视频源按钮点击事件"""
        speed = self.next_speed_slider.value() / 100.0
        success = self.playback_controller.prepare_next_video_with_speed(speed)
        if success:
            QMessageBox.information(self, "成功", f"下一个视频源已准备完成\n预设速度: {speed}x")
        else:
            QMessageBox.warning(self, "失败", "准备下一个视频源失败")
            
    @pyqtSlot()
    def on_manual_switch_clicked(self):
        """手动切换视频按钮点击事件"""
        success = self.playback_controller.manual_switch_video()
        if success:
            QMessageBox.information(self, "成功", "视频切换成功")
        else:
            QMessageBox.warning(self, "失败", "视频切换失败")
            
    @pyqtSlot()
    def on_force_switch_a_clicked(self):
        """强制切换到A按钮点击事件"""
        source_a = self.source_a_combo.currentText()
        if source_a:
            success = self.playback_controller.force_switch_to_video(source_a)
            if success:
                QMessageBox.information(self, "成功", f"强制切换到视频源A: {source_a}")
            else:
                QMessageBox.warning(self, "失败", "强制切换失败")
                
    @pyqtSlot()
    def on_force_switch_b_clicked(self):
        """强制切换到B按钮点击事件"""
        source_b = self.source_b_combo.currentText()
        if source_b:
            success = self.playback_controller.force_switch_to_video(source_b)
            if success:
                QMessageBox.information(self, "成功", f"强制切换到视频源B: {source_b}")
            else:
                QMessageBox.warning(self, "失败", "强制切换失败")
                
    @pyqtSlot()
    def on_start_monitor_clicked(self):
        """开始自动监控按钮点击事件"""
        success = self.playback_controller.start_dual_video_monitoring()
        if success:
            self.start_monitor_button.setEnabled(False)
            self.stop_monitor_button.setEnabled(True)
            QMessageBox.information(self, "成功", "自动监控已开始")
        else:
            QMessageBox.warning(self, "失败", "启动自动监控失败")
            
    @pyqtSlot()
    def on_stop_monitor_clicked(self):
        """停止自动监控按钮点击事件"""
        self.playback_controller.stop_dual_video_monitoring()
        self.start_monitor_button.setEnabled(True)
        self.stop_monitor_button.setEnabled(False)
        QMessageBox.information(self, "成功", "自动监控已停止")
        
    @pyqtSlot()
    def on_ensure_single_clicked(self):
        """确保单一显示按钮点击事件"""
        success = self.playback_controller.ensure_single_video_source_display()
        if success:
            QMessageBox.information(self, "成功", "已确保只有一个视频源显示")
        else:
            QMessageBox.warning(self, "失败", "确保单一显示失败")
            
    @pyqtSlot()
    def update_status(self):
        """更新状态显示"""
        try:
            status = self.playback_controller.get_status()
            dual_video_status = status.get('dual_video_status', {})
            
            # 格式化状态信息
            status_lines = [
                f"当前激活源: {dual_video_status.get('current_active_source', '未设置')}",
                f"下一个源: {dual_video_status.get('next_source', '未设置')}",
                f"预设速度: {dual_video_status.get('next_source_speed', 1.0):.1f}x",
                f"监控状态: {'运行中' if dual_video_status.get('monitoring', False) else '已停止'}",
                f"OBS连接: {'已连接' if dual_video_status.get('obs_connected', False) else '未连接'}",
                "",
                f"可见视频源: {self.playback_controller.get_all_visible_video_sources()}"
            ]
            
            self.status_text.setPlainText("\n".join(status_lines))
            
        except Exception as e:
            self.logger.error(f"更新状态失败: {e}")
            
    def get_group_style(self):
        """获取组框样式"""
        return """
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """
        
    def get_input_style(self):
        """获取输入框样式"""
        return """
            QComboBox {
                padding: 5px;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #007bff;
            }
        """
        
    def get_button_style(self, bg_color, text_color="white"):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: {text_color};
                border: none;
                border-radius: 3px;
                padding: 8px 15px;
                font-weight: bold;
                min-width: 80px;
            }}
            QPushButton:hover {{
                opacity: 0.8;
            }}
            QPushButton:disabled {{
                background-color: #6c757d;
                color: #adb5bd;
            }}
        """
        
    def get_slider_style(self):
        """获取滑块样式"""
        return """
            QSlider::groove:horizontal {
                border: 1px solid #dee2e6;
                height: 8px;
                background: #f8f9fa;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #007bff;
                border: 1px solid #007bff;
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
            QSlider::handle:horizontal:hover {
                background: #0056b3;
            }
        """
