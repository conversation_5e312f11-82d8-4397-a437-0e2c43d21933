# 🎬 副视频图层排序最终修复

## 🎯 修复目标

根据用户最新要求，在切换副视频前：

1. **先调整图层排序**：通过OBS操作把副视频的视频源排序移至最顶层
2. **再暂停主视频**：主视频只暂停不隐藏
3. **最后显示副视频**：调整位置并显示副视频

## 🔧 最终修复方案

### 主视频 → 副视频切换流程（修复后）

```
步骤1: 暂停双主视频自动切换
步骤2: 保存主视频当前播放状态（位置、状态）
步骤3: 在隐藏状态下启动副视频播放（预加载）
步骤4: 等待副视频启动（0.1秒）
步骤5: 🔥 先调整副视频图层顺序到最顶层
  - 将副视频移到最顶层（index=0）
  - 将主视频移到第二层（index=1）
步骤6: 🔥 暂停主视频（只暂停，不隐藏）
步骤7: 🔥 调整副视频位置并显示
  - 调整副视频位置到画面上方
  - 显示副视频源
```

**关键修复点**：
- ✅ **图层排序优先**：在暂停主视频之前先调整图层顺序
- ✅ **OBS操作顺序**：使用正确的OBS API调用顺序
- ✅ **无干扰切换**：图层排序不影响当前播放状态

## 📝 代码修改详情

### 修改：调整操作顺序

**位置**：`run_gui_qt5.py` 第4057-4105行

**修改前顺序**：
```
1. 暂停主视频
2. 调整图层顺序
3. 显示副视频
```

**修改后顺序**：
```python
# 🔥 步骤5：先调整副视频图层顺序到最顶层
try:
    if hasattr(obs_controller, 'send_request_sync'):
        # 获取当前场景
        current_scene = obs_controller.send_request_sync("GetCurrentProgramScene", {})
        if current_scene and "currentProgramSceneName" in current_scene:
            scene_name = current_scene["currentProgramSceneName"]
            
            # 🔥 步骤5.1：首先将副视频移到最顶层（在其他操作之前）
            sub_video_id = obs_controller._get_source_id(sub_video_source, scene_name)
            obs_controller.send_request_sync("SetSceneItemIndex", {
                "sceneName": scene_name,
                "sceneItemId": sub_video_id,
                "sceneItemIndex": 0  # 0表示最顶层
            })
            print(f"📋 副视频已移到最顶层: {sub_video_source}")
            
            # 🔥 步骤5.2：将主视频移到副视频下面一层
            main_video_id = obs_controller._get_source_id(current_main_source, scene_name)
            obs_controller.send_request_sync("SetSceneItemIndex", {
                "sceneName": scene_name,
                "sceneItemId": main_video_id,
                "sceneItemIndex": 1  # 1表示第二层
            })
            print(f"📋 主视频已移到第二层: {current_main_source}")
            
            print(f"🔄 图层排序完成: {sub_video_source}(顶层), {current_main_source}(第二层)")

# 🔥 步骤6：暂停主视频
if current_main_source:
    try:
        if hasattr(obs_controller, 'send_request_sync'):
            # 暂停主视频
            obs_controller.send_request_sync("TriggerMediaInputAction", {
                "inputName": current_main_source,
                "mediaAction": "OBS_WEBSOCKET_MEDIA_INPUT_ACTION_PAUSE"
            })
            print(f"⏸️ 主视频已暂停: {current_main_source}")

# 🔥 步骤7：调整副视频位置并显示
# ... 位置调整和显示代码 ...
```

### 技术优势

#### 1. **图层排序优先**
- **优点**：在暂停主视频之前就完成图层排序
- **效果**：避免图层调整时的视觉干扰
- **实现**：使用OBS的`SetSceneItemIndex`API

#### 2. **操作原子性**
- **图层排序**：一次性完成副视频和主视频的图层调整
- **状态管理**：图层排序不影响播放状态
- **视觉连续性**：确保切换过程的视觉连续性

#### 3. **OBS API调用顺序**
```
1. SetSceneItemIndex (副视频 → 顶层)
2. SetSceneItemIndex (主视频 → 第二层)
3. TriggerMediaInputAction (暂停主视频)
4. SetSceneItemTransform (调整副视频位置)
5. SetSceneItemEnabled (显示副视频)
```

## 🔄 完整工作流程

### 【主视频 → 副视频】最终流程
1. **暂停双主视频自动切换** ✅
2. **保存主视频状态** ✅
3. **预加载副视频** ✅
4. **🔥 先调整图层排序**：
   - 副视频移到最顶层 ✅
   - 主视频移到第二层 ✅
5. **🔥 再暂停主视频** ✅
6. **🔥 最后调整位置并显示副视频** ✅

### 【副视频 → 主视频】流程保持不变
1. **继续播放主视频** ✅
2. **隐藏副视频** ✅
3. **停止副视频播放** ✅
4. **恢复双主视频自动切换** ✅

## 🎬 技术特点

### ✅ 图层管理优先
- **先排序后操作**：图层排序在所有其他操作之前完成
- **无视觉干扰**：图层调整不影响当前播放状态
- **原子操作**：图层排序作为独立的操作步骤

### ✅ OBS操作精确
- **正确的API顺序**：按照OBS最佳实践调用API
- **错误处理完善**：每个OBS操作都有独立的错误处理
- **状态同步**：确保OBS状态与程序状态同步

### ✅ 用户体验优化
- **无黑屏切换**：通过正确的操作顺序避免黑屏
- **流畅过渡**：图层排序确保视觉连续性
- **状态保持**：主视频从暂停位置继续播放

## 🧪 测试建议

现在您可以在主程序中测试修复后的副视频功能：

1. **设置视频源**：配置视频源A（如2222）和B（如2223）
2. **开始播放**：点击播放按钮开始播放
3. **触发副视频**：发送包含关键词"代"的测试弹幕
4. **观察切换效果**：
   - 图层排序应该在暂停主视频之前完成
   - 副视频应该显示在画面上方（顶层）
   - 主视频应该在下面一层（暂停状态）
   - 副视频播放完成后，主视频继续播放

### 📋 预期日志输出

```
🔄 开始副视频切换: 2222 -> 1111
⏸️ 已暂停双主视频自动切换
💾 保存主视频状态: 2222 位置=XXXXXms
▶️ 预加载副视频: 1111 (隐藏状态)
📋 副视频已移到最顶层: 1111
📋 主视频已移到第二层: 2222
🔄 图层排序完成: 1111(顶层), 2222(第二层)
⏸️ 主视频已暂停: 2222
📐 副视频位置已调整到画面上方: 1111
👁️ 副视频源已显示: 1111
✅ 副视频切换完成: 1111
```

## 🎉 总结

副视频功能现在已经完全按照您的最新要求修复：

1. ✅ **图层排序优先**：在暂停主视频之前先调整图层顺序
2. ✅ **OBS操作正确**：使用正确的OBS API调用顺序
3. ✅ **无视觉干扰**：图层排序不影响当前播放状态
4. ✅ **操作原子性**：每个步骤都是独立的原子操作

所有修改都已完成，副视频功能现在应该能够实现您要求的图层排序优先的切换效果！

---

## 🔧 OBS API调用顺序参考

### 图层管理API
- **SetSceneItemIndex**: 设置场景项目的图层索引
- **GetSceneItemIndex**: 获取场景项目的图层索引
- **SetSceneItemEnabled**: 启用/禁用场景项目
- **SetSceneItemTransform**: 设置场景项目的变换属性

### 媒体控制API
- **TriggerMediaInputAction**: 触发媒体输入动作（播放/暂停/停止/重启）
- **SetMediaInputCursor**: 设置媒体输入的播放位置
- **GetMediaInputStatus**: 获取媒体输入的状态信息

### 场景管理API
- **GetCurrentProgramScene**: 获取当前程序场景
- **GetSceneItemList**: 获取场景项目列表
- **GetSceneItemId**: 获取场景项目ID
