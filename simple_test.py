#!/usr/bin/env python3
"""
简化测试公司代码功能
"""

# 测试提取公司代码
def extract_company_code(username: str) -> str:
    try:
        if '-' in username:
            return username.split('-', 1)[0]
        return ""
    except Exception as e:
        print(f"[ERROR] 提取公司代码失败: {e}")
        return ""

# 测试用例
test_usernames = ["jane-1", "abc-user123", "company-test-user", "nocompanycode"]

print("=== 公司代码提取测试 ===")
for username in test_usernames:
    company_code = extract_company_code(username)
    print(f"用户名: '{username}' -> 公司代码: '{company_code}'")

# 测试过滤功能
print("\n=== 过滤功能测试 ===")
speakers_data = [
    {"name": "jane-kaka"},
    {"name": "jane-bukeai"},
    {"name": "xy-speaker1"},
    {"name": "abc-voice1"},
    {"name": "jane-voice2"},
    {"name": "other-speaker"},
]

company_code = "jane"
print(f"当前公司代码: {company_code}")

filtered_speakers = []
for speaker in speakers_data:
    speaker_name = speaker["name"]
    if company_code and company_code in speaker_name:
        if speaker_name.startswith(f"{company_code}-"):
            display_name = speaker_name[len(company_code)+1:]
        else:
            display_name = speaker_name
        
        filtered_speakers.append({
            "original": speaker_name,
            "display": display_name
        })
        print(f"✓ 显示: '{display_name}' (原名: '{speaker_name}')")

print(f"过滤结果: {len(filtered_speakers)} 个符合条件的主播")

# 测试添加前缀功能
print("\n=== 添加前缀功能测试 ===")
def add_company_prefix(name: str, company_code: str) -> str:
    if company_code and not name.startswith(f"{company_code}-"):
        return f"{company_code}-{name}"
    return name

test_names = ["script1", "dialogue1", "jane-existing"]
for name in test_names:
    result = add_company_prefix(name, company_code)
    print(f"输入: '{name}' -> 输出: '{result}'")

print("\n=== 测试完成 ===")
