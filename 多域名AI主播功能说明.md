# 多域名AI主播功能实现说明

## 🎯 功能概述

本功能实现了从多个域名获取AI主播列表，并根据用户的公司代码进行过滤，同时在语音下载时使用对应主播的域名API。

## 🏗️ 架构设计

### 1. 核心组件

- **MultiDomainSpeakerService**: 多域名主播服务
- **SpeakerManager**: 主播管理器（扩展支持多域名）
- **VoiceDownloader**: 语音下载器（支持动态域名）

### 2. 数据流程

```
1. 获取域名列表 → API: http://**************:12456/api/aizhubo
2. 遍历域名获取主播 → 各域名/voice/speakers
3. 根据公司代码过滤 → 只显示相关主播
4. 语音下载时使用对应域名 → 动态API URL
```

## 📋 实现细节

### 1. 域名API响应格式

```json
{
  "1": "http://ct.scjanelife.com/",
  "2": "http://**************/",
  "3": "http://**************/"
}
```

### 2. 主播API响应格式

```json
{
  "BERT-VITS2": [
    {
      "id": 0,
      "name": "jane-bukeai2",
      "description": "主播描述"
    }
  ]
}
```

### 3. 公司代码提取规则

- 从主播名称中提取：`jane-bukeai2` → 公司代码：`jane`
- 从用户名中提取：`jane-user` → 公司代码：`jane`

### 4. 显示格式化

- 原始名称：`jane-bukeai2`
- 显示名称：`bukeai2`
- 隐藏公司代码前缀，不显示域名

## 🔧 核心文件修改

### 1. 新增文件

- `src/services/multi_domain_speaker_service.py`: 多域名主播服务
- `test_multi_domain_speakers.py`: 功能测试程序

### 2. 修改文件

- `src/core/voice/speaker_manager.py`: 扩展支持多域名
- `src/core/voice/voice_downloader.py`: 支持动态域名下载
- `run_gui_qt5.py`: 主界面集成多域名功能

## 🚀 使用方法

### 1. 主播列表刷新

```python
# 在主界面中
def refresh_broadcaster_list(self):
    # 初始化多域名服务
    self.multi_domain_speaker_service = MultiDomainSpeakerService()
    
    # 获取过滤后的主播
    speakers = self.multi_domain_speaker_service.fetch_all_speakers(self.company_code)
    
    # 更新界面显示
    for speaker in speakers:
        display_text = speaker['display_name']  # 只显示主播名称
        self.broadcaster_combo.addItem(display_text)
```

### 2. 语音下载

```python
# 根据主播获取对应域名API
def get_speaker_voice_api_url(self, speaker_id):
    if hasattr(self, 'current_speaker_data'):
        domain_url = self.current_speaker_data.get('domain_url', '')
        if domain_url:
            return f"{domain_url.rstrip('/')}/voice/bert-vits2"
    return "http://**************/voice/bert-vits2"  # 默认域名
```

## 📊 测试结果

### 成功获取的域名：
- ✅ `http://ct.scjanelife.com` - 11个jane主播
- ✅ `http://**************` - 10个主播
- ❌ `http://**************` - 服务不可用(502)

### 公司代码过滤：
- ✅ jane公司：11个主播
- ❌ test公司：0个主播
- ❌ backup公司：0个主播

### 域名URL获取：
- ✅ jane主播 → `http://ct.scjanelife.com`

## 🔍 配置说明

### 1. 域名API配置

默认域名API：`http://**************:12456/api/aizhubo`

可在 `MultiDomainSpeakerService` 初始化时修改：

```python
service = MultiDomainSpeakerService("http://your-api.com/domains")
```

### 2. 公司代码映射

系统自动从主播名称中提取公司代码，格式：`{公司代码}-{主播名}`

### 3. 显示格式

- 主播选择框：`{主播名}`
- 隐藏公司代码前缀
- 不显示域名信息（内部记录）

## 🛠️ 故障排除

### 1. 域名无法访问

- 检查网络连接
- 确认域名API是否正常
- 查看日志中的错误信息

### 2. 主播列表为空

- 确认公司代码是否正确
- 检查主播名称格式是否符合规范
- 验证域名返回的数据格式

### 3. 语音下载失败

- 确认主播对应的域名是否可用
- 检查语音API路径是否正确
- 验证主播ID和参数是否有效

## 📈 性能优化

### 1. 缓存机制

- 域名列表缓存
- 主播数据缓存
- 避免重复请求

### 2. 并发控制

- 限制同时请求的域名数量
- 设置合理的超时时间
- 错误重试机制

### 3. 用户体验

- 异步加载主播列表
- 显示加载进度
- 错误提示友好化

## 🔮 未来扩展

### 1. 域名管理界面

- 可视化域名配置
- 域名状态监控
- 手动添加/删除域名

### 2. 负载均衡

- 智能选择可用域名
- 故障转移机制
- 性能监控

### 3. 配置持久化

- 域名配置保存到数据库
- 用户偏好设置
- 历史记录管理
