"""
AI直播系统 v2 - 时间播报器
定时播报时间和自定义消息
"""

import threading
import time
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class TimeAnnouncer:
    """时间播报器"""
    
    def __init__(self, voice_manager=None):
        self.logger = create_logger("time_announcer")
        self.voice_manager = voice_manager
        
        # 播报配置
        self.config = {
            'enabled': False,
            'interval_minutes': 30,  # 播报间隔（分钟）
            'announce_time': True,   # 是否播报时间
            'announce_custom': True, # 是否播报自定义消息
            'time_format': '%H点%M分',  # 时间格式
            'voice_enabled': True,   # 是否语音播报
            'text_enabled': True     # 是否文本播报
        }
        
        # 播报模板
        self.time_templates = [
            '现在时间是{time}',
            '当前时间{time}',
            '时间提醒：现在是{time}',
            '现在是{time}，感谢大家的观看'
        ]
        
        # 自定义消息
        self.custom_messages = [
            '感谢大家的支持！',
            '欢迎新朋友关注直播间！',
            '有什么问题可以在弹幕里提问哦！',
            '记得点赞关注支持一下！'
        ]
        
        # 特殊时间消息
        self.special_time_messages = {
            '9点0分': '早上好！新的一天开始了！',
            '12点0分': '中午好！该吃午饭了！',
            '18点0分': '晚上好！感谢大家的陪伴！',
            '22点0分': '夜深了，大家注意休息！',
            '0点0分': '午夜时分，还在看直播的朋友辛苦了！'
        }
        
        # 运行状态
        self.is_running = False
        self.announce_thread = None
        self.stop_event = threading.Event()
        
        # 回调函数
        self.on_time_announced: Optional[Callable] = None
        self.on_custom_announced: Optional[Callable] = None
        self.on_announcement_error: Optional[Callable] = None
        
        # 统计信息
        self.stats = {
            'total_announcements': 0,
            'time_announcements': 0,
            'custom_announcements': 0,
            'voice_announcements': 0,
            'text_announcements': 0,
            'last_announcement_time': None,
            'start_time': None
        }
        
    @handle_exceptions("TimeAnnouncer")
    def start(self) -> bool:
        """启动时间播报"""
        try:
            if self.is_running:
                self.logger.warning("时间播报已启动")
                return True
                
            if not self.config['enabled']:
                self.logger.warning("时间播报未启用")
                return False
                
            self.is_running = True
            self.stop_event.clear()
            self.stats['start_time'] = datetime.now()
            
            self.announce_thread = threading.Thread(target=self._announce_loop, daemon=True)
            self.announce_thread.start()
            
            self.logger.info("时间播报已启动")
            return True
            
        except Exception as e:
            self.logger.error(f"启动时间播报失败: {e}")
            return False
            
    def stop(self):
        """停止时间播报"""
        try:
            if not self.is_running:
                return
                
            self.is_running = False
            self.stop_event.set()
            
            if self.announce_thread and self.announce_thread.is_alive():
                self.announce_thread.join(timeout=3.0)
                
            self.logger.info("时间播报已停止")
            
        except Exception as e:
            self.logger.error(f"停止时间播报失败: {e}")
            
    def _announce_loop(self):
        """播报循环"""
        last_announcement = datetime.now()
        
        while not self.stop_event.is_set():
            try:
                current_time = datetime.now()
                
                # 检查是否到了播报时间
                time_diff = current_time - last_announcement
                if time_diff.total_seconds() >= self.config['interval_minutes'] * 60:
                    
                    # 执行播报
                    self._do_announcement(current_time)
                    last_announcement = current_time
                    
                # 等待下次检查（每分钟检查一次）
                self.stop_event.wait(60)
                
            except Exception as e:
                self.logger.error(f"播报循环异常: {e}")
                if self.on_announcement_error:
                    self.on_announcement_error(str(e))
                    
                # 出错后等待一段时间
                self.stop_event.wait(60)
                
    def _do_announcement(self, current_time: datetime):
        """执行播报"""
        try:
            announcements = []
            
            # 时间播报
            if self.config['announce_time']:
                time_announcement = self._generate_time_announcement(current_time)
                if time_announcement:
                    announcements.append({
                        'type': 'time',
                        'content': time_announcement
                    })
                    
            # 自定义消息播报
            if self.config['announce_custom']:
                custom_announcement = self._generate_custom_announcement(current_time)
                if custom_announcement:
                    announcements.append({
                        'type': 'custom',
                        'content': custom_announcement
                    })
                    
            # 执行播报
            for announcement in announcements:
                self._execute_announcement(announcement)
                
        except Exception as e:
            self.logger.error(f"执行播报失败: {e}")
            
    def _generate_time_announcement(self, current_time: datetime) -> Optional[str]:
        """生成时间播报内容"""
        try:
            time_str = current_time.strftime(self.config['time_format'])
            
            # 检查是否有特殊时间消息
            if time_str in self.special_time_messages:
                return self.special_time_messages[time_str]
                
            # 使用普通时间模板
            import random
            template = random.choice(self.time_templates)
            return template.format(time=time_str)
            
        except Exception as e:
            self.logger.error(f"生成时间播报失败: {e}")
            return None
            
    def _generate_custom_announcement(self, current_time: datetime) -> Optional[str]:
        """生成自定义播报内容"""
        try:
            if not self.custom_messages:
                return None
                
            import random
            return random.choice(self.custom_messages)
            
        except Exception as e:
            self.logger.error(f"生成自定义播报失败: {e}")
            return None
            
    def _execute_announcement(self, announcement: Dict[str, Any]):
        """执行播报"""
        try:
            content = announcement['content']
            announcement_type = announcement['type']
            
            self.stats['total_announcements'] += 1
            self.stats['last_announcement_time'] = datetime.now()
            
            if announcement_type == 'time':
                self.stats['time_announcements'] += 1
            else:
                self.stats['custom_announcements'] += 1
                
            # 文本播报
            if self.config['text_enabled']:
                self.stats['text_announcements'] += 1
                self.logger.info(f"播报: {content}")
                
            # 语音播报
            if self.config['voice_enabled'] and self.voice_manager:
                try:
                    voice_file = self.voice_manager.generate_voice(content)
                    if voice_file:
                        self.stats['voice_announcements'] += 1
                        # 这里可以播放语音文件
                        
                except Exception as e:
                    self.logger.error(f"语音播报失败: {e}")
                    
            # 触发回调
            if announcement_type == 'time' and self.on_time_announced:
                self.on_time_announced(content)
            elif announcement_type == 'custom' and self.on_custom_announced:
                self.on_custom_announced(content)
                
        except Exception as e:
            self.logger.error(f"执行播报失败: {e}")
            
    @handle_exceptions("TimeAnnouncer")
    def announce_now(self, message: Optional[str] = None) -> bool:
        """立即播报"""
        try:
            if message:
                # 播报自定义消息
                announcement = {
                    'type': 'manual',
                    'content': message
                }
            else:
                # 播报当前时间
                current_time = datetime.now()
                time_content = self._generate_time_announcement(current_time)
                if not time_content:
                    return False
                    
                announcement = {
                    'type': 'time',
                    'content': time_content
                }
                
            self._execute_announcement(announcement)
            return True
            
        except Exception as e:
            self.logger.error(f"立即播报失败: {e}")
            return False
            
    @handle_exceptions("TimeAnnouncer")
    def add_custom_message(self, message: str):
        """添加自定义消息"""
        if message not in self.custom_messages:
            self.custom_messages.append(message)
            self.logger.info(f"添加自定义消息: {message}")
            
    @handle_exceptions("TimeAnnouncer")
    def remove_custom_message(self, message: str):
        """移除自定义消息"""
        if message in self.custom_messages:
            self.custom_messages.remove(message)
            self.logger.info(f"移除自定义消息: {message}")
            
    @handle_exceptions("TimeAnnouncer")
    def add_time_template(self, template: str):
        """添加时间模板"""
        if template not in self.time_templates:
            self.time_templates.append(template)
            self.logger.info(f"添加时间模板: {template}")
            
    @handle_exceptions("TimeAnnouncer")
    def remove_time_template(self, template: str):
        """移除时间模板"""
        if template in self.time_templates:
            self.time_templates.remove(template)
            self.logger.info(f"移除时间模板: {template}")
            
    @handle_exceptions("TimeAnnouncer")
    def set_special_time_message(self, time_str: str, message: str):
        """设置特殊时间消息"""
        self.special_time_messages[time_str] = message
        self.logger.info(f"设置特殊时间消息: {time_str} -> {message}")
        
    @handle_exceptions("TimeAnnouncer")
    def remove_special_time_message(self, time_str: str):
        """移除特殊时间消息"""
        if time_str in self.special_time_messages:
            del self.special_time_messages[time_str]
            self.logger.info(f"移除特殊时间消息: {time_str}")
            
    @handle_exceptions("TimeAnnouncer")
    def update_config(self, config: Dict[str, Any]):
        """更新配置"""
        old_enabled = self.config['enabled']
        self.config.update(config)
        
        # 如果启用状态改变，重启播报
        if old_enabled != self.config['enabled']:
            if self.is_running:
                self.stop()
            if self.config['enabled']:
                self.start()
                
        self.logger.info(f"播报配置已更新: {list(config.keys())}")
        
    @handle_exceptions("TimeAnnouncer")
    def get_config(self) -> Dict[str, Any]:
        """获取配置"""
        return self.config.copy()
        
    @handle_exceptions("TimeAnnouncer")
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        # 计算运行时间
        if stats['start_time']:
            runtime = datetime.now() - stats['start_time']
            stats['runtime_seconds'] = runtime.total_seconds()
            stats['runtime_formatted'] = str(runtime).split('.')[0]
        else:
            stats['runtime_seconds'] = 0
            stats['runtime_formatted'] = "00:00:00"
            
        stats['is_running'] = self.is_running
        stats['next_announcement_in'] = self._get_next_announcement_time()
        stats['custom_messages_count'] = len(self.custom_messages)
        stats['time_templates_count'] = len(self.time_templates)
        stats['special_messages_count'] = len(self.special_time_messages)
        
        return stats
        
    def _get_next_announcement_time(self) -> int:
        """获取下次播报时间（秒）"""
        if not self.is_running or not self.stats['last_announcement_time']:
            return 0
            
        last_time = self.stats['last_announcement_time']
        next_time = last_time + timedelta(minutes=self.config['interval_minutes'])
        now = datetime.now()
        
        if next_time > now:
            return int((next_time - now).total_seconds())
        else:
            return 0
            
    @handle_exceptions("TimeAnnouncer")
    def get_custom_messages(self) -> List[str]:
        """获取自定义消息列表"""
        return self.custom_messages.copy()
        
    @handle_exceptions("TimeAnnouncer")
    def get_time_templates(self) -> List[str]:
        """获取时间模板列表"""
        return self.time_templates.copy()
        
    @handle_exceptions("TimeAnnouncer")
    def get_special_time_messages(self) -> Dict[str, str]:
        """获取特殊时间消息"""
        return self.special_time_messages.copy()
        
    @handle_exceptions("TimeAnnouncer")
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_announcements': 0,
            'time_announcements': 0,
            'custom_announcements': 0,
            'voice_announcements': 0,
            'text_announcements': 0,
            'last_announcement_time': None,
            'start_time': datetime.now() if self.is_running else None
        }
        self.logger.info("播报统计已重置")
