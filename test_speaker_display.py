#!/usr/bin/env python3
"""
测试主播显示格式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.services.multi_domain_speaker_service import MultiDomainSpeakerService


def test_speaker_display_format():
    """测试主播显示格式"""
    print("🎭 测试主播显示格式")
    print("-" * 50)
    
    service = MultiDomainSpeakerService()
    
    try:
        # 获取jane公司的主播
        company_code = "jane"
        speakers = service.fetch_all_speakers(company_code)
        
        if speakers:
            print(f"✅ 获取到 {len(speakers)} 个主播 (公司代码: {company_code})")
            print()
            print("主播显示格式测试:")
            print("=" * 40)
            
            for i, speaker in enumerate(speakers[:10], 1):  # 显示前10个
                original_name = speaker.get('original_name', speaker['name'])
                display_name = speaker.get('display_name', speaker['name'])
                domain_name = speaker.get('domain_name', 'unknown')
                domain_url = speaker.get('domain_url', '')
                
                print(f"{i:2d}. 原始名称: {original_name}")
                print(f"    显示名称: {display_name}")
                print(f"    域名信息: {domain_name} ({domain_url})")
                print(f"    界面显示: {display_name}")  # 这是用户在界面上看到的
                print()
            
            if len(speakers) > 10:
                print(f"... 还有 {len(speakers) - 10} 个主播")
                
        else:
            print("❌ 未获取到主播")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    
    finally:
        service.close()


def test_company_code_filtering():
    """测试公司代码过滤效果"""
    print("\n🏢 测试公司代码过滤效果")
    print("-" * 50)
    
    service = MultiDomainSpeakerService()
    
    try:
        # 测试不同公司代码
        test_codes = ["jane", "test", "backup"]
        
        for company_code in test_codes:
            print(f"📋 公司代码: {company_code}")
            speakers = service.fetch_all_speakers(company_code)
            
            if speakers:
                print(f"   ✅ 找到 {len(speakers)} 个主播:")
                for speaker in speakers[:3]:  # 显示前3个
                    display_name = speaker.get('display_name', speaker['name'])
                    print(f"      - {display_name}")
                if len(speakers) > 3:
                    print(f"      ... 还有 {len(speakers) - 3} 个")
            else:
                print(f"   ❌ 未找到主播")
            print()
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    
    finally:
        service.close()


def simulate_ui_display():
    """模拟界面显示效果"""
    print("\n🖥️ 模拟界面显示效果")
    print("-" * 50)
    
    service = MultiDomainSpeakerService()
    
    try:
        # 模拟用户jane登录
        company_code = "jane"
        speakers = service.fetch_all_speakers(company_code)
        
        if speakers:
            print("模拟主播下拉框内容:")
            print("┌" + "─" * 30 + "┐")
            
            for i, speaker in enumerate(speakers[:8], 1):  # 显示前8个
                display_name = speaker.get('display_name', speaker['name'])
                print(f"│ {i:2d}. {display_name:<25} │")
            
            if len(speakers) > 8:
                print(f"│ ... 还有 {len(speakers) - 8} 个主播{' ' * 12} │")
                
            print("└" + "─" * 30 + "┘")
            print()
            print("✅ 界面显示简洁，只显示主播名称")
            print("✅ 隐藏了公司代码前缀")
            print("✅ 不显示域名信息，避免界面混乱")
            
        else:
            print("❌ 未获取到主播")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    
    finally:
        service.close()


def main():
    """主测试函数"""
    print("🚀 主播显示格式测试")
    print("=" * 60)
    
    # 测试1: 主播显示格式
    test_speaker_display_format()
    
    # 测试2: 公司代码过滤
    test_company_code_filtering()
    
    # 测试3: 模拟界面显示
    simulate_ui_display()
    
    print("\n✅ 显示格式测试完成!")
    print("\n📝 总结:")
    print("   - 主播列表只显示主播名称")
    print("   - 隐藏公司代码前缀")
    print("   - 不显示域名信息")
    print("   - 界面简洁清晰")


if __name__ == "__main__":
    main()
