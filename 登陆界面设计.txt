# 🔍 AI主播系统现有界面和功能详细描述

## 📋 系统概述

### 🎯 系统架构
```
AI主播系统
├── 程序入口 (main.py)
├── 登录系统 (login_ui.py)
├── 主界面系统 (ui_design.py)
├── OBS媒体API (obs_media_api.py)
├── WebSocket客户端 (websocket_client.py)
├── 配置管理 (config.py)
├── 用户管理 (user_manager.py)
├── 更新管理 (update_manager.py)
```

### 🚀 程序启动流程
1. **main.py**  → 显示登录窗口
2. **登录成功** → 检查更新 → 创建主窗口
3. **主窗口初始化** → 系统就绪

## 🎨 界面详细描述

### 1. 登录界面 (LoginWindow)

#### 界面布局
```
┌─────────────────────────────────────────────────────────┐
│                    AI主播系统                           │
│                   智能语音助手                          │
│━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━│
│ ┌─登录─┐ ┌─注册─┐ ┌─充值─┐                            │
│ │      │ │      │ │      │                            │
│ │用户名│ │用户名│ │用户名│                            │
│ │密码  │ │密码  │ │密码  │                            │
│ │☑记住 │ │确认  │ │金额  │                            │
│ │[登录]│ │[注册]│ │[充值]│                            │
│ └──────┘ └──────┘ └──────┘                            │
│━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━│
│           © 2025 AI主播系统 版权所有 | 版本 1.0.0      │
└─────────────────────────────────────────────────────────┘
```

#### 界面元素详细
- **窗口属性**：550x680，不可调整大小
- **标签页控件**：3个标签页（登录、注册、充值）
- **登录标签页**：
  - 用户名输入框：支持自动填充
  - 密码输入框：支持显示/隐藏密码
  - 记住密码复选框：自动保存到配置文件
  - 登录按钮：验证用户名密码和机器码
- **注册标签页**：
  - 用户名、密码、确认密码、手机号输入框
  - 注册按钮：提交注册信息到服务器
- **充值标签页**：
  - 用户名、密码输入框（自动填充）
  - 充值金额选择
  - 充值按钮：处理充值请求


## 🔧 核心功能详细描述

### 1. 用户认证系统

#### 登录功能
- **输入验证**：用户名、密码格式检查
- **机器码验证**：获取硬件信息生成唯一机器码
- **服务器验证**：发送登录请求到服务器验证
- **自动登录**：记住密码功能，下次自动填充

#### 注册功能
- **用户信息收集**：用户名、密码、手机号
- **数据验证**：格式检查、重复检查
- **服务器注册**：提交注册信息到服务器
- **注册结果处理**：成功提示或错误处理

#### 充值功能
- **账户验证**：验证用户名密码
- **金额选择**：预设金额或自定义金额
- **支付处理**：调用支付接口
- **余额更新**：更新用户账户余额

#### 配置文件管理
- **配置加载**：
  - 从config.json加载配置
  - 从_internal/config.json加载内部配置
  - 配置合并和优先级处理
- **配置保存**：
  - 保存用户设置
  - 保存系统配置
  - 配置文件备份


### 7. 更新管理系统

#### 更新检查
- **版本比较**：
  - 获取服务器最新版本
  - 比较本地版本号
  - 显示更新信息
- **更新下载**：
  - 下载更新包
  - 显示下载进度
  - 验证文件完整性
- **更新安装**：
  - 备份当前版本
  - 安装更新文件
  - 重启应用程序

### 9. 日志系统

#### 日志记录
- **多级别日志**：
  - INFO：一般信息
  - WARNING：警告信息
  - ERROR：错误信息
  - DEBUG：调试信息
- **日志分类**：
  - 系统日志：系统运行状态
  - 操作日志：用户操作记录
  - 错误日志：错误和异常记录

#### 日志管理
- **日志查看**：
  - 实时日志显示
  - 日志级别过滤
  - 日志搜索功能
- **日志维护**：
  - 日志文件轮转
  - 日志清理
  - 日志导出

## 📊 数据结构

### 配置文件结构
```json
{
  "client_version": "1.0.0",
  "username": "用户名",
  "password": "密码",
  "obs_settings": {
    "host": "localhost",
    "port": 4455,
    "password": ""
  },
}
```

### 3. 配置管理系统 (config.py)

#### 配置文件结构
```python

# 内部配置文件：_internal/config.json
{
    "client_version": "1.0.0",
    "username": "用户名",
    "password": "密码",
    "obs_settings": {
        "host": "localhost",
        "port": 4455,
        "password": ""
    }
}
```


### 4. 用户管理系统 (user_manager.py)

#### 用户认证流程
```python
def authenticate_user(username, password, machine_code):
    # 1. 参数验证
    # 2. 发送登录请求到服务器
    # 3. 验证机器码
    # 4. 获取用户权限信息
    # 5. 返回认证结果
```


### 5. 更新管理系统 (update_manager.py)

#### 更新检查机制
- **版本比较**：比较本地版本和服务器版本
- **更新通知**：发现新版本时通知用户
- **强制更新**：支持强制更新机制

#### 更新执行流程
```python
def execute_update():
    # 1. 下载更新包
    # 2. 验证文件完整性
    # 3. 备份当前版本
    # 4. 停止相关服务
    # 5. 解压更新文件
    # 6. 替换程序文件
    # 7. 重启应用程序
```