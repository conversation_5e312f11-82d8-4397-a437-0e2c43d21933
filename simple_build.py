#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI主播系统简化打包脚本
"""

import os
import sys
import subprocess

def main():
    """简化的打包流程"""
    print("🚀 AI主播系统简化打包工具")
    print("=" * 40)
    
    # 检查主程序文件
    if not os.path.exists("run_gui_qt5.py"):
        print("❌ 未找到主程序文件 run_gui_qt5.py")
        return False
    
    # 安装PyInstaller
    print("🔄 安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller安装成功")
    except:
        print("⚠️ PyInstaller可能已安装")
    
    # 构建命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",  # 打包成单个文件
        "--windowed",  # 不显示控制台
        "--name=AI主播系统",  # 程序名称
        "--add-data=src;src",  # 添加src目录
        "run_gui_qt5.py"  # 主程序文件
    ]
    
    # 如果图标文件存在，添加图标
    if os.path.exists("简生活图标.ico"):
        cmd.insert(-1, "--icon=简生活图标.ico")
        cmd.insert(-1, "--add-data=简生活图标.ico;.")
    
    print(f"🔄 执行打包命令...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ 打包成功!")
        print("📁 可执行文件: dist/AI主播系统.exe")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 打包完成! 可执行文件在 dist 目录中")
    else:
        print("\n❌ 打包失败")
    input("按回车键退出...")
