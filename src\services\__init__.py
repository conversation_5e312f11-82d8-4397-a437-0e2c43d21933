"""
AI Broadcaster v2 - 服务层
提供基础服务功能
"""

from .logging_service import setup_logging, get_logger, create_logger
from .error_handler import ErrorHandler

# 网络服务延迟导入，避免启动时的警告
NETWORK_SERVICE_AVAILABLE = False
NetworkService = None

def get_network_service():
    """延迟导入网络服务"""
    global NetworkService, NETWORK_SERVICE_AVAILABLE
    if NetworkService is None:
        try:
            from .network_service import NetworkService
            NETWORK_SERVICE_AVAILABLE = True
        except ImportError:
            NetworkService = None
            NETWORK_SERVICE_AVAILABLE = False
    return NetworkService

__all__ = [
    'setup_logging',
    'get_logger',
    'create_logger',
    'ErrorHandler',
    'get_network_service'
]

# 延迟导入其他服务
def get_voice_service():
    """延迟导入语音服务"""
    try:
        from .voice_service import VoiceService
        return VoiceService
    except ImportError:
        return None

def get_script_service():
    """延迟导入话术服务"""
    try:
        from .script_service import ScriptService
        return ScriptService
    except ImportError:
        return None

def get_dialogue_service():
    """延迟导入对话服务"""
    try:
        from .dialogue_service import DialogueService
        return DialogueService
    except ImportError:
        return None

def get_danmaku_service():
    """延迟导入弹幕服务"""
    try:
        from .danmaku_service import DanmakuService
        return DanmakuService
    except ImportError:
        return None

def get_obs_service():
    """延迟导入OBS服务"""
    try:
        from .obs_service import OBSService
        return OBSService
    except ImportError:
        return None

def get_api_manager():
    """延迟导入API管理器"""
    try:
        from .api_manager import APIManager
        return APIManager
    except ImportError:
        return None

# 添加到导出列表
__all__.extend([
    'get_voice_service',
    'get_script_service',
    'get_dialogue_service',
    'get_danmaku_service',
    'get_obs_service',
    'get_api_manager'
])
