"""
AI Broadcaster v2 - 登录窗口
用户登录和注册界面
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QTabWidget, QMessageBox,
    QCheckBox, QFrame, QSpacerItem, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QPixmap, QIcon

from ..services.logging_service import create_logger
from ..data.database_manager import DatabaseManager
from ..core.auth.user_manager import UserManager


class LoginWorker(QThread):
    """登录工作线程"""
    login_result = pyqtSignal(bool, str, object)  # 成功, 消息, 用户信息

    def __init__(self, user_manager, username, password):
        super().__init__()
        self.user_manager = user_manager
        self.username = username
        self.password = password

    def run(self):
        try:
            success, msg, user_info = self.user_manager.login_user(self.username, self.password)
            self.login_result.emit(success, msg, user_info)
        except Exception as e:
            self.login_result.emit(False, f"登录异常: {str(e)}", None)


class RegisterWorker(QThread):
    """注册工作线程"""
    register_result = pyqtSignal(bool, str)  # 成功, 消息

    def __init__(self, user_manager, username, password, phone):
        super().__init__()
        self.user_manager = user_manager
        self.username = username
        self.password = password
        self.phone = phone

    def run(self):
        try:
            success, msg = self.user_manager.register_user(self.username, self.password, self.phone)
            self.register_result.emit(success, msg)
        except Exception as e:
            self.register_result.emit(False, f"注册异常: {str(e)}")


class LoginWindow(QMainWindow):
    """登录窗口"""

    # 信号定义
    login_success = pyqtSignal(object)  # 登录成功信号，传递用户信息

    def __init__(self):
        super().__init__()
        self.logger = create_logger("login_window")

        # 初始化数据库和用户管理器
        self.db_manager = DatabaseManager()
        self.user_manager = UserManager(self.db_manager)

        # 工作线程
        self.login_worker = None
        self.register_worker = None

        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("AI Broadcaster v2 - 登录")
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)

        # 设置窗口居中
        self.center_window()

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)

        # 标题
        self.create_title_section(main_layout)

        # 选项卡
        self.create_tab_widget(main_layout)

        # 底部信息
        self.create_footer_section(main_layout)

    def center_window(self):
        """窗口居中"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def create_title_section(self, layout):
        """创建标题部分"""
        title_layout = QVBoxLayout()

        # 应用图标（如果有的话）
        # icon_label = QLabel()
        # icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # title_layout.addWidget(icon_label)

        # 应用标题
        title_label = QLabel("AI Broadcaster v2")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(24)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2c3e50; margin: 10px 0;")
        title_layout.addWidget(title_label)

        # 副标题
        subtitle_label = QLabel("智能AI广播系统")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_font = QFont()
        subtitle_font.setPointSize(12)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setStyleSheet("color: #7f8c8d; margin-bottom: 20px;")
        title_layout.addWidget(subtitle_label)

        layout.addLayout(title_layout)

    def create_tab_widget(self, layout):
        """创建选项卡部件"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #3498db;
            }
        """)

        # 登录选项卡
        self.create_login_tab()

        # 注册选项卡
        self.create_register_tab()

        layout.addWidget(self.tab_widget)

    def create_login_tab(self):
        """创建登录选项卡"""
        login_widget = QWidget()
        login_layout = QVBoxLayout(login_widget)
        login_layout.setSpacing(15)
        login_layout.setContentsMargins(20, 20, 20, 20)

        # 用户名输入
        self.login_username = QLineEdit()
        self.login_username.setPlaceholderText("请输入用户名")
        self.login_username.setStyleSheet(self.get_input_style())
        login_layout.addWidget(QLabel("用户名:"))
        login_layout.addWidget(self.login_username)

        # 密码输入
        self.login_password = QLineEdit()
        self.login_password.setPlaceholderText("请输入密码")
        self.login_password.setEchoMode(QLineEdit.Password)
        self.login_password.setStyleSheet(self.get_input_style())
        login_layout.addWidget(QLabel("密码:"))
        login_layout.addWidget(self.login_password)

        # 记住密码
        self.remember_password = QCheckBox("记住密码")
        login_layout.addWidget(self.remember_password)

        # 登录按钮
        self.login_button = QPushButton("登录")
        self.login_button.setStyleSheet(self.get_button_style("#3498db"))
        self.login_button.setMinimumHeight(40)
        login_layout.addWidget(self.login_button)

        # 添加弹性空间
        login_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        self.tab_widget.addTab(login_widget, "登录")

    def create_register_tab(self):
        """创建注册选项卡"""
        register_widget = QWidget()
        register_layout = QVBoxLayout(register_widget)
        register_layout.setSpacing(15)
        register_layout.setContentsMargins(20, 20, 20, 20)

        # 用户名输入
        self.register_username = QLineEdit()
        self.register_username.setPlaceholderText("请输入用户名")
        self.register_username.setStyleSheet(self.get_input_style())
        register_layout.addWidget(QLabel("用户名:"))
        register_layout.addWidget(self.register_username)

        # 密码输入
        self.register_password = QLineEdit()
        self.register_password.setPlaceholderText("请输入密码")
        self.register_password.setEchoMode(QLineEdit.Password)
        self.register_password.setStyleSheet(self.get_input_style())
        register_layout.addWidget(QLabel("密码:"))
        register_layout.addWidget(self.register_password)

        # 确认密码
        self.register_confirm_password = QLineEdit()
        self.register_confirm_password.setPlaceholderText("请再次输入密码")
        self.register_confirm_password.setEchoMode(QLineEdit.Password)
        self.register_confirm_password.setStyleSheet(self.get_input_style())
        register_layout.addWidget(QLabel("确认密码:"))
        register_layout.addWidget(self.register_confirm_password)

        # 手机号输入
        self.register_phone = QLineEdit()
        self.register_phone.setPlaceholderText("请输入手机号（可选）")
        self.register_phone.setStyleSheet(self.get_input_style())
        register_layout.addWidget(QLabel("手机号:"))
        register_layout.addWidget(self.register_phone)

        # 注册按钮
        self.register_button = QPushButton("注册")
        self.register_button.setStyleSheet(self.get_button_style("#27ae60"))
        self.register_button.setMinimumHeight(40)
        register_layout.addWidget(self.register_button)

        # 添加弹性空间
        register_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        self.tab_widget.addTab(register_widget, "注册")

    def create_footer_section(self, layout):
        """创建底部信息"""
        footer_layout = QVBoxLayout()

        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("color: #bdc3c7;")
        footer_layout.addWidget(line)

        # 版本信息
        version_label = QLabel("版本: v2.0.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("color: #95a5a6; font-size: 10px; margin: 5px 0;")
        footer_layout.addWidget(version_label)

        layout.addLayout(footer_layout)

    def get_input_style(self):
        """获取输入框样式"""
        return """
            QLineEdit {
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """

    def get_button_style(self, color):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
        """

    def darken_color(self, color, factor=0.9):
        """使颜色变暗"""
        # 简单的颜色变暗处理
        color_map = {
            "#3498db": "#2980b9",
            "#27ae60": "#229954"
        }
        return color_map.get(color, color)

    def setup_connections(self):
        """设置信号连接"""
        # 按钮点击事件
        self.login_button.clicked.connect(self.on_login_clicked)
        self.register_button.clicked.connect(self.on_register_clicked)

        # 回车键登录
        self.login_password.returnPressed.connect(self.on_login_clicked)
        self.register_confirm_password.returnPressed.connect(self.on_register_clicked)

    @pyqtSlot()
    def on_login_clicked(self):
        """登录按钮点击事件"""
        username = self.login_username.text().strip()
        password = self.login_password.text()

        if not username or not password:
            QMessageBox.warning(self, "提示", "请输入用户名和密码")
            return

        # 禁用登录按钮
        self.login_button.setEnabled(False)
        self.login_button.setText("登录中...")

        # 启动登录线程
        self.login_worker = LoginWorker(self.user_manager, username, password)
        self.login_worker.login_result.connect(self.on_login_result)
        self.login_worker.start()

    @pyqtSlot()
    def on_register_clicked(self):
        """注册按钮点击事件"""
        username = self.register_username.text().strip()
        password = self.register_password.text()
        confirm_password = self.register_confirm_password.text()
        phone = self.register_phone.text().strip()

        # 验证输入
        if not username or not password:
            QMessageBox.warning(self, "提示", "请输入用户名和密码")
            return

        if password != confirm_password:
            QMessageBox.warning(self, "提示", "两次输入的密码不一致")
            return

        if len(password) < 6:
            QMessageBox.warning(self, "提示", "密码长度至少6位")
            return

        # 禁用注册按钮
        self.register_button.setEnabled(False)
        self.register_button.setText("注册中...")

        # 启动注册线程
        self.register_worker = RegisterWorker(self.user_manager, username, password, phone)
        self.register_worker.register_result.connect(self.on_register_result)
        self.register_worker.start()

    @pyqtSlot(bool, str, object)
    def on_login_result(self, success, message, user_info):
        """登录结果处理"""
        # 恢复登录按钮
        self.login_button.setEnabled(True)
        self.login_button.setText("登录")

        if success:
            self.logger.info(f"用户登录成功: {user_info['username']}")
            QMessageBox.information(self, "成功", message)

            # 发送登录成功信号
            self.login_success.emit(user_info)

            # 关闭登录窗口
            self.close()
        else:
            self.logger.warning(f"用户登录失败: {message}")
            QMessageBox.warning(self, "登录失败", message)

    @pyqtSlot(bool, str)
    def on_register_result(self, success, message):
        """注册结果处理"""
        # 恢复注册按钮
        self.register_button.setEnabled(True)
        self.register_button.setText("注册")

        if success:
            self.logger.info("用户注册成功")
            QMessageBox.information(self, "成功", message + "\n请切换到登录选项卡进行登录")

            # 切换到登录选项卡
            self.tab_widget.setCurrentIndex(0)

            # 清空注册表单
            self.register_username.clear()
            self.register_password.clear()
            self.register_confirm_password.clear()
            self.register_phone.clear()
        else:
            self.logger.warning(f"用户注册失败: {message}")
            QMessageBox.warning(self, "注册失败", message)

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 清理资源
        if self.login_worker and self.login_worker.isRunning():
            self.login_worker.terminate()
            self.login_worker.wait()

        if self.register_worker and self.register_worker.isRunning():
            self.register_worker.terminate()
            self.register_worker.wait()

        if self.db_manager:
            self.db_manager.close()

        event.accept()
