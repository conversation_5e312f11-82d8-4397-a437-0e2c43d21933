#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单音频播放器 - 重新设计
专注于连续播放，去除复杂逻辑
"""

import os
import time
import threading
from pathlib import Path
from PyQt5.QtCore import QTimer, QObject, pyqtSignal


class SimpleAudioPlayer(QObject):
    """简单音频播放器 - 重新设计"""
    
    # 信号
    playback_finished = pyqtSignal(str)  # 播放完成信号
    
    def __init__(self):
        super().__init__()
        self.is_playing = False
        self.current_file = None
        self.player_type = "pygame"  # 默认播放器
        self.stop_requested = False
        
    def play(self, file_path):
        """播放音频文件"""
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return False
            
        if self.is_playing:
            print(f"⚠️ 正在播放其他音频，跳过: {Path(file_path).name}")
            return False
            
        self.current_file = file_path
        self.is_playing = True
        self.stop_requested = False
        
        print(f"🎵 开始播放: {Path(file_path).name}")
        
        # 在新线程中播放
        play_thread = threading.Thread(target=self._play_worker, args=(file_path,))
        play_thread.daemon = True
        play_thread.start()
        
        return True
        
    def _play_worker(self, file_path):
        """播放工作线程 - 简化版本"""
        try:
            # 根据播放器类型播放
            if self.player_type == "pygame":
                self._play_pygame(file_path)
            elif self.player_type == "winsound":
                self._play_winsound(file_path)
            elif self.player_type == "vlc":
                self._play_vlc(file_path)
            elif self.player_type == "system":
                self._play_system(file_path)
            else:
                self._play_pygame(file_path)  # 默认
                
        except Exception as e:
            print(f"❌ 播放失败: {e}")
        finally:
            # 播放完成处理
            self._on_playback_finished(file_path)
            
    def _play_pygame(self, file_path):
        """pygame播放"""
        try:
            import pygame
            
            # 初始化
            if not pygame.mixer.get_init():
                pygame.mixer.init()
                
            # 停止之前的播放
            pygame.mixer.music.stop()
            
            # 加载并播放
            pygame.mixer.music.load(file_path)
            pygame.mixer.music.play()
            
            # 等待播放完成
            while pygame.mixer.music.get_busy() and not self.stop_requested:
                time.sleep(0.1)
                
        except ImportError:
            print("❌ pygame不可用，使用模拟播放")
            self._simulate_play(file_path)
        except Exception as e:
            print(f"❌ pygame播放失败: {e}")
            self._simulate_play(file_path)
            
    def _play_winsound(self, file_path):
        """winsound播放"""
        try:
            import winsound
            winsound.PlaySound(file_path, winsound.SND_FILENAME)
        except Exception as e:
            print(f"❌ winsound播放失败: {e}")
            self._simulate_play(file_path)
            
    def _play_vlc(self, file_path):
        """VLC播放"""
        try:
            import subprocess
            
            vlc_paths = [
                'vlc',
                'C:\\Program Files\\VideoLAN\\VLC\\vlc.exe',
                'C:\\Program Files (x86)\\VideoLAN\\VLC\\vlc.exe'
            ]
            
            for vlc_path in vlc_paths:
                try:
                    subprocess.run([vlc_path, '--intf', 'dummy', '--play-and-exit', file_path], 
                                 check=True, timeout=30)
                    return
                except (FileNotFoundError, subprocess.TimeoutExpired):
                    continue
                    
            # VLC未找到，回退
            print("⚠️ VLC未找到，回退到pygame")
            self._play_pygame(file_path)
            
        except Exception as e:
            print(f"❌ VLC播放失败: {e}")
            self._simulate_play(file_path)
            
    def _play_system(self, file_path):
        """系统播放器"""
        try:
            import subprocess
            import platform
            
            system = platform.system()
            if system == "Windows":
                subprocess.Popen(['start', '', file_path], shell=True)
            elif system == "Darwin":  # macOS
                subprocess.Popen(['open', file_path])
            else:  # Linux
                subprocess.Popen(['xdg-open', file_path])
                
            # 估算播放时长并等待
            duration = self._get_audio_duration(file_path)
            start_time = time.time()
            
            while time.time() - start_time < duration and not self.stop_requested:
                time.sleep(0.1)
                
        except Exception as e:
            print(f"❌ 系统播放器失败: {e}")
            self._simulate_play(file_path)
            
    def _simulate_play(self, file_path):
        """模拟播放"""
        duration = self._get_audio_duration(file_path)
        print(f"🎵 模拟播放: {Path(file_path).name} ({duration:.1f}秒)")
        
        start_time = time.time()
        while time.time() - start_time < duration and not self.stop_requested:
            time.sleep(0.1)
            
    def _get_audio_duration(self, file_path):
        """获取音频时长"""
        try:
            import wave
            with wave.open(file_path, 'rb') as wav_file:
                frames = wav_file.getnframes()
                sample_rate = wav_file.getframerate()
                duration = frames / float(sample_rate)
                return duration
        except:
            # 根据文件大小估算
            file_size = Path(file_path).stat().st_size
            return max(2, min(10, file_size / 16000))  # 2-10秒
            
    def _on_playback_finished(self, file_path):
        """播放完成处理"""
        self.is_playing = False
        self.current_file = None
        print(f"✅ 播放完成: {Path(file_path).name}")
        
        # 发送信号
        self.playback_finished.emit(file_path)
        
    def stop(self):
        """停止播放"""
        if self.is_playing:
            print(f"🛑 停止播放: {Path(self.current_file).name if self.current_file else '未知'}")
            self.stop_requested = True
            self.is_playing = False
            
            try:
                import pygame
                pygame.mixer.music.stop()
            except:
                pass
                
    def set_player_type(self, player_type):
        """设置播放器类型"""
        self.player_type = player_type
        print(f"🔧 切换播放器: {player_type}")


class SimplePlaylistManager(QObject):
    """简单播放列表管理器"""
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.player = SimpleAudioPlayer()
        self.playlist = []
        self.current_index = 0
        self.is_playing = False
        self.play_timer = QTimer()
        self.interval_timer = QTimer()
        
        # 连接信号
        self.player.playback_finished.connect(self.on_audio_finished)
        self.play_timer.timeout.connect(self.check_and_play_next)
        self.interval_timer.timeout.connect(self.resume_playing)
        
        # 设置定时器
        self.play_timer.setSingleShot(False)
        self.interval_timer.setSingleShot(True)
        
    def start_playing(self):
        """开始播放"""
        if self.is_playing:
            print("⚠️ 已在播放中")
            return
            
        self.is_playing = True
        self.current_index = 0
        print("🎬 开始播放列表")
        
        # 立即播放第一个
        self.play_next()
        
        # 启动检查定时器
        self.play_timer.start(1000)  # 每秒检查一次
        
    def stop_playing(self):
        """停止播放"""
        self.is_playing = False
        self.play_timer.stop()
        self.interval_timer.stop()
        self.player.stop()
        print("🛑 停止播放列表")
        
    def update_playlist(self):
        """更新播放列表"""
        try:
            # 从主窗口获取播放列表
            if hasattr(self.main_window, 'playlist_items'):
                # 只获取已下载的项目
                self.playlist = [
                    item for item in self.main_window.playlist_items
                    if item.get('status') == '已下载' and 
                       item.get('filename') and 
                       os.path.exists(item.get('filename', ''))
                ]
                print(f"📋 更新播放列表: {len(self.playlist)} 个可播放项目")
            else:
                self.playlist = []
                
        except Exception as e:
            print(f"❌ 更新播放列表失败: {e}")
            self.playlist = []
            
    def play_next(self):
        """播放下一个"""
        if not self.is_playing:
            return
            
        # 更新播放列表
        self.update_playlist()
        
        if not self.playlist:
            print("📋 播放列表为空")
            return
            
        # 获取下一个要播放的项目
        next_item = self.get_next_item()
        if not next_item:
            print("📋 没有更多项目可播放")
            return
            
        # 播放
        file_path = next_item.get('filename')
        if file_path and os.path.exists(file_path):
            success = self.player.play(file_path)
            if success:
                # 更新状态
                next_item['status'] = '播放中'
                self.update_main_window_display()
                
    def get_next_item(self):
        """获取下一个播放项目 - 简单优先级"""
        if not self.playlist:
            return None
            
        # 优先级：弹幕话术 > 报时话术 > 主视频话术
        priority_order = ['弹幕话术', '报时话术', '主视频话术']
        
        for priority_type in priority_order:
            for item in self.playlist:
                if (item.get('voice_type') == priority_type and 
                    item.get('status') == '已下载'):
                    return item
                    
        # 如果没有找到，返回第一个已下载的
        for item in self.playlist:
            if item.get('status') == '已下载':
                return item
                
        return None
        
    def on_audio_finished(self, file_path):
        """音频播放完成"""
        print(f"🔔 播放完成回调: {Path(file_path).name}")
        
        # 删除已播放的项目
        self.remove_played_item(file_path)
        
        # 应用播放间隔
        self.apply_interval()
        
    def remove_played_item(self, file_path):
        """删除已播放项目（只删除报时话术，保留主视频话术和弹幕话术）"""
        try:
            if hasattr(self.main_window, 'playlist_items'):
                # 从主窗口播放列表中删除
                items_to_remove = []
                for item in self.main_window.playlist_items:
                    if item.get('filename') == file_path:
                        voice_type = item.get('voice_type', '')

                        # 🔥 修复：只删除报时话术，保留主视频话术和弹幕话术
                        if voice_type == '报时话术':
                            items_to_remove.append(item)
                            print(f"🗑️ 删除已播放的报时话术: {item.get('content', '')[:30]}...")
                        else:
                            # 主视频话术和弹幕话术保留，只重置状态
                            item['status'] = '已下载'
                            print(f"🔄 保留已播放项目: {voice_type} - {item.get('content', '')[:30]}...")

                for item in items_to_remove:
                    self.main_window.playlist_items.remove(item)

                # 🔥 重要修复：重新更新播放列表，确保状态同步
                self.update_playlist()
                print(f"📋 播放列表已更新: {len(self.playlist)} 个可播放项目")

                # 更新显示
                self.update_main_window_display()

        except Exception as e:
            print(f"❌ 删除已播放项目失败: {e}")
            
    def apply_interval(self):
        """应用播放间隔"""
        try:
            # 获取间隔设置
            min_interval = getattr(self.main_window, 'min_interval', None)
            max_interval = getattr(self.main_window, 'max_interval', None)
            
            if min_interval and max_interval:
                import random
                interval = random.uniform(min_interval.value(), max_interval.value())
                interval = max(0.5, min(interval, 3.0))  # 限制在0.5-3秒
            else:
                interval = 1.0  # 默认1秒
                
            print(f"⏱️ 播放间隔: {interval:.1f}秒")
            
            # 启动间隔定时器
            self.interval_timer.start(int(interval * 1000))
            
        except Exception as e:
            print(f"❌ 应用播放间隔失败: {e}")
            # 出错时立即继续播放
            self.resume_playing()
            
    def resume_playing(self):
        """恢复播放"""
        if self.is_playing:
            print("🔄 间隔结束，继续播放")
            self.play_next()
            
    def check_and_play_next(self):
        """检查并播放下一个（定时器调用）"""
        if not self.is_playing:
            return
            
        # 如果当前没有在播放，且间隔定时器也没有运行，则播放下一个
        if not self.player.is_playing and not self.interval_timer.isActive():
            self.play_next()
            
    def update_main_window_display(self):
        """更新主窗口显示"""
        try:
            if hasattr(self.main_window, 'update_table_display'):
                self.main_window.update_table_display()
            if hasattr(self.main_window, 'save_playlist_to_file'):
                self.main_window.save_playlist_to_file()
        except Exception as e:
            print(f"❌ 更新主窗口显示失败: {e}")
            
    def set_player_type(self, player_type):
        """设置播放器类型"""
        self.player.set_player_type(player_type)


if __name__ == "__main__":
    # 简单测试
    player = SimpleAudioPlayer()
    
    # 查找测试文件
    voice_dir = Path("voices")
    if voice_dir.exists():
        wav_files = list(voice_dir.glob("*.wav"))[:3]  # 测试3个文件
        
        for wav_file in wav_files:
            player.play(str(wav_file))
            time.sleep(1)  # 等待播放完成
            
        print("✅ 测试完成")
    else:
        print("❌ voices目录不存在")
