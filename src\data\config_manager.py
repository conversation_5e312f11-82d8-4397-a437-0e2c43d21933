"""
AI Broadcaster v2 - 配置管理器
统一的配置文件管理
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional, Union
from copy import deepcopy

from ..services.logging_service import create_logger
from ..services.error_handler import handle_exceptions


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.logger = create_logger("config_manager")
        self._configs: Dict[str, Dict[str, Any]] = {}
        self._config_files: Dict[str, str] = {
            'default': 'config/default_config.json',
            'app': 'config/app_config.json',
            'user': 'config/user_config.json'
        }
        self._merged_config: Optional[Dict[str, Any]] = None
    
    @handle_exceptions("ConfigManager")
    def load_config(self, config_type: str = 'app') -> Optional[Dict[str, Any]]:
        """
        加载指定类型的配置
        
        Args:
            config_type: 配置类型 ('default', 'app', 'user')
            
        Returns:
            配置字典
        """
        if config_type in self._configs:
            return self._configs[config_type]
        
        config_file = self._config_files.get(config_type)
        if not config_file:
            self.logger.log_failure(f"未知的配置类型: {config_type}")
            return None
        
        config_path = Path(config_file)
        if not config_path.exists():
            self.logger.log_warning(f"配置文件不存在: {config_file}")
            return None
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 处理环境变量替换
            config = self._resolve_environment_variables(config)
            
            self._configs[config_type] = config
            self.logger.log_success(f"配置加载成功: {config_file}")
            return config
            
        except Exception as e:
            self.logger.log_failure(f"配置加载失败 {config_file}: {e}")
            return None
    
    @handle_exceptions("ConfigManager")
    def save_config(self, config: Dict[str, Any], config_type: str = 'user') -> bool:
        """
        保存配置
        
        Args:
            config: 配置字典
            config_type: 配置类型
            
        Returns:
            是否保存成功
        """
        config_file = self._config_files.get(config_type)
        if not config_file:
            self.logger.log_failure(f"未知的配置类型: {config_type}")
            return False
        
        try:
            config_path = Path(config_file)
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            self._configs[config_type] = config
            self._merged_config = None  # 清除缓存的合并配置
            
            self.logger.log_success(f"配置保存成功: {config_file}")
            return True
            
        except Exception as e:
            self.logger.log_failure(f"配置保存失败 {config_file}: {e}")
            return False
    
    def get_merged_config(self) -> Dict[str, Any]:
        """
        获取合并后的配置
        优先级: user > app > default
        
        Returns:
            合并后的配置字典
        """
        if self._merged_config is not None:
            return self._merged_config
        
        # 加载所有配置
        default_config = self.load_config('default') or {}
        app_config = self.load_config('app') or {}
        user_config = self.load_config('user') or {}
        
        # 深度合并配置
        merged = deepcopy(default_config)
        merged = self._deep_merge(merged, app_config)
        merged = self._deep_merge(merged, user_config)
        
        self._merged_config = merged
        return merged
    
    def get_value(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，如 'app.name' 或 'audio.voice.language'
            default: 默认值
            
        Returns:
            配置值
        """
        config = self.get_merged_config()
        keys = key_path.split('.')
        
        current = config
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default
        
        return current
    
    def set_value(self, key_path: str, value: Any, config_type: str = 'user') -> bool:
        """
        设置配置值
        
        Args:
            key_path: 配置键路径
            value: 配置值
            config_type: 配置类型
            
        Returns:
            是否设置成功
        """
        config = self.load_config(config_type) or {}
        keys = key_path.split('.')
        
        current = config
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
        
        # 保存配置
        success = self.save_config(config, config_type)
        if success:
            self._merged_config = None  # 清除缓存
        
        return success
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度合并字典
        
        Args:
            base: 基础字典
            override: 覆盖字典
            
        Returns:
            合并后的字典
        """
        result = deepcopy(base)
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = deepcopy(value)
        
        return result
    
    def _resolve_environment_variables(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析环境变量
        
        Args:
            config: 配置字典
            
        Returns:
            解析后的配置字典
        """
        def resolve_value(value):
            if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                env_var = value[2:-1]
                return os.getenv(env_var, value)
            elif isinstance(value, dict):
                return {k: resolve_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [resolve_value(item) for item in value]
            else:
                return value
        
        return resolve_value(config)
    
    def reload_configs(self):
        """重新加载所有配置"""
        self._configs.clear()
        self._merged_config = None
        self.logger.info("配置已重新加载")
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        return {
            'loaded_configs': list(self._configs.keys()),
            'config_files': self._config_files,
            'merged_config_cached': self._merged_config is not None
        }
