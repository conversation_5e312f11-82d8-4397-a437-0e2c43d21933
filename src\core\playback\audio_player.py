"""
AI Broadcaster v2 - 音频播放器
支持多种音频格式的播放和控制
"""

import os
import threading
import time
import random
from typing import Optional, Callable, Dict, Any, List
from pathlib import Path

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    print("⚠️ pygame未安装，音频播放功能受限")

try:
    import sounddevice as sd
    import soundfile as sf
    SOUNDDEVICE_AVAILABLE = True
except ImportError:
    SOUNDDEVICE_AVAILABLE = False
    print("⚠️ sounddevice/soundfile未安装，音频播放功能受限")

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class AudioPlayer:
    """音频播放器"""
    
    def __init__(self, device_name: str = None, volume: float = 0.8):
        self.logger = create_logger("audio_player")
        
        # 播放状态
        self.is_playing = False
        self.is_paused = False
        self.current_file = None
        self.current_position = 0.0
        self.total_duration = 0.0
        
        # 音频设置
        self.device_name = device_name
        self.volume = max(0.0, min(1.0, volume))
        
        # 播放线程
        self.play_thread = None
        self.stop_event = threading.Event()
        
        # 回调函数
        self.on_playback_start: Optional[Callable] = None
        self.on_playback_end: Optional[Callable] = None
        self.on_playback_pause: Optional[Callable] = None
        self.on_playback_resume: Optional[Callable] = None
        self.on_position_change: Optional[Callable] = None
        
        # 初始化音频系统
        self._init_audio_system()
        
        self.logger.info("音频播放器初始化完成")
    
    def _init_audio_system(self):
        """初始化音频系统"""
        try:
            if PYGAME_AVAILABLE:
                pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
                self.audio_backend = 'pygame'
                self.logger.info("使用pygame音频后端")
            elif SOUNDDEVICE_AVAILABLE:
                self.audio_backend = 'sounddevice'
                self.logger.info("使用sounddevice音频后端")
            else:
                self.audio_backend = 'none'
                self.logger.warning("没有可用的音频后端")
                
        except Exception as e:
            self.logger.error(f"初始化音频系统失败: {e}")
            self.audio_backend = 'none'
    
    def get_audio_devices(self) -> List[Dict[str, Any]]:
        """获取可用音频设备"""
        try:
            if SOUNDDEVICE_AVAILABLE:
                devices = sd.query_devices()
                audio_devices = []
                
                for i, device in enumerate(devices):
                    if device['max_outputs'] > 0:  # 只返回输出设备
                        audio_devices.append({
                            'index': i,
                            'name': device['name'],
                            'channels': device['max_outputs'],
                            'sample_rate': device['default_samplerate']
                        })
                
                return audio_devices
            else:
                return []
                
        except Exception as e:
            self.logger.error(f"获取音频设备失败: {e}")
            return []
    
    @handle_exceptions("AudioPlayer")
    def play_file(self, file_path: str, start_position: float = 0.0) -> bool:
        """播放音频文件"""
        try:
            if not Path(file_path).exists():
                self.logger.error(f"音频文件不存在: {file_path}")
                return False
            
            # 停止当前播放
            self.stop()
            
            self.current_file = file_path
            self.current_position = start_position
            self.is_playing = True
            self.is_paused = False
            self.stop_event.clear()
            
            # 启动播放线程
            self.play_thread = threading.Thread(
                target=self._play_thread_func, 
                args=(file_path, start_position),
                daemon=True
            )
            self.play_thread.start()
            
            self.logger.info(f"开始播放音频: {Path(file_path).name}")
            
            # 触发播放开始回调
            if self.on_playback_start:
                self.on_playback_start(file_path)
            
            return True
            
        except Exception as e:
            self.logger.error(f"播放音频文件失败: {e}")
            return False
    
    def _play_thread_func(self, file_path: str, start_position: float):
        """播放线程函数"""
        try:
            if self.audio_backend == 'pygame':
                self._play_with_pygame(file_path, start_position)
            elif self.audio_backend == 'sounddevice':
                self._play_with_sounddevice(file_path, start_position)
            else:
                self.logger.warning("没有可用的音频后端，模拟播放")
                self._simulate_playback(file_path)
                
        except Exception as e:
            self.logger.error(f"播放线程异常: {e}")
        finally:
            self.is_playing = False
            self.is_paused = False
            
            # 触发播放结束回调
            if self.on_playback_end:
                self.on_playback_end(self.current_file)
    
    def _play_with_pygame(self, file_path: str, start_position: float):
        """使用pygame播放音频"""
        try:
            pygame.mixer.music.load(file_path)
            pygame.mixer.music.set_volume(self.volume)
            pygame.mixer.music.play(start=start_position)
            
            # 监控播放状态
            while pygame.mixer.music.get_busy() and not self.stop_event.is_set():
                if not self.is_paused:
                    self.current_position += 0.1
                    
                    # 触发位置变化回调
                    if self.on_position_change:
                        self.on_position_change(self.current_position)
                
                time.sleep(0.1)
                
        except Exception as e:
            self.logger.error(f"pygame播放失败: {e}")
    
    def _play_with_sounddevice(self, file_path: str, start_position: float):
        """使用sounddevice播放音频"""
        try:
            # 读取音频文件
            data, sample_rate = sf.read(file_path)
            
            # 计算开始位置
            start_frame = int(start_position * sample_rate)
            if start_frame < len(data):
                data = data[start_frame:]
            
            self.total_duration = len(data) / sample_rate
            
            # 设置音量
            data = data * self.volume
            
            # 播放音频
            sd.play(data, sample_rate, device=self.device_name)
            
            # 监控播放状态
            start_time = time.time()
            while sd.get_stream().active and not self.stop_event.is_set():
                if not self.is_paused:
                    self.current_position = start_position + (time.time() - start_time)
                    
                    # 触发位置变化回调
                    if self.on_position_change:
                        self.on_position_change(self.current_position)
                
                time.sleep(0.1)
                
        except Exception as e:
            self.logger.error(f"sounddevice播放失败: {e}")
    
    def _simulate_playback(self, file_path: str):
        """模拟播放（用于测试）"""
        try:
            # 模拟播放时长（随机3-10秒）
            duration = random.uniform(3.0, 10.0)
            self.total_duration = duration
            
            start_time = time.time()
            while time.time() - start_time < duration and not self.stop_event.is_set():
                if not self.is_paused:
                    self.current_position = time.time() - start_time
                    
                    # 触发位置变化回调
                    if self.on_position_change:
                        self.on_position_change(self.current_position)
                
                time.sleep(0.1)
                
        except Exception as e:
            self.logger.error(f"模拟播放失败: {e}")
    
    def pause(self):
        """暂停播放"""
        if self.is_playing and not self.is_paused:
            self.is_paused = True
            
            if self.audio_backend == 'pygame':
                pygame.mixer.music.pause()
            elif self.audio_backend == 'sounddevice':
                sd.stop()
            
            self.logger.info("音频播放已暂停")
            
            # 触发暂停回调
            if self.on_playback_pause:
                self.on_playback_pause(self.current_file)
    
    def resume(self):
        """恢复播放"""
        if self.is_playing and self.is_paused:
            self.is_paused = False
            
            if self.audio_backend == 'pygame':
                pygame.mixer.music.unpause()
            elif self.audio_backend == 'sounddevice':
                # sounddevice需要重新播放
                if self.current_file:
                    self.play_file(self.current_file, self.current_position)
                    return
            
            self.logger.info("音频播放已恢复")
            
            # 触发恢复回调
            if self.on_playback_resume:
                self.on_playback_resume(self.current_file)
    
    def stop(self):
        """停止播放"""
        if self.is_playing:
            self.stop_event.set()
            self.is_playing = False
            self.is_paused = False
            
            if self.audio_backend == 'pygame':
                pygame.mixer.music.stop()
            elif self.audio_backend == 'sounddevice':
                sd.stop()
            
            # 等待播放线程结束
            if self.play_thread and self.play_thread.is_alive():
                self.play_thread.join(timeout=1.0)
            
            self.current_position = 0.0
            self.logger.info("音频播放已停止")
    
    def set_volume(self, volume: float):
        """设置音量"""
        self.volume = max(0.0, min(1.0, volume))
        
        if self.audio_backend == 'pygame' and self.is_playing:
            pygame.mixer.music.set_volume(self.volume)
        
        self.logger.debug(f"音量设置为: {self.volume:.2f}")
    
    def set_device(self, device_name: str):
        """设置音频设备"""
        self.device_name = device_name
        self.logger.info(f"音频设备设置为: {device_name}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取播放状态"""
        return {
            'is_playing': self.is_playing,
            'is_paused': self.is_paused,
            'current_file': self.current_file,
            'current_position': self.current_position,
            'total_duration': self.total_duration,
            'volume': self.volume,
            'device_name': self.device_name,
            'audio_backend': self.audio_backend
        }
    
    def get_supported_formats(self) -> list:
        """获取支持的音频格式"""
        if self.audio_backend == 'pygame':
            return ['.mp3', '.wav', '.ogg']
        elif self.audio_backend == 'sounddevice':
            return ['.wav', '.flac', '.aiff']
        else:
            return []
    
    def close(self):
        """关闭音频播放器"""
        self.stop()
        
        if self.audio_backend == 'pygame':
            pygame.mixer.quit()
        
        self.logger.info("音频播放器已关闭")
