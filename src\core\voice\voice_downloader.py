"""
AI Broadcaster v2 - 语音下载器
从API下载语音文件并缓存
"""

import hashlib
import requests
import json
from pathlib import Path
from typing import Optional, Dict, Any
from urllib.parse import urljoin

from ...data.database_manager import DatabaseManager
from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions
from ...utils.file_utils import FileUtils


class VoiceDownloader:
    """语音下载器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.logger = create_logger("voice_downloader")
        self.db = db_manager
        self.voice_dir = Path("voices")
        self.voice_dir.mkdir(parents=True, exist_ok=True)
        
        # API配置 - 这些应该从配置文件读取
        self.api_base_url = "http://your-api-server.com"  # 替换为实际API地址
        self.api_key = ""  # 从配置文件读取
        
    def _generate_text_hash(self, text: str, timestamp: int = None) -> str:
        """生成语音内容+时间戳的哈希"""
        if timestamp is None:
            import time
            timestamp = int(time.time() * 1000)
        content = f"{text}_{timestamp}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()[:16]

    def _get_cache_file_path(self, text_hash: str) -> Path:
        """获取缓存文件路径"""
        return self.voice_dir / f"{text_hash}.wav"
    
    @handle_exceptions("VoiceDownloader")
    def check_cache(self, text: str, speaker_id: int, speed: float) -> Optional[str]:
        """检查语音缓存"""
        try:
            text_hash = self._generate_text_hash(text, speaker_id, speed)
            
            # 查询数据库缓存记录
            cache_records = self.db.execute_query(
                """SELECT file_path FROM voice_cache 
                   WHERE text_hash = ? AND speaker_id = ? AND speed = ?""",
                (text_hash, speaker_id, speed)
            )
            
            if cache_records:
                file_path = cache_records[0]['file_path']
                if Path(file_path).exists():
                    self.logger.debug(f"找到语音缓存: {file_path}")
                    return file_path
                else:
                    # 文件不存在，删除缓存记录
                    self.db.execute_update(
                        "DELETE FROM voice_cache WHERE text_hash = ?",
                        (text_hash,)
                    )
            
            return None
            
        except Exception as e:
            self.logger.error(f"检查语音缓存失败: {e}")
            return None
    
    @handle_exceptions("VoiceDownloader")
    def download_voice(self, text: str, speaker_id: int, speed: float = 1.0) -> Optional[str]:
        """
        下载语音文件
        
        Args:
            text: 要转换的文本
            speaker_id: 主播ID
            speed: 语速
            
        Returns:
            语音文件路径
        """
        try:
            # 先检查缓存
            cached_file = self.check_cache(text, speaker_id, speed)
            if cached_file:
                return cached_file
            
            # 🔥 修改：生成语音内容+时间戳的哈希值文件名
            import time
            timestamp = int(time.time() * 1000)
            text_hash = self._generate_text_hash(text, timestamp)
            file_path = self._get_cache_file_path(text_hash)
            
            # 准备API请求
            api_url = urljoin(self.api_base_url, "/api/tts")
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.api_key}' if self.api_key else ''
            }
            
            data = {
                'text': text,
                'speaker_id': speaker_id,
                'speed': speed,
                'format': 'wav'
            }
            
            self.logger.info(f"开始下载语音: {text[:50]}...")
            
            # 🔥 修复：发送请求时禁用代理
            response = requests.post(
                api_url,
                headers=headers,
                json=data,
                timeout=30,
                stream=True,
                proxies={'http': None, 'https': None}
            )
            
            if response.status_code == 200:
                # 保存文件
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                
                # 获取文件大小
                file_size = file_path.stat().st_size
                
                # 保存到缓存数据库
                self.db.execute_insert(
                    """INSERT INTO voice_cache 
                       (text_hash, speaker_id, speed, file_path, file_size) 
                       VALUES (?, ?, ?, ?, ?)""",
                    (text_hash, speaker_id, speed, str(file_path), file_size)
                )
                
                self.logger.log_success(f"语音下载完成: {file_path}")
                return str(file_path)
                
            else:
                self.logger.error(f"API请求失败: {response.status_code} - {response.text}")
                return None
                
        except requests.RequestException as e:
            self.logger.error(f"网络请求失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"下载语音失败: {e}")
            return None
    
    @handle_exceptions("VoiceDownloader")
    def download_voice_mock(self, text: str, speaker_id: int, speed: float = 1.0) -> Optional[str]:
        """
        模拟下载语音文件（用于测试）
        生成一个空的WAV文件作为占位符
        """
        try:
            # 检查缓存
            cached_file = self.check_cache(text, speaker_id, speed)
            if cached_file:
                return cached_file
            
            # 🔥 修改：生成语音内容+时间戳的哈希值文件名
            import time
            timestamp = int(time.time() * 1000)
            text_hash = self._generate_text_hash(text, timestamp)
            file_path = self._get_cache_file_path(text_hash)
            
            # 创建一个简单的WAV文件头（44字节）
            wav_header = bytearray([
                0x52, 0x49, 0x46, 0x46,  # "RIFF"
                0x24, 0x00, 0x00, 0x00,  # 文件大小-8
                0x57, 0x41, 0x56, 0x45,  # "WAVE"
                0x66, 0x6D, 0x74, 0x20,  # "fmt "
                0x10, 0x00, 0x00, 0x00,  # fmt chunk大小
                0x01, 0x00,              # 音频格式(PCM)
                0x01, 0x00,              # 声道数
                0x44, 0xAC, 0x00, 0x00,  # 采样率(44100)
                0x88, 0x58, 0x01, 0x00,  # 字节率
                0x02, 0x00,              # 块对齐
                0x10, 0x00,              # 位深度
                0x64, 0x61, 0x74, 0x61,  # "data"
                0x00, 0x00, 0x00, 0x00   # 数据大小
            ])
            
            # 写入文件
            with open(file_path, 'wb') as f:
                f.write(wav_header)
            
            file_size = file_path.stat().st_size
            
            # 保存到缓存数据库
            self.db.execute_insert(
                """INSERT INTO voice_cache 
                   (text_hash, speaker_id, speed, file_path, file_size) 
                   VALUES (?, ?, ?, ?, ?)""",
                (text_hash, speaker_id, speed, str(file_path), file_size)
            )
            
            self.logger.info(f"模拟语音文件创建完成: {file_path}")
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"创建模拟语音文件失败: {e}")
            return None
    
    @handle_exceptions("VoiceDownloader")
    def clear_cache(self, days_old: int = 30) -> int:
        """
        清理过期缓存
        
        Args:
            days_old: 清理多少天前的缓存
            
        Returns:
            清理的文件数量
        """
        try:
            from datetime import datetime, timedelta
            
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            # 查询过期的缓存记录
            old_cache = self.db.execute_query(
                "SELECT file_path FROM voice_cache WHERE created_at < ?",
                (cutoff_date.isoformat(),)
            )
            
            deleted_count = 0
            for record in old_cache:
                file_path = Path(record['file_path'])
                if file_path.exists():
                    try:
                        file_path.unlink()
                        deleted_count += 1
                    except Exception as e:
                        self.logger.warning(f"删除缓存文件失败: {file_path} - {e}")
            
            # 删除数据库记录
            self.db.execute_update(
                "DELETE FROM voice_cache WHERE created_at < ?",
                (cutoff_date.isoformat(),)
            )
            
            self.logger.log_success(f"清理缓存完成，删除了 {deleted_count} 个文件")
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"清理缓存失败: {e}")
            return 0
    
    @handle_exceptions("VoiceDownloader")
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            # 查询缓存记录数
            count_result = self.db.execute_query("SELECT COUNT(*) as count FROM voice_cache")
            cache_count = count_result[0]['count'] if count_result else 0
            
            # 查询缓存总大小
            size_result = self.db.execute_query("SELECT SUM(file_size) as total_size FROM voice_cache")
            total_size = size_result[0]['total_size'] if size_result and size_result[0]['total_size'] else 0
            
            # 检查实际文件
            actual_files = list(self.voice_dir.glob("*.wav"))
            actual_count = len(actual_files)
            actual_size = sum(f.stat().st_size for f in actual_files if f.exists())
            
            return {
                'cache_records': cache_count,
                'cache_size_bytes': total_size,
                'cache_size_formatted': FileUtils.format_file_size(total_size),
                'actual_files': actual_count,
                'actual_size_bytes': actual_size,
                'actual_size_formatted': FileUtils.format_file_size(actual_size),
                'cache_directory': str(self.voice_dir)
            }
            
        except Exception as e:
            self.logger.error(f"获取缓存统计失败: {e}")
            return {}
    
    def set_api_config(self, base_url: str, api_key: str = ""):
        """设置API配置"""
        self.api_base_url = base_url
        self.api_key = api_key
        self.logger.info(f"API配置已更新: {base_url}")
    
    def test_api_connection(self) -> tuple[bool, str]:
        """测试API连接"""
        try:
            test_url = urljoin(self.api_base_url, "/api/health")
            response = requests.get(test_url, timeout=10)
            
            if response.status_code == 200:
                return True, "API连接正常"
            else:
                return False, f"API返回错误: {response.status_code}"
                
        except requests.RequestException as e:
            return False, f"连接失败: {str(e)}"
        except Exception as e:
            return False, f"测试失败: {str(e)}"
