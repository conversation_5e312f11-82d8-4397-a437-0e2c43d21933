"""
AI Broadcaster v2 - 话术管理器
话术的增删改查和管理功能
"""

import random
from typing import List, Dict, Any, Optional
from datetime import datetime

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class ScriptManager:
    """话术管理器"""
    
    def __init__(self, db_manager):
        self.logger = create_logger("script_manager")
        self.db_manager = db_manager
        
    @handle_exceptions("ScriptManager")
    def create_script(self, name: str, content: str, user_id: Optional[int] = None, 
                     category: str = "default", tags: str = "") -> Optional[int]:
        """
        创建新话术
        
        Args:
            name: 话术名称
            content: 话术内容
            user_id: 用户ID
            category: 分类
            tags: 标签
            
        Returns:
            话术ID，失败返回None
        """
        try:
            script_id = self.db_manager.execute_insert(
                """INSERT INTO scripts (name, content, user_id, category, tags, created_at, updated_at) 
                   VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)""",
                (name, content, user_id, category, tags)
            )
            
            if script_id:
                self.logger.info(f"创建话术成功: {name} (ID: {script_id})")
                return script_id
            else:
                self.logger.error(f"创建话术失败: {name}")
                return None
                
        except Exception as e:
            self.logger.error(f"创建话术异常: {e}")
            return None
            
    @handle_exceptions("ScriptManager")
    def get_script(self, script_id: int) -> Optional[Dict[str, Any]]:
        """获取单个话术"""
        try:
            scripts = self.db_manager.execute_query(
                "SELECT * FROM scripts WHERE id = ?",
                (script_id,)
            )
            
            if scripts:
                return scripts[0]
            return None
            
        except Exception as e:
            self.logger.error(f"获取话术失败: {e}")
            return None
            
    @handle_exceptions("ScriptManager")
    def get_scripts_by_user(self, user_id: Optional[int] = None, 
                           category: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取用户的话术列表"""
        try:
            query = "SELECT * FROM scripts WHERE (user_id = ? OR user_id IS NULL)"
            params = [user_id]
            
            if category:
                query += " AND category = ?"
                params.append(category)
                
            query += " ORDER BY updated_at DESC"
            
            scripts = self.db_manager.execute_query(query, params)
            return scripts or []
            
        except Exception as e:
            self.logger.error(f"获取话术列表失败: {e}")
            return []
            
    @handle_exceptions("ScriptManager")
    def update_script(self, script_id: int, name: Optional[str] = None, 
                     content: Optional[str] = None, category: Optional[str] = None,
                     tags: Optional[str] = None) -> bool:
        """更新话术"""
        try:
            # 构建更新字段
            update_fields = []
            params = []
            
            if name is not None:
                update_fields.append("name = ?")
                params.append(name)
                
            if content is not None:
                update_fields.append("content = ?")
                params.append(content)
                
            if category is not None:
                update_fields.append("category = ?")
                params.append(category)
                
            if tags is not None:
                update_fields.append("tags = ?")
                params.append(tags)
                
            if not update_fields:
                return True  # 没有更新内容
                
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            params.append(script_id)
            
            query = f"UPDATE scripts SET {', '.join(update_fields)} WHERE id = ?"
            
            rows_affected = self.db_manager.execute_update(query, params)
            
            if rows_affected > 0:
                self.logger.info(f"更新话术成功: ID {script_id}")
                return True
            else:
                self.logger.warning(f"话术不存在或无变化: ID {script_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"更新话术失败: {e}")
            return False
            
    @handle_exceptions("ScriptManager")
    def delete_script(self, script_id: int) -> bool:
        """删除话术"""
        try:
            # 先删除相关的时间段
            self.db_manager.execute_update(
                "DELETE FROM time_segments WHERE script_id = ?",
                (script_id,)
            )
            
            # 删除话术
            rows_affected = self.db_manager.execute_update(
                "DELETE FROM scripts WHERE id = ?",
                (script_id,)
            )
            
            if rows_affected > 0:
                self.logger.info(f"删除话术成功: ID {script_id}")
                return True
            else:
                self.logger.warning(f"话术不存在: ID {script_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除话术失败: {e}")
            return False
            
    @handle_exceptions("ScriptManager")
    def search_scripts(self, keyword: str, user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """搜索话术"""
        try:
            query = """
                SELECT * FROM scripts 
                WHERE (user_id = ? OR user_id IS NULL) 
                AND (name LIKE ? OR content LIKE ? OR tags LIKE ?)
                ORDER BY updated_at DESC
            """
            
            search_pattern = f"%{keyword}%"
            params = [user_id, search_pattern, search_pattern, search_pattern]
            
            scripts = self.db_manager.execute_query(query, params)
            return scripts or []
            
        except Exception as e:
            self.logger.error(f"搜索话术失败: {e}")
            return []
            
    @handle_exceptions("ScriptManager")
    def get_script_lines(self, script_id: int) -> List[str]:
        """获取话术的所有行"""
        try:
            script = self.get_script(script_id)
            if not script or not script['content']:
                return []
                
            # 分割内容为行，过滤空行
            lines = [line.strip() for line in script['content'].split('\n') if line.strip()]
            return lines
            
        except Exception as e:
            self.logger.error(f"获取话术行失败: {e}")
            return []
            
    @handle_exceptions("ScriptManager")
    def get_random_line(self, script_id: int) -> Optional[str]:
        """从话术中随机获取一行"""
        try:
            lines = self.get_script_lines(script_id)
            if lines:
                return random.choice(lines)
            return None
            
        except Exception as e:
            self.logger.error(f"获取随机话术行失败: {e}")
            return None
            
    @handle_exceptions("ScriptManager")
    def get_categories(self, user_id: Optional[int] = None) -> List[str]:
        """获取话术分类列表"""
        try:
            query = """
                SELECT DISTINCT category FROM scripts 
                WHERE (user_id = ? OR user_id IS NULL) AND category IS NOT NULL
                ORDER BY category
            """
            
            results = self.db_manager.execute_query(query, (user_id,))
            categories = [row['category'] for row in results if row['category']]
            return categories
            
        except Exception as e:
            self.logger.error(f"获取分类列表失败: {e}")
            return []
            
    @handle_exceptions("ScriptManager")
    def get_script_stats(self, user_id: Optional[int] = None) -> Dict[str, Any]:
        """获取话术统计信息"""
        try:
            # 总数统计
            total_query = "SELECT COUNT(*) as total FROM scripts WHERE (user_id = ? OR user_id IS NULL)"
            total_result = self.db_manager.execute_query(total_query, (user_id,))
            total_count = total_result[0]['total'] if total_result else 0
            
            # 分类统计
            category_query = """
                SELECT category, COUNT(*) as count FROM scripts 
                WHERE (user_id = ? OR user_id IS NULL) 
                GROUP BY category 
                ORDER BY count DESC
            """
            category_results = self.db_manager.execute_query(category_query, (user_id,))
            
            # 最近更新
            recent_query = """
                SELECT name, updated_at FROM scripts 
                WHERE (user_id = ? OR user_id IS NULL) 
                ORDER BY updated_at DESC 
                LIMIT 5
            """
            recent_results = self.db_manager.execute_query(recent_query, (user_id,))
            
            return {
                'total_count': total_count,
                'categories': category_results or [],
                'recent_updates': recent_results or []
            }
            
        except Exception as e:
            self.logger.error(f"获取话术统计失败: {e}")
            return {
                'total_count': 0,
                'categories': [],
                'recent_updates': []
            }
            
    @handle_exceptions("ScriptManager")
    def duplicate_script(self, script_id: int, new_name: Optional[str] = None) -> Optional[int]:
        """复制话术"""
        try:
            original_script = self.get_script(script_id)
            if not original_script:
                return None
                
            # 生成新名称
            if not new_name:
                new_name = f"{original_script['name']} - 副本"
                
            # 创建副本
            new_script_id = self.create_script(
                name=new_name,
                content=original_script['content'],
                user_id=original_script['user_id'],
                category=original_script['category'],
                tags=original_script['tags']
            )
            
            if new_script_id:
                self.logger.info(f"复制话术成功: {original_script['name']} -> {new_name}")
                
            return new_script_id
            
        except Exception as e:
            self.logger.error(f"复制话术失败: {e}")
            return None
            
    @handle_exceptions("ScriptManager")
    def import_scripts_from_text(self, text: str, user_id: Optional[int] = None, 
                                category: str = "imported") -> int:
        """从文本批量导入话术"""
        try:
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            imported_count = 0
            
            for i, line in enumerate(lines, 1):
                script_name = f"导入话术 {i}"
                script_id = self.create_script(
                    name=script_name,
                    content=line,
                    user_id=user_id,
                    category=category
                )
                
                if script_id:
                    imported_count += 1
                    
            self.logger.info(f"批量导入话术完成: {imported_count}/{len(lines)}")
            return imported_count
            
        except Exception as e:
            self.logger.error(f"批量导入话术失败: {e}")
            return 0
            
    @handle_exceptions("ScriptManager")
    def export_scripts_to_text(self, user_id: Optional[int] = None, 
                              category: Optional[str] = None) -> str:
        """导出话术为文本"""
        try:
            scripts = self.get_scripts_by_user(user_id, category)
            
            export_lines = []
            for script in scripts:
                export_lines.append(f"# {script['name']}")
                if script['content']:
                    export_lines.extend(script['content'].split('\n'))
                export_lines.append("")  # 空行分隔
                
            return '\n'.join(export_lines)
            
        except Exception as e:
            self.logger.error(f"导出话术失败: {e}")
            return ""
