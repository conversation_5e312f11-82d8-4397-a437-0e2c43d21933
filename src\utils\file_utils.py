"""
AI Broadcaster v2 - 文件工具类
提供文件操作相关的工具函数
"""

import os
import shutil
import hashlib
import mimetypes
from pathlib import Path
from typing import List, Optional, Union, Dict, Any


class FileUtils:
    """文件工具类"""
    
    @staticmethod
    def ensure_dir(path: Union[str, Path]) -> bool:
        """
        确保目录存在
        
        Args:
            path: 目录路径
            
        Returns:
            是否成功
        """
        try:
            Path(path).mkdir(parents=True, exist_ok=True)
            return True
        except Exception:
            return False
    
    @staticmethod
    def get_file_size(file_path: Union[str, Path]) -> Optional[int]:
        """
        获取文件大小
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件大小（字节）
        """
        try:
            return Path(file_path).stat().st_size
        except Exception:
            return None
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        格式化文件大小
        
        Args:
            size_bytes: 字节数
            
        Returns:
            格式化后的大小字符串
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    @staticmethod
    def get_file_extension(file_path: Union[str, Path]) -> str:
        """
        获取文件扩展名
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件扩展名（不包含点）
        """
        return Path(file_path).suffix.lstrip('.')
    
    @staticmethod
    def get_mime_type(file_path: Union[str, Path]) -> Optional[str]:
        """
        获取文件MIME类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            MIME类型
        """
        mime_type, _ = mimetypes.guess_type(str(file_path))
        return mime_type
    
    @staticmethod
    def is_audio_file(file_path: Union[str, Path]) -> bool:
        """
        判断是否为音频文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为音频文件
        """
        audio_extensions = {
            'mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a', 'opus'
        }
        extension = FileUtils.get_file_extension(file_path).lower()
        return extension in audio_extensions
    
    @staticmethod
    def is_image_file(file_path: Union[str, Path]) -> bool:
        """
        判断是否为图片文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为图片文件
        """
        image_extensions = {
            'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp', 'svg'
        }
        extension = FileUtils.get_file_extension(file_path).lower()
        return extension in image_extensions
    
    @staticmethod
    def is_text_file(file_path: Union[str, Path]) -> bool:
        """
        判断是否为文本文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为文本文件
        """
        text_extensions = {
            'txt', 'md', 'json', 'xml', 'yaml', 'yml', 'csv', 'log',
            'py', 'js', 'html', 'css', 'sql', 'ini', 'cfg', 'conf'
        }
        extension = FileUtils.get_file_extension(file_path).lower()
        return extension in text_extensions
    
    @staticmethod
    def calculate_hash(file_path: Union[str, Path], algorithm: str = 'md5') -> Optional[str]:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法
            
        Returns:
            哈希值
        """
        try:
            hash_obj = hashlib.new(algorithm)
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            return hash_obj.hexdigest()
        except Exception:
            return None
    
    @staticmethod
    def find_files(directory: Union[str, Path], 
                   pattern: str = "*", 
                   recursive: bool = True) -> List[Path]:
        """
        查找文件
        
        Args:
            directory: 搜索目录
            pattern: 文件模式
            recursive: 是否递归搜索
            
        Returns:
            文件路径列表
        """
        directory = Path(directory)
        if not directory.exists():
            return []
        
        try:
            if recursive:
                return list(directory.rglob(pattern))
            else:
                return list(directory.glob(pattern))
        except Exception:
            return []
    
    @staticmethod
    def clean_filename(filename: str) -> str:
        """
        清理文件名，移除非法字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的文件名
        """
        # Windows和Unix系统的非法字符
        illegal_chars = '<>:"/\\|?*'
        
        # 替换非法字符
        for char in illegal_chars:
            filename = filename.replace(char, '_')
        
        # 移除前后空格和点
        filename = filename.strip(' .')
        
        # 确保文件名不为空
        if not filename:
            filename = "unnamed"
        
        return filename
    
    @staticmethod
    def get_unique_filename(file_path: Union[str, Path]) -> Path:
        """
        获取唯一文件名（如果文件已存在，则添加数字后缀）
        
        Args:
            file_path: 文件路径
            
        Returns:
            唯一的文件路径
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            return file_path
        
        stem = file_path.stem
        suffix = file_path.suffix
        parent = file_path.parent
        
        counter = 1
        while True:
            new_name = f"{stem}_{counter}{suffix}"
            new_path = parent / new_name
            if not new_path.exists():
                return new_path
            counter += 1
    
    @staticmethod
    def copy_with_progress(src: Union[str, Path], 
                          dst: Union[str, Path], 
                          callback: Optional[callable] = None) -> bool:
        """
        带进度的文件复制
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            callback: 进度回调函数 (bytes_copied, total_bytes)
            
        Returns:
            是否复制成功
        """
        try:
            src_path = Path(src)
            dst_path = Path(dst)
            
            if not src_path.exists():
                return False
            
            # 确保目标目录存在
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            total_size = src_path.stat().st_size
            copied_size = 0
            
            with open(src_path, 'rb') as src_file, open(dst_path, 'wb') as dst_file:
                while True:
                    chunk = src_file.read(8192)
                    if not chunk:
                        break
                    
                    dst_file.write(chunk)
                    copied_size += len(chunk)
                    
                    if callback:
                        callback(copied_size, total_size)
            
            return True
            
        except Exception:
            return False
    
    @staticmethod
    def get_directory_size(directory: Union[str, Path]) -> int:
        """
        获取目录大小
        
        Args:
            directory: 目录路径
            
        Returns:
            目录大小（字节）
        """
        total_size = 0
        try:
            for file_path in Path(directory).rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception:
            pass
        
        return total_size
    
    @staticmethod
    def cleanup_temp_files(temp_dir: Union[str, Path], max_age_hours: int = 24):
        """
        清理临时文件
        
        Args:
            temp_dir: 临时目录
            max_age_hours: 最大保留时间（小时）
        """
        try:
            import time
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600
            
            for file_path in Path(temp_dir).rglob('*'):
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        file_path.unlink()
        except Exception:
            pass
