#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的副视频功能测试程序
专门测试副视频触发和播放逻辑
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_sub_video_simple():
    """简化的副视频功能测试"""
    print("🧪 开始简化副视频功能测试...")
    
    try:
        # 导入主程序
        from run_gui_qt5 import MainWindow
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建主窗口（需要用户信息）
        user_info = {
            'username': 'test_user',
            'user_id': 1,
            'expire_time': '2025-12-31'
        }
        window = MainWindow(user_info)
        
        # 等待界面初始化完成
        def start_test():
            print("✅ 界面初始化完成，开始副视频测试...")
            
            # 测试1：检查副视频触发逻辑
            print("\n🔍 测试1：副视频触发逻辑...")
            test_content = "感谢老板的火箭，太给力了！"
            
            # 直接调用副视频检查方法
            if hasattr(window, 'check_sub_video_trigger'):
                result = window.check_sub_video_trigger(test_content)
                print(f"📝 测试内容: {test_content}")
                print(f"🔍 副视频触发结果: {result}")
                
                if result:
                    print(f"✅ 副视频触发成功: {result}")
                else:
                    print("❌ 副视频未触发")
            else:
                print("❌ 未找到副视频触发检查方法")
            
            # 测试2：手动添加副视频项目到播放列表
            print("\n🎬 测试2：手动添加副视频项目...")
            test_item = {
                'id': 9999,
                'content': '感谢大家的支持，送个火箭吧！',
                'voice_type': '弹幕话术',
                'time_segment': '测试时间段',
                'status': '已下载',
                'filename': 'test_sub_video.wav',
                'sub_video': '1111'  # 手动设置副视频源
            }
            
            print(f"📋 测试项目:")
            print(f"  内容: {test_item['content']}")
            print(f"  副视频: {test_item['sub_video']}")
            
            # 测试副视频播放流程
            if hasattr(window, 'handle_sub_video_playback'):
                print("✅ 找到副视频播放方法")
                try:
                    print("🎬 开始测试副视频播放流程...")
                    result = window.handle_sub_video_playback(test_item)
                    if result:
                        print("✅ 副视频播放流程测试成功")
                    else:
                        print("❌ 副视频播放流程测试失败")
                except Exception as e:
                    print(f"❌ 副视频播放流程测试异常: {e}")
            else:
                print("❌ 未找到副视频播放方法")
            
            # 测试3：OBS切换方法
            print("\n🔄 测试3：OBS副视频切换...")
            if hasattr(window, 'switch_to_sub_video_with_obs'):
                print("✅ 找到OBS副视频切换方法")
                try:
                    result = window.switch_to_sub_video_with_obs('1111')
                    if result:
                        print("✅ OBS副视频切换测试成功")
                    else:
                        print("❌ OBS副视频切换测试失败")
                except Exception as e:
                    print(f"❌ OBS副视频切换测试异常: {e}")
            else:
                print("❌ 未找到OBS副视频切换方法")
            
            # 测试4：OBS回切方法
            print("\n🔙 测试4：OBS主视频回切...")
            if hasattr(window, 'switch_back_to_main_video_with_obs'):
                print("✅ 找到OBS主视频回切方法")
                try:
                    result = window.switch_back_to_main_video_with_obs()
                    if result:
                        print("✅ OBS主视频回切测试成功")
                    else:
                        print("❌ OBS主视频回切测试失败")
                except Exception as e:
                    print(f"❌ OBS主视频回切测试异常: {e}")
            else:
                print("❌ 未找到OBS主视频回切方法")
            
            # 测试5：播放列表中的副视频检测
            print("\n📋 测试5：播放列表副视频检测...")
            
            # 添加测试项目到播放列表
            if hasattr(window, 'playlist_items'):
                window.playlist_items.append(test_item)
                print(f"✅ 添加测试项目到播放列表")
                
                # 更新表格显示
                if hasattr(window, 'update_table_display'):
                    window.update_table_display()
                    print(f"✅ 更新表格显示")
                
                # 检查播放列表中的副视频
                for i, item in enumerate(window.playlist_items):
                    sub_video = item.get('sub_video', '无')
                    content = item.get('content', '')[:30]
                    print(f"  项目 {i+1}: {content}... - 副视频: {sub_video}")
                    
                    if sub_video and sub_video != '无':
                        print(f"✅ 检测到副视频需求: {sub_video}")
                    else:
                        print(f"⚠️ 无副视频需求")
            
            # 退出测试
            def exit_test():
                print("\n🏁 简化副视频功能测试完成")
                print("📋 测试总结:")
                print("  ✅ 副视频触发逻辑测试")
                print("  ✅ 副视频播放流程测试")
                print("  ✅ OBS切换控制测试")
                print("  ✅ 播放列表副视频检测测试")
                app.quit()

            # 5秒后退出测试
            QTimer.singleShot(5000, exit_test)
        
        # 2秒后开始测试
        QTimer.singleShot(2000, start_test)
        
        # 显示窗口
        window.show()
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sub_video_simple()
