"""
使用主程序测试实际效果：
直接调用run_gui_qt5.py中的功能来测试黑屏和自动切换问题
"""

import sys
import os
from pathlib import Path
import time

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_main_program_real():
    """使用主程序测试实际效果"""
    print("🚀 使用主程序测试实际效果")
    print("=" * 50)
    
    try:
        # 创建应用程序
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 模拟用户信息
        user_info = {
            'username': 'test_user',
            'user_id': 1,
            'machine_code': 'TEST_MACHINE_001',
            'expire_time': '2025年12月31日23:59:59'
        }
        
        print("📋 创建主界面...")
        import run_gui_qt5
        main_window = run_gui_qt5.MainWindow(user_info)
        
        # 不显示窗口，只测试功能
        # main_window.show()
        
        print("✅ 主界面创建成功")
        
        # 测试1: 连接OBS
        print("\n📋 测试1: 连接OBS...")
        
        # 设置OBS连接参数
        main_window.obs_host_input.setText("localhost")
        main_window.obs_port_input.setText("4455")
        main_window.obs_password_input.setText("")
        
        # 尝试连接OBS
        main_window.connect_obs()
        
        # 等待连接完成
        time.sleep(2)
        
        # 检查连接状态
        if hasattr(main_window, 'playback_controller') and main_window.playback_controller:
            obs_controller = main_window.playback_controller.obs_controller
            is_connected = getattr(obs_controller, 'is_connected', getattr(obs_controller, 'connected', False))
            
            if is_connected:
                print("✅ OBS连接成功")
                
                # 刷新源列表
                main_window.refresh_obs_sources()
                time.sleep(1)
                
                # 检查可用的视频源
                source_a_count = main_window.video_source_a_combo.count()
                source_b_count = main_window.video_source_b_combo.count()
                
                print(f"📋 可用视频源数量: A={source_a_count}, B={source_b_count}")
                
                if source_a_count > 0 and source_b_count > 0:
                    print("✅ 找到可用的视频源")
                    
                    # 测试2: 设置双主视频源
                    print("\n📋 测试2: 设置双主视频源...")
                    
                    # 选择第一个和第二个源（如果存在）
                    if source_a_count > 0:
                        main_window.video_source_a_combo.setCurrentIndex(0)
                        source_a = main_window.video_source_a_combo.currentText()
                        print(f"选择视频源A: {source_a}")
                    
                    if source_b_count > 1:
                        main_window.video_source_b_combo.setCurrentIndex(1)
                        source_b = main_window.video_source_b_combo.currentText()
                        print(f"选择视频源B: {source_b}")
                    elif source_b_count > 0:
                        main_window.video_source_b_combo.setCurrentIndex(0)
                        source_b = main_window.video_source_b_combo.currentText()
                        print(f"选择视频源B: {source_b}")
                    
                    # 设置变速范围
                    main_window.min_speed_input.setText("1.1")
                    main_window.max_speed_input.setText("2.0")
                    
                    # 应用OBS设置（不显示对话框）
                    print("📋 应用OBS设置...")
                    
                    # 直接调用设置逻辑，跳过对话框
                    source_a = main_window.video_source_a_combo.currentText()
                    source_b = main_window.video_source_b_combo.currentText()
                    min_speed = 1.1
                    max_speed = 2.0
                    
                    if source_a and source_b and source_a != source_b:
                        # 设置双主视频源
                        success = main_window.playback_controller.set_dual_video_sources(source_a, source_b)
                        if success:
                            print(f"✅ 双主视频源设置成功: A={source_a}, B={source_b}")
                            
                            # 设置变速范围
                            dual_video_manager = main_window.playback_controller.dual_video_manager
                            dual_video_manager.set_speed_range(min_speed, max_speed)
                            dual_video_manager.set_switch_threshold(5.0)
                            
                            print(f"✅ 变速范围设置: {min_speed}x - {max_speed}x")
                            
                            # 测试3: 启动自动监控
                            print("\n📋 测试3: 启动自动监控...")
                            
                            monitor_success = main_window.playback_controller.start_dual_video_monitoring()
                            if monitor_success:
                                print("✅ 自动监控启动成功")
                                
                                # 获取管理器状态
                                status = dual_video_manager.get_manager_status()
                                print(f"📊 监控状态:")
                                print(f"  - 当前激活: {status.get('current_active_source')}")
                                print(f"  - 下一个源: {status.get('next_source')}")
                                print(f"  - 监控运行: {status.get('monitoring')}")
                                print(f"  - OBS连接: {status.get('obs_connected')}")
                                
                                # 测试4: 手动切换测试
                                print("\n📋 测试4: 手动切换测试...")
                                
                                # 记录切换前状态
                                old_active = dual_video_manager.current_active_source
                                old_next = dual_video_manager.next_source
                                print(f"切换前: 当前={old_active}, 下一个={old_next}")
                                
                                # 执行手动切换
                                switch_success = main_window.playback_controller.manual_switch_video()
                                
                                if switch_success:
                                    # 记录切换后状态
                                    new_active = dual_video_manager.current_active_source
                                    new_next = dual_video_manager.next_source
                                    new_speed = dual_video_manager.next_source_speed
                                    
                                    print(f"✅ 手动切换成功")
                                    print(f"切换后: 当前={new_active}, 下一个={new_next}")
                                    print(f"预设速度: {new_speed}x")
                                    
                                    # 验证切换结果
                                    if old_active != new_active and old_next == new_active:
                                        print("✅ 切换逻辑正确: 当前源和下一个源正确交换")
                                        
                                        # 测试5: 监控视频状态
                                        print("\n📋 测试5: 监控视频状态...")
                                        
                                        # 获取当前视频状态
                                        video_status = dual_video_manager.get_current_video_status()
                                        if video_status:
                                            print(f"📊 当前视频状态:")
                                            print(f"  - 源名称: {video_status.get('active_source')}")
                                            print(f"  - 媒体状态: {video_status.get('media_state')}")
                                            print(f"  - 播放时长: {video_status.get('media_duration', 0)}ms")
                                            print(f"  - 当前位置: {video_status.get('media_cursor', 0)}ms")
                                            
                                            # 计算播放进度
                                            duration = video_status.get('media_duration', 0)
                                            cursor = video_status.get('media_cursor', 0)
                                            if duration > 0:
                                                progress = (cursor / duration) * 100
                                                remaining = (duration - cursor) / 1000.0
                                                print(f"  - 播放进度: {progress:.1f}%")
                                                print(f"  - 剩余时间: {remaining:.1f}秒")
                                                
                                                if progress > 0:
                                                    print("✅ 视频状态获取正常")
                                                else:
                                                    print("⚠️ 视频可能未播放或状态异常")
                                            else:
                                                print("⚠️ 无法获取视频时长信息")
                                        else:
                                            print("❌ 无法获取视频状态")
                                        
                                        # 测试6: 停止监控
                                        print("\n📋 测试6: 停止监控...")
                                        main_window.playback_controller.stop_dual_video_monitoring()
                                        print("✅ 监控已停止")
                                        
                                        print("\n🎉 主程序功能测试完成！")
                                        print("\n📊 测试结果总结:")
                                        print("  ✅ OBS连接: 成功")
                                        print("  ✅ 视频源设置: 成功")
                                        print("  ✅ 监控启动: 成功")
                                        print("  ✅ 手动切换: 成功")
                                        print("  ✅ 状态获取: 成功")
                                        print("  ✅ 监控停止: 成功")
                                        
                                        print("\n💡 如果仍有问题，可能的原因:")
                                        print("  1. 视频文件路径不正确或文件损坏")
                                        print("  2. OBS媒体源设置有问题")
                                        print("  3. 视频文件格式不支持")
                                        print("  4. OBS版本兼容性问题")
                                        
                                        return True
                                    else:
                                        print("❌ 切换逻辑错误: 源状态未正确交换")
                                        return False
                                else:
                                    print("❌ 手动切换失败")
                                    return False
                            else:
                                print("❌ 自动监控启动失败")
                                return False
                        else:
                            print("❌ 双主视频源设置失败")
                            return False
                    else:
                        print("❌ 视频源选择无效")
                        return False
                else:
                    print("❌ 没有找到足够的视频源")
                    print("💡 请在OBS中添加至少2个媒体源")
                    return False
            else:
                print("❌ OBS连接失败")
                print("💡 请确保:")
                print("  1. OBS Studio正在运行")
                print("  2. WebSocket服务器已启用")
                print("  3. 端口设置为4455")
                return False
        else:
            print("❌ 播放控制器初始化失败")
            return False
        
    except Exception as e:
        print(f"❌ 主程序测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        try:
            if 'main_window' in locals():
                main_window.close()
            if 'app' in locals():
                app.quit()
        except:
            pass


def main():
    """主测试函数"""
    print("🚀 主程序实际效果测试")
    print("=" * 50)
    
    # 测试主程序功能
    success = test_main_program_real()
    
    if success:
        print(f"\n🎉 主程序测试成功！")
        print(f"代码修复有效，功能正常工作")
    else:
        print(f"\n❌ 主程序测试失败")
        print(f"请检查OBS设置和视频源配置")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
