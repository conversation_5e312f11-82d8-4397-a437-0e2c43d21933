"""
测试单一视频源显示功能
验证双主视频管理器确保只有一个视频源显示的功能
"""

import time
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.playback_controller import PlaybackController
from src.services.logging_service import create_logger


def test_single_source_display():
    """测试单一视频源显示功能"""
    logger = create_logger("test_single_source")
    
    print("🎥 测试单一视频源显示功能")
    print("=" * 50)
    
    try:
        # 1. 创建播放控制器
        print("\n📋 步骤1: 初始化播放控制器...")
        controller = PlaybackController()
        print("✅ 播放控制器初始化完成")
        
        # 2. 尝试连接OBS
        print("\n📋 步骤2: 连接OBS...")
        obs_controller = controller.obs_controller
        
        # 更新连接参数
        obs_controller.host = "localhost"
        obs_controller.port = 4455
        obs_controller.password = ""
        obs_controller.websocket_url = "ws://localhost:4455"
        
        # 尝试连接
        success = obs_controller.connect()
        
        if success:
            print("✅ OBS连接成功")
            time.sleep(2)  # 等待连接稳定
            
            # 获取可用的媒体源
            sources = obs_controller.get_source_list()
            print(f"✅ 找到 {len(sources)} 个媒体源")
            if sources:
                print("可用的媒体源:")
                for i, source in enumerate(sources):
                    print(f"  {i+1}. {source}")
        else:
            print("❌ OBS连接失败，将在模拟模式下测试")
            return test_single_source_display_simulation()
        
        # 3. 设置双主视频源
        print("\n📋 步骤3: 设置双主视频源...")
        if len(sources) >= 2:
            source_a = sources[0]
            source_b = sources[1]
        else:
            source_a = "1111"
            source_b = "2222"
        
        success = controller.set_dual_video_sources(source_a, source_b)
        if success:
            print(f"✅ 双主视频源设置成功: A={source_a}, B={source_b}")
        else:
            print("❌ 双主视频源设置失败")
            return False
        
        # 4. 测试获取可见视频源
        print("\n📋 步骤4: 检查当前可见的视频源...")
        visible_sources = controller.get_all_visible_video_sources()
        print(f"当前可见的视频源: {visible_sources}")
        print(f"可见视频源数量: {len(visible_sources)}")
        
        # 5. 测试确保单一视频源显示
        print("\n📋 步骤5: 确保只有一个视频源显示...")
        success = controller.ensure_single_video_source_display()
        if success:
            print("✅ 单一视频源显示确保成功")
        else:
            print("❌ 单一视频源显示确保失败")
        
        # 6. 再次检查可见视频源
        print("\n📋 步骤6: 再次检查可见的视频源...")
        visible_sources_after = controller.get_all_visible_video_sources()
        print(f"处理后可见的视频源: {visible_sources_after}")
        print(f"处理后可见视频源数量: {len(visible_sources_after)}")
        
        # 验证结果
        if len(visible_sources_after) <= 1:
            print("✅ 验证通过：只有一个或零个视频源可见")
        else:
            print(f"❌ 验证失败：仍有 {len(visible_sources_after)} 个视频源可见")
        
        # 7. 测试视频源切换
        print("\n📋 步骤7: 测试视频源切换...")
        dual_manager = controller.dual_video_manager
        
        # 手动切换
        print("执行手动切换...")
        switch_success = controller.manual_switch_video()
        print(f"手动切换结果: {'成功' if switch_success else '失败'}")
        
        # 检查切换后的可见源
        visible_after_switch = controller.get_all_visible_video_sources()
        print(f"切换后可见的视频源: {visible_after_switch}")
        print(f"切换后可见视频源数量: {len(visible_after_switch)}")
        
        if len(visible_after_switch) <= 1:
            print("✅ 切换后验证通过：只有一个或零个视频源可见")
        else:
            print(f"❌ 切换后验证失败：仍有 {len(visible_after_switch)} 个视频源可见")
        
        # 8. 测试强制切换
        print("\n📋 步骤8: 测试强制切换...")
        force_success = dual_manager.force_switch_to_source(source_b)
        print(f"强制切换结果: {'成功' if force_success else '失败'}")
        
        # 检查强制切换后的可见源
        visible_after_force = controller.get_all_visible_video_sources()
        print(f"强制切换后可见的视频源: {visible_after_force}")
        print(f"强制切换后可见视频源数量: {len(visible_after_force)}")
        
        if len(visible_after_force) <= 1:
            print("✅ 强制切换后验证通过：只有一个或零个视频源可见")
        else:
            print(f"❌ 强制切换后验证失败：仍有 {len(visible_after_force)} 个视频源可见")
        
        # 9. 获取管理器状态
        print("\n📋 步骤9: 获取管理器状态...")
        status = dual_manager.get_manager_status()
        print("管理器状态:")
        print(f"  - 当前激活源: {status.get('current_active_source')}")
        print(f"  - 下一个源: {status.get('next_source')}")
        print(f"  - OBS连接: {status.get('obs_connected')}")
        
        print("\n🎉 单一视频源显示功能测试完成")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        logger.error(f"测试异常: {e}")
        return False
    finally:
        # 清理资源
        try:
            controller.close()
        except:
            pass


def test_single_source_display_simulation():
    """模拟模式测试"""
    print("\n🔄 模拟模式测试...")
    
    try:
        # 创建模拟的OBS控制器
        class MockOBSController:
            def __init__(self):
                self.connected = True
                self.requests = []
            
            def send_request_sync(self, request_type, params=None):
                self.requests.append({
                    'type': request_type,
                    'params': params or {}
                })
                
                if request_type == "GetCurrentProgramScene":
                    return {"currentProgramSceneName": "测试场景"}
                elif request_type == "GetSceneItemList":
                    return {
                        "sceneItems": [
                            {
                                "sourceName": "媒体源1",
                                "sourceType": "ffmpeg_source",
                                "sceneItemId": 1,
                                "sceneItemEnabled": True,
                                "sceneItemIndex": 1
                            },
                            {
                                "sourceName": "媒体源2",
                                "sourceType": "ffmpeg_source",
                                "sceneItemId": 2,
                                "sceneItemEnabled": True,
                                "sceneItemIndex": 2
                            },
                            {
                                "sourceName": "媒体源3",
                                "sourceType": "ffmpeg_source",
                                "sceneItemId": 3,
                                "sceneItemEnabled": True,
                                "sceneItemIndex": 3
                            }
                        ]
                    }
                elif request_type == "SetSceneItemEnabled":
                    return {"success": True}
                
                return None
        
        # 创建控制器并替换OBS控制器
        controller = PlaybackController()
        controller.obs_controller = MockOBSController()
        controller.dual_video_manager.obs_controller = MockOBSController()
        
        # 设置双主视频源
        controller.set_dual_video_sources("媒体源1", "媒体源2")
        
        # 测试确保单一视频源显示
        print("测试确保单一视频源显示...")
        success = controller.ensure_single_video_source_display()
        print(f"结果: {'成功' if success else '失败'}")
        
        # 测试获取可见视频源
        visible_sources = controller.get_all_visible_video_sources()
        print(f"模拟可见视频源: {visible_sources}")
        
        print("✅ 模拟模式测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 模拟模式测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始单一视频源显示功能测试\n")
    
    success = test_single_source_display()
    
    if success:
        print("\n🎉 测试成功！")
    else:
        print("\n⚠️ 测试失败")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
