#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试变量替换功能
验证游戏名称和游戏类型是否正确从系统设置中获取
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_variable_replacement():
    """测试变量替换功能"""
    print("🧪 测试变量替换功能...")
    
    # 模拟系统设置中的游戏信息
    test_cases = [
        {
            "text": "欢迎来到{gamename}的直播间！",
            "expected_contains": ["上古"],
            "description": "游戏名称替换"
        },
        {
            "text": "今天我们玩的是{gametype}游戏",
            "expected_contains": ["仙侠"],
            "description": "游戏类型替换"
        },
        {
            "text": "{gamename}是一款{gametype}游戏，现在时间是{time}",
            "expected_contains": ["上古", "仙侠"],
            "description": "混合变量替换"
        },
        {
            "text": "【欢迎|你好】来到{gamename}！",
            "expected_contains": ["上古"],
            "description": "随机选择+变量替换"
        }
    ]
    
    # 模拟变量替换方法
    def mock_replace_variables(text):
        """模拟变量替换方法"""
        import datetime
        import random
        import re
        
        # 模拟从系统设置获取的游戏信息
        game_name = "上古"  # 从日志中看到的恢复值
        game_type = "仙侠"  # 从日志中看到的恢复值
        
        # 获取当前时间信息
        now = datetime.datetime.now()
        current_date = now.strftime("%Y年%m月%d日")
        current_time = now.strftime("%H点%M分")
        
        # 模拟在线人数
        people_count = random.randint(100, 999)
        
        # 变量替换映射
        replacements = {
            '{nick}': '主播',
            '{date}': current_date,
            '{time}': current_time,
            '{people}': str(people_count),
            '{gift}': '礼物',
            '{gametype}': game_type,
            '{gamename}': game_name,
            '{user1}': '用户1',
            '{user2}': '用户2',
            '{user3}': '用户3'
        }
        
        # 执行替换
        result = text
        for var, value in replacements.items():
            result = result.replace(var, value)
        
        # 处理随机选择【选项1|选项2|选项3】
        def replace_random_choice(match):
            choices = match.group(1).split('|')
            return random.choice(choices)
        
        result = re.sub(r'【([^】]+)】', replace_random_choice, result)
        
        return result
    
    print("\n📋 测试用例:")
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. {case['description']}")
        print(f"   原文: {case['text']}")
        
        # 执行替换
        result = mock_replace_variables(case['text'])
        print(f"   结果: {result}")
        
        # 验证结果
        success = True
        for expected in case['expected_contains']:
            if expected not in result:
                print(f"   ❌ 缺少期望内容: {expected}")
                success = False
        
        if success:
            print(f"   ✅ 测试通过")
        else:
            print(f"   ❌ 测试失败")
    
    print("\n🎯 测试总结:")
    print("✅ 游戏名称变量 {gamename} 应该替换为: 上古")
    print("✅ 游戏类型变量 {gametype} 应该替换为: 仙侠")
    print("✅ 时间变量 {time} 应该替换为当前时间")
    print("✅ 随机选择 【选项1|选项2】 应该随机选择一个")

def test_system_settings_loading():
    """测试系统设置加载"""
    print("\n🔧 测试系统设置加载...")
    
    # 检查配置文件
    config_files = [
        "data/user_settings.json",
        "data/app_config.json",
        "data/system_settings.json"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ 配置文件存在: {config_file}")
            try:
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 检查游戏相关设置
                if 'game_name' in data:
                    print(f"   📋 游戏名称: {data['game_name']}")
                if 'game_type' in data:
                    print(f"   📋 游戏类型: {data['game_type']}")
                if 'game' in data and isinstance(data['game'], dict):
                    game_config = data['game']
                    if 'name' in game_config:
                        print(f"   📋 游戏名称(嵌套): {game_config['name']}")
                    if 'type' in game_config:
                        print(f"   📋 游戏类型(嵌套): {game_config['type']}")
                        
            except Exception as e:
                print(f"   ❌ 读取配置文件失败: {e}")
        else:
            print(f"⚠️ 配置文件不存在: {config_file}")

if __name__ == "__main__":
    print("🎮 变量替换功能测试")
    print("=" * 50)
    
    test_variable_replacement()
    test_system_settings_loading()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    
    print("\n💡 如果在实际程序中变量替换不正确，请检查：")
    print("1. 系统设置是否正确保存")
    print("2. 恢复设置时是否正确加载到界面控件")
    print("3. 变量替换方法是否从正确的控件获取值")
