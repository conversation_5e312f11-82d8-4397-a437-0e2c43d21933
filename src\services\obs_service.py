"""
AI主播系统 v2 - OBS WebSocket服务
OBS Studio的WebSocket控制
"""

import asyncio
import json
import websockets
import hashlib
import base64
from typing import Dict, Any, List, Optional
from datetime import datetime

from .logging_service import create_logger
from .error_handler import handle_exceptions


class OBSService:
    """OBS WebSocket服务"""
    
    def __init__(self, host: str = "localhost", port: int = 4455, password: str = ""):
        self.logger = create_logger("obs_service")
        self.host = host
        self.port = port
        self.password = password
        self.websocket_url = f"ws://{host}:{port}"
        
        self.websocket = None
        self.is_connected = False
        self.request_id = 0
        
        # 存储待处理的请求
        self.pending_requests = {}
        
    @handle_exceptions("OBSService")
    async def connect(self) -> bool:
        """
        连接到OBS WebSocket服务器
        
        Returns:
            是否连接成功
        """
        try:
            self.logger.info(f"连接OBS服务器: {self.websocket_url}")
            
            # 建立WebSocket连接
            self.websocket = await websockets.connect(
                self.websocket_url,
                ping_interval=30,
                ping_timeout=10
            )
            
            # 进行认证
            if await self._authenticate():
                self.is_connected = True
                self.logger.info("OBS服务器连接并认证成功")
                return True
            else:
                await self.websocket.close()
                self.websocket = None
                return False
                
        except Exception as e:
            self.logger.error(f"连接OBS服务器失败: {e}")
            self.is_connected = False
            return False
            
    @handle_exceptions("OBSService")
    async def disconnect(self):
        """断开WebSocket连接"""
        try:
            if self.websocket:
                await self.websocket.close()
                self.websocket = None
                
            self.is_connected = False
            self.logger.info("OBS服务器连接已断开")
            
        except Exception as e:
            self.logger.error(f"断开OBS连接失败: {e}")
            
    @handle_exceptions("OBSService")
    async def _authenticate(self) -> bool:
        """进行OBS WebSocket认证"""
        try:
            # 发送Hello消息
            hello_message = {
                "op": 1,
                "d": {
                    "rpcVersion": 1
                }
            }
            
            await self.websocket.send(json.dumps(hello_message))
            
            # 接收Hello响应
            response = await self.websocket.recv()
            hello_response = json.loads(response)
            
            if hello_response.get("op") != 0:
                self.logger.error("收到无效的Hello响应")
                return False
                
            # 检查是否需要认证
            auth_required = hello_response.get("d", {}).get("authentication")
            
            if auth_required and self.password:
                # 进行认证
                challenge = auth_required.get("challenge")
                salt = auth_required.get("salt")
                
                # 生成认证字符串
                secret = base64.b64encode(
                    hashlib.sha256((self.password + salt).encode()).digest()
                ).decode()
                
                auth_response = base64.b64encode(
                    hashlib.sha256((secret + challenge).encode()).digest()
                ).decode()
                
                # 发送认证消息
                identify_message = {
                    "op": 1,
                    "d": {
                        "rpcVersion": 1,
                        "authentication": auth_response
                    }
                }
                
                await self.websocket.send(json.dumps(identify_message))
                
                # 接收认证响应
                auth_resp = await self.websocket.recv()
                auth_result = json.loads(auth_resp)
                
                if auth_result.get("op") != 2:
                    self.logger.error("OBS认证失败")
                    return False
                    
            elif auth_required and not self.password:
                self.logger.error("OBS需要密码认证，但未提供密码")
                return False
            else:
                # 不需要认证，发送Identify消息
                identify_message = {
                    "op": 1,
                    "d": {
                        "rpcVersion": 1
                    }
                }
                
                await self.websocket.send(json.dumps(identify_message))
                
                # 接收Identified响应
                identified_resp = await self.websocket.recv()
                identified_result = json.loads(identified_resp)
                
                if identified_result.get("op") != 2:
                    self.logger.error("OBS连接失败")
                    return False
                    
            self.logger.info("OBS认证成功")
            return True
            
        except Exception as e:
            self.logger.error(f"OBS认证失败: {e}")
            return False
            
    @handle_exceptions("OBSService")
    async def send_request(self, request_type: str, request_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        发送请求到OBS
        
        Args:
            request_type: 请求类型
            request_data: 请求数据
            
        Returns:
            响应结果
        """
        if not self.is_connected or not self.websocket:
            return {
                'success': False,
                'message': '未连接到OBS服务器'
            }
            
        try:
            # 生成请求ID
            self.request_id += 1
            request_id = str(self.request_id)
            
            # 构建请求消息
            request_message = {
                "op": 6,
                "d": {
                    "requestType": request_type,
                    "requestId": request_id
                }
            }
            
            if request_data:
                request_message["d"]["requestData"] = request_data
                
            # 发送请求
            await self.websocket.send(json.dumps(request_message))
            
            # 等待响应
            response = await asyncio.wait_for(
                self.websocket.recv(),
                timeout=5.0
            )
            
            response_data = json.loads(response)
            
            # 检查响应
            if response_data.get("op") == 7:  # RequestResponse
                response_d = response_data.get("d", {})
                if response_d.get("requestId") == request_id:
                    if response_d.get("requestStatus", {}).get("result"):
                        return {
                            'success': True,
                            'data': response_d.get("responseData", {})
                        }
                    else:
                        return {
                            'success': False,
                            'message': response_d.get("requestStatus", {}).get("comment", "请求失败")
                        }
                        
            return {
                'success': False,
                'message': '收到无效响应'
            }
            
        except asyncio.TimeoutError:
            return {
                'success': False,
                'message': '请求超时'
            }
        except Exception as e:
            self.logger.error(f"发送OBS请求失败: {e}")
            return {
                'success': False,
                'message': f'请求失败: {str(e)}'
            }
            
    @handle_exceptions("OBSService")
    async def get_scene_list(self) -> Dict[str, Any]:
        """获取场景列表"""
        return await self.send_request("GetSceneList")
        
    @handle_exceptions("OBSService")
    async def set_current_scene(self, scene_name: str) -> Dict[str, Any]:
        """切换当前场景"""
        return await self.send_request("SetCurrentProgramScene", {
            "sceneName": scene_name
        })
        
    @handle_exceptions("OBSService")
    async def get_current_scene(self) -> Dict[str, Any]:
        """获取当前场景"""
        return await self.send_request("GetCurrentProgramScene")
        
    @handle_exceptions("OBSService")
    async def get_source_list(self) -> Dict[str, Any]:
        """获取源列表"""
        return await self.send_request("GetInputList")
        
    @handle_exceptions("OBSService")
    async def set_source_visibility(self, scene_name: str, source_name: str, visible: bool) -> Dict[str, Any]:
        """设置源的可见性"""
        return await self.send_request("SetSceneItemEnabled", {
            "sceneName": scene_name,
            "sceneItemId": source_name,
            "sceneItemEnabled": visible
        })
        
    @handle_exceptions("OBSService")
    async def start_recording(self) -> Dict[str, Any]:
        """开始录制"""
        return await self.send_request("StartRecord")
        
    @handle_exceptions("OBSService")
    async def stop_recording(self) -> Dict[str, Any]:
        """停止录制"""
        return await self.send_request("StopRecord")
        
    @handle_exceptions("OBSService")
    async def start_streaming(self) -> Dict[str, Any]:
        """开始推流"""
        return await self.send_request("StartStream")
        
    @handle_exceptions("OBSService")
    async def stop_streaming(self) -> Dict[str, Any]:
        """停止推流"""
        return await self.send_request("StopStream")
        
    def get_connection_status(self) -> Dict[str, Any]:
        """获取连接状态"""
        return {
            'connected': self.is_connected,
            'host': self.host,
            'port': self.port,
            'websocket_url': self.websocket_url
        }
        
    def set_connection_info(self, host: str, port: int, password: str = ""):
        """设置连接信息"""
        self.host = host
        self.port = port
        self.password = password
        self.websocket_url = f"ws://{host}:{port}"
        self.logger.info(f"设置OBS连接信息: {self.websocket_url}")
