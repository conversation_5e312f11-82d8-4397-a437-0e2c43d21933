"""
AI Broadcaster v2 - 播放控制器
统一的音频播放控制和管理
"""

import time
import threading
import random
from typing import Optional, Dict, Any, List, Callable
from enum import Enum

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions
from .audio_player import AudioPlayer
from .playlist_manager import PlaylistManager, PlaylistItem


class PlaybackState(Enum):
    """播放状态枚举"""
    STOPPED = "stopped"
    PLAYING = "playing"
    PAUSED = "paused"
    LOADING = "loading"


class PlaybackController:
    """播放控制器"""

    def __init__(self):
        self.logger = create_logger("playback_controller")

        # 核心组件
        self.audio_player = AudioPlayer()
        self.playlist_manager = PlaylistManager()

        # 播放状态
        self.state = PlaybackState.STOPPED
        self.auto_play_next = True
        self.play_interval_min = 1.0  # 最小播放间隔（秒）
        self.play_interval_max = 3.0  # 最大播放间隔（秒）

        # 自动播放控制
        self._auto_play_thread = None
        self._auto_play_stop_event = threading.Event()
        self._is_auto_playing = False

        # 回调函数
        self.on_state_change: Optional[Callable] = None
        self.on_track_change: Optional[Callable] = None
        self.on_playback_error: Optional[Callable] = None

        # 设置音频播放器回调
        self._setup_audio_callbacks()

        # 设置播放列表回调
        self._setup_playlist_callbacks()

    def _setup_audio_callbacks(self):
        """设置音频播放器回调"""
        self.audio_player.on_play_start = self._on_audio_play_start
        self.audio_player.on_play_end = self._on_audio_play_end
        self.audio_player.on_play_error = self._on_audio_play_error

    def _setup_playlist_callbacks(self):
        """设置播放列表回调"""
        self.playlist_manager.on_current_change = self._on_playlist_current_change

    def _on_audio_play_start(self):
        """音频开始播放回调"""
        self.state = PlaybackState.PLAYING
        self.logger.debug("音频开始播放")

        if self.on_state_change:
            self.on_state_change(self.state)

    def _on_audio_play_end(self):
        """音频播放结束回调"""
        self.logger.debug("音频播放结束")

        if self.auto_play_next:
            # 自动播放下一个
            self._play_next_auto()
        else:
            self.state = PlaybackState.STOPPED
            if self.on_state_change:
                self.on_state_change(self.state)

    def _on_audio_play_error(self, error_msg: str):
        """音频播放错误回调"""
        self.logger.error(f"音频播放错误: {error_msg}")
        self.state = PlaybackState.STOPPED

        if self.on_state_change:
            self.on_state_change(self.state)

        if self.on_playback_error:
            self.on_playback_error(error_msg)

    def _on_playlist_current_change(self, item: PlaylistItem):
        """播放列表当前项变化回调"""
        if self.on_track_change:
            self.on_track_change(item)

    @handle_exceptions("PlaybackController")
    def play(self, file_path: Optional[str] = None) -> bool:
        """
        播放音频

        Args:
            file_path: 音频文件路径，None则播放当前播放列表项

        Returns:
            是否成功开始播放
        """
        try:
            self.state = PlaybackState.LOADING

            if file_path:
                # 播放指定文件
                success = self.audio_player.play(file_path)
                if success:
                    self.logger.info(f"开始播放: {file_path}")
                return success
            else:
                # 播放当前播放列表项
                current_item = self.playlist_manager.get_current_item()
                if not current_item:
                    # 获取下一个播放项
                    current_item = self.playlist_manager.get_next_item()

                if current_item:
                    success = self.audio_player.play(current_item.file_path)
                    if success:
                        self.logger.info(f"开始播放: {current_item.title}")
                    return success
                else:
                    self.logger.warning("播放列表为空")
                    self.state = PlaybackState.STOPPED
                    return False

        except Exception as e:
            self.logger.error(f"播放失败: {e}")
            self.state = PlaybackState.STOPPED
            return False

    def pause(self):
        """暂停播放"""
        if self.state == PlaybackState.PLAYING:
            self.audio_player.pause()
            self.state = PlaybackState.PAUSED
            self.logger.info("播放已暂停")

            if self.on_state_change:
                self.on_state_change(self.state)

    def resume(self):
        """恢复播放"""
        if self.state == PlaybackState.PAUSED:
            self.audio_player.resume()
            self.state = PlaybackState.PLAYING
            self.logger.info("播放已恢复")

            if self.on_state_change:
                self.on_state_change(self.state)

    def stop(self):
        """停止播放"""
        self.audio_player.stop()
        self.state = PlaybackState.STOPPED
        self.logger.info("播放已停止")

        if self.on_state_change:
            self.on_state_change(self.state)

    def play_next(self) -> bool:
        """播放下一个"""
        next_item = self.playlist_manager.get_next_item()
        if next_item:
            return self.play(next_item.file_path)
        else:
            self.logger.info("没有下一个播放项")
            return False

    def play_previous(self) -> bool:
        """播放上一个"""
        prev_item = self.playlist_manager.get_previous_item()
        if prev_item:
            return self.play(prev_item.file_path)
        else:
            self.logger.info("没有上一个播放项")
            return False

    def _play_next_auto(self):
        """自动播放下一个（带延迟）"""
        def delayed_play():
            # 随机延迟
            delay = random.uniform(self.play_interval_min, self.play_interval_max)
            self.logger.debug(f"自动播放延迟: {delay:.1f}秒")

            time.sleep(delay)

            # 检查是否仍需要自动播放
            if self.auto_play_next and self.state != PlaybackState.PLAYING:
                self.play_next()

        # 在新线程中执行延迟播放
        thread = threading.Thread(target=delayed_play, daemon=True)
        thread.start()

    def start_auto_play(self):
        """开始自动播放模式"""
        if self._is_auto_playing:
            self.logger.warning("自动播放已在运行中")
            return

        self._auto_play_stop_event.clear()
        self._auto_play_thread = threading.Thread(target=self._auto_play_loop, daemon=True)
        self._auto_play_thread.start()
        self._is_auto_playing = True

        self.logger.info("自动播放模式已启动")

    def stop_auto_play(self):
        """停止自动播放模式"""
        if not self._is_auto_playing:
            return

        self._auto_play_stop_event.set()

        if self._auto_play_thread and self._auto_play_thread.is_alive():
            self._auto_play_thread.join(timeout=2.0)

        self._is_auto_playing = False
        self.logger.info("自动播放模式已停止")

    def _auto_play_loop(self):
        """自动播放循环"""
        while not self._auto_play_stop_event.is_set():
            try:
                # 如果当前没有播放，则开始播放
                if self.state == PlaybackState.STOPPED:
                    if self.playlist_manager.get_playlist_info()['total_items'] > 0:
                        self.play()

                # 等待一段时间再检查
                self._auto_play_stop_event.wait(1.0)

            except Exception as e:
                self.logger.error(f"自动播放循环异常: {e}")
                time.sleep(1.0)

    # 播放列表操作
    def add_to_playlist(self, file_path: str, title: str = "", metadata: Optional[Dict[str, Any]] = None) -> bool:
        """添加到播放列表"""
        return self.playlist_manager.add_item(file_path, title, metadata)

    def remove_from_playlist(self, index: int) -> bool:
        """从播放列表移除"""
        return self.playlist_manager.remove_item(index)

    def clear_playlist(self):
        """清空播放列表"""
        self.playlist_manager.clear_playlist()

    def set_current_track(self, index: int) -> bool:
        """设置当前播放曲目"""
        return self.playlist_manager.set_current_index(index)

    # 播放模式设置
    def set_shuffle_mode(self, enabled: bool):
        """设置随机播放模式"""
        self.playlist_manager.set_shuffle_mode(enabled)

    def set_repeat_mode(self, enabled: bool):
        """设置重复播放模式"""
        self.playlist_manager.set_repeat_mode(enabled)

    def set_auto_play_next(self, enabled: bool):
        """设置自动播放下一个"""
        self.auto_play_next = enabled
        self.logger.info(f"自动播放下一个: {'开启' if enabled else '关闭'}")

    def set_play_interval(self, min_seconds: float, max_seconds: float):
        """设置播放间隔"""
        self.play_interval_min = min_seconds
        self.play_interval_max = max_seconds
        self.logger.info(f"播放间隔设置为: {min_seconds}-{max_seconds}秒")

    # 音频设置
    def set_volume(self, volume: float):
        """设置音量"""
        self.audio_player.set_volume(volume)

    def set_output_device(self, device_index: Optional[int]):
        """设置输出设备"""
        self.audio_player.set_output_device(device_index)

    def get_audio_devices(self) -> List[Dict[str, Any]]:
        """获取音频设备列表"""
        return self.audio_player.get_audio_devices()

    # 状态查询
    def get_state(self) -> PlaybackState:
        """获取播放状态"""
        return self.state

    def is_playing(self) -> bool:
        """是否正在播放"""
        return self.state == PlaybackState.PLAYING

    def is_paused(self) -> bool:
        """是否已暂停"""
        return self.state == PlaybackState.PAUSED

    def get_current_track(self) -> Optional[Dict[str, Any]]:
        """获取当前播放曲目"""
        current_item = self.playlist_manager.get_current_item()
        return current_item.to_dict() if current_item else None

    def get_playlist_info(self) -> Dict[str, Any]:
        """获取播放列表信息"""
        return self.playlist_manager.get_playlist_info()

    def get_playback_info(self) -> Dict[str, Any]:
        """获取播放信息"""
        player_info = self.audio_player.get_player_info()
        playlist_info = self.get_playlist_info()
        current_track = self.get_current_track()

        return {
            'state': self.state.value,
            'current_track': current_track,
            'playlist': playlist_info,
            'player': player_info,
            'auto_play_next': self.auto_play_next,
            'is_auto_playing': self._is_auto_playing,
            'play_interval': {
                'min': self.play_interval_min,
                'max': self.play_interval_max
            }
        }

    def close(self):
        """关闭播放控制器"""
        self.stop_auto_play()
        self.stop()
        self.audio_player.close()
        self.logger.info("播放控制器已关闭")
