#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试话术上传修复
验证服务器500错误的修复
"""

import sys
import json
import requests
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_upload_formats():
    """测试不同的上传格式"""
    print("🧪 测试话术上传格式修复")
    print("=" * 60)
    
    server_url = "http://**************:12456"
    
    # 测试数据
    test_data = {
        "0秒 - 60秒": "0-60",
        "60秒 - 120秒": "60-120"
    }
    
    script_name = "xy-cs-test"
    
    # 测试1: JSON对象格式（修复前的格式）
    print("\n📤 测试1: JSON对象格式")
    print("-" * 40)
    
    upload_request_1 = {
        "类型": "上传话术",
        "话术名": script_name,
        "上传数据": test_data  # JSON对象
    }
    
    print(f"📋 请求数据类型: {type(upload_request_1['上传数据'])}")
    print(f"📋 请求数据: {upload_request_1}")
    
    try:
        response = requests.post(f"{server_url}/", json=upload_request_1, timeout=10)
        print(f"📡 响应状态码: {response.status_code}")
        print(f"📡 响应内容: {response.text[:200]}...")
        
        if response.status_code == 200:
            print("✅ JSON对象格式上传成功")
        else:
            print("❌ JSON对象格式上传失败")
            
    except Exception as e:
        print(f"❌ JSON对象格式测试异常: {e}")
    
    # 测试2: JSON字符串格式（修复后的格式）
    print("\n📤 测试2: JSON字符串格式")
    print("-" * 40)
    
    upload_request_2 = {
        "类型": "上传话术",
        "话术名": script_name + "-str",
        "上传数据": json.dumps(test_data, ensure_ascii=False, indent=2)  # JSON字符串
    }
    
    print(f"📋 请求数据类型: {type(upload_request_2['上传数据'])}")
    print(f"📋 请求数据预览: {str(upload_request_2)[:300]}...")
    
    try:
        response = requests.post(f"{server_url}/", json=upload_request_2, timeout=10)
        print(f"📡 响应状态码: {response.status_code}")
        print(f"📡 响应内容: {response.text[:200]}...")
        
        if response.status_code == 200:
            print("✅ JSON字符串格式上传成功")
        else:
            print("❌ JSON字符串格式上传失败")
            
    except Exception as e:
        print(f"❌ JSON字符串格式测试异常: {e}")
    
    # 测试3: 普通文本格式
    print("\n📤 测试3: 普通文本格式")
    print("-" * 40)
    
    upload_request_3 = {
        "类型": "上传话术",
        "话术名": script_name + "-text",
        "上传数据": "这是一个普通的话术内容\n第二行内容\n第三行内容"
    }
    
    print(f"📋 请求数据类型: {type(upload_request_3['上传数据'])}")
    print(f"📋 请求数据: {upload_request_3}")
    
    try:
        response = requests.post(f"{server_url}/", json=upload_request_3, timeout=10)
        print(f"📡 响应状态码: {response.status_code}")
        print(f"📡 响应内容: {response.text[:200]}...")
        
        if response.status_code == 200:
            print("✅ 普通文本格式上传成功")
        else:
            print("❌ 普通文本格式上传失败")
            
    except Exception as e:
        print(f"❌ 普通文本格式测试异常: {e}")

def test_script_service():
    """测试修复后的ScriptService"""
    print("\n🧪 测试修复后的ScriptService")
    print("=" * 60)
    
    try:
        from src.services.script_service import ScriptService
        
        # 创建服务实例
        service = ScriptService("http://**************:12456")
        print("✅ ScriptService 创建成功")
        
        # 测试数据
        test_data = {
            "0秒 - 60秒": "测试内容1",
            "60秒 - 120秒": "测试内容2"
        }
        
        script_name = "xy-cs-service-test"
        
        # 测试上传
        print(f"\n📤 测试上传话术: {script_name}")
        result = service.upload_script(script_name, test_data)
        
        print(f"📊 上传结果: {result}")
        
        if result['success']:
            print("✅ ScriptService 上传成功")
        else:
            print(f"❌ ScriptService 上传失败: {result['message']}")
            
        return result['success']
        
    except Exception as e:
        print(f"❌ ScriptService 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 话术上传修复测试")
    print("=" * 80)
    
    # 测试1: 直接API调用
    test_upload_formats()
    
    # 测试2: ScriptService
    service_success = test_script_service()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 测试总结")
    print("=" * 80)
    
    print("🔧 修复内容:")
    print("1. 将JSON对象格式转换为JSON字符串格式")
    print("2. 确保服务器接收到正确的数据格式")
    print("3. 保持向后兼容性")
    
    print(f"\n📊 ScriptService测试: {'✅ 通过' if service_success else '❌ 失败'}")
    
    if service_success:
        print("\n🎉 修复成功！现在可以正常上传话术了！")
        print("\n💡 修复说明:")
        print("- 服务器期望接收JSON字符串格式，而不是JSON对象")
        print("- 已修改ScriptService将JSON对象转换为字符串")
        print("- 保持了对所有格式的兼容性")
    else:
        print("\n❌ 修复可能不完整，需要进一步调试")

if __name__ == "__main__":
    main()
