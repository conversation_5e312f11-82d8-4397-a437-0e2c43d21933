#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
副视频功能演示程序
让用户直观看到副视频触发效果
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_sub_video_demo():
    """副视频功能演示"""
    print("🎬 副视频功能演示程序")
    print("=" * 50)
    
    try:
        # 导入主程序
        from run_gui_qt5 import MainWindow
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import QTimer
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建主窗口
        user_info = {
            'username': 'demo_user',
            'user_id': 1,
            'expire_time': '2025-12-31'
        }
        window = MainWindow(user_info)
        
        # 演示步骤计数器
        demo_step = 0
        
        def next_demo_step():
            nonlocal demo_step
            demo_step += 1
            
            if demo_step == 1:
                show_step1()
            elif demo_step == 2:
                show_step2()
            elif demo_step == 3:
                show_step3()
            elif demo_step == 4:
                show_step4()
            elif demo_step == 5:
                show_step5()
            else:
                finish_demo()
        
        def show_step1():
            """步骤1：配置副视频"""
            print("\n📋 步骤1：配置副视频关键词")
            
            # 确保副视频配置
            if hasattr(window, 'sub_video_manager') and hasattr(window.sub_video_manager, 'sub_videos'):
                sub_videos_data = window.sub_video_manager.sub_videos
                
                if isinstance(sub_videos_data, dict):
                    # 添加多个测试关键词
                    test_configs = {
                        '火箭': {
                            'keyword': '火箭',
                            'video_source': '火箭特效源',
                            'scripts': ['感谢{nick}的火箭支持！'],
                            'used_scripts': [],
                            'created_at': None,
                            'last_triggered': None,
                            'trigger_count': 0
                        },
                        '礼物': {
                            'keyword': '礼物',
                            'video_source': '礼物特效源',
                            'scripts': ['谢谢{nick}的礼物！'],
                            'used_scripts': [],
                            'created_at': None,
                            'last_triggered': None,
                            'trigger_count': 0
                        },
                        '666': {
                            'keyword': '666',
                            'video_source': '666特效源',
                            'scripts': ['{nick}说666，太棒了！'],
                            'used_scripts': [],
                            'created_at': None,
                            'last_triggered': None,
                            'trigger_count': 0
                        }
                    }
                    
                    for keyword, config in test_configs.items():
                        sub_videos_data[keyword] = config
                        print(f"✅ 配置副视频: {keyword} -> {config['video_source']}")
            
            # 显示消息框
            msg = QMessageBox()
            msg.setWindowTitle("副视频演示 - 步骤1")
            msg.setText("✅ 副视频关键词配置完成！\n\n已配置关键词：\n• 火箭 → 火箭特效源\n• 礼物 → 礼物特效源\n• 666 → 666特效源\n\n点击确定继续下一步")
            msg.exec_()
            
            QTimer.singleShot(500, next_demo_step)
        
        def show_step2():
            """步骤2：测试副视频触发"""
            print("\n🔍 步骤2：测试副视频触发检测")
            
            test_messages = [
                "感谢老板的火箭，太给力了！",
                "谢谢大家的礼物支持",
                "主播666，继续加油！",
                "普通弹幕，没有关键词"
            ]
            
            results = []
            for msg in test_messages:
                if hasattr(window, 'check_sub_video_trigger'):
                    result = window.check_sub_video_trigger(msg)
                    results.append(f"弹幕：{msg}\n副视频：{result if result else '无'}")
                    print(f"📝 测试弹幕: {msg}")
                    print(f"🔍 副视频结果: {result if result else '无'}")
            
            # 显示测试结果
            msg = QMessageBox()
            msg.setWindowTitle("副视频演示 - 步骤2")
            msg.setText("🔍 副视频触发测试结果：\n\n" + "\n\n".join(results) + "\n\n点击确定继续下一步")
            msg.exec_()
            
            QTimer.singleShot(500, next_demo_step)
        
        def show_step3():
            """步骤3：添加弹幕到播放列表"""
            print("\n🎬 步骤3：添加弹幕话术到播放列表")
            
            test_danmaku = "感谢老板的火箭，太给力了！"
            
            # 检测副视频
            sub_video_result = None
            if hasattr(window, 'check_sub_video_trigger'):
                sub_video_result = window.check_sub_video_trigger(test_danmaku)
            
            # 添加到播放列表
            if hasattr(window, 'add_danmaku_to_playlist'):
                window.add_danmaku_to_playlist(test_danmaku, sub_video_result)
                print(f"✅ 弹幕话术已添加: {test_danmaku}")
                print(f"✅ 副视频源: {sub_video_result}")
            
            # 检查播放列表
            playlist_info = "播放列表为空"
            if hasattr(window, 'playlist_items'):
                for item in window.playlist_items:
                    if item.get('voice_type') == '弹幕话术':
                        content = item.get('content', '')[:30]
                        sub_video = item.get('sub_video', '无')
                        status = item.get('status', '未知')
                        playlist_info = f"内容：{content}...\n状态：{status}\n副视频：{sub_video}"
                        break
            
            msg = QMessageBox()
            msg.setWindowTitle("副视频演示 - 步骤3")
            msg.setText(f"🎬 弹幕话术添加成功！\n\n{playlist_info}\n\n点击确定继续下一步")
            msg.exec_()
            
            QTimer.singleShot(500, next_demo_step)
        
        def show_step4():
            """步骤4：模拟副视频播放"""
            print("\n🎬 步骤4：模拟副视频播放流程")
            
            # 创建测试项目
            test_item = {
                'id': 9999,
                'content': '感谢老板的火箭，太给力了！',
                'voice_type': '弹幕话术',
                'time_segment': '测试时间段',
                'status': '已下载',
                'filename': 'test_fire_rocket.wav',
                'sub_video': '火箭特效源'
            }
            
            print(f"🎬 模拟播放项目: {test_item['content']}")
            print(f"🎬 副视频源: {test_item['sub_video']}")
            
            # 测试副视频播放流程
            play_result = "未测试"
            if hasattr(window, 'handle_sub_video_playback'):
                try:
                    result = window.handle_sub_video_playback(test_item)
                    play_result = "成功启动" if result else "启动失败"
                    print(f"🎬 副视频播放流程: {play_result}")
                except Exception as e:
                    play_result = f"异常: {str(e)[:50]}"
                    print(f"❌ 副视频播放异常: {e}")
            
            msg = QMessageBox()
            msg.setWindowTitle("副视频演示 - 步骤4")
            msg.setText(f"🎬 副视频播放流程测试\n\n播放项目：{test_item['content'][:30]}...\n副视频源：{test_item['sub_video']}\n测试结果：{play_result}\n\n点击确定继续下一步")
            msg.exec_()
            
            QTimer.singleShot(500, next_demo_step)
        
        def show_step5():
            """步骤5：OBS切换演示"""
            print("\n🔄 步骤5：OBS切换功能演示")
            
            obs_results = []
            
            # 测试切换到副视频
            if hasattr(window, 'switch_to_sub_video_with_obs'):
                try:
                    result = window.switch_to_sub_video_with_obs('火箭特效源')
                    obs_results.append(f"切换到副视频：{'成功' if result else '失败'}")
                    print(f"🔄 切换到副视频: {'成功' if result else '失败'}")
                except Exception as e:
                    obs_results.append(f"切换到副视频：异常 {str(e)[:30]}")
                    print(f"❌ 切换到副视频异常: {e}")
            
            # 等待一下再切回
            time.sleep(1)
            
            # 测试切换回主视频
            if hasattr(window, 'switch_back_to_main_video_with_obs'):
                try:
                    result = window.switch_back_to_main_video_with_obs()
                    obs_results.append(f"切换回主视频：{'成功' if result else '失败'}")
                    print(f"🔄 切换回主视频: {'成功' if result else '失败'}")
                except Exception as e:
                    obs_results.append(f"切换回主视频：异常 {str(e)[:30]}")
                    print(f"❌ 切换回主视频异常: {e}")
            
            msg = QMessageBox()
            msg.setWindowTitle("副视频演示 - 步骤5")
            msg.setText(f"🔄 OBS切换功能测试\n\n" + "\n".join(obs_results) + "\n\n注意：如果OBS未连接，切换功能会显示失败，这是正常的。\n\n点击确定完成演示")
            msg.exec_()
            
            QTimer.singleShot(500, next_demo_step)
        
        def finish_demo():
            """完成演示"""
            print("\n🎉 副视频功能演示完成")
            
            msg = QMessageBox()
            msg.setWindowTitle("副视频演示完成")
            msg.setText("🎉 副视频功能演示完成！\n\n演示内容：\n✅ 副视频关键词配置\n✅ 副视频触发检测\n✅ 弹幕话术添加\n✅ 副视频播放流程\n✅ OBS切换控制\n\n副视频功能已经集成到主程序中，\n当用户发送包含关键词的弹幕时，\n系统会自动触发对应的副视频效果！")
            msg.exec_()
            
            app.quit()
        
        # 开始演示
        def start_demo():
            print("✅ 程序初始化完成，开始副视频功能演示...")
            
            msg = QMessageBox()
            msg.setWindowTitle("副视频功能演示")
            msg.setText("🎬 欢迎使用副视频功能演示程序！\n\n本演示将展示：\n• 副视频关键词配置\n• 弹幕触发副视频检测\n• 播放列表副视频标识\n• 副视频播放流程\n• OBS切换控制\n\n点击确定开始演示")
            msg.exec_()
            
            next_demo_step()
        
        # 3秒后开始演示
        QTimer.singleShot(3000, start_demo)
        
        # 显示窗口
        window.show()
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 演示程序失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sub_video_demo()
