"""
AI Broadcaster v2 - OBS控制器
基于测试程序的成功实现
"""

import asyncio
import websockets
import json
import hashlib
import base64
import threading
import time
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime

from .logging_service import create_logger
from .error_handler import handle_exceptions


class OBSController:
    """OBS控制器 - 基于测试程序的成功实现"""

    def __init__(self, host: str = "localhost", port: int = 4455, password: str = ""):
        self.logger = create_logger("obs_controller")
        self.host = host
        self.port = port
        self.password = password
        self.websocket_url = f"ws://{host}:{port}"

        # 连接状态
        self.websocket = None
        self.connected = False
        self.connecting = False
        self.is_authenticated = False

        # 消息处理
        self.request_id = 0
        self.pending_requests = {}

        # 视频源配置
        self.video_source_a = None
        self.video_source_b = None
        self.current_active_source = None

        # 速度设置
        self.min_speed = 0.5
        self.max_speed = 2.0

        # 回调函数
        self.on_connection_change: Optional[Callable] = None
        self.on_scene_change: Optional[Callable] = None

        # 事件循环
        self.loop = None
        self.loop_thread = None
        self.stop_event = threading.Event()

        # 媒体源列表
        self.media_sources = []
        self.scenes = []

        self.logger.info("OBS控制器初始化完成")

    def log(self, message):
        """打印日志"""
        self.logger.info(message)

    @handle_exceptions("OBSController")
    def connect(self) -> bool:
        """连接到OBS - 基于测试程序的成功实现"""
        if self.connected or self.connecting:
            return self.connected

        try:
            self.connecting = True
            self.log(f"正在连接到OBS WebSocket服务器: {self.websocket_url}")

            # 启动异步连接
            self.stop_event.clear()
            self.loop_thread = threading.Thread(target=self._run_async_connection, daemon=True)
            self.loop_thread.start()

            # 等待连接结果
            for _ in range(50):  # 等待5秒
                if self.connected:
                    return True
                time.sleep(0.1)

            self.log("连接OBS超时")
            self.connecting = False
            return False

        except Exception as e:
            self.log(f"连接OBS WebSocket服务器失败: {e}")
            self.connecting = False
            return False

    def _run_async_connection(self):
        """运行异步连接并保持事件循环运行"""
        try:
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)

            # 连接到OBS
            self.loop.run_until_complete(self._async_connect())

            # 如果连接成功，保持事件循环运行
            if self.connected:
                self.log("✅ 事件循环保持运行中...")
                # 运行事件循环直到停止
                self.loop.run_until_complete(self._keep_loop_running())

        except Exception as e:
            self.log(f"异步连接异常: {e}")
        finally:
            # 只有在明确停止时才关闭事件循环
            if self.loop and not self.loop.is_closed():
                self.loop.close()
                self.log("事件循环已关闭")

    async def _keep_loop_running(self):
        """保持事件循环运行"""
        try:
            while self.connected and not self.stop_event.is_set():
                await asyncio.sleep(0.1)
        except Exception as e:
            self.log(f"事件循环运行异常: {e}")

    async def _async_connect(self):
        """异步连接到OBS - 基于测试程序的成功实现"""
        try:
            self.log(f"正在连接到OBS: {self.websocket_url}")

            # 连接WebSocket
            self.websocket = await websockets.connect(self.websocket_url)
            self.log("WebSocket连接成功")

            # 等待Hello消息
            hello_message = await self.websocket.recv()
            hello_data = json.loads(hello_message)
            self.log(f"收到Hello消息: {hello_data}")

            if hello_data.get("op") == 0:  # Hello
                # 发送Identify消息
                await self._send_identify(hello_data.get("d", {}))

                # 等待Identified消息
                identified_message = await self.websocket.recv()
                identified_data = json.loads(identified_message)
                self.log(f"收到Identified消息: {identified_data}")

                if identified_data.get("op") == 2:  # Identified
                    self.connected = True
                    self.connecting = False
                    self.is_authenticated = True
                    self.log("✅ 成功连接到OBS WebSocket服务器")

                    # 开始监听消息
                    asyncio.create_task(self._listen_messages())

                    # 等待一下让监听开始
                    await asyncio.sleep(0.1)

                    # 测试基本功能
                    await self._test_basic_functions()

                    # 触发连接成功回调
                    if self.on_connection_change:
                        self.on_connection_change(True)

                    return True
                else:
                    self.log(f"❌ 身份验证失败: {identified_data}")
                    return False
            else:
                self.log(f"❌ 未收到Hello消息: {hello_data}")
                return False

        except ConnectionRefusedError:
            self.log("❌ 连接被拒绝 - 请确保OBS Studio正在运行且WebSocket服务器已启用")
            return False
        except websockets.exceptions.ConnectionClosed:
            self.log("❌ 连接已关闭")
            return False
        except Exception as e:
            self.log(f"❌ 连接失败: {e}")
            return False
        finally:
            if not self.connected:
                self.connecting = False
                if self.on_connection_change:
                    self.on_connection_change(False)

    async def _send_identify(self, hello_data):
        """发送身份验证消息"""
        identify_data = {
            "op": 1,  # Identify
            "d": {
                "rpcVersion": 1
            }
        }

        # 如果需要密码验证
        if "authentication" in hello_data and self.password:
            auth_data = hello_data["authentication"]
            challenge = auth_data.get("challenge", "")
            salt = auth_data.get("salt", "")

            self.log("需要密码验证")

            # 生成认证字符串
            secret = base64.b64encode(
                hashlib.sha256((self.password + salt).encode()).digest()
            ).decode()

            auth_response = base64.b64encode(
                hashlib.sha256((secret + challenge).encode()).digest()
            ).decode()

            identify_data["d"]["authentication"] = auth_response
        else:
            self.log("无需密码验证")

        await self.websocket.send(json.dumps(identify_data))
        self.log("已发送Identify消息")

    async def _send_request(self, request_type, request_data=None):
        """发送请求 - 基于测试程序的成功实现"""
        if not self.is_authenticated:
            self.log("❌ 未连接到OBS")
            return None

        self.request_id += 1
        request_id = str(self.request_id)

        request = {
            "op": 6,  # Request
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }

        try:
            # 创建Future等待响应
            future = asyncio.Future()
            self.pending_requests[request_id] = future

            await self.websocket.send(json.dumps(request))
            self.log(f"发送请求: {request_type}")

            # 等待响应
            result = await asyncio.wait_for(future, timeout=10.0)
            self.log(f"✅ 请求成功: {request_type}")
            return result

        except asyncio.TimeoutError:
            self.pending_requests.pop(request_id, None)
            self.log(f"❌ 请求超时: {request_type}")
            return None
        except Exception as e:
            self.pending_requests.pop(request_id, None)
            self.log(f"❌ 请求异常: {request_type} - {e}")
            return None

    async def _test_basic_functions(self):
        """测试基本功能 - 基于测试程序的成功实现"""
        if not self.is_authenticated:
            self.log("❌ 未连接，无法测试功能")
            return

        self.log("🔍 开始测试基本功能...")

        # 测试获取版本信息
        version_info = await self._send_request("GetVersion")
        if version_info:
            obs_version = version_info.get('obsVersion', 'Unknown')
            ws_version = version_info.get('obsWebSocketVersion', 'Unknown')
            self.log(f"✅ OBS版本: {obs_version}")
            self.log(f"✅ WebSocket版本: {ws_version}")

        # 测试获取场景列表
        scenes_info = await self._send_request("GetSceneList")
        if scenes_info:
            scenes = scenes_info.get("scenes", [])
            self.scenes = [scene.get("sceneName", "Unknown") for scene in scenes]
            self.log(f"✅ 找到 {len(scenes)} 个场景:")
            for scene_name in self.scenes:
                self.log(f"   - {scene_name}")

        # 测试获取输入源列表
        inputs_info = await self._send_request("GetInputList")
        if inputs_info:
            inputs = inputs_info.get("inputs", [])
            self.media_sources = []
            self.log(f"✅ 找到 {len(inputs)} 个输入源:")

            # 定义媒体源类型
            media_source_types = [
                'ffmpeg_source',      # 媒体源
                'vlc_source',         # VLC视频源
                'image_source',       # 图像源
                'slideshow_source',   # 图像幻灯片放映
                'color_source',       # 颜色源
                'text_gdiplus',       # 文本(GDI+)
                'text_ft2_source',    # 文本(FreeType 2)
            ]

            for inp in inputs:
                source_name = inp.get("inputName", "Unknown")
                source_kind = inp.get("inputKind", "")

                # 只添加媒体源类型，过滤掉音频设备
                if source_kind in media_source_types:
                    self.media_sources.append(source_name)
                    self.log(f"   - {source_name} ({source_kind}) [媒体源]")
                else:
                    self.log(f"   - {source_name} ({source_kind}) [已过滤]")

    async def _listen_messages(self):
        """监听OBS消息"""
        try:
            while self.connected and not self.stop_event.is_set():
                message = await self.websocket.recv()
                data = json.loads(message)
                await self._handle_message(data)

        except websockets.exceptions.ConnectionClosed:
            self.log("OBS连接已断开")
            self.connected = False
            if self.on_connection_change:
                self.on_connection_change(False)
        except Exception as e:
            self.log(f"监听OBS消息异常: {e}")

    async def _handle_message(self, data: Dict[str, Any]):
        """处理OBS消息"""
        op = data.get("op")

        if op == 7:  # RequestResponse
            response_d = data.get("d", {})
            request_id = response_d.get("requestId")
            if request_id and request_id in self.pending_requests:
                future = self.pending_requests.pop(request_id)
                if not future.cancelled():
                    if response_d.get("requestStatus", {}).get("result", False):
                        future.set_result(response_d.get("responseData", {}))
                    else:
                        error_msg = response_d.get("requestStatus", {}).get("comment", "未知错误")
                        self.log(f"❌ OBS请求失败: {error_msg}")
                        future.set_result(None)

        elif op == 5:  # Event
            event_type = data.get("d", {}).get("eventType")
            event_data = data.get("d", {}).get("eventData", {})

            # 处理场景切换事件
            if event_type == "CurrentProgramSceneChanged":
                scene_name = event_data.get("sceneName")
                self.log(f"场景切换到: {scene_name}")
                if self.on_scene_change:
                    self.on_scene_change(scene_name)

    @handle_exceptions("OBSController")
    def disconnect(self):
        """断开OBS连接"""
        try:
            self.stop_event.set()
            self.connected = False
            self.connecting = False
            self.is_authenticated = False

            if self.websocket and self.loop:
                asyncio.run_coroutine_threadsafe(self._disconnect_async(), self.loop)

            if self.loop_thread and self.loop_thread.is_alive():
                self.loop_thread.join(timeout=2.0)

            # 清空媒体源列表
            self.media_sources = []
            self.scenes = []

            self.log("已断开OBS连接")

            # 触发连接状态回调
            if self.on_connection_change:
                self.on_connection_change(False)

        except Exception as e:
            self.log(f"断开OBS连接异常: {e}")

    async def _disconnect_async(self):
        """异步断开连接"""
        if self.websocket:
            await self.websocket.close()
            self.log("已断开WebSocket连接")

    async def _send_request(self, request_type: str, request_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """发送请求到OBS"""
        if not self.connected or not self.websocket:
            raise Exception("OBS未连接")

        self.request_id += 1
        request = {
            "op": 6,  # Request
            "d": {
                "requestType": request_type,
                "requestId": str(self.request_id),
                "requestData": request_data or {}
            }
        }

        # 创建Future等待响应
        future = asyncio.Future()
        self.pending_requests[str(self.request_id)] = future

        # 发送请求
        await self.websocket.send(json.dumps(request))

        # 等待响应
        try:
            response = await asyncio.wait_for(future, timeout=5.0)
            return response
        except asyncio.TimeoutError:
            self.pending_requests.pop(str(self.request_id), None)
            raise Exception("OBS请求超时")

    def send_request_sync(self, request_type: str, request_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """同步发送请求"""
        if not self.loop:
            raise Exception("事件循环未启动")

        future = asyncio.run_coroutine_threadsafe(
            self._send_request(request_type, request_data), self.loop
        )
        return future.result(timeout=10)

    def get_scene_list(self) -> List[str]:
        """获取场景列表"""
        try:
            if not self.connected:
                self.log("OBS未连接，无法获取场景列表")
                return []

            # 返回已加载的场景列表
            self.log(f"返回OBS场景列表: {len(self.scenes)} 个场景")
            return self.scenes
        except Exception as e:
            self.log(f"获取场景列表失败: {e}")
            return []

    def get_source_list(self) -> List[str]:
        """获取所有输入源列表（包括媒体源、图像源、文本源等）"""
        try:
            if not self.connected:
                self.log("OBS未连接，无法获取源列表")
                return []

            # 返回已加载的所有输入源列表
            self.log(f"返回OBS输入源列表: {len(self.media_sources)} 个源")
            return self.media_sources
        except Exception as e:
            self.log(f"获取输入源列表失败: {e}")
            return []

    def set_video_sources(self, source_a: str, source_b: str):
        """设置视频源A和B"""
        self.video_source_a = source_a
        self.video_source_b = source_b
        self.current_active_source = source_a
        self.logger.info(f"设置视频源: A={source_a}, B={source_b}")

    def switch_to_source_a(self):
        """切换到视频源A"""
        try:
            if self.video_source_a:
                self._show_source(self.video_source_a)
                self._hide_source(self.video_source_b)
                self.current_active_source = self.video_source_a
                self.logger.info("切换到视频源A")
        except Exception as e:
            self.logger.error(f"切换到视频源A失败: {e}")

    def switch_to_source_b(self):
        """切换到视频源B"""
        try:
            if self.video_source_b:
                self._show_source(self.video_source_b)
                self._hide_source(self.video_source_a)
                self.current_active_source = self.video_source_b
                self.logger.info("切换到视频源B")
        except Exception as e:
            self.logger.error(f"切换到视频源B失败: {e}")

    def _show_source(self, source_name: str):
        """显示源"""
        if source_name:
            try:
                # 🔥 修复：获取当前场景名称
                current_scene = self.send_request_sync("GetCurrentProgramScene", {})
                if current_scene and "currentProgramSceneName" in current_scene:
                    scene_name = current_scene["currentProgramSceneName"]

                    self.send_request_sync("SetSceneItemEnabled", {
                        "sceneName": scene_name,
                        "sceneItemId": self._get_source_id(source_name, scene_name),
                        "sceneItemEnabled": True
                    })
                    self.log(f"✅ 显示源: {source_name}")
                else:
                    self.log("❌ 无法获取当前场景名称")
            except Exception as e:
                self.log(f"❌ 显示源失败: {e}")

    def _hide_source(self, source_name: str):
        """隐藏源"""
        if source_name:
            try:
                # 🔥 修复：获取当前场景名称
                current_scene = self.send_request_sync("GetCurrentProgramScene", {})
                if current_scene and "currentProgramSceneName" in current_scene:
                    scene_name = current_scene["currentProgramSceneName"]

                    self.send_request_sync("SetSceneItemEnabled", {
                        "sceneName": scene_name,
                        "sceneItemId": self._get_source_id(source_name, scene_name),
                        "sceneItemEnabled": False
                    })
                    self.log(f"✅ 隐藏源: {source_name}")
                else:
                    self.log("❌ 无法获取当前场景名称")
            except Exception as e:
                self.log(f"❌ 隐藏源失败: {e}")

    def _get_source_id(self, source_name: str, scene_name: str = None) -> int:
        """获取源ID"""
        try:
            # 🔥 修复：获取当前场景名称
            if not scene_name:
                current_scene = self.send_request_sync("GetCurrentProgramScene", {})
                if current_scene and "currentProgramSceneName" in current_scene:
                    scene_name = current_scene["currentProgramSceneName"]
                else:
                    self.log("❌ 无法获取当前场景名称")
                    return 0

            # 🔥 修复：通过OBS WebSocket获取场景项目ID
            scene_items = self.send_request_sync("GetSceneItemList", {
                "sceneName": scene_name
            })

            if scene_items and "sceneItems" in scene_items:
                for item in scene_items["sceneItems"]:
                    if item.get("sourceName") == source_name:
                        source_id = item.get("sceneItemId", 0)
                        self.log(f"✅ 找到源 '{source_name}' 的ID: {source_id}")
                        return source_id

            self.log(f"⚠️ 未找到源 '{source_name}' 的ID，返回默认值0")
            return 0

        except Exception as e:
            self.log(f"❌ 获取源ID失败: {e}")
            return 0

    def set_source_speed(self, source_name: str, speed: float):
        """设置源播放速度"""
        try:
            # 限制速度范围
            speed = max(self.min_speed, min(self.max_speed, speed))

            self.send_request_sync("SetInputSettings", {
                "inputName": source_name,
                "inputSettings": {
                    "speed_percent": int(speed * 100)
                }
            })

            self.logger.info(f"设置{source_name}播放速度: {speed}")

        except Exception as e:
            self.logger.error(f"设置源播放速度失败: {e}")

    def set_speed_range(self, min_speed: float, max_speed: float):
        """设置速度范围"""
        self.min_speed = max(0.1, min_speed)
        self.max_speed = min(5.0, max_speed)
        self.logger.info(f"设置速度范围: {self.min_speed} - {self.max_speed}")

    async def get_media_status(self, source_name: str) -> Dict[str, Any]:
        """获取媒体源状态"""
        try:
            if not self.connected:
                self.log("OBS未连接，无法获取媒体状态")
                return {}

            # 获取媒体输入状态
            media_status = await self._send_request("GetMediaInputStatus", {
                "inputName": source_name
            })

            if not media_status:
                return {}

            # 获取输入源设置（包含文件路径等信息）
            input_settings = await self._send_request("GetInputSettings", {
                "inputName": source_name
            })

            # 组合状态信息，确保数值类型安全
            media_duration = media_status.get('mediaDuration')
            media_cursor = media_status.get('mediaCursor')

            # 安全转换为数字，处理None值
            if media_duration is None:
                media_duration = 0
            else:
                try:
                    media_duration = int(media_duration)
                except (ValueError, TypeError):
                    media_duration = 0

            if media_cursor is None:
                media_cursor = 0
            else:
                try:
                    media_cursor = int(media_cursor)
                except (ValueError, TypeError):
                    media_cursor = 0

            status_info = {
                'source_name': source_name,
                'media_state': media_status.get('mediaState', 'unknown'),
                'media_duration': media_duration,
                'media_cursor': media_cursor,
                'media_ended': media_status.get('mediaEnded', False),
                'media_playing': media_status.get('mediaState') == 'OBS_MEDIA_STATE_PLAYING',
                'media_paused': media_status.get('mediaState') == 'OBS_MEDIA_STATE_PAUSED',
                'media_stopped': media_status.get('mediaState') == 'OBS_MEDIA_STATE_STOPPED',
            }

            # 添加文件信息
            if input_settings:
                settings = input_settings.get('inputSettings', {})
                status_info.update({
                    'file_path': settings.get('local_file', ''),
                    'loop': settings.get('looping', False),
                    'restart_on_activate': settings.get('restart_on_activate', False),
                    'speed_percent': settings.get('speed_percent', 100),
                })

            # 计算进度百分比
            if status_info['media_duration'] > 0:
                status_info['progress_percent'] = (status_info['media_cursor'] / status_info['media_duration']) * 100
            else:
                status_info['progress_percent'] = 0

            # 格式化时间显示
            status_info['current_time_formatted'] = self._format_time(status_info['media_cursor'])
            status_info['total_time_formatted'] = self._format_time(status_info['media_duration'])

            self.log(f"媒体源 {source_name} 状态: {status_info['media_state']}, "
                    f"进度: {status_info['current_time_formatted']}/{status_info['total_time_formatted']} "
                    f"({status_info['progress_percent']:.1f}%)")

            return status_info

        except Exception as e:
            self.log(f"获取媒体源状态异常: {e}")
            return {}

    def _format_time(self, milliseconds) -> str:
        """格式化时间显示（毫秒转换为 HH:MM:SS）"""
        try:
            # 安全处理None值和非数字值
            if milliseconds is None:
                return "00:00:00"

            # 尝试转换为整数
            try:
                milliseconds = int(milliseconds)
            except (ValueError, TypeError):
                return "00:00:00"

            if milliseconds <= 0:
                return "00:00:00"

            seconds = milliseconds // 1000
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            seconds = seconds % 60

            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        except Exception as e:
            self.log(f"格式化时间异常: {e}")
            return "00:00:00"

    def get_main_video_status_sync(self) -> Dict[str, Any]:
        """同步获取主视频（视频源A）的状态"""
        try:
            if not self.video_source_a:
                self.log("未设置主视频源")
                return {}

            # 在事件循环中运行异步方法
            if self.loop and self.loop.is_running():
                future = asyncio.run_coroutine_threadsafe(
                    self.get_media_status(self.video_source_a),
                    self.loop
                )
                return future.result(timeout=5.0)
            else:
                self.log("异步事件循环未运行")
                return {}

        except Exception as e:
            self.log(f"获取主视频状态异常: {e}")
            return {}

    def get_sub_video_status_sync(self) -> Dict[str, Any]:
        """同步获取副视频（视频源B）的状态"""
        try:
            if not self.video_source_b:
                self.log("未设置副视频源")
                return {}

            # 在事件循环中运行异步方法
            if self.loop and self.loop.is_running():
                future = asyncio.run_coroutine_threadsafe(
                    self.get_media_status(self.video_source_b),
                    self.loop
                )
                return future.result(timeout=5.0)
            else:
                self.log("异步事件循环未运行")
                return {}

        except Exception as e:
            self.log(f"获取副视频状态异常: {e}")
            return {}

    def get_media_status_sync(self, source_name: str) -> Dict[str, Any]:
        """同步获取媒体源状态"""
        try:
            if not self.connected:
                self.log("OBS未连接，无法获取媒体状态")
                return {}

            # 在事件循环中运行异步方法
            if self.loop and self.loop.is_running():
                future = asyncio.run_coroutine_threadsafe(
                    self.get_media_status(source_name),
                    self.loop
                )
                return future.result(timeout=5.0)
            else:
                self.log("异步事件循环未运行")
                return {}

        except Exception as e:
            self.log(f"同步获取媒体状态异常: {e}")
            return {}

    def get_connection_status(self) -> Dict[str, Any]:
        """获取连接状态"""
        return {
            'connected': self.connected,
            'connecting': self.connecting,
            'host': self.host,
            'port': self.port,
            'video_source_a': self.video_source_a,
            'video_source_b': self.video_source_b,
            'current_active_source': self.current_active_source,
            'speed_range': {
                'min': self.min_speed,
                'max': self.max_speed
            }
        }


def main():
    """测试函数"""
    obs = OBSController()

    # 测试连接
    if obs.connect():
        print("✅ OBS连接成功")

        # 获取场景列表
        scenes = obs.get_scene_list()
        print(f"场景列表: {scenes}")

        # 获取源列表
        sources = obs.get_source_list()
        print(f"源列表: {sources}")

        # 等待一段时间
        time.sleep(2)

        # 断开连接
        obs.disconnect()
    else:
        print("❌ OBS连接失败")


if __name__ == "__main__":
    main()
