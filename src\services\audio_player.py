"""
AI Broadcaster v2 - 音频播放器
支持多种音频格式的播放和控制
"""

import os
import threading
import time
import random
from typing import Optional, Callable, Dict, Any
from pathlib import Path

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    print("⚠️ pygame未安装，音频播放功能受限")

try:
    import sounddevice as sd
    import soundfile as sf
    SOUNDDEVICE_AVAILABLE = True
except ImportError:
    SOUNDDEVICE_AVAILABLE = False
    print("⚠️ sounddevice/soundfile未安装，音频播放功能受限")

from .logging_service import create_logger
from .error_handler import handle_exceptions


class AudioPlayer:
    """音频播放器"""
    
    def __init__(self, device_name: str = None, volume: float = 0.8):
        self.logger = create_logger("audio_player")
        
        # 播放状态
        self.is_playing = False
        self.is_paused = False
        self.current_file = None
        self.current_position = 0.0
        self.total_duration = 0.0
        
        # 音频设置
        self.device_name = device_name
        self.volume = max(0.0, min(1.0, volume))
        
        # 播放线程
        self.play_thread = None
        self.stop_event = threading.Event()
        
        # 回调函数
        self.on_playback_start: Optional[Callable] = None
        self.on_playback_end: Optional[Callable] = None
        self.on_playback_pause: Optional[Callable] = None
        self.on_playback_resume: Optional[Callable] = None
        self.on_position_change: Optional[Callable] = None
        
        # 初始化音频系统
        self._init_audio_system()
        
        self.logger.info("音频播放器初始化完成")
    
    def _init_audio_system(self):
        """初始化音频系统"""
        try:
            if PYGAME_AVAILABLE:
                # 检查pygame是否已经初始化
                if not pygame.mixer.get_init():
                    # 使用与主程序一致的参数初始化
                    pygame.mixer.init(frequency=44100, size=-16, channels=2, buffer=4096)
                    self.logger.info("初始化pygame音频后端")
                else:
                    # 获取当前初始化参数
                    init_params = pygame.mixer.get_init()
                    self.logger.info(f"pygame已初始化，参数: {init_params}")

                self.audio_backend = 'pygame'
                self.logger.info("使用pygame音频后端")
            elif SOUNDDEVICE_AVAILABLE:
                self.audio_backend = 'sounddevice'
                self.logger.info("使用sounddevice音频后端")
            else:
                self.audio_backend = 'none'
                self.logger.warning("没有可用的音频后端")

        except Exception as e:
            self.logger.error(f"初始化音频系统失败: {e}")
            self.audio_backend = 'none'
    
    @handle_exceptions("AudioPlayer")
    def play_file(self, file_path: str, start_position: float = 0.0) -> bool:
        """播放音频文件"""
        try:
            if not Path(file_path).exists():
                self.logger.error(f"音频文件不存在: {file_path}")
                return False
            
            # 停止当前播放
            self.stop()
            
            self.current_file = file_path
            self.current_position = start_position
            self.is_playing = True
            self.is_paused = False
            self.stop_event.clear()
            
            # 启动播放线程
            self.play_thread = threading.Thread(
                target=self._play_thread_func, 
                args=(file_path, start_position),
                daemon=True
            )
            self.play_thread.start()
            
            self.logger.info(f"开始播放音频: {Path(file_path).name}")
            
            # 触发播放开始回调
            if self.on_playback_start:
                self.on_playback_start(file_path)
            
            return True
            
        except Exception as e:
            self.logger.error(f"播放音频文件失败: {e}")
            return False
    
    def _play_thread_func(self, file_path: str, start_position: float):
        """播放线程函数"""
        self.logger.info(f"播放线程开始 - 文件: {file_path}, 起始位置: {start_position}")
        try:
            if self.audio_backend == 'pygame':
                self._play_with_pygame(file_path, start_position)
            elif self.audio_backend == 'sounddevice':
                self._play_with_sounddevice(file_path, start_position)
            else:
                self.logger.warning("没有可用的音频后端，模拟播放")
                self._simulate_playback(file_path)

        except Exception as e:
            self.logger.error(f"播放线程异常: {e}")
        finally:
            self.is_playing = False
            self.is_paused = False

            self.logger.info(f"播放线程结束 - 文件: {file_path}")

            # 触发播放结束回调
            if self.on_playback_end:
                self.logger.info(f"触发播放结束回调 - 文件: {self.current_file}")
                self.on_playback_end(self.current_file)
            else:
                self.logger.warning("没有设置播放结束回调")
    
    def _play_with_pygame(self, file_path: str, start_position: float):
        """使用pygame播放音频"""
        try:
            self.logger.info(f"🔊 加载音频文件: {file_path}")
            pygame.mixer.music.load(file_path)
            pygame.mixer.music.set_volume(self.volume)
            self.logger.info(f"✅ 音频文件加载成功，音量设置为: {self.volume}")

            # 🔥 获取音频文件的实际时长
            import wave
            try:
                with wave.open(file_path, 'rb') as wav_file:
                    frames = wav_file.getnframes()
                    sample_rate = wav_file.getframerate()
                    audio_duration = frames / float(sample_rate)
                    self.logger.info(f"📏 音频文件实际时长: {audio_duration:.2f}秒")
            except Exception as e:
                self.logger.warning(f"⚠️ 无法获取音频时长: {e}")
                audio_duration = 30.0  # 默认最大时长30秒

            self.logger.info(f"🎵 开始播放，起始位置: {start_position}")
            pygame.mixer.music.play(start=start_position)

            # 检查播放是否成功启动
            initial_busy = pygame.mixer.music.get_busy()
            self.logger.info(f"📊 播放启动状态检查: busy={initial_busy}")

            if not initial_busy:
                self.logger.warning("⚠️ 播放启动失败，pygame.mixer.music.get_busy() 返回 False")
                return

            # 🔥 关键修复：使用基于时间的播放完成检测，不依赖pygame.mixer.music.get_busy()
            import time
            start_time = time.time()
            loop_count = 0
            max_duration = audio_duration + 2.0  # 允许2秒的缓冲时间

            self.logger.info(f"🔄 开始基于时间的播放监控，音频时长: {audio_duration:.2f}秒, 最大时长: {max_duration:.2f}秒")

            while not self.stop_event.is_set():
                # 计算实际播放时间
                elapsed_time = time.time() - start_time

                if not self.is_paused:
                    self.current_position = elapsed_time

                    # 触发位置变化回调
                    if self.on_position_change:
                        self.on_position_change(self.current_position)

                # 🔥 关键修复：基于时间检查播放是否完成
                if self.current_position >= audio_duration:
                    self.logger.info(f"✅ 播放时长达到音频文件时长，正常结束播放")
                    self.logger.info(f"✅ 当前位置: {self.current_position:.1f}秒, 音频时长: {audio_duration:.2f}秒")
                    pygame.mixer.music.stop()
                    break

                # 🔥 超时保护：检查播放时长是否超过最大允许时长
                if self.current_position > max_duration:
                    self.logger.warning(f"⚠️ 播放时长超过最大允许时长，强制结束播放")
                    self.logger.warning(f"⚠️ 当前位置: {self.current_position:.1f}秒, 最大时长: {max_duration:.2f}秒")
                    pygame.mixer.music.stop()
                    break

                # 🔥 检查pygame状态（仅用于日志，不用于控制循环）
                busy_status = pygame.mixer.music.get_busy()
                if not busy_status and self.current_position < audio_duration * 0.8:
                    self.logger.warning(f"⚠️ pygame报告播放已停止，但时间未到，可能出现问题")
                    self.logger.warning(f"⚠️ 当前位置: {self.current_position:.1f}秒, 音频时长: {audio_duration:.2f}秒, busy: {busy_status}")

                # 🔥 更频繁的调试信息（每1秒输出一次）
                loop_count += 1
                if loop_count % 10 == 0:  # 每1秒（10 * 0.1秒）
                    self.logger.info(f"🎵 播放中... 位置: {self.current_position:.1f}秒, busy: {busy_status}, 暂停: {self.is_paused}, 停止请求: {self.stop_event.is_set()}, 音频时长: {audio_duration:.2f}秒")

                time.sleep(0.1)

            self.logger.info(f"🔄 播放监控循环结束，最终位置: {self.current_position:.1f}秒")

            # 播放结束
            final_busy = pygame.mixer.music.get_busy()
            stop_requested = self.stop_event.is_set()
            self.logger.info(f"🏁 播放循环结束 - busy: {final_busy}, stop_requested: {stop_requested}, 播放时长: {self.current_position:.1f}秒, 音频时长: {audio_duration:.2f}秒")

        except Exception as e:
            self.logger.error(f"❌ pygame播放失败: {e}")
            import traceback
            self.logger.error(f"❌ 详细错误信息: {traceback.format_exc()}")
    
    def _play_with_sounddevice(self, file_path: str, start_position: float):
        """使用sounddevice播放音频"""
        try:
            # 读取音频文件
            data, sample_rate = sf.read(file_path)
            
            # 计算开始位置
            start_frame = int(start_position * sample_rate)
            if start_frame < len(data):
                data = data[start_frame:]
            
            self.total_duration = len(data) / sample_rate
            
            # 设置音量
            data = data * self.volume
            
            # 播放音频
            sd.play(data, sample_rate, device=self.device_name)
            
            # 监控播放状态
            start_time = time.time()
            while sd.get_stream().active and not self.stop_event.is_set():
                if not self.is_paused:
                    self.current_position = start_position + (time.time() - start_time)
                    
                    # 触发位置变化回调
                    if self.on_position_change:
                        self.on_position_change(self.current_position)
                
                time.sleep(0.1)
                
        except Exception as e:
            self.logger.error(f"sounddevice播放失败: {e}")
    
    def _simulate_playback(self, file_path: str):
        """模拟播放（用于测试）"""
        try:
            # 模拟播放时长（随机3-10秒）
            duration = random.uniform(3.0, 10.0)
            self.total_duration = duration
            
            start_time = time.time()
            while time.time() - start_time < duration and not self.stop_event.is_set():
                if not self.is_paused:
                    self.current_position = time.time() - start_time
                    
                    # 触发位置变化回调
                    if self.on_position_change:
                        self.on_position_change(self.current_position)
                
                time.sleep(0.1)
                
        except Exception as e:
            self.logger.error(f"模拟播放失败: {e}")
    
    def pause(self):
        """暂停播放"""
        if self.is_playing and not self.is_paused:
            self.is_paused = True
            
            if self.audio_backend == 'pygame':
                pygame.mixer.music.pause()
            elif self.audio_backend == 'sounddevice':
                sd.stop()
            
            self.logger.info("音频播放已暂停")
            
            # 触发暂停回调
            if self.on_playback_pause:
                self.on_playback_pause(self.current_file)
    
    def resume(self):
        """恢复播放"""
        if self.is_playing and self.is_paused:
            self.is_paused = False
            
            if self.audio_backend == 'pygame':
                pygame.mixer.music.unpause()
            elif self.audio_backend == 'sounddevice':
                # sounddevice需要重新播放
                if self.current_file:
                    self.play_file(self.current_file, self.current_position)
                    return
            
            self.logger.info("音频播放已恢复")
            
            # 触发恢复回调
            if self.on_playback_resume:
                self.on_playback_resume(self.current_file)
    
    def stop(self):
        """停止播放"""
        if self.is_playing:
            self.stop_event.set()
            self.is_playing = False
            self.is_paused = False
            
            if self.audio_backend == 'pygame':
                pygame.mixer.music.stop()
            elif self.audio_backend == 'sounddevice':
                sd.stop()
            
            # 等待播放线程结束
            if self.play_thread and self.play_thread.is_alive():
                self.play_thread.join(timeout=1.0)
            
            self.current_position = 0.0
            self.logger.info("音频播放已停止")
    
    def set_volume(self, volume: float):
        """设置音量"""
        self.volume = max(0.0, min(1.0, volume))
        
        if self.audio_backend == 'pygame' and self.is_playing:
            pygame.mixer.music.set_volume(self.volume)
        
        self.logger.debug(f"音量设置为: {self.volume:.2f}")
    
    def set_device(self, device_name: str):
        """设置音频设备"""
        self.device_name = device_name
        self.logger.info(f"音频设备设置为: {device_name}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取播放状态"""
        return {
            'is_playing': self.is_playing,
            'is_paused': self.is_paused,
            'current_file': self.current_file,
            'current_position': self.current_position,
            'total_duration': self.total_duration,
            'volume': self.volume,
            'device_name': self.device_name,
            'audio_backend': self.audio_backend
        }
    
    def get_supported_formats(self) -> list:
        """获取支持的音频格式"""
        if self.audio_backend == 'pygame':
            return ['.mp3', '.wav', '.ogg']
        elif self.audio_backend == 'sounddevice':
            return ['.wav', '.flac', '.aiff']
        else:
            return []
    
    def close(self):
        """关闭音频播放器"""
        self.stop()
        
        if self.audio_backend == 'pygame':
            pygame.mixer.quit()
        
        self.logger.info("音频播放器已关闭")


def main():
    """测试函数"""
    player = AudioPlayer()
    
    # 设置回调
    def on_start(file_path):
        print(f"🎵 开始播放: {file_path}")
    
    def on_end(file_path):
        print(f"⏹️ 播放结束: {file_path}")
    
    def on_position(position):
        print(f"⏱️ 播放位置: {position:.1f}秒")
    
    player.on_playback_start = on_start
    player.on_playback_end = on_end
    player.on_position_change = on_position
    
    # 测试播放（模拟）
    print("测试音频播放...")
    player.play_file("test_audio.wav")
    
    # 等待播放
    time.sleep(2)
    
    # 测试暂停
    print("暂停播放...")
    player.pause()
    time.sleep(1)
    
    # 测试恢复
    print("恢复播放...")
    player.resume()
    time.sleep(2)
    
    # 测试停止
    print("停止播放...")
    player.stop()
    
    # 获取状态
    status = player.get_status()
    print(f"播放器状态: {status}")
    
    # 关闭播放器
    player.close()


if __name__ == "__main__":
    main()
