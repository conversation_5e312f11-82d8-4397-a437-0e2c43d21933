#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的更新功能
验证所有导入问题都已解决
"""

import sys
import json
import requests
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_update_service_import():
    """测试更新服务导入"""
    print("🧪 测试更新服务导入")
    print("=" * 50)
    
    try:
        from src.services.update_service import UpdateService
        print("✅ UpdateService 导入成功")
        
        # 测试创建实例
        service = UpdateService("http://**************:12456")
        print("✅ UpdateService 实例创建成功")
        
        # 测试获取当前版本
        current_version = service.get_current_version_from_config()
        print(f"✅ 当前版本: {current_version}")
        
        return True, service
        
    except Exception as e:
        print(f"❌ UpdateService 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_simple_update_dialog_import():
    """测试简化更新对话框导入"""
    print("\n🧪 测试简化更新对话框导入")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.ui.simple_update_dialog import SimpleUpdateDialog
        print("✅ SimpleUpdateDialog 导入成功")
        
        # 创建应用实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 测试创建对话框
        mock_update_info = {
            'version': '2.2.2',
            'description': '测试更新内容',
            'release_date': '2025-06-13'
        }
        
        dialog = SimpleUpdateDialog(mock_update_info, '2.0.0')
        print("✅ SimpleUpdateDialog 实例创建成功")
        
        return True, dialog
        
    except Exception as e:
        print(f"❌ SimpleUpdateDialog 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_api_call():
    """测试API调用"""
    print("\n🧪 测试API调用")
    print("=" * 50)
    
    try:
        url = "http://**************:12456/admin/api/updates/current"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功")
            
            if data.get('状态') == '成功':
                update_data = data.get('数据', {})
                version = update_data.get('version', '')
                print(f"✅ 最新版本: {version}")
                return True, update_data
            else:
                print(f"❌ API返回失败状态: {data}")
                return False, None
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        return False, None

def test_complete_update_flow():
    """测试完整的更新流程"""
    print("\n🧪 测试完整更新流程")
    print("=" * 50)
    
    try:
        # 1. 导入更新服务
        from src.services.update_service import UpdateService
        service = UpdateService("http://**************:12456")
        
        # 2. 检查更新
        print("🔍 检查更新...")
        update_result = service.check_for_updates()
        
        if update_result['success']:
            print(f"✅ 更新检查成功")
            print(f"   有更新: {update_result['has_update']}")
            print(f"   当前版本: {update_result['current_version']}")
            
            if update_result['has_update']:
                info = update_result['update_info']
                print(f"   最新版本: {info['version']}")
                print(f"   发布日期: {info['release_date']}")
                print(f"   更新内容: {info['description'][:50]}...")
                
                # 3. 测试版本更新
                print("\n🔄 测试版本更新...")
                test_version = info['version']
                if service.update_local_version(test_version):
                    print(f"✅ 版本更新成功: {test_version}")
                    
                    # 恢复原版本
                    original_version = update_result['current_version']
                    service.update_local_version(original_version)
                    print(f"🔄 已恢复原版本: {original_version}")
                else:
                    print("❌ 版本更新失败")
                    
            return True
        else:
            print(f"❌ 更新检查失败: {update_result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 测试修复后的更新功能")
    print("=" * 80)
    
    # 测试结果
    results = {}
    
    # 测试1: 更新服务导入
    results['service_import'], service = test_update_service_import()
    
    # 测试2: 对话框导入
    results['dialog_import'], dialog = test_simple_update_dialog_import()
    
    # 测试3: API调用
    results['api_call'], api_data = test_api_call()
    
    # 测试4: 完整流程
    results['complete_flow'] = test_complete_update_flow()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 测试总结")
    print("=" * 80)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        test_display = {
            'service_import': '更新服务导入',
            'dialog_import': '对话框导入',
            'api_call': 'API调用',
            'complete_flow': '完整流程'
        }
        print(f"{test_display[test_name]}: {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print("\n🎉 所有测试通过！更新功能修复成功！")
        print("\n📝 修复内容:")
        print("1. ✅ 修复了相对导入问题")
        print("2. ✅ 修复了PyQt6/PyQt5混用问题")
        print("3. ✅ 创建了简化的更新对话框")
        print("4. ✅ 添加了导入错误处理")
        print("\n🚀 现在可以正常使用更新功能了！")
    else:
        print("\n❌ 部分测试失败，需要进一步修复")
        failed_tests = [name for name, result in results.items() if not result]
        print(f"失败的测试: {failed_tests}")

if __name__ == "__main__":
    main()
