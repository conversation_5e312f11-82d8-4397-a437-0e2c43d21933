#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 pygame 音频播放完成检测
"""

import pygame
import time
import os

def test_pygame_playback():
    """测试 pygame 播放完成检测"""
    print("🔧 初始化 pygame...")
    pygame.mixer.init()
    
    # 测试音频文件
    test_file = "voices/0230d44514a5dc9d.wav"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print(f"🎵 开始播放测试文件: {test_file}")
    
    try:
        # 加载并播放音频
        pygame.mixer.music.load(test_file)
        pygame.mixer.music.play()
        
        start_time = time.time()
        print(f"⏰ 播放开始时间: {time.strftime('%H:%M:%S')}")
        
        # 监控播放状态
        while pygame.mixer.music.get_busy():
            elapsed = time.time() - start_time
            print(f"⏱️ 播放中... 已播放 {elapsed:.1f} 秒")
            time.sleep(1.0)  # 每秒检查一次
        
        end_time = time.time()
        total_time = end_time - start_time
        print(f"✅ 播放完成！总播放时间: {total_time:.1f} 秒")
        print(f"⏰ 播放结束时间: {time.strftime('%H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ 播放失败: {e}")
    finally:
        pygame.mixer.quit()

if __name__ == "__main__":
    test_pygame_playback()
