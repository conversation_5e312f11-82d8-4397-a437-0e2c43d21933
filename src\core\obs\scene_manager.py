"""
AI直播系统 v2 - 场景管理器
OBS场景的管理和自动切换
"""

import time
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class SceneManager:
    """场景管理器"""
    
    def __init__(self, obs_controller):
        self.logger = create_logger("scene_manager")
        self.obs_controller = obs_controller
        
        # 场景配置
        self.scene_configs = {}  # 场景配置信息
        self.scene_schedules = []  # 场景调度计划
        self.auto_switch_enabled = False
        
        # 自动切换
        self.switch_thread = None
        self.stop_event = threading.Event()
        
        # 回调函数
        self.on_scene_switched: Optional[Callable] = None
        self.on_auto_switch_error: Optional[Callable] = None
        
        # 统计信息
        self.stats = {
            'total_switches': 0,
            'auto_switches': 0,
            'manual_switches': 0,
            'last_switch_time': None,
            'last_scene': None
        }
        
    @handle_exceptions("SceneManager")
    def add_scene_config(self, scene_name: str, config: Dict[str, Any]):
        """
        添加场景配置
        
        Args:
            scene_name: 场景名称
            config: 场景配置
                - description: 场景描述
                - auto_switch_duration: 自动切换持续时间（秒）
                - priority: 优先级
                - conditions: 切换条件
        """
        self.scene_configs[scene_name] = {
            'name': scene_name,
            'description': config.get('description', ''),
            'auto_switch_duration': config.get('auto_switch_duration', 30),
            'priority': config.get('priority', 1),
            'conditions': config.get('conditions', {}),
            'created_at': datetime.now(),
            'switch_count': 0
        }
        
        self.logger.info(f"添加场景配置: {scene_name}")
        
    @handle_exceptions("SceneManager")
    def remove_scene_config(self, scene_name: str):
        """移除场景配置"""
        if scene_name in self.scene_configs:
            del self.scene_configs[scene_name]
            self.logger.info(f"移除场景配置: {scene_name}")
            
    @handle_exceptions("SceneManager")
    def switch_to_scene(self, scene_name: str, auto_switch: bool = False) -> bool:
        """
        切换到指定场景
        
        Args:
            scene_name: 场景名称
            auto_switch: 是否为自动切换
            
        Returns:
            是否切换成功
        """
        try:
            if not self.obs_controller.is_connected:
                self.logger.error("OBS未连接，无法切换场景")
                return False
                
            success = self.obs_controller.switch_scene(scene_name)
            
            if success:
                # 更新统计
                self.stats['total_switches'] += 1
                if auto_switch:
                    self.stats['auto_switches'] += 1
                else:
                    self.stats['manual_switches'] += 1
                    
                self.stats['last_switch_time'] = datetime.now()
                self.stats['last_scene'] = scene_name
                
                # 更新场景配置统计
                if scene_name in self.scene_configs:
                    self.scene_configs[scene_name]['switch_count'] += 1
                    
                self.logger.info(f"切换到场景: {scene_name} ({'自动' if auto_switch else '手动'})")
                
                # 触发回调
                if self.on_scene_switched:
                    self.on_scene_switched(scene_name, auto_switch)
                    
                return True
            else:
                self.logger.error(f"切换场景失败: {scene_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"切换场景异常: {e}")
            return False
            
    @handle_exceptions("SceneManager")
    def add_scene_schedule(self, schedule: Dict[str, Any]):
        """
        添加场景调度计划
        
        Args:
            schedule: 调度计划
                - scene_name: 场景名称
                - start_time: 开始时间 (HH:MM)
                - end_time: 结束时间 (HH:MM)
                - days_of_week: 星期几 (1-7，逗号分隔)
                - priority: 优先级
                - enabled: 是否启用
        """
        schedule_item = {
            'id': len(self.scene_schedules) + 1,
            'scene_name': schedule['scene_name'],
            'start_time': schedule['start_time'],
            'end_time': schedule['end_time'],
            'days_of_week': schedule.get('days_of_week', '1,2,3,4,5,6,7'),
            'priority': schedule.get('priority', 1),
            'enabled': schedule.get('enabled', True),
            'created_at': datetime.now()
        }
        
        self.scene_schedules.append(schedule_item)
        self.scene_schedules.sort(key=lambda x: x['priority'], reverse=True)
        
        self.logger.info(f"添加场景调度: {schedule['scene_name']} {schedule['start_time']}-{schedule['end_time']}")
        
    @handle_exceptions("SceneManager")
    def remove_scene_schedule(self, schedule_id: int):
        """移除场景调度计划"""
        self.scene_schedules = [s for s in self.scene_schedules if s['id'] != schedule_id]
        self.logger.info(f"移除场景调度: ID {schedule_id}")
        
    @handle_exceptions("SceneManager")
    def get_current_scheduled_scene(self) -> Optional[str]:
        """获取当前时间应该显示的场景"""
        try:
            current_time = datetime.now()
            current_time_str = current_time.strftime("%H:%M")
            current_weekday = current_time.isoweekday()
            
            for schedule in self.scene_schedules:
                if not schedule['enabled']:
                    continue
                    
                # 检查星期
                days = [int(d.strip()) for d in schedule['days_of_week'].split(',') if d.strip().isdigit()]
                if current_weekday not in days:
                    continue
                    
                # 检查时间
                if schedule['start_time'] <= current_time_str <= schedule['end_time']:
                    return schedule['scene_name']
                    
            return None
            
        except Exception as e:
            self.logger.error(f"获取当前调度场景失败: {e}")
            return None
            
    @handle_exceptions("SceneManager")
    def start_auto_switch(self):
        """启动自动场景切换"""
        if self.auto_switch_enabled:
            self.logger.warning("自动场景切换已启用")
            return
            
        self.auto_switch_enabled = True
        self.stop_event.clear()
        
        self.switch_thread = threading.Thread(target=self._auto_switch_loop, daemon=True)
        self.switch_thread.start()
        
        self.logger.info("自动场景切换已启动")
        
    def stop_auto_switch(self):
        """停止自动场景切换"""
        if not self.auto_switch_enabled:
            return
            
        self.auto_switch_enabled = False
        self.stop_event.set()
        
        if self.switch_thread and self.switch_thread.is_alive():
            self.switch_thread.join(timeout=3.0)
            
        self.logger.info("自动场景切换已停止")
        
    def _auto_switch_loop(self):
        """自动切换循环"""
        last_scene = None
        
        while not self.stop_event.is_set():
            try:
                # 获取当前应该显示的场景
                scheduled_scene = self.get_current_scheduled_scene()
                
                if scheduled_scene and scheduled_scene != last_scene:
                    # 需要切换场景
                    success = self.switch_to_scene(scheduled_scene, auto_switch=True)
                    if success:
                        last_scene = scheduled_scene
                        
                # 等待下次检查
                self.stop_event.wait(30)  # 每30秒检查一次
                
            except Exception as e:
                self.logger.error(f"自动场景切换异常: {e}")
                if self.on_auto_switch_error:
                    self.on_auto_switch_error(str(e))
                    
                # 出错后等待一段时间
                self.stop_event.wait(60)
                
    @handle_exceptions("SceneManager")
    def get_scene_list(self) -> List[str]:
        """获取OBS场景列表"""
        if self.obs_controller.is_connected:
            return self.obs_controller.get_scenes()
        else:
            return []
            
    @handle_exceptions("SceneManager")
    def get_current_scene(self) -> Optional[str]:
        """获取当前场景"""
        status = self.obs_controller.get_status()
        return status.get('current_scene')
        
    @handle_exceptions("SceneManager")
    def get_scene_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取场景配置"""
        return self.scene_configs.copy()
        
    @handle_exceptions("SceneManager")
    def get_scene_schedules(self) -> List[Dict[str, Any]]:
        """获取场景调度计划"""
        return self.scene_schedules.copy()
        
    @handle_exceptions("SceneManager")
    def get_scene_stats(self) -> Dict[str, Any]:
        """获取场景统计信息"""
        return {
            'total_scenes': len(self.scene_configs),
            'total_schedules': len(self.scene_schedules),
            'auto_switch_enabled': self.auto_switch_enabled,
            'stats': self.stats.copy()
        }
        
    @handle_exceptions("SceneManager")
    def create_scene_preset(self, name: str, scenes: List[str], 
                           durations: List[int]) -> Dict[str, Any]:
        """
        创建场景预设
        
        Args:
            name: 预设名称
            scenes: 场景列表
            durations: 每个场景的持续时间（秒）
            
        Returns:
            预设配置
        """
        if len(scenes) != len(durations):
            raise ValueError("场景数量与持续时间数量不匹配")
            
        preset = {
            'name': name,
            'scenes': scenes,
            'durations': durations,
            'total_duration': sum(durations),
            'created_at': datetime.now()
        }
        
        self.logger.info(f"创建场景预设: {name}")
        return preset
        
    @handle_exceptions("SceneManager")
    def get_scene_preview(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取场景切换预览"""
        try:
            preview = []
            current_time = datetime.now()
            
            for i in range(hours):
                check_time = current_time + timedelta(hours=i)
                check_time_str = check_time.strftime("%H:%M")
                check_weekday = check_time.isoweekday()
                
                scheduled_scene = None
                for schedule in self.scene_schedules:
                    if not schedule['enabled']:
                        continue
                        
                    days = [int(d.strip()) for d in schedule['days_of_week'].split(',') if d.strip().isdigit()]
                    if check_weekday not in days:
                        continue
                        
                    if schedule['start_time'] <= check_time_str <= schedule['end_time']:
                        scheduled_scene = schedule['scene_name']
                        break
                        
                preview.append({
                    'time': check_time.strftime("%Y-%m-%d %H:%M"),
                    'hour': check_time.hour,
                    'scene': scheduled_scene,
                    'has_schedule': scheduled_scene is not None
                })
                
            return preview
            
        except Exception as e:
            self.logger.error(f"获取场景预览失败: {e}")
            return []
