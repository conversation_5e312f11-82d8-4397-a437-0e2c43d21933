#!/usr/bin/env python3
"""
时间段选择对话框
用于添加时间段话术功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                                QPushButton, QSpinBox, QFormLayout, QGroupBox,
                                QMessageBox, QApplication)
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont
except ImportError:
    print("❌ PyQt5 未安装，请运行: pip install PyQt5")
    sys.exit(1)


class TimeSegmentDialog(QDialog):
    """时间段选择对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.start_time = 0
        self.end_time = 60
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("添加时间段话术")
        self.setFixedSize(350, 200)
        self.setModal(True)

        # 主布局
        layout = QVBoxLayout()
        self.setLayout(layout)

        # 时间段设置组
        time_group = QGroupBox("时间段设置")
        time_layout = QFormLayout()
        time_group.setLayout(time_layout)

        # 开始时间
        self.start_time_spin = QSpinBox()
        self.start_time_spin.setRange(0, 3600)  # 0-3600秒（1小时）
        self.start_time_spin.setValue(0)
        self.start_time_spin.setSuffix(" 秒")
        self.start_time_spin.setMinimumWidth(120)
        time_layout.addRow("开始时间:", self.start_time_spin)

        # 结束时间
        self.end_time_spin = QSpinBox()
        self.end_time_spin.setRange(1, 3600)  # 1-3600秒
        self.end_time_spin.setValue(60)
        self.end_time_spin.setSuffix(" 秒")
        self.end_time_spin.setMinimumWidth(120)
        time_layout.addRow("结束时间:", self.end_time_spin)

        # 连接信号，确保结束时间大于开始时间
        self.start_time_spin.valueChanged.connect(self.on_start_time_changed)
        self.end_time_spin.valueChanged.connect(self.on_end_time_changed)

        layout.addWidget(time_group)

        # 说明文字
        info_label = QLabel("说明：时间段话术将在指定的时间范围内随机播放")
        info_label.setStyleSheet("color: #666; font-size: 12px;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 按钮组
        button_layout = QHBoxLayout()

        self.ok_button = QPushButton("确认")
        self.ok_button.clicked.connect(self.accept)
        self.ok_button.setDefault(True)

        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addStretch()
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)

        layout.addLayout(button_layout)

        # 设置样式
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                min-width: 80px;
                min-height: 30px;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                background-color: #f8f9fa;
            }
            QPushButton:hover {
                background-color: #e9ecef;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
            QPushButton:default {
                background-color: #007bff;
                color: white;
                border-color: #007bff;
            }
            QPushButton:default:hover {
                background-color: #0056b3;
            }
        """)

    def on_start_time_changed(self, value):
        """开始时间变化"""
        # 确保结束时间大于开始时间
        if self.end_time_spin.value() <= value:
            self.end_time_spin.setValue(value + 1)

    def on_end_time_changed(self, value):
        """结束时间变化"""
        # 确保开始时间小于结束时间
        if self.start_time_spin.value() >= value:
            self.start_time_spin.setValue(value - 1)

    def get_time_segment(self):
        """获取时间段"""
        return {
            'start_time': self.start_time_spin.value(),
            'end_time': self.end_time_spin.value()
        }

    def accept(self):
        """确认按钮点击"""
        start_time = self.start_time_spin.value()
        end_time = self.end_time_spin.value()

        # 验证时间段
        if start_time >= end_time:
            QMessageBox.warning(self, "时间段错误", "结束时间必须大于开始时间！")
            return

        if end_time - start_time < 1:
            QMessageBox.warning(self, "时间段错误", "时间段至少需要1秒！")
            return

        self.start_time = start_time
        self.end_time = end_time
        super().accept()


def main():
    """测试函数"""
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)

    dialog = TimeSegmentDialog()
    if dialog.exec_() == QDialog.Accepted:
        time_segment = dialog.get_time_segment()
        print(f"选择的时间段: {time_segment['start_time']}秒 - {time_segment['end_time']}秒")

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
