#!/usr/bin/env python3
"""
AI直播系统 v2 - 用户系统测试脚本
测试用户注册、登录、充值功能
"""

import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.logging_service import setup_logging, create_logger
from src.data.database_manager import DatabaseManager
from src.core.user import UserManager, AuthManager, PaymentManager


def test_user_registration():
    """测试用户注册"""
    print("👤 测试用户注册...")
    
    try:
        # 创建数据库管理器
        db = DatabaseManager("data/test_user_system.db")
        user_manager = UserManager(db)
        
        # 测试用户注册
        result = user_manager.register_user(
            username="testuser",
            password="123456",
            email="<EMAIL>",
            phone="13800138000"
        )
        
        if result['success']:
            print(f"  ✅ 用户注册成功: {result['user_info']['username']}")
            print(f"  ✅ 用户ID: {result['user_id']}")
            print(f"  ✅ 用户类型: {result['user_info']['user_type']}")
            print(f"  ✅ 试用天数: {result['user_info']['trial_days']}")
            return True, db, result['user_info']
        else:
            print(f"  ❌ 用户注册失败: {result['message']}")
            return False, None, None
            
    except Exception as e:
        print(f"  ❌ 用户注册测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None


def test_user_login(db, user_info):
    """测试用户登录"""
    print("\n🔐 测试用户登录...")
    
    try:
        user_manager = UserManager(db)
        auth_manager = AuthManager(db)
        
        # 测试登录
        login_result = user_manager.login_user("testuser", "123456")
        
        if login_result['success']:
            print(f"  ✅ 用户登录成功: {login_result['user_info']['username']}")
            
            # 生成令牌
            tokens = auth_manager.generate_tokens(login_result['user_info'])
            
            if tokens:
                print(f"  ✅ 令牌生成成功")
                print(f"  ✅ 访问令牌: {tokens['access_token'][:50]}...")
                print(f"  ✅ 刷新令牌: {tokens['refresh_token'][:50]}...")
                print(f"  ✅ 用户权限: {tokens['permissions']}")
                
                # 验证令牌
                payload = auth_manager.verify_token(tokens['access_token'])
                if payload:
                    print(f"  ✅ 令牌验证成功: 用户ID {payload['user_id']}")
                else:
                    print(f"  ❌ 令牌验证失败")
                    
                return True, tokens
            else:
                print(f"  ❌ 令牌生成失败")
                return False, None
        else:
            print(f"  ❌ 用户登录失败: {login_result['message']}")
            return False, None
            
    except Exception as e:
        print(f"  ❌ 用户登录测试失败: {e}")
        return False, None


def test_payment_system(db, user_info):
    """测试支付系统"""
    print("\n💰 测试支付系统...")
    
    try:
        payment_manager = PaymentManager(db)
        
        # 获取充值套餐
        packages = payment_manager.get_recharge_packages()
        print(f"  ✅ 获取充值套餐: {len(packages)} 个")
        
        for package_id, package_info in packages.items():
            print(f"    - {package_info['name']}: ¥{package_info['amount']} ({package_info['days']}天)")
            
        # 创建充值订单
        order_result = payment_manager.create_recharge_order(
            user_info['id'],
            'basic',  # 基础套餐
            'alipay'  # 支付宝
        )
        
        if order_result['success']:
            print(f"  ✅ 创建充值订单成功: {order_result['order_no']}")
            print(f"  ✅ 订单金额: ¥{order_result['amount']}")
            
            # 模拟支付处理
            payment_result = payment_manager.process_payment(order_result['order_no'])
            
            if payment_result['success']:
                print(f"  ✅ 支付处理成功")
                print(f"  ✅ 充值信息: {payment_result['recharge_info']}")
                
                # 获取订单状态
                status_result = payment_manager.get_order_status(order_result['order_no'])
                if status_result['success']:
                    order = status_result['order']
                    print(f"  ✅ 订单状态: {order['status']} (1=成功)")
                    
                return True
            else:
                print(f"  ❌ 支付处理失败: {payment_result['message']}")
                return False
        else:
            print(f"  ❌ 创建充值订单失败: {order_result['message']}")
            return False
            
    except Exception as e:
        print(f"  ❌ 支付系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_user_management(db, user_info):
    """测试用户管理"""
    print("\n👥 测试用户管理...")
    
    try:
        user_manager = UserManager(db)
        
        # 更新用户信息
        update_result = user_manager.update_user_info(
            user_info['id'],
            nickname="测试用户",
            email="<EMAIL>"
        )
        
        if update_result:
            print(f"  ✅ 用户信息更新成功")
        else:
            print(f"  ❌ 用户信息更新失败")
            
        # 修改密码
        password_result = user_manager.change_password(
            user_info['id'],
            "123456",  # 旧密码
            "654321"   # 新密码
        )
        
        if password_result['success']:
            print(f"  ✅ 密码修改成功")
            
            # 用新密码登录测试
            login_result = user_manager.login_user("testuser", "654321")
            if login_result['success']:
                print(f"  ✅ 新密码登录成功")
            else:
                print(f"  ❌ 新密码登录失败")
        else:
            print(f"  ❌ 密码修改失败: {password_result['message']}")
            
        # 获取用户统计
        stats = user_manager.get_user_stats()
        print(f"  ✅ 用户统计: 总用户数 {stats['total_users']}, 活跃用户 {stats['active_users']}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 用户管理测试失败: {e}")
        return False


def test_auth_features(db, tokens):
    """测试认证功能"""
    print("\n🔑 测试认证功能...")
    
    try:
        auth_manager = AuthManager(db)
        
        # 刷新令牌
        new_tokens = auth_manager.refresh_access_token(tokens['refresh_token'])
        
        if new_tokens:
            print(f"  ✅ 令牌刷新成功")
            print(f"  ✅ 新访问令牌: {new_tokens['access_token'][:50]}...")
        else:
            print(f"  ❌ 令牌刷新失败")
            
        # 创建API密钥
        api_key = auth_manager.create_api_key(1, "测试API密钥")
        
        if api_key:
            print(f"  ✅ API密钥创建成功: {api_key}")
            
            # 验证API密钥
            key_info = auth_manager.verify_api_key(api_key)
            if key_info:
                print(f"  ✅ API密钥验证成功: 用户 {key_info['username']}")
            else:
                print(f"  ❌ API密钥验证失败")
        else:
            print(f"  ❌ API密钥创建失败")
            
        # 权限检查
        user_permissions = auth_manager.get_user_permissions(1)  # VIP用户权限
        has_permission = auth_manager.check_permission(user_permissions, 'user:vip')
        
        print(f"  ✅ 用户权限: {user_permissions}")
        print(f"  ✅ VIP权限检查: {'有权限' if has_permission else '无权限'}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 认证功能测试失败: {e}")
        return False


def test_ui_components():
    """测试UI组件"""
    print("\n🖥️ 测试UI组件...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from src.ui.user_ui import UserLoginWidget, UserRegisterWidget, UserRechargeWidget
        from src.data.database_manager import DatabaseManager
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建数据库
        db = DatabaseManager("data/test_ui.db")
        
        # 测试登录界面
        login_widget = UserLoginWidget(db)
        print(f"  ✅ 登录界面创建成功")
        
        # 测试注册界面
        register_widget = UserRegisterWidget(db)
        print(f"  ✅ 注册界面创建成功")
        
        # 创建测试用户信息
        test_user = {
            'id': 1,
            'username': 'testuser',
            'user_type': 0
        }
        
        # 测试充值界面
        recharge_widget = UserRechargeWidget(db, test_user)
        print(f"  ✅ 充值界面创建成功")
        
        print(f"  ✅ 所有UI组件创建成功")
        
        # 清理
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"  ❌ UI组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("👤 AI直播系统 v2 - 用户系统测试")
    print("=" * 60)
    
    # 初始化日志系统
    setup_logging()
    logger = create_logger("test")
    
    test_results = []
    
    try:
        # 测试用户注册
        reg_success, db, user_info = test_user_registration()
        test_results.append(("用户注册", reg_success))
        
        if not reg_success:
            return 1
            
        # 测试用户登录
        login_success, tokens = test_user_login(db, user_info)
        test_results.append(("用户登录", login_success))
        
        # 测试支付系统
        payment_success = test_payment_system(db, user_info)
        test_results.append(("支付系统", payment_success))
        
        # 测试用户管理
        mgmt_success = test_user_management(db, user_info)
        test_results.append(("用户管理", mgmt_success))
        
        # 测试认证功能
        if tokens:
            auth_success = test_auth_features(db, tokens)
            test_results.append(("认证功能", auth_success))
            
        # 测试UI组件
        ui_success = test_ui_components()
        test_results.append(("UI组件", ui_success))
        
        # 输出测试结果
        print("\n🎉 测试结果总结:")
        print("=" * 60)
        
        all_passed = True
        for module_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {module_name}: {status}")
            if not result:
                all_passed = False
                
        if all_passed:
            print("\n🎊 所有用户系统测试通过！")
            print("\n📋 已完成的用户功能:")
            print("  - 用户注册和登录")
            print("  - 密码加密和验证")
            print("  - JWT令牌认证")
            print("  - 权限管理系统")
            print("  - 充值和支付系统")
            print("  - 用户界面组件")
            
            print("\n🚀 用户系统已准备就绪！")
        else:
            print("\n⚠️  部分用户功能测试失败，请检查相关模块。")
            
        # 清理测试数据
        test_files = [
            "data/test_user_system.db",
            "data/test_ui.db"
        ]
        
        for file_path in test_files:
            test_file = Path(file_path)
            if test_file.exists():
                test_file.unlink()
                
        print("\n🧹 测试数据已清理")
        
        return 0 if all_passed else 1
        
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
