#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试副视频无黑屏切换功能
"""

import time
import json
from pathlib import Path

def test_sub_video_seamless_switch():
    """测试副视频无黑屏切换功能"""
    print("🎬 副视频无黑屏切换测试")
    print("=" * 60)
    
    # 检查副视频配置
    config_file = Path("data/sub_videos.json")
    if not config_file.exists():
        print("❌ 副视频配置文件不存在")
        return False
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print(f"✅ 副视频配置加载成功，包含 {len(config)} 个关键词:")
    for keyword, data in config.items():
        video_source = data.get('video_source', '未知')
        scripts_count = len(data.get('scripts', []))
        print(f"  • {keyword} → {video_source} ({scripts_count}个话术)")
    
    if not config:
        print("❌ 没有副视频配置")
        return False
    
    # 获取第一个关键词用于测试
    first_keyword = list(config.keys())[0]
    first_video_source = config[first_keyword].get('video_source', '')
    
    print(f"\n🎯 测试关键词: '{first_keyword}' → 副视频源: '{first_video_source}'")
    
    print("\n🔄 修复后的副视频切换流程:")
    print("【主视频 → 副视频】")
    print("  1. 暂停双主视频自动切换")
    print("  2. 保存主视频当前播放状态（位置、状态）")
    print("  3. 暂停主视频播放")
    print("  4. 设置副视频为正常速度（100%）")
    print("  5. 启动副视频播放（从头开始）")
    print("  6. 原子操作：显示副视频 + 隐藏主视频")
    print("")
    print("【副视频 → 主视频】")
    print("  1. 停止副视频播放")
    print("  2. 设置主视频为正常速度（100%）")
    print("  3. 恢复主视频到保存的播放位置")
    print("  4. 恢复主视频播放")
    print("  5. 原子操作：显示主视频 + 隐藏副视频")
    print("  6. 更新双主视频管理器当前源")
    print("  7. 恢复双主视频自动切换")
    
    print("\n💡 修复要点:")
    print("✅ 1. 语音下载时自动过滤副视频关键词")
    print("✅ 2. 切换到正确的副视频源（不乱切换）")
    print("✅ 3. 副视频和主视频播放时不变速（100%速度）")
    print("✅ 4. 切回的主视频是切换前暂停的那个源")
    print("✅ 5. 副视频播放期间暂停双主视频自动切换")
    print("✅ 6. 副视频播放完成后才切回主视频")
    print("✅ 7. 恢复主视频的播放位置和状态")
    
    print("\n🎯 测试建议:")
    print("1. 在主程序中点击播放按钮开始播放")
    print("2. 发送测试弹幕，包含关键词：'代'")
    print("3. 观察OBS中的视频切换是否无黑屏")
    print("4. 检查副视频播放完成后是否正确切回主视频")
    print("5. 观察CPU使用率是否正常")
    
    print("\n📋 预期效果:")
    print("• 弹幕触发副视频时，立即无黑屏切换到副视频")
    print("• 副视频语音播放期间，只显示副视频")
    print("• 副视频语音播放完成后，无黑屏切换回主视频")
    print("• 整个过程中没有黑屏闪烁")
    print("• CPU使用率保持正常（不会同时播放两个视频）")
    
    return True

def main():
    """主函数"""
    success = test_sub_video_seamless_switch()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 副视频无黑屏切换测试准备完成！")
        print("请在主程序中进行实际测试。")
    else:
        print("\n" + "=" * 60)
        print("❌ 测试准备失败，请检查配置。")

if __name__ == "__main__":
    main()
