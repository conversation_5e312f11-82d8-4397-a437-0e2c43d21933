#!/usr/bin/env python3
"""
测试时间段对话框
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PyQt5.QtWidgets import QApplication
    from src.ui.time_segment_dialog import TimeSegmentDialog
    
    def test_dialog():
        app = QApplication(sys.argv)
        
        dialog = TimeSegmentDialog()
        print("✅ 时间段对话框创建成功")
        
        if dialog.exec_() == dialog.Accepted:
            time_segment = dialog.get_time_segment()
            print(f"✅ 选择的时间段: {time_segment['start_time']}秒 - {time_segment['end_time']}秒")
        else:
            print("❌ 用户取消了时间段选择")
        
        sys.exit(app.exec_())
    
    if __name__ == "__main__":
        test_dialog()
        
except Exception as e:
    print(f"❌ 测试时间段对话框失败: {e}")
    import traceback
    traceback.print_exc()
