#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket客户端模块
用于与OBS WebSocket服务器通信
"""

import json
import threading
import time
import logging

# 尝试导入websocket库
try:
    import websocket
    WEBSOCKET_AVAILABLE = True
except ImportError:
    WEBSOCKET_AVAILABLE = False
    print("警告: websocket-client库未安装，OBS WebSocket功能将不可用")

class WebSocketClient:
    """WebSocket客户端类"""
    
    def __init__(self, url="ws://localhost:4455", password=""):
        self.url = url
        self.password = password
        self.ws = None
        self.connected = False
        self.logger = logging.getLogger(__name__)
        
        # 回调函数
        self.on_message_callback = None
        self.on_connect_callback = None
        self.on_disconnect_callback = None
        self.on_error_callback = None
        
    def connect(self):
        """连接到WebSocket服务器"""
        if not WEBSOCKET_AVAILABLE:
            self.logger.warning("WebSocket库不可用，无法连接")
            return False
            
        try:
            self.ws = websocket.WebSocketApp(
                self.url,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )
            
            # 在新线程中运行WebSocket
            self.ws_thread = threading.Thread(target=self.ws.run_forever)
            self.ws_thread.daemon = True
            self.ws_thread.start()
            
            return True
            
        except Exception as e:
            self.logger.error(f"WebSocket连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开WebSocket连接"""
        if self.ws:
            self.ws.close()
            self.connected = False
    
    def send_message(self, message):
        """发送消息"""
        if not self.connected or not self.ws:
            self.logger.warning("WebSocket未连接，无法发送消息")
            return False
            
        try:
            if isinstance(message, dict):
                message = json.dumps(message)
            self.ws.send(message)
            return True
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False
    
    def _on_open(self, ws):
        """WebSocket连接打开回调"""
        self.connected = True
        self.logger.info("WebSocket连接已建立")
        
        if self.on_connect_callback:
            self.on_connect_callback()
    
    def _on_message(self, ws, message):
        """WebSocket消息接收回调"""
        try:
            data = json.loads(message)
            if self.on_message_callback:
                self.on_message_callback(data)
        except json.JSONDecodeError:
            self.logger.error(f"无法解析WebSocket消息: {message}")
    
    def _on_error(self, ws, error):
        """WebSocket错误回调"""
        self.logger.error(f"WebSocket错误: {error}")
        
        if self.on_error_callback:
            self.on_error_callback(error)
    
    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket连接关闭回调"""
        self.connected = False
        self.logger.info("WebSocket连接已关闭")
        
        if self.on_disconnect_callback:
            self.on_disconnect_callback()
    
    def set_callbacks(self, on_message=None, on_connect=None, on_disconnect=None, on_error=None):
        """设置回调函数"""
        if on_message:
            self.on_message_callback = on_message
        if on_connect:
            self.on_connect_callback = on_connect
        if on_disconnect:
            self.on_disconnect_callback = on_disconnect
        if on_error:
            self.on_error_callback = on_error


# 模拟WebSocket客户端（当websocket库不可用时）
class DummyWebSocketClient:
    """模拟WebSocket客户端"""
    
    def __init__(self, url="ws://localhost:4455", password=""):
        self.url = url
        self.password = password
        self.connected = False
        self.logger = logging.getLogger(__name__)
        
    def connect(self):
        """模拟连接"""
        self.logger.info("使用模拟WebSocket客户端")
        return False
    
    def disconnect(self):
        """模拟断开连接"""
        pass
    
    def send_message(self, message):
        """模拟发送消息"""
        return False
    
    def set_callbacks(self, **kwargs):
        """模拟设置回调"""
        pass


# 根据库的可用性选择实现
if not WEBSOCKET_AVAILABLE:
    WebSocketClient = DummyWebSocketClient
