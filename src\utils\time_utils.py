"""
AI Broadcaster v2 - 时间工具类
提供时间相关的工具函数
"""

import time
from datetime import datetime, timedelta, timezone
from typing import Optional, Union


class TimeUtils:
    """时间工具类"""
    
    @staticmethod
    def now() -> datetime:
        """获取当前时间"""
        return datetime.now()
    
    @staticmethod
    def utc_now() -> datetime:
        """获取当前UTC时间"""
        return datetime.now(timezone.utc)
    
    @staticmethod
    def timestamp() -> float:
        """获取当前时间戳"""
        return time.time()
    
    @staticmethod
    def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """
        格式化日期时间
        
        Args:
            dt: 日期时间对象
            format_str: 格式字符串
            
        Returns:
            格式化后的字符串
        """
        return dt.strftime(format_str)
    
    @staticmethod
    def parse_datetime(date_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> Optional[datetime]:
        """
        解析日期时间字符串
        
        Args:
            date_str: 日期时间字符串
            format_str: 格式字符串
            
        Returns:
            日期时间对象
        """
        try:
            return datetime.strptime(date_str, format_str)
        except ValueError:
            return None
    
    @staticmethod
    def format_duration(seconds: Union[int, float]) -> str:
        """
        格式化时长
        
        Args:
            seconds: 秒数
            
        Returns:
            格式化后的时长字符串
        """
        if seconds < 60:
            return f"{seconds:.1f}秒"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}分钟"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}小时"
    
    @staticmethod
    def get_time_difference(start: datetime, end: Optional[datetime] = None) -> timedelta:
        """
        计算时间差
        
        Args:
            start: 开始时间
            end: 结束时间（默认为当前时间）
            
        Returns:
            时间差
        """
        if end is None:
            end = datetime.now()
        return end - start
    
    @staticmethod
    def is_business_hours(dt: Optional[datetime] = None, 
                         start_hour: int = 9, 
                         end_hour: int = 18) -> bool:
        """
        判断是否在工作时间内
        
        Args:
            dt: 日期时间（默认为当前时间）
            start_hour: 开始小时
            end_hour: 结束小时
            
        Returns:
            是否在工作时间内
        """
        if dt is None:
            dt = datetime.now()
        
        # 周末不算工作时间
        if dt.weekday() >= 5:
            return False
        
        return start_hour <= dt.hour < end_hour
    
    @staticmethod
    def add_business_days(dt: datetime, days: int) -> datetime:
        """
        添加工作日
        
        Args:
            dt: 基准日期
            days: 要添加的工作日数
            
        Returns:
            添加后的日期
        """
        result = dt
        while days > 0:
            result += timedelta(days=1)
            if result.weekday() < 5:  # 周一到周五
                days -= 1
        return result
    
    @staticmethod
    def get_week_start(dt: Optional[datetime] = None) -> datetime:
        """
        获取周开始时间（周一）
        
        Args:
            dt: 日期时间（默认为当前时间）
            
        Returns:
            周开始时间
        """
        if dt is None:
            dt = datetime.now()
        
        days_since_monday = dt.weekday()
        week_start = dt - timedelta(days=days_since_monday)
        return week_start.replace(hour=0, minute=0, second=0, microsecond=0)
    
    @staticmethod
    def get_month_start(dt: Optional[datetime] = None) -> datetime:
        """
        获取月开始时间
        
        Args:
            dt: 日期时间（默认为当前时间）
            
        Returns:
            月开始时间
        """
        if dt is None:
            dt = datetime.now()
        
        return dt.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    
    @staticmethod
    def sleep_until(target_time: datetime):
        """
        睡眠直到指定时间
        
        Args:
            target_time: 目标时间
        """
        now = datetime.now()
        if target_time > now:
            sleep_seconds = (target_time - now).total_seconds()
            time.sleep(sleep_seconds)
    
    @staticmethod
    def get_timezone_offset() -> str:
        """
        获取时区偏移
        
        Returns:
            时区偏移字符串
        """
        offset = time.timezone if time.daylight == 0 else time.altzone
        hours, remainder = divmod(abs(offset), 3600)
        minutes = remainder // 60
        sign = '-' if offset > 0 else '+'
        return f"{sign}{hours:02d}:{minutes:02d}"
    
    @staticmethod
    def convert_timezone(dt: datetime, from_tz: timezone, to_tz: timezone) -> datetime:
        """
        转换时区
        
        Args:
            dt: 日期时间
            from_tz: 源时区
            to_tz: 目标时区
            
        Returns:
            转换后的日期时间
        """
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=from_tz)
        return dt.astimezone(to_tz)
