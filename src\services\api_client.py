"""
AI主播系统 v2 - API客户端
与服务器进行通信的客户端
"""

import requests
import json
import socket
import platform
import hashlib
from typing import Dict, Any, Optional
from datetime import datetime

from .logging_service import create_logger
from .error_handler import handle_exceptions


class APIClient:
    """API客户端 - 与服务器通信"""
    
    def __init__(self, base_url: str = "http://localhost:12456"):
        self.logger = create_logger("api_client")
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()

        # 设置请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'AI-Broadcaster-Client/2.0.0'
        })

        # 禁用代理，直接连接
        self.session.proxies = {'http': None, 'https': None}

        # 设置超时
        self.timeout = 10
        
    def get_machine_code(self) -> str:
        """获取机器码"""
        try:
            # 获取硬件信息
            machine_info = f"{platform.node()}-{platform.machine()}-{platform.processor()}"
            
            # 获取MAC地址
            import uuid
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                           for elements in range(0, 2*6, 2)][::-1])
            
            # 组合信息生成机器码
            combined_info = f"{machine_info}-{mac}"
            machine_code = hashlib.md5(combined_info.encode()).hexdigest()[:16]
            
            self.logger.info(f"生成机器码: {machine_code}")
            return machine_code
            
        except Exception as e:
            self.logger.error(f"获取机器码失败: {e}")
            # 使用备用方案
            return hashlib.md5(platform.node().encode()).hexdigest()[:16]
            
    def get_client_ip(self) -> str:
        """获取客户端IP地址"""
        try:
            # 尝试连接外部服务器获取真实IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                ip = s.getsockname()[0]
                return ip
        except Exception:
            try:
                # 备用方案：获取本地IP
                hostname = socket.gethostname()
                ip = socket.gethostbyname(hostname)
                return ip
            except Exception as e:
                self.logger.error(f"获取IP地址失败: {e}")
                return "127.0.0.1"
                
    @handle_exceptions("APIClient")
    def user_login(self, username: str, password: str) -> Dict[str, Any]:
        """
        用户登录
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            登录结果
        """
        try:
            # 准备请求数据
            data = {
                "username": username,
                "password": password,
                "machine_code": self.get_machine_code(),
                "ip": self.get_client_ip()
            }
            
            self.logger.info(f"发送登录请求: 用户名={username}, 机器码={data['machine_code']}")
            
            # 发送请求
            response = self.session.post(
                f"{self.base_url}/user/login",
                json=data,
                timeout=self.timeout
            )
            
            # 检查HTTP状态码
            if response.status_code != 200:
                return {
                    'success': False,
                    'message': f'服务器错误: HTTP {response.status_code}'
                }
                
            # 解析响应
            result = response.json()
            
            # 转换响应格式
            if result.get('状态') == '成功':
                return {
                    'success': True,
                    'message': result.get('信息', '登录成功'),
                    'token': result.get('token'),
                    'expire_time': result.get('过期时间'),
                    'user_info': {
                        'username': username,
                        'token': result.get('token'),
                        'expire_time': result.get('过期时间')
                    }
                }
            else:
                return {
                    'success': False,
                    'message': result.get('信息', '登录失败')
                }
                
        except requests.exceptions.ConnectionError:
            self.logger.error("无法连接到服务器")
            return {
                'success': False,
                'message': '无法连接到服务器，请检查网络连接'
            }
        except requests.exceptions.Timeout:
            self.logger.error("请求超时")
            return {
                'success': False,
                'message': '请求超时，请稍后重试'
            }
        except json.JSONDecodeError:
            self.logger.error("服务器响应格式错误")
            return {
                'success': False,
                'message': '服务器响应格式错误'
            }
        except Exception as e:
            self.logger.error(f"登录请求失败: {e}")
            return {
                'success': False,
                'message': f'登录失败: {str(e)}'
            }
            
    @handle_exceptions("APIClient")
    def user_register(self, username: str, password: str, phone: str = "") -> Dict[str, Any]:
        """
        用户注册
        
        Args:
            username: 用户名
            password: 密码
            phone: 手机号
            
        Returns:
            注册结果
        """
        try:
            # 准备请求数据
            data = {
                "username": username,
                "password": password,
                "phone": phone,
                "machine_code": self.get_machine_code(),
                "ip": self.get_client_ip()
            }
            
            self.logger.info(f"发送注册请求: 用户名={username}, 手机号={phone}")
            
            # 发送请求
            response = self.session.post(
                f"{self.base_url}/user/register",
                json=data,
                timeout=self.timeout
            )
            
            # 检查HTTP状态码
            if response.status_code != 200:
                return {
                    'success': False,
                    'message': f'服务器错误: HTTP {response.status_code}'
                }
                
            # 解析响应
            result = response.json()
            
            # 转换响应格式
            if result.get('状态') == '成功':
                return {
                    'success': True,
                    'message': result.get('信息', '注册成功'),
                    'user_id': result.get('用户ID'),
                    'user_info': {
                        'username': username,
                        'user_id': result.get('用户ID')
                    }
                }
            else:
                return {
                    'success': False,
                    'message': result.get('信息', '注册失败')
                }
                
        except requests.exceptions.ConnectionError:
            self.logger.error("无法连接到服务器")
            return {
                'success': False,
                'message': '无法连接到服务器，请检查网络连接'
            }
        except requests.exceptions.Timeout:
            self.logger.error("请求超时")
            return {
                'success': False,
                'message': '请求超时，请稍后重试'
            }
        except json.JSONDecodeError:
            self.logger.error("服务器响应格式错误")
            return {
                'success': False,
                'message': '服务器响应格式错误'
            }
        except Exception as e:
            self.logger.error(f"注册请求失败: {e}")
            return {
                'success': False,
                'message': f'注册失败: {str(e)}'
            }
            
    @handle_exceptions("APIClient")
    def user_recharge(self, username: str, password: str, card_code: str) -> Dict[str, Any]:
        """
        用户充值
        
        Args:
            username: 用户名
            password: 密码
            card_code: 充值卡密
            
        Returns:
            充值结果
        """
        try:
            # 准备请求数据
            data = {
                "username": username,
                "password": password,
                "card_code": card_code,
                "machine_code": self.get_machine_code(),
                "ip": self.get_client_ip()
            }
            
            self.logger.info(f"发送充值请求: 用户名={username}, 卡密={card_code[:4]}****")
            
            # 发送请求
            response = self.session.post(
                f"{self.base_url}/user/recharge",
                json=data,
                timeout=self.timeout
            )
            
            # 检查HTTP状态码
            if response.status_code != 200:
                return {
                    'success': False,
                    'message': f'服务器错误: HTTP {response.status_code}'
                }
                
            # 解析响应
            result = response.json()
            
            # 转换响应格式
            if result.get('状态') == '成功':
                return {
                    'success': True,
                    'message': result.get('信息', '充值成功'),
                    'days': result.get('天数'),
                    'expire_time': result.get('到期时间'),
                    'recharge_info': {
                        'days_added': result.get('天数'),
                        'new_expire_time': result.get('到期时间')
                    }
                }
            else:
                return {
                    'success': False,
                    'message': result.get('信息', '充值失败')
                }
                
        except requests.exceptions.ConnectionError:
            self.logger.error("无法连接到服务器")
            return {
                'success': False,
                'message': '无法连接到服务器，请检查网络连接'
            }
        except requests.exceptions.Timeout:
            self.logger.error("请求超时")
            return {
                'success': False,
                'message': '请求超时，请稍后重试'
            }
        except json.JSONDecodeError:
            self.logger.error("服务器响应格式错误")
            return {
                'success': False,
                'message': '服务器响应格式错误'
            }
        except Exception as e:
            self.logger.error(f"充值请求失败: {e}")
            return {
                'success': False,
                'message': f'充值失败: {str(e)}'
            }
            
    def test_connection(self) -> bool:
        """测试服务器连接"""
        try:
            response = self.session.get(f"{self.base_url}/", timeout=5)
            return response.status_code == 200
        except Exception:
            return False
            
    def set_server_url(self, url: str):
        """设置服务器地址"""
        self.base_url = url.rstrip('/')
        self.logger.info(f"设置服务器地址: {self.base_url}")
        
    def close(self):
        """关闭会话"""
        self.session.close()
