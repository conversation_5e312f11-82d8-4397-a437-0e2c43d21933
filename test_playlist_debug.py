#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试播放列表调试功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_playlist_initialization():
    """测试播放列表初始化逻辑"""
    print("🧪 测试播放列表初始化逻辑...")
    
    # 模拟时间段数据结构
    script_time_segments = {
        "话术1": {
            "10秒 - 20秒": {
                "start": 10,
                "end": 20,
                "content": """# 时间段话术 (10秒 - 20秒)
# 话术: 话术1

1***欢迎{nick}进入直播间！
2***现在是第10-20秒时间段
3***【大家好|各位好|hello】，感谢观看！
4***正在玩{gamename}，很有趣哦！
5***在线人数：{people}人"""
            },
            "30秒 - 40秒": {
                "start": 30,
                "end": 40,
                "content": """# 时间段话术 (30秒 - 40秒)
# 话术: 话术1

1***感谢{nick}的关注！
2***现在时间是{time}
3***【欢迎|你好|感谢】大家的支持
4***游戏类型：{gametype}
5***最近进入的用户：{user1}、{user2}"""
            }
        },
        "话术2": {
            "15秒 - 25秒": {
                "start": 15,
                "end": 25,
                "content": """# 时间段话术 (15秒 - 25秒)
# 话术: 话术2

1***大家好，我是主播
2***今天给大家带来精彩内容
3***【喜欢|关注|点赞】的朋友们谢谢啦
4***现在日期是{date}"""
            }
        }
    }
    
    print(f"✅ 模拟数据创建完成，共 {len(script_time_segments)} 个话术")
    
    # 测试解析时间段内容
    for script_name, time_segments in script_time_segments.items():
        print(f"\n📋 话术: {script_name}")
        
        for segment_name, segment_data in time_segments.items():
            content = segment_data.get('content', '')
            print(f"  时间段: {segment_name}")
            print(f"  原始内容长度: {len(content)} 字符")
            
            # 解析话术内容，每行一条话术
            scripts = []
            for line in content.split('\n'):
                line = line.strip()
                # 跳过注释行和空行
                if line and not line.startswith('#'):
                    # 移除行首的编号（如 "1***"）
                    if '***' in line:
                        script_text = line.split('***', 1)[1].strip()
                        if script_text:
                            scripts.append(script_text)
                    else:
                        scripts.append(line)
            
            print(f"  解析出的话术数量: {len(scripts)}")
            for i, script in enumerate(scripts, 1):
                print(f"    {i}. {script}")
    
    return script_time_segments

def test_random_selection():
    """测试随机选择逻辑"""
    print("\n🧪 测试随机选择逻辑...")
    
    scripts = [
        "欢迎{nick}进入直播间！",
        "现在是第10-20秒时间段",
        "【大家好|各位好|hello】，感谢观看！",
        "正在玩{gamename}，很有趣哦！",
        "在线人数：{people}人"
    ]
    
    import random
    
    def select_random_scripts(scripts, count):
        """从脚本列表中随机选择指定数量的不重复脚本"""
        if len(scripts) <= count:
            return scripts.copy()
        return random.sample(scripts, count)
    
    for prepare_count in [2, 3, 5, 10]:
        selected = select_random_scripts(scripts, prepare_count)
        print(f"  预备数量: {prepare_count}, 选择结果: {len(selected)} 条")
        for i, script in enumerate(selected, 1):
            print(f"    {i}. {script}")

def test_playlist_item_creation():
    """测试播放列表项创建"""
    print("\n🧪 测试播放列表项创建...")
    
    # 模拟创建播放列表项
    playlist_items = []
    item_id = 1
    
    scripts = [
        "欢迎{nick}进入直播间！",
        "现在是第10-20秒时间段",
        "【大家好|各位好|hello】，感谢观看！"
    ]
    
    for script_content in scripts:
        playlist_item = {
            'id': item_id,
            'voice_type': '主视频话术',
            'content': script_content,
            'time_segment': '10秒 - 20秒',
            'status': '未下载',
            'filename': '',
            'sub_video': '无'
        }
        playlist_items.append(playlist_item)
        item_id += 1
    
    print(f"✅ 创建了 {len(playlist_items)} 个播放列表项")
    
    # 模拟表格显示
    print("\n📋 播放列表显示:")
    print("编号 | 语音类型   | 语音内容                    | 所在时间段   | 语音状态 | 语音文件名 | 有无副视频")
    print("-" * 100)
    
    for item in playlist_items:
        content_display = item['content'][:20] + "..." if len(item['content']) > 20 else item['content']
        print(f"{item['id']:4} | {item['voice_type']:8} | {content_display:25} | {item['time_segment']:10} | {item['status']:8} | {item['filename']:10} | {item['sub_video']}")

def test_variable_replacement():
    """测试变量替换"""
    print("\n🧪 测试变量替换...")
    
    import datetime
    import random
    import re
    
    def replace_variables(text):
        """替换文本中的变量词"""
        try:
            # 获取当前时间信息
            now = datetime.datetime.now()
            current_date = now.strftime("%Y年%m月%d日")
            current_time = now.strftime("%H:%M")
            
            # 模拟游戏信息
            game_name = "测试游戏"
            game_type = "动作游戏"
            
            # 模拟在线人数
            people_count = random.randint(100, 999)
            
            # 模拟最近用户列表
            recent_users = ["用户A", "用户B", "用户C"]
            
            # 变量替换映射
            replacements = {
                '{nick}': '测试主播',
                '{date}': current_date,
                '{time}': current_time,
                '{people}': str(people_count),
                '{gift}': '礼物',
                '{gametype}': game_type,
                '{gamename}': game_name,
                '{user1}': recent_users[0],
                '{user2}': recent_users[1],
                '{user3}': recent_users[2]
            }
            
            # 执行替换
            result = text
            for var, value in replacements.items():
                result = result.replace(var, value)
            
            # 处理随机选择【选项1|选项2|选项3】
            def replace_random_choice(match):
                choices = match.group(1).split('|')
                return random.choice(choices)
            
            result = re.sub(r'【([^】]+)】', replace_random_choice, result)
            
            return result
            
        except Exception as e:
            print(f"❌ 替换变量失败: {e}")
            return text
    
    test_texts = [
        "欢迎{nick}进入直播间！",
        "现在时间是{time}，日期是{date}",
        "在线人数：{people}人，游戏：{gamename}",
        "【欢迎|你好|感谢】{nick}的到来！",
        "最近进入的用户：{user1}、{user2}、{user3}"
    ]
    
    for text in test_texts:
        processed = replace_variables(text)
        print(f"  原文: {text}")
        print(f"  处理: {processed}")
        print()

def main():
    """主测试函数"""
    print("🚀 开始测试播放列表调试功能...")
    print("=" * 60)
    
    try:
        # 测试各个功能模块
        script_data = test_playlist_initialization()
        test_random_selection()
        test_playlist_item_creation()
        test_variable_replacement()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成！")
        
        print("\n📋 问题诊断:")
        print("  1. 检查话术选择框是否有选中的话术")
        print("  2. 检查选中的话术是否有时间段设置")
        print("  3. 检查时间段内容是否有有效的话术行")
        print("  4. 检查播放列表表格是否正确初始化")
        
        print("\n🔧 建议解决方案:")
        print("  1. 先在话术管理中创建话术和时间段")
        print("  2. 确保时间段中有话术内容（格式：编号***话术内容）")
        print("  3. 在话术选择框中选择对应话术")
        print("  4. 然后点击播放按钮")
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
