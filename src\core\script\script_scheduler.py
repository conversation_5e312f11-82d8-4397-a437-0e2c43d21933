"""
AI Broadcaster v2 - 话术调度器
根据时间段自动选择和调度话术
"""

import random
import threading
import time
from typing import Optional, Dict, Any, List, Callable
from datetime import datetime, timedelta

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions
from .script_manager import ScriptManager
from .time_segment_manager import TimeSegmentManager


class ScriptScheduler:
    """话术调度器"""
    
    def __init__(self, db_manager):
        self.logger = create_logger("script_scheduler")
        self.db_manager = db_manager
        
        # 管理器
        self.script_manager = ScriptManager(db_manager)
        self.time_segment_manager = TimeSegmentManager(db_manager)
        
        # 调度状态
        self.is_running = False
        self.scheduler_thread = None
        self.stop_event = threading.Event()
        
        # 配置
        self.check_interval = 60  # 检查间隔（秒）
        self.fallback_scripts = []  # 备用话术ID列表
        
        # 回调函数
        self.on_script_selected: Optional[Callable] = None
        self.on_schedule_change: Optional[Callable] = None
        self.on_scheduler_error: Optional[Callable] = None
        
        # 统计信息
        self.stats = {
            'total_selections': 0,
            'scheduled_selections': 0,
            'fallback_selections': 0,
            'last_selection_time': None,
            'last_selected_script': None
        }
        
    @handle_exceptions("ScriptScheduler")
    def start_scheduler(self):
        """启动调度器"""
        if self.is_running:
            self.logger.warning("调度器已在运行中")
            return
            
        self.stop_event.clear()
        self.is_running = True
        
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        self.logger.info("话术调度器已启动")
        
    def stop_scheduler(self):
        """停止调度器"""
        if not self.is_running:
            return
            
        self.stop_event.set()
        self.is_running = False
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5.0)
            
        self.logger.info("话术调度器已停止")
        
    def _scheduler_loop(self):
        """调度器主循环"""
        while not self.stop_event.is_set():
            try:
                # 检查是否需要选择新话术
                self._check_and_select_script()
                
                # 等待下次检查
                self.stop_event.wait(self.check_interval)
                
            except Exception as e:
                self.logger.error(f"调度器循环异常: {e}")
                if self.on_scheduler_error:
                    self.on_scheduler_error(str(e))
                    
                # 出错后等待一段时间再继续
                self.stop_event.wait(30)
                
    def _check_and_select_script(self):
        """检查并选择话术"""
        try:
            current_time = datetime.now()
            
            # 获取当前时间应该播放的话术
            scheduled_scripts = self.time_segment_manager.get_active_scripts_for_time(current_time)
            
            selected_script = None
            selection_type = "none"
            
            if scheduled_scripts:
                # 有计划话术，随机选择一个（考虑优先级）
                selected_script = self._select_from_scheduled(scheduled_scripts)
                selection_type = "scheduled"
                self.stats['scheduled_selections'] += 1
            else:
                # 没有计划话术，使用备用话术
                selected_script = self._select_fallback_script()
                selection_type = "fallback"
                self.stats['fallback_selections'] += 1
                
            if selected_script:
                self.stats['total_selections'] += 1
                self.stats['last_selection_time'] = current_time
                self.stats['last_selected_script'] = selected_script
                
                self.logger.info(f"选择话术: {selected_script.get('name', 'Unknown')} ({selection_type})")
                
                # 触发回调
                if self.on_script_selected:
                    self.on_script_selected(selected_script, selection_type)
                    
        except Exception as e:
            self.logger.error(f"检查和选择话术失败: {e}")
            
    def _select_from_scheduled(self, scheduled_scripts: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """从计划话术中选择"""
        try:
            if not scheduled_scripts:
                return None
                
            # 按优先级分组
            priority_groups = {}
            for script in scheduled_scripts:
                priority = script.get('priority', 1)
                if priority not in priority_groups:
                    priority_groups[priority] = []
                priority_groups[priority].append(script)
                
            # 选择最高优先级组
            max_priority = max(priority_groups.keys())
            high_priority_scripts = priority_groups[max_priority]
            
            # 从最高优先级组中随机选择
            return random.choice(high_priority_scripts)
            
        except Exception as e:
            self.logger.error(f"从计划话术中选择失败: {e}")
            return None
            
    def _select_fallback_script(self) -> Optional[Dict[str, Any]]:
        """选择备用话术"""
        try:
            if not self.fallback_scripts:
                # 如果没有设置备用话术，随机选择一个话术
                all_scripts = self.script_manager.get_scripts_by_user()
                if all_scripts:
                    return random.choice(all_scripts)
                return None
                
            # 从备用话术中随机选择
            script_id = random.choice(self.fallback_scripts)
            return self.script_manager.get_script(script_id)
            
        except Exception as e:
            self.logger.error(f"选择备用话术失败: {e}")
            return None
            
    @handle_exceptions("ScriptScheduler")
    def get_current_script(self) -> Optional[Dict[str, Any]]:
        """获取当前应该播放的话术"""
        try:
            current_time = datetime.now()
            scheduled_scripts = self.time_segment_manager.get_active_scripts_for_time(current_time)
            
            if scheduled_scripts:
                return self._select_from_scheduled(scheduled_scripts)
            else:
                return self._select_fallback_script()
                
        except Exception as e:
            self.logger.error(f"获取当前话术失败: {e}")
            return None
            
    @handle_exceptions("ScriptScheduler")
    def get_random_script_line(self, script_id: Optional[int] = None) -> Optional[str]:
        """获取随机话术行"""
        try:
            if script_id is None:
                # 获取当前话术
                current_script = self.get_current_script()
                if not current_script:
                    return None
                script_id = current_script['id']
                
            return self.script_manager.get_random_line(script_id)
            
        except Exception as e:
            self.logger.error(f"获取随机话术行失败: {e}")
            return None
            
    @handle_exceptions("ScriptScheduler")
    def set_fallback_scripts(self, script_ids: List[int]):
        """设置备用话术"""
        self.fallback_scripts = script_ids.copy()
        self.logger.info(f"设置备用话术: {len(script_ids)} 个")
        
    @handle_exceptions("ScriptScheduler")
    def add_fallback_script(self, script_id: int):
        """添加备用话术"""
        if script_id not in self.fallback_scripts:
            self.fallback_scripts.append(script_id)
            self.logger.info(f"添加备用话术: {script_id}")
            
    @handle_exceptions("ScriptScheduler")
    def remove_fallback_script(self, script_id: int):
        """移除备用话术"""
        if script_id in self.fallback_scripts:
            self.fallback_scripts.remove(script_id)
            self.logger.info(f"移除备用话术: {script_id}")
            
    @handle_exceptions("ScriptScheduler")
    def get_schedule_preview(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取未来时间表预览"""
        try:
            preview = []
            current_time = datetime.now()
            
            for i in range(hours):
                check_time = current_time + timedelta(hours=i)
                scheduled_scripts = self.time_segment_manager.get_active_scripts_for_time(check_time)
                
                preview.append({
                    'time': check_time.strftime("%Y-%m-%d %H:%M"),
                    'hour': check_time.hour,
                    'scripts_count': len(scheduled_scripts),
                    'scripts': [
                        {
                            'name': script['name'],
                            'priority': script.get('priority', 1),
                            'category': script.get('category', 'default')
                        }
                        for script in scheduled_scripts[:3]  # 只显示前3个
                    ]
                })
                
            return preview
            
        except Exception as e:
            self.logger.error(f"获取时间表预览失败: {e}")
            return []
            
    @handle_exceptions("ScriptScheduler")
    def get_scheduler_stats(self) -> Dict[str, Any]:
        """获取调度器统计信息"""
        return {
            'is_running': self.is_running,
            'check_interval': self.check_interval,
            'fallback_scripts_count': len(self.fallback_scripts),
            'stats': self.stats.copy()
        }
        
    @handle_exceptions("ScriptScheduler")
    def set_check_interval(self, interval_seconds: int):
        """设置检查间隔"""
        if interval_seconds < 10:
            interval_seconds = 10  # 最小10秒
        elif interval_seconds > 3600:
            interval_seconds = 3600  # 最大1小时
            
        self.check_interval = interval_seconds
        self.logger.info(f"设置检查间隔: {interval_seconds} 秒")
        
    @handle_exceptions("ScriptScheduler")
    def force_select_script(self) -> Optional[Dict[str, Any]]:
        """强制选择话术"""
        try:
            self._check_and_select_script()
            return self.stats.get('last_selected_script')
            
        except Exception as e:
            self.logger.error(f"强制选择话术失败: {e}")
            return None
            
    @handle_exceptions("ScriptScheduler")
    def get_next_schedule_change(self) -> Optional[Dict[str, Any]]:
        """获取下一个时间表变化"""
        try:
            current_time = datetime.now()
            
            # 查找下一个时间段开始
            next_script = self.time_segment_manager.get_next_scheduled_script(current_time)
            
            if next_script:
                next_time_str = next_script['start_time']
                next_time = datetime.strptime(
                    f"{current_time.strftime('%Y-%m-%d')} {next_time_str}",
                    "%Y-%m-%d %H:%M"
                )
                
                # 如果时间已过，说明是明天的
                if next_time <= current_time:
                    next_time += timedelta(days=1)
                    
                return {
                    'next_time': next_time,
                    'script_name': next_script['script_name'],
                    'time_until': str(next_time - current_time).split('.')[0]  # 去掉微秒
                }
                
            return None
            
        except Exception as e:
            self.logger.error(f"获取下一个时间表变化失败: {e}")
            return None
