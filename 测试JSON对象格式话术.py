#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的JSON对象格式话术上传和解析功能
"""

import json
import requests

def test_json_object_format():
    """测试JSON对象格式的话术上传"""
    print("=" * 60)
    print("🧪 测试JSON对象格式话术上传和解析")
    print("=" * 60)
    
    # 服务器配置
    SERVER_URL = "http://localhost:12456"
    
    # 测试数据：时间段话术（JSON对象格式）
    script_name = "测试JSON对象格式话术"
    time_segments_data = {
        "0秒 - 10秒": "1***哈喽，大家好，首先感谢【大哥|哥哥|宝子】们的捧场。\n2***稍作停留也是爱，所能接触的都是这四个大字。",
        "10秒 - 20秒": "6***自古套路得人心，但唯有真心得天下。\n7***新来的家人们，相遇就是缘分。",
        "20秒 - 30秒": "11***相信自己的眼睛，相信自己的耳朵。\n12***斩青丝斩难过斩断红尘不为过。",
        "30秒 - 40秒": "16***今天只为做人气，玩归玩，闹归闹，不拿真诚开玩笑。\n17***我一说，你一笑，【大哥|哥哥|宝子】们开心比啥都重要。",
        "40秒 - 50秒": "21***只管开心只管爽，都是这四个大字都是这四个大字。\n22***走过南呀闯过北，主播认识大家不后悔。"
    }
    
    print(f"📝 话术名称: {script_name}")
    print(f"📊 时间段数量: {len(time_segments_data)}")
    print(f"🔍 数据格式: JSON对象")
    
    # 1. 测试上传（新格式：JSON对象）
    print("\n" + "=" * 40)
    print("📤 测试上传话术（JSON对象格式）")
    print("=" * 40)
    
    upload_request = {
        "类型": "上传话术",
        "话术名": script_name,
        "上传数据": time_segments_data  # 直接使用JSON对象
    }
    
    print(f"📋 请求格式:")
    print(f"  - 类型: {upload_request['类型']}")
    print(f"  - 话术名: {upload_request['话术名']}")
    print(f"  - 上传数据类型: {type(upload_request['上传数据'])}")
    print(f"  - 上传数据预览: {str(upload_request['上传数据'])[:200]}...")
    
    try:
        response = requests.post(f"{SERVER_URL}/", json=upload_request, timeout=10)
        print(f"\n📡 上传响应:")
        print(f"  - 状态码: {response.status_code}")
        print(f"  - 响应内容: {response.text[:200]}...")
        
        if response.status_code == 200:
            print("✅ 上传成功")
        else:
            print("❌ 上传失败")
            
    except Exception as e:
        print(f"❌ 上传异常: {e}")
    
    # 2. 测试获取话术
    print("\n" + "=" * 40)
    print("📥 测试获取话术内容")
    print("=" * 40)
    
    get_request = {
        "类型": "获取话术",
        "话术名": script_name
    }
    
    try:
        response = requests.post(f"{SERVER_URL}/", json=get_request, timeout=10)
        print(f"📡 获取响应:")
        print(f"  - 状态码: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            print(f"  - 内容长度: {len(content)} 字符")
            print(f"  - 内容预览: {content[:300]}...")
            
            # 尝试解析为JSON
            try:
                if content.strip().startswith('{') and content.strip().endswith('}'):
                    parsed_data = json.loads(content)
                    print(f"\n🔍 解析结果:")
                    print(f"  - 数据类型: {type(parsed_data)}")
                    print(f"  - 是否为字典: {isinstance(parsed_data, dict)}")
                    
                    if isinstance(parsed_data, dict):
                        print(f"  - 键数量: {len(parsed_data)}")
                        print(f"  - 键列表: {list(parsed_data.keys())}")
                        
                        # 检查是否为时间段格式
                        is_time_segment = all(isinstance(v, str) for v in parsed_data.values())
                        print(f"  - 是否为时间段格式: {is_time_segment}")
                        
                        if is_time_segment:
                            print(f"\n📋 时间段数据:")
                            for segment_name, segment_content in parsed_data.items():
                                lines = segment_content.split('\n')
                                print(f"  - {segment_name}: {len(lines)} 行话术")
                                for line in lines[:2]:  # 只显示前2行
                                    print(f"    * {line}")
                                if len(lines) > 2:
                                    print(f"    ... 还有 {len(lines) - 2} 行")
                        
                        print("✅ 解析成功")
                    else:
                        print("⚠️ 不是字典格式")
                else:
                    print("⚠️ 不是JSON格式")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                
        else:
            print(f"❌ 获取失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 获取异常: {e}")
    
    # 3. 对比测试：字符串格式 vs JSON对象格式
    print("\n" + "=" * 40)
    print("🔄 对比测试：字符串格式 vs JSON对象格式")
    print("=" * 40)
    
    # 字符串格式上传
    string_script_name = "测试字符串格式话术"
    string_content = json.dumps(time_segments_data, ensure_ascii=False, indent=2)
    
    string_upload_request = {
        "类型": "上传话术",
        "话术名": string_script_name,
        "上传数据": string_content  # JSON字符串
    }
    
    print(f"📝 字符串格式测试:")
    print(f"  - 话术名: {string_script_name}")
    print(f"  - 数据类型: {type(string_upload_request['上传数据'])}")
    print(f"  - 数据长度: {len(string_upload_request['上传数据'])} 字符")
    
    try:
        response = requests.post(f"{SERVER_URL}/", json=string_upload_request, timeout=10)
        if response.status_code == 200:
            print("✅ 字符串格式上传成功")
        else:
            print("❌ 字符串格式上传失败")
    except Exception as e:
        print(f"❌ 字符串格式上传异常: {e}")
    
    print(f"\n📊 格式对比总结:")
    print(f"  - JSON对象格式: 直接传递字典对象，更简洁")
    print(f"  - JSON字符串格式: 需要先序列化为字符串，兼容性更好")
    print(f"  - 推荐使用: JSON对象格式（新版本）")
    
    print("\n" + "=" * 60)
    print("🎯 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_json_object_format()
