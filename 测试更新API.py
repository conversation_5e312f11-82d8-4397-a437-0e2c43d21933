#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新API功能
不依赖UI组件，纯API测试
"""

import sys
import json
import requests
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_update_api():
    """测试更新API"""
    print("🧪 测试更新API功能")
    print("=" * 60)
    
    # API配置
    server_url = "http://43.143.225.151:12456"
    update_api_url = f"{server_url}/admin/api/updates/current"
    
    print(f"📡 服务器地址: {server_url}")
    print(f"📡 更新API: {update_api_url}")
    
    try:
        # 发送GET请求
        print("\n📤 发送GET请求...")
        response = requests.get(update_api_url, timeout=10)
        
        print(f"📥 响应状态码: {response.status_code}")
        print(f"📥 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            # 解析JSON响应
            try:
                data = response.json()
                print(f"📊 响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                
                # 提取关键信息
                version = data.get('version', '')
                description = data.get('description', '')
                release_date = data.get('release_date', '')
                
                print(f"\n✅ 解析结果:")
                print(f"   版本号: {version}")
                print(f"   发布日期: {release_date}")
                print(f"   更新内容: {description}")
                
                return {
                    'success': True,
                    'version': version,
                    'description': description,
                    'release_date': release_date
                }
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"📄 原始响应: {response.text}")
                return {'success': False, 'error': f'JSON解析失败: {e}'}
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"📄 错误响应: {response.text}")
            return {'success': False, 'error': f'HTTP {response.status_code}'}
            
    except requests.RequestException as e:
        print(f"❌ 网络请求异常: {e}")
        return {'success': False, 'error': f'网络异常: {e}'}

def test_config_file():
    """测试配置文件操作"""
    print("\n🧪 测试配置文件操作")
    print("=" * 60)
    
    config_file = Path("config/app_config.json")
    print(f"📁 配置文件路径: {config_file}")
    print(f"📁 文件存在: {config_file.exists()}")
    
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            current_version = config.get('app', {}).get('version', '未知')
            print(f"📊 当前版本: {current_version}")
            
            # 测试版本更新
            print(f"\n🔄 测试版本更新...")
            backup_version = current_version
            test_version = "2.1.0"
            
            # 更新版本
            config['app']['version'] = test_version
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 版本已更新为: {test_version}")
            
            # 验证更新
            with open(config_file, 'r', encoding='utf-8') as f:
                updated_config = json.load(f)
            
            updated_version = updated_config.get('app', {}).get('version', '未知')
            print(f"✅ 验证更新后版本: {updated_version}")
            
            # 恢复原版本
            config['app']['version'] = backup_version
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"🔄 已恢复原版本: {backup_version}")
            
            return {'success': True, 'current_version': backup_version}
            
        except Exception as e:
            print(f"❌ 配置文件操作失败: {e}")
            return {'success': False, 'error': str(e)}
    else:
        print("❌ 配置文件不存在")
        return {'success': False, 'error': '配置文件不存在'}

def test_version_comparison():
    """测试版本号比较"""
    print("\n🧪 测试版本号比较")
    print("=" * 60)
    
    def compare_versions(current: str, latest: str) -> bool:
        """比较版本号"""
        try:
            current_parts = [int(x) for x in current.split('.')]
            latest_parts = [int(x) for x in latest.split('.')]
            
            max_len = max(len(current_parts), len(latest_parts))
            current_parts.extend([0] * (max_len - len(current_parts)))
            latest_parts.extend([0] * (max_len - len(latest_parts)))
            
            for i in range(max_len):
                if latest_parts[i] > current_parts[i]:
                    return True
                elif latest_parts[i] < current_parts[i]:
                    return False
            
            return False
        except Exception as e:
            print(f"版本比较失败: {e}")
            return False
    
    # 测试用例
    test_cases = [
        ("2.0.0", "2.1.0", True),   # 有更新
        ("2.0.0", "2.0.1", True),   # 有更新
        ("2.1.0", "2.0.0", False),  # 无更新
        ("2.0.0", "2.0.0", False),  # 版本相同
        ("1.9.9", "2.0.0", True),   # 大版本更新
    ]
    
    for current, latest, expected in test_cases:
        result = compare_versions(current, latest)
        status = "✅" if result == expected else "❌"
        print(f"{status} {current} vs {latest} -> {result} (期望: {expected})")
    
    return {'success': True}

def test_update_service():
    """测试更新服务类"""
    print("\n🧪 测试更新服务类")
    print("=" * 60)
    
    try:
        from src.services.update_service import UpdateService
        
        # 创建更新服务实例
        update_service = UpdateService("http://43.143.225.151:12456")
        print("✅ 更新服务实例创建成功")
        
        # 测试获取当前版本
        current_version = update_service.get_current_version_from_config()
        print(f"📊 当前版本: {current_version}")
        
        # 测试检查更新
        print("\n🔍 检查更新...")
        update_result = update_service.check_for_updates()
        
        print(f"📊 检查结果: {json.dumps(update_result, ensure_ascii=False, indent=2)}")
        
        if update_result['success']:
            if update_result['has_update']:
                print(f"🆕 发现新版本: {update_result['update_info']['version']}")
                
                # 测试格式化更新信息
                formatted_text = update_service.get_update_info_text(update_result['update_info'])
                print(f"\n📝 格式化的更新信息:")
                print(formatted_text)
            else:
                print("✅ 当前已是最新版本")
        else:
            print(f"❌ 检查更新失败: {update_result['message']}")
        
        return update_result
        
    except Exception as e:
        print(f"❌ 更新服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

def main():
    """主函数"""
    print("🚀 开始更新功能完整测试")
    print("=" * 80)
    
    # 测试1: API调用
    print("\n📋 测试1: 更新API调用")
    api_result = test_update_api()
    
    # 测试2: 配置文件操作
    print("\n📋 测试2: 配置文件操作")
    config_result = test_config_file()
    
    # 测试3: 版本号比较
    print("\n📋 测试3: 版本号比较")
    version_result = test_version_comparison()
    
    # 测试4: 更新服务类
    print("\n📋 测试4: 更新服务类")
    service_result = test_update_service()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 测试总结")
    print("=" * 80)
    print(f"✅ API调用: {'通过' if api_result['success'] else '失败'}")
    print(f"✅ 配置文件: {'通过' if config_result['success'] else '失败'}")
    print(f"✅ 版本比较: {'通过' if version_result['success'] else '失败'}")
    print(f"✅ 更新服务: {'通过' if service_result['success'] else '失败'}")
    
    all_passed = all([
        api_result['success'],
        config_result['success'],
        version_result['success'],
        service_result['success']
    ])
    
    if all_passed:
        print("\n🎉 所有测试通过！更新功能可以正常使用！")
    else:
        print("\n❌ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
