"""
AI Broadcaster v2 - 日志系统
提供统一的日志管理功能
"""

import os
import logging
import logging.config
from pathlib import Path
from typing import Optional

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    print("⚠️  PyYAML未安装，将使用基础日志配置")


class LoggerManager:
    """日志管理器"""

    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._setup_logging()
            self._initialized = True

    def _setup_logging(self):
        """设置日志配置"""
        # 确保日志目录存在
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # 加载日志配置
        config_path = Path("config/logging.yaml")
        if config_path.exists() and YAML_AVAILABLE:
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                logging.config.dictConfig(config)
                print(f"✅ 日志配置已从 {config_path} 加载")
            except Exception as e:
                print(f"❌ 加载日志配置失败: {e}")
                self._setup_basic_logging()
        else:
            if not config_path.exists():
                print(f"⚠️  日志配置文件不存在: {config_path}")
            else:
                print("⚠️  PyYAML未安装，无法加载YAML日志配置")
            self._setup_basic_logging()

    def _setup_basic_logging(self):
        """设置基础日志配置"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        print("✅ 使用基础日志配置")

    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        return logging.getLogger(name)


# 全局日志管理器实例
_logger_manager = LoggerManager()


def setup_logging():
    """初始化日志系统"""
    global _logger_manager
    _logger_manager = LoggerManager()
    return _logger_manager


def get_logger(name: str) -> logging.Logger:
    """
    获取日志器

    Args:
        name: 日志器名称

    Returns:
        logging.Logger: 日志器实例
    """
    return _logger_manager.get_logger(name)


class AppLogger:
    """应用程序日志器包装类"""

    def __init__(self, name: str):
        self.logger = get_logger(name)
        self.name = name

    def debug(self, message: str, *args, **kwargs):
        """调试日志"""
        self.logger.debug(message, *args, **kwargs)

    def info(self, message: str, *args, **kwargs):
        """信息日志"""
        self.logger.info(message, *args, **kwargs)

    def warning(self, message: str, *args, **kwargs):
        """警告日志"""
        self.logger.warning(message, *args, **kwargs)

    def error(self, message: str, *args, **kwargs):
        """错误日志"""
        self.logger.error(message, *args, **kwargs)

    def critical(self, message: str, *args, **kwargs):
        """严重错误日志"""
        self.logger.critical(message, *args, **kwargs)

    def exception(self, message: str, *args, **kwargs):
        """异常日志（包含堆栈跟踪）"""
        self.logger.exception(message, *args, **kwargs)

    def log_startup(self, component: str):
        """记录组件启动"""
        self.info(f"🚀 {component} 启动中...")

    def log_shutdown(self, component: str):
        """记录组件关闭"""
        self.info(f"🛑 {component} 正在关闭...")

    def log_success(self, message: str):
        """记录成功操作"""
        self.info(f"✅ {message}")

    def log_failure(self, message: str):
        """记录失败操作"""
        self.error(f"❌ {message}")

    def log_warning(self, message: str):
        """记录警告"""
        self.warning(f"⚠️  {message}")


# 便捷函数
def create_logger(name: str) -> AppLogger:
    """创建应用程序日志器"""
    return AppLogger(name)
