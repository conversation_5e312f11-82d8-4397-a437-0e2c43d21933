"""
AI Broadcaster v2 - 报时管理器
定时播报规定的报时话术
"""

import random
import re
import threading
import time
from typing import List, Optional, Callable
from datetime import datetime, timedelta

from ..services.logging_service import create_logger
from ..services.error_handler import handle_exceptions


class TimeReportManager:
    """报时管理器"""
    
    def __init__(self):
        self.logger = create_logger("time_report_manager")
        
        # 报时设置
        self.enabled = False
        self.interval_minutes = 5  # 报时间隔（分钟）
        self.scripts = []  # 报时话术列表
        self.used_scripts = set()  # 已使用的话术索引
        
        # 定时器
        self.timer = None
        self.last_report_time = None
        
        # 回调函数
        self.on_time_report: Optional[Callable] = None  # 报时触发回调
        
        self.logger.info("报时管理器初始化完成")
    
    @handle_exceptions("TimeReportManager")
    def set_config(self, enabled: bool, interval_minutes: int, scripts: List[str]):
        """设置报时配置"""
        try:
            self.enabled = enabled
            self.interval_minutes = max(1, interval_minutes)  # 最小1分钟
            
            # 解析话术内容
            self.scripts = self._parse_scripts(scripts)
            self.used_scripts.clear()  # 重置已使用记录
            
            self.logger.info(f"报时配置更新: 启用={enabled}, 间隔={interval_minutes}分钟, 话术数量={len(self.scripts)}")
            
            # 重启定时器
            if self.enabled:
                self.start()
            else:
                self.stop()
                
        except Exception as e:
            self.logger.error(f"设置报时配置失败: {e}")
    
    @handle_exceptions("TimeReportManager")
    def start(self):
        """启动报时"""
        try:
            if not self.enabled or not self.scripts:
                self.logger.warning("报时未启用或无话术内容")
                return
            
            # 停止现有定时器
            self.stop()
            
            # 计算下次报时时间
            next_time = self._calculate_next_time()
            delay = (next_time - datetime.now()).total_seconds()
            
            if delay > 0:
                self.timer = threading.Timer(delay, self._on_timer)
                self.timer.daemon = True
                self.timer.start()
                
                self.logger.info(f"报时已启动，下次报时时间: {next_time.strftime('%H:%M:%S')}")
            else:
                # 立即执行一次
                self._trigger_report()
                
        except Exception as e:
            self.logger.error(f"启动报时失败: {e}")
    
    @handle_exceptions("TimeReportManager")
    def stop(self):
        """停止报时"""
        try:
            if self.timer:
                self.timer.cancel()
                self.timer = None
                
            self.logger.info("报时已停止")
            
        except Exception as e:
            self.logger.error(f"停止报时失败: {e}")
    
    def _calculate_next_time(self) -> datetime:
        """计算下次报时时间"""
        now = datetime.now()
        
        if self.last_report_time:
            # 基于上次报时时间计算
            next_time = self.last_report_time + timedelta(minutes=self.interval_minutes)
            
            # 如果计算出的时间已过，则从当前时间开始
            if next_time <= now:
                next_time = now + timedelta(minutes=self.interval_minutes)
        else:
            # 首次报时，从当前时间开始
            next_time = now + timedelta(minutes=self.interval_minutes)
        
        return next_time
    
    def _on_timer(self):
        """定时器触发"""
        try:
            self._trigger_report()
            
            # 如果仍然启用，设置下次定时器
            if self.enabled:
                self.start()
                
        except Exception as e:
            self.logger.error(f"定时器触发异常: {e}")
    
    def _trigger_report(self):
        """触发报时"""
        try:
            # 获取随机话术
            script = self.get_random_script()
            
            if script:
                self.last_report_time = datetime.now()
                
                self.logger.info(f"触发报时: {script[:50]}...")
                
                # 触发回调
                if self.on_time_report:
                    self.on_time_report(script)
            else:
                self.logger.warning("无可用的报时话术")
                
        except Exception as e:
            self.logger.error(f"触发报时失败: {e}")
    
    def get_random_script(self) -> Optional[str]:
        """获取随机不重复的报时话术"""
        if not self.scripts:
            return None
        
        used_scripts = self.used_scripts
        
        # 如果所有话术都用完了，重置
        if len(used_scripts) >= len(self.scripts):
            used_scripts.clear()
            self.logger.info("报时话术已全部使用完毕，重置")
        
        # 获取未使用的话术
        available_indices = [i for i in range(len(self.scripts)) if i not in used_scripts]
        
        if not available_indices:
            return None
        
        # 随机选择一个话术
        selected_index = random.choice(available_indices)
        selected_script = self.scripts[selected_index]
        
        # 标记为已使用
        used_scripts.add(selected_index)
        
        # 处理变量词和随机选择
        processed_script = self._process_script_variables(selected_script)
        
        self.logger.debug(f"获取报时话术，剩余未使用: {len(self.scripts) - len(used_scripts)}")
        return processed_script
    
    def _parse_scripts(self, scripts: List[str]) -> List[str]:
        """解析话术内容，提取有效话术"""
        parsed_scripts = []
        
        for script in scripts:
            script = script.strip()
            # 跳过空行和注释行
            if not script or script.startswith('#'):
                continue
            
            # 匹配话术格式：数字***内容 或直接内容
            match = re.match(r'^\d+\*\*\*(.+)$', script)
            if match:
                script_content = match.group(1).strip()
                parsed_scripts.append(script_content)
            else:
                # 直接添加内容
                parsed_scripts.append(script)
        
        return parsed_scripts
    
    def _process_script_variables(self, script: str) -> str:
        """处理话术中的变量词和随机选择"""
        processed = script
        
        # 处理随机选择：【选项1|选项2|选项3】
        def replace_random_choice(match):
            choices = match.group(1).split('|')
            return random.choice(choices)
        
        processed = re.sub(r'【([^】]+)】', replace_random_choice, processed)
        
        # 处理时间变量
        now = datetime.now()
        processed = processed.replace('{time}', now.strftime('%H点%M分'))
        processed = processed.replace('{date}', now.strftime('%Y-%m-%d'))
        processed = processed.replace('{hour}', str(now.hour))
        processed = processed.replace('{minute}', str(now.minute))
        
        # 这里可以添加更多变量词处理
        # 例如：{nick}, {gametype}, {gamename} 等
        
        return processed
    
    def get_status(self) -> dict:
        """获取报时状态"""
        next_time = None
        if self.enabled and self.timer:
            next_time = self._calculate_next_time()
        
        return {
            'enabled': self.enabled,
            'interval_minutes': self.interval_minutes,
            'script_count': len(self.scripts),
            'used_scripts': len(self.used_scripts),
            'remaining_scripts': len(self.scripts) - len(self.used_scripts),
            'last_report_time': self.last_report_time.isoformat() if self.last_report_time else None,
            'next_report_time': next_time.isoformat() if next_time else None,
            'timer_active': self.timer is not None and self.timer.is_alive() if self.timer else False
        }
    
    def force_report(self) -> bool:
        """强制触发一次报时"""
        try:
            if not self.scripts:
                self.logger.warning("无报时话术内容")
                return False
            
            self._trigger_report()
            return True
            
        except Exception as e:
            self.logger.error(f"强制报时失败: {e}")
            return False
    
    def get_all_scripts(self) -> List[str]:
        """获取所有报时话术"""
        return self.scripts.copy()
    
    def get_statistics(self) -> dict:
        """获取报时统计信息"""
        total_reports = 0
        if self.last_report_time:
            # 估算总报时次数（这里可以改进为实际记录）
            total_reports = 1
        
        return {
            'total_scripts': len(self.scripts),
            'used_scripts': len(self.used_scripts),
            'total_reports': total_reports,
            'enabled': self.enabled,
            'interval_minutes': self.interval_minutes
        }


def main():
    """测试函数"""
    manager = TimeReportManager()
    
    # 设置报时话术
    scripts = [
        "1***现在时间是{time}，感谢大家的观看！",
        "2***【时间过得真快|又到了整点时间|时间提醒】，现在是{hour}点{minute}分",
        "3***报时时间到！今天是{date}，当前时间{time}",
        "4***【大家好|各位观众|朋友们】，现在为您报时：{time}"
    ]
    
    # 配置报时（测试用，间隔设为1分钟）
    manager.set_config(enabled=True, interval_minutes=1, scripts=scripts)
    
    # 设置回调
    def on_report(script):
        print(f"📢 报时: {script}")
    
    manager.on_time_report = on_report
    
    # 测试强制报时
    print("测试强制报时:")
    for i in range(3):
        manager.force_report()
        time.sleep(1)
    
    # 测试状态
    status = manager.get_status()
    print(f"报时状态: {status}")
    
    # 等待自动报时（测试用）
    print("等待自动报时...")
    time.sleep(65)  # 等待1分钟多一点
    
    # 停止报时
    manager.stop()


if __name__ == "__main__":
    main()
