#!/usr/bin/env python3
"""
AI主播系统 v2 - API服务测试脚本
测试所有新实现的API服务功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.logging_service import setup_logging, create_logger
from src.services.voice_service import VoiceService
from src.services.script_service import ScriptService
from src.services.dialogue_service import DialogueService
from src.services.danmaku_service import DanmakuService
from src.services.obs_service import OBSService


async def test_voice_service():
    """测试语音服务"""
    print("\n" + "="*50)
    print("🎤 测试语音服务")
    print("="*50)
    
    voice_service = VoiceService()
    
    # 测试获取主播列表
    print("\n📋 获取主播列表...")
    speakers_result = voice_service.get_speakers()
    if speakers_result['success']:
        print(f"✅ 获取成功，共 {len(speakers_result['speakers'])} 个主播")
        for speaker in speakers_result['speakers'][:3]:  # 只显示前3个
            print(f"   - ID: {speaker.get('id')}, 名称: {speaker.get('name')}")
    else:
        print(f"❌ 获取失败: {speakers_result['message']}")
    
    # 测试语音下载
    print("\n🔊 测试语音下载...")
    test_text = "你好，欢迎来到AI主播系统！"
    download_result = voice_service.download_voice(test_text, speaker_id=0, speed=1.0)
    if download_result['success']:
        cached = "（缓存）" if download_result.get('cached') else "（新下载）"
        print(f"✅ 语音下载成功 {cached}")
        print(f"   文件路径: {download_result['file_path']}")
    else:
        print(f"❌ 语音下载失败: {download_result['message']}")
    
    # 测试缓存信息
    print("\n📊 语音缓存信息...")
    cache_info = voice_service.get_voice_cache_info()
    print(f"   缓存文件数: {cache_info['file_count']}")
    print(f"   缓存大小: {cache_info['total_size_mb']} MB")
    
    voice_service.close()


async def test_script_service():
    """测试话术服务"""
    print("\n" + "="*50)
    print("📝 测试话术服务")
    print("="*50)
    
    script_service = ScriptService()
    
    # 测试获取话术列表
    print("\n📋 获取话术列表...")
    scripts_result = script_service.get_script_list()
    if scripts_result['success']:
        print(f"✅ 获取成功，共 {len(scripts_result['scripts'])} 个话术")
        for script in scripts_result['scripts'][:3]:  # 只显示前3个
            print(f"   - {script.get('name')}")
    else:
        print(f"❌ 获取失败: {scripts_result['message']}")
    
    # 测试获取话术内容（如果有话术的话）
    if scripts_result['success'] and scripts_result['scripts']:
        first_script = scripts_result['scripts'][0]['name']
        print(f"\n📄 获取话术内容: {first_script}")
        content_result = script_service.get_script_content(first_script)
        if content_result['success']:
            content = content_result['content'][:100] + "..." if len(content_result['content']) > 100 else content_result['content']
            print(f"✅ 获取成功: {content}")
        else:
            print(f"❌ 获取失败: {content_result['message']}")
    
    script_service.close()


async def test_dialogue_service():
    """测试AI对话服务"""
    print("\n" + "="*50)
    print("💬 测试AI对话服务")
    print("="*50)
    
    dialogue_service = DialogueService()
    
    # 测试获取对话列表
    print("\n📋 获取AI对话列表...")
    dialogues_result = dialogue_service.get_dialogue_list()
    if dialogues_result['success']:
        print(f"✅ 获取成功，共 {len(dialogues_result['dialogues'])} 个对话")
        for dialogue in dialogues_result['dialogues'][:3]:  # 只显示前3个
            print(f"   - {dialogue.get('name')}")
    else:
        print(f"❌ 获取失败: {dialogues_result['message']}")
    
    # 测试获取对话内容（如果有对话的话）
    if dialogues_result['success'] and dialogues_result['dialogues']:
        first_dialogue = dialogues_result['dialogues'][0]['name']
        print(f"\n📄 获取对话内容: {first_dialogue}")
        content_result = dialogue_service.get_dialogue_content(first_dialogue)
        if content_result['success']:
            content = content_result['content'][:100] + "..." if len(content_result['content']) > 100 else content_result['content']
            print(f"✅ 获取成功: {content}")
        else:
            print(f"❌ 获取失败: {content_result['message']}")
    
    dialogue_service.close()


async def test_danmaku_service():
    """测试弹幕服务"""
    print("\n" + "="*50)
    print("🌐 测试弹幕WebSocket服务")
    print("="*50)
    
    danmaku_service = DanmakuService()
    
    # 添加消息处理器
    def handle_danmaku(message):
        print(f"📨 收到弹幕: {message['user']} - {message['message']}")
    
    def handle_connection(connected):
        status = "连接" if connected else "断开"
        print(f"🔗 弹幕服务器{status}")
    
    danmaku_service.add_message_handler(handle_danmaku)
    danmaku_service.add_connection_handler(handle_connection)
    
    # 测试连接
    print("\n🔗 连接弹幕服务器...")
    connected = await danmaku_service.connect()
    if connected:
        print("✅ 连接成功")
        
        # 监听5秒钟
        print("👂 监听弹幕消息（5秒）...")
        listen_task = asyncio.create_task(danmaku_service.start_listening())
        await asyncio.sleep(5)
        
        # 断开连接
        await danmaku_service.disconnect()
        print("✅ 已断开连接")
    else:
        print("❌ 连接失败")


async def test_obs_service():
    """测试OBS服务"""
    print("\n" + "="*50)
    print("🎥 测试OBS WebSocket服务")
    print("="*50)
    
    obs_service = OBSService()
    
    # 测试连接
    print("\n🔗 连接OBS服务器...")
    connected = await obs_service.connect()
    if connected:
        print("✅ 连接成功")
        
        # 测试获取场景列表
        print("\n📋 获取场景列表...")
        scenes_result = await obs_service.get_scene_list()
        if scenes_result['success']:
            scenes = scenes_result['data'].get('scenes', [])
            print(f"✅ 获取成功，共 {len(scenes)} 个场景")
            for scene in scenes[:3]:  # 只显示前3个
                print(f"   - {scene.get('sceneName')}")
        else:
            print(f"❌ 获取失败: {scenes_result['message']}")
        
        # 测试获取当前场景
        print("\n🎬 获取当前场景...")
        current_scene_result = await obs_service.get_current_scene()
        if current_scene_result['success']:
            current_scene = current_scene_result['data'].get('currentProgramSceneName')
            print(f"✅ 当前场景: {current_scene}")
        else:
            print(f"❌ 获取失败: {current_scene_result['message']}")
        
        # 断开连接
        await obs_service.disconnect()
        print("✅ 已断开OBS连接")
    else:
        print("❌ OBS连接失败（请确保OBS Studio已启动并启用WebSocket服务器）")


async def main():
    """主测试函数"""
    # 初始化日志
    setup_logging()
    logger = create_logger("api_test")
    
    print("🚀 AI主播系统 v2 - API服务测试")
    print("="*60)
    
    try:
        # 测试各个服务
        await test_voice_service()
        await test_script_service()
        await test_dialogue_service()
        await test_danmaku_service()
        await test_obs_service()
        
        print("\n" + "="*60)
        print("🎉 所有API服务测试完成！")
        
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        print(f"\n❌ 测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
