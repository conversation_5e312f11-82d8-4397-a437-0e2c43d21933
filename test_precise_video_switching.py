#!/usr/bin/env python3
"""
测试精确视频切换逻辑
新的切换顺序：在旧源要播放完成前1秒，新源变速 → 播放新源 → 显示新源 → 隐藏旧源 → 停止旧源
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_precise_switching_logic():
    """测试精确切换逻辑"""
    print("🧪 测试精确视频切换逻辑")
    print("=" * 50)
    
    try:
        from src.core.playback.dual_video_manager import DualVideoManager
        
        # 创建模拟的OBS控制器
        class MockOBSController:
            def __init__(self):
                self.is_connected = True
                self.connected = True
                self.test_scenario = "normal"
                self.call_log = []
                
            def get_media_status_sync(self, source_name):
                scenarios = {
                    "normal": {
                        'media_state': 'OBS_MEDIA_STATE_PLAYING',
                        'progress_percent': 50.0,
                        'media_duration': 60000,  # 60秒
                        'media_cursor': 30000     # 30秒，剩余30秒
                    },
                    "approaching_end": {
                        'media_state': 'OBS_MEDIA_STATE_PLAYING',
                        'progress_percent': 98.3,
                        'media_duration': 60000,  # 60秒
                        'media_cursor': 59000     # 59秒，剩余1秒
                    },
                    "very_close_end": {
                        'media_state': 'OBS_MEDIA_STATE_PLAYING',
                        'progress_percent': 99.8,
                        'media_duration': 60000,  # 60秒
                        'media_cursor': 59900     # 59.9秒，剩余0.1秒
                    },
                    "ended": {
                        'media_state': 'OBS_MEDIA_STATE_ENDED',
                        'progress_percent': 100.0,
                        'media_duration': 60000,
                        'media_cursor': 60000
                    }
                }
                return scenarios.get(self.test_scenario, scenarios["normal"])
                
            def send_request_sync(self, request_type, params=None):
                # 记录调用
                self.call_log.append({
                    'type': request_type,
                    'params': params,
                    'time': time.time()
                })
                return {"success": True}
        
        # 创建双主视频管理器
        mock_obs = MockOBSController()
        dual_manager = DualVideoManager(obs_controller=mock_obs)
        dual_manager.set_video_sources("test_video_a", "test_video_b")
        
        # 重置切换时间，允许切换
        dual_manager._last_switch_time = 0
        
        print("📋 测试场景1: 正常播放中（剩余30秒）")
        mock_obs.test_scenario = "normal"
        mock_obs.call_log.clear()
        
        result1 = dual_manager._check_active_source_status_ui_design_style()
        print(f"  结果: {'触发准备' if dual_manager._next_source_prepared else '未触发准备'}")
        print(f"  调用次数: {len(mock_obs.call_log)}")
        
        print("\n📋 测试场景2: 接近结束（剩余1秒）- 应该开始准备新源")
        mock_obs.test_scenario = "approaching_end"
        mock_obs.call_log.clear()
        dual_manager._next_source_prepared = False  # 重置状态
        
        result2 = dual_manager._check_active_source_status_ui_design_style()
        print(f"  结果: {'✅ 触发准备' if dual_manager._next_source_prepared else '❌ 未触发准备'}")
        print(f"  调用次数: {len(mock_obs.call_log)}")
        
        # 检查调用类型
        call_types = [call['type'] for call in mock_obs.call_log]
        has_speed_setting = any("SetInputSettings" in call_type for call_type in call_types)
        has_media_action = any("TriggerMediaInputAction" in call_type for call_type in call_types)
        
        print(f"  包含变速调用: {'✅' if has_speed_setting else '❌'}")
        print(f"  包含播放调用: {'✅' if has_media_action else '❌'}")
        
        print("\n📋 测试场景3: 非常接近结束（剩余0.1秒）- 应该执行最终切换")
        mock_obs.test_scenario = "very_close_end"
        mock_obs.call_log.clear()
        # 保持准备状态
        
        old_active = dual_manager.current_active_source
        result3 = dual_manager._check_active_source_status_ui_design_style()
        new_active = dual_manager.current_active_source
        
        print(f"  结果: {'✅ 执行切换' if old_active != new_active else '❌ 未执行切换'}")
        print(f"  切换前: {old_active}")
        print(f"  切换后: {new_active}")
        print(f"  调用次数: {len(mock_obs.call_log)}")
        
        # 检查最终切换的调用
        show_calls = [call for call in mock_obs.call_log if "SetSceneItemEnabled" in call.get('type', '')]
        print(f"  显示/隐藏调用: {len(show_calls)}")
        
        print("\n📋 测试场景4: 直接测试准备方法")
        dual_manager._next_source_prepared = False
        mock_obs.call_log.clear()
        
        prepare_result = dual_manager._prepare_next_source_for_seamless_switch()
        print(f"  准备结果: {'✅ 成功' if prepare_result else '❌ 失败'}")
        print(f"  准备状态: {'✅ 已准备' if dual_manager._next_source_prepared else '❌ 未准备'}")
        print(f"  调用次数: {len(mock_obs.call_log)}")
        
        print("\n📋 测试场景5: 直接测试最终切换方法")
        dual_manager._next_source_prepared = True
        mock_obs.call_log.clear()
        old_active = dual_manager.current_active_source
        
        final_result = dual_manager._execute_final_switch()
        new_active = dual_manager.current_active_source
        
        print(f"  最终切换结果: {'✅ 成功' if final_result else '❌ 失败'}")
        print(f"  切换前: {old_active}")
        print(f"  切换后: {new_active}")
        print(f"  状态重置: {'✅ 已重置' if not dual_manager._next_source_prepared else '❌ 未重置'}")
        print(f"  调用次数: {len(mock_obs.call_log)}")
        
        # 综合评估
        all_tests_passed = (
            not result1 and  # 正常播放不应该触发
            dual_manager._next_source_prepared and  # 接近结束应该触发准备
            old_active != new_active and  # 应该发生切换
            prepare_result and  # 准备方法应该成功
            final_result  # 最终切换应该成功
        )
        
        if all_tests_passed:
            print("\n✅ 精确视频切换逻辑测试通过")
            return True
        else:
            print("\n❌ 精确视频切换逻辑测试失败")
            return False
        
    except Exception as e:
        print(f"❌ 精确切换逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_timing_accuracy():
    """测试时机准确性"""
    print("\n🧪 测试切换时机准确性")
    print("=" * 50)
    
    try:
        from src.core.playback.dual_video_manager import DualVideoManager
        
        # 创建模拟的OBS控制器，支持不同剩余时间
        class TimingMockOBSController:
            def __init__(self):
                self.is_connected = True
                self.connected = True
                self.remaining_seconds = 5.0  # 可调整的剩余时间
                
            def get_media_status_sync(self, source_name):
                duration = 60000  # 60秒总时长
                cursor = duration - (self.remaining_seconds * 1000)  # 根据剩余时间计算位置
                progress = (cursor / duration) * 100
                
                return {
                    'media_state': 'OBS_MEDIA_STATE_PLAYING',
                    'progress_percent': progress,
                    'media_duration': duration,
                    'media_cursor': cursor
                }
                
            def send_request_sync(self, request_type, params=None):
                return {"success": True}
        
        # 创建双主视频管理器
        mock_obs = TimingMockOBSController()
        dual_manager = DualVideoManager(obs_controller=mock_obs)
        dual_manager.set_video_sources("test_video_a", "test_video_b")
        dual_manager._last_switch_time = 0
        
        # 测试不同的剩余时间
        test_times = [
            (5.0, False, "剩余5秒 - 不应该触发"),
            (2.0, False, "剩余2秒 - 不应该触发"),
            (1.0, True, "剩余1秒 - 应该开始准备"),
            (0.5, True, "剩余0.5秒 - 应该保持准备状态"),
            (0.1, True, "剩余0.1秒 - 应该执行最终切换"),
        ]
        
        all_timing_correct = True
        
        for remaining_time, should_prepare, description in test_times:
            print(f"\n📋 {description}")
            
            # 设置剩余时间
            mock_obs.remaining_seconds = remaining_time
            
            # 重置状态（除非是连续测试）
            if remaining_time >= 1.0:
                dual_manager._next_source_prepared = False
            
            # 检查状态
            old_prepared = dual_manager._next_source_prepared
            old_active = dual_manager.current_active_source

            dual_manager._check_active_source_status_ui_design_style()

            new_prepared = dual_manager._next_source_prepared
            new_active = dual_manager.current_active_source

            # 特殊处理0.1秒的情况（应该执行最终切换）
            if remaining_time <= 0.1:
                # 对于最终切换，检查是否发生了视频源切换
                if old_active != new_active:
                    print(f"  ✅ 正确执行最终切换 ({old_active} -> {new_active})")
                    # 最终切换后状态会被重置，这是正确的
                    if not new_prepared:
                        print(f"  ✅ 切换后状态正确重置")
                    else:
                        print(f"  ⚠️ 切换后状态未重置")
                else:
                    print(f"  ❌ 应该执行最终切换但没有切换")
                    all_timing_correct = False
            else:
                # 判断是否符合预期
                if should_prepare:
                    if new_prepared:
                        print(f"  ✅ 正确触发准备状态")
                    else:
                        print(f"  ❌ 应该触发准备但没有触发")
                        all_timing_correct = False
                else:
                    if not new_prepared:
                        print(f"  ✅ 正确没有触发准备")
                    else:
                        print(f"  ⚠️ 不应该触发准备但触发了")

            print(f"  准备状态: {old_prepared} -> {new_prepared}")
            if old_active != new_active:
                print(f"  视频切换: {old_active} -> {new_active}")
        
        if all_timing_correct:
            print("\n✅ 切换时机准确性测试通过")
            return True
        else:
            print("\n❌ 切换时机准确性测试失败")
            return False
        
    except Exception as e:
        print(f"❌ 时机准确性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_usage_guide():
    """创建使用指南"""
    print("\n📋 精确视频切换使用指南")
    print("=" * 60)
    
    guide = """
# 🎯 精确视频切换系统

## 新的切换顺序（按您的要求）:

### 阶段1: 提前1秒准备（剩余时间 ≤ 1秒且 > 0.1秒）
1. **新源变速** - 为新视频源设置随机播放速度
2. **播放新源** - 在隐藏状态下启动新视频源播放

### 阶段2: 最终切换（剩余时间 ≤ 0.1秒）
3. **显示新源** - 显示已经在播放的新视频源
4. **隐藏旧源** - 隐藏正在播放的旧视频源
5. **停止旧源** - 停止旧视频源的播放

## 🚀 技术优势

### 无缝切换:
- ✅ **提前准备**: 新源在隐藏状态下提前1秒开始播放
- ✅ **精确时机**: 在剩余0.1秒时执行显示切换
- ✅ **零黑屏**: 新源已经在播放，切换瞬间完成

### 时机控制:
- ✅ **1秒准备阈值**: 剩余时间≤1秒开始准备
- ✅ **0.1秒切换阈值**: 剩余时间≤0.1秒执行切换
- ✅ **状态管理**: 防止重复准备和切换

### 性能优化:
- ✅ **分阶段执行**: 避免同时执行所有操作
- ✅ **状态缓存**: 记录准备状态避免重复操作
- ✅ **冷却机制**: 3秒冷却时间防止频繁切换

## ⚙️ 配置参数

- **准备阈值**: 1.0秒（可调整）
- **切换阈值**: 0.1秒（可调整）
- **冷却时间**: 3.0秒（可调整）
- **检查间隔**: 1.0秒（可调整）

## 🎯 使用效果

### 预期表现:
- ✅ **完全无黑屏**: 切换过程中始终有视频显示
- ✅ **精确时机**: 在最佳时机执行切换
- ✅ **流畅过渡**: 新源已经预加载并播放
- ✅ **稳定性**: 防止频繁切换和错误触发

### 日志输出示例:
```
🎬 剩余时间0.9秒，开始准备新源: test_video_b
🎛️ 新源变速完成: test_video_b = 1.5x
▶️ 新源播放启动: test_video_b (隐藏状态)
🎬 精确切换：剩余时间0.1秒，执行最终切换: test_video_a
👁️ 显示新源: test_video_b
🙈 隐藏旧源: test_video_a
🛑 停止旧源: test_video_a
✅ 精确切换完成，当前激活: test_video_b, 下一个: test_video_a
```

## 🎊 现在您的视频切换将完全按照要求的顺序执行！
"""
    
    print(guide)
    return guide


def main():
    """主函数"""
    print("🚀 测试精确视频切换系统")
    print("=" * 80)
    
    # 运行测试
    tests = [
        ("精确切换逻辑", test_precise_switching_logic),
        ("切换时机准确性", test_timing_accuracy),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n📊 测试结果")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 精确视频切换系统实现成功！")
        
        # 创建使用指南
        create_usage_guide()
        
        print(f"\n🎯 现在可以启动主程序验证实际效果:")
        print(f"  python run_gui_qt5.py")
        print(f"\n🎊 视频切换将完全按照您的要求执行！")
        
        return True
    else:
        print(f"\n⚠️ 还有 {total - passed} 个问题需要修复")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
