#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试更新功能
验证完整的更新流程
"""

import sys
import json
import requests
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_raw_api():
    """测试原始API"""
    print("🧪 测试原始API")
    print("=" * 50)
    
    url = "http://**************:12456/admin/api/updates/current"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"原始响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            # 解析实际格式
            if data.get('状态') == '成功':
                update_data = data.get('数据', {})
                version = update_data.get('version', '')
                description = update_data.get('description', '')
                release_date = update_data.get('release_date', '')
                
                print(f"\n✅ 解析成功:")
                print(f"   版本: {version}")
                print(f"   日期: {release_date}")
                print(f"   描述: {description}")
                
                return {
                    'success': True,
                    'version': version,
                    'description': description,
                    'release_date': release_date
                }
            else:
                print(f"❌ API返回失败状态: {data}")
                return {'success': False}
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return {'success': False}
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        return {'success': False}

def test_update_service():
    """测试更新服务"""
    print("\n🧪 测试更新服务")
    print("=" * 50)
    
    try:
        # 导入更新服务（避免循环导入）
        sys.path.append('src/services')
        from update_service import UpdateService
        
        # 创建服务实例
        service = UpdateService("http://**************:12456")
        print("✅ 更新服务创建成功")
        
        # 获取当前版本
        current_version = service.get_current_version_from_config()
        print(f"📊 当前版本: {current_version}")
        
        # 检查更新
        print("\n🔍 检查更新...")
        result = service.check_for_updates()
        
        print(f"📊 检查结果:")
        print(f"   成功: {result.get('success', False)}")
        print(f"   有更新: {result.get('has_update', False)}")
        print(f"   当前版本: {result.get('current_version', '未知')}")
        
        if result.get('success') and 'update_info' in result:
            info = result['update_info']
            print(f"   最新版本: {info.get('version', '未知')}")
            print(f"   发布日期: {info.get('release_date', '未知')}")
            print(f"   更新内容: {info.get('description', '无')}")
            
            # 测试格式化文本
            formatted = service.get_update_info_text(info)
            print(f"\n📝 格式化文本:")
            print(formatted)
        
        return result
        
    except Exception as e:
        print(f"❌ 更新服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False}

def test_config_operations():
    """测试配置文件操作"""
    print("\n🧪 测试配置文件操作")
    print("=" * 50)
    
    config_file = Path("config/app_config.json")
    
    if not config_file.exists():
        print("❌ 配置文件不存在")
        return {'success': False}
    
    try:
        # 读取当前配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        original_version = config.get('app', {}).get('version', '2.0.0')
        print(f"📊 原始版本: {original_version}")
        
        # 测试版本更新
        test_version = "2.2.2"
        print(f"🔄 测试更新到版本: {test_version}")
        
        # 更新版本
        if 'app' not in config:
            config['app'] = {}
        config['app']['version'] = test_version
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        # 验证更新
        with open(config_file, 'r', encoding='utf-8') as f:
            updated_config = json.load(f)
        
        updated_version = updated_config.get('app', {}).get('version', '未知')
        print(f"✅ 更新后版本: {updated_version}")
        
        # 恢复原版本
        config['app']['version'] = original_version
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"🔄 已恢复原版本: {original_version}")
        
        return {
            'success': True,
            'original_version': original_version,
            'test_version': test_version
        }
        
    except Exception as e:
        print(f"❌ 配置操作失败: {e}")
        return {'success': False}

def main():
    """主函数"""
    print("🚀 最终更新功能测试")
    print("=" * 80)
    
    # 测试1: 原始API
    api_result = test_raw_api()
    
    # 测试2: 更新服务
    service_result = test_update_service()
    
    # 测试3: 配置操作
    config_result = test_config_operations()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 测试总结")
    print("=" * 80)
    
    api_ok = api_result.get('success', False)
    service_ok = service_result.get('success', False)
    config_ok = config_result.get('success', False)
    
    print(f"✅ 原始API: {'通过' if api_ok else '失败'}")
    print(f"✅ 更新服务: {'通过' if service_ok else '失败'}")
    print(f"✅ 配置操作: {'通过' if config_ok else '失败'}")
    
    if api_ok and service_ok and config_ok:
        print("\n🎉 所有测试通过！更新功能已准备就绪！")
        
        if service_result.get('has_update'):
            print(f"\n🆕 检测到新版本可用:")
            info = service_result.get('update_info', {})
            print(f"   当前版本: {service_result.get('current_version', '未知')}")
            print(f"   最新版本: {info.get('version', '未知')}")
            print(f"   发布日期: {info.get('release_date', '未知')}")
            print("\n💡 在主程序登录后会自动显示更新对话框")
        else:
            print("\n✅ 当前已是最新版本")
            
        print("\n📝 集成说明:")
        print("1. 更新功能已集成到登录成功后的流程中")
        print("2. 如果有新版本，会自动弹出更新对话框")
        print("3. 用户可以选择'已更新'或'不更新'")
        print("4. 选择'已更新'会更新本地版本记录")
        
    else:
        print("\n❌ 部分测试失败，请检查:")
        if not api_ok:
            print("   - API连接问题")
        if not service_ok:
            print("   - 更新服务问题")
        if not config_ok:
            print("   - 配置文件问题")

if __name__ == "__main__":
    main()
