@echo off
chcp 65001 >nul
echo ========================================
echo   AI Broadcaster v2 - 开发运行脚本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

:: 显示Python版本
echo 🐍 Python版本:
python --version
echo.

:: 检查依赖是否安装
echo 📦 检查依赖包...
python -c "import yaml, json" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  缺少必要依赖，正在安装...
    pip install pyyaml colorlog
    echo.
)

:: 显示菜单
:menu
echo 请选择运行模式:
echo 1. 命令行模式 (默认)
echo 2. 命令行模式 + 自动广播
echo 3. 图形界面模式 (待实现)
echo 4. 调试模式
echo 5. 显示帮助
echo 6. 退出
echo.
set /p choice=请输入选择 (1-6): 

if "%choice%"=="" set choice=1
if "%choice%"=="1" goto cli_mode
if "%choice%"=="2" goto auto_broadcast
if "%choice%"=="3" goto gui_mode
if "%choice%"=="4" goto debug_mode
if "%choice%"=="5" goto help_mode
if "%choice%"=="6" goto exit

echo ❌ 无效选择，请重新输入
echo.
goto menu

:cli_mode
echo.
echo 🚀 启动命令行模式...
python main.py
goto end

:auto_broadcast
echo.
echo 🚀 启动命令行模式 + 自动广播...
python main.py --auto-broadcast
goto end

:gui_mode
echo.
echo 🚀 启动图形界面模式...
python main.py --ui
goto end

:debug_mode
echo.
echo 🚀 启动调试模式...
python main.py --debug
goto end

:help_mode
echo.
echo 📖 显示帮助信息...
python main.py --help
echo.
pause
goto menu

:end
echo.
echo 程序已退出
pause

:exit
echo.
echo 👋 再见！
exit /b 0
