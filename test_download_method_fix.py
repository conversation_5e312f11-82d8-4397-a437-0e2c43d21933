#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载方法修复测试脚本
测试 check_and_download_pending_voices 方法是否已正确添加
"""

import sys
import os
from pathlib import Path

def test_download_method_fix():
    """测试下载方法修复"""
    print("🔧 测试下载方法修复...")
    
    try:
        main_file = "run_gui_qt5.py"
        
        if not Path(main_file).exists():
            print(f"⚠️ 主文件不存在: {main_file}")
            return False
        
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否存在 check_and_download_pending_voices 方法
        method_patterns = [
            "def check_and_download_pending_voices(self):",
            "检查并下载待下载的语音",
            "pending_items = [",
            "item.get('status') == '未下载'",
            "max_concurrent = 3"
        ]
        
        found_patterns = []
        for pattern in method_patterns:
            if pattern in content:
                found_patterns.append(pattern)
        
        print(f"📋 检查到的方法特征:")
        for pattern in found_patterns:
            print(f"  ✅ {pattern}")
        
        missing_patterns = [pattern for pattern in method_patterns if pattern not in found_patterns]
        if missing_patterns:
            print(f"⚠️ 缺失的特征:")
            for pattern in missing_patterns:
                print(f"  ❌ {pattern}")
        
        # 检查方法调用是否正确连接
        connection_patterns = [
            "self.download_check_timer.timeout.connect(self.check_and_download_pending_voices)",
            "download_check_timer.start(5000)"
        ]
        
        found_connections = []
        for pattern in connection_patterns:
            if pattern in content:
                found_connections.append(pattern)
        
        print(f"📋 检查到的连接:")
        for pattern in found_connections:
            print(f"  ✅ {pattern}")
        
        # 检查其他调用位置
        call_patterns = [
            "self.check_and_download_pending_voices()",
            "QTimer.singleShot(100, self.check_and_download_pending_voices)"
        ]
        
        found_calls = []
        for pattern in call_patterns:
            call_count = content.count(pattern)
            if call_count > 0:
                found_calls.append(f"{pattern} ({call_count} 次)")
        
        print(f"📋 检查到的方法调用:")
        for call in found_calls:
            print(f"  ✅ {call}")
        
        return len(found_patterns) >= 4 and len(found_connections) >= 1
        
    except Exception as e:
        print(f"❌ 下载方法修复测试失败: {e}")
        return False

def test_method_implementation():
    """测试方法实现的完整性"""
    print("\n🔧 测试方法实现的完整性...")
    
    try:
        main_file = "run_gui_qt5.py"
        
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找 check_and_download_pending_voices 方法的实现
        method_start = content.find("def check_and_download_pending_voices(self):")
        if method_start == -1:
            print("❌ 未找到 check_and_download_pending_voices 方法")
            return False
        
        # 查找方法结束位置
        method_end = content.find("\n        def ", method_start + 1)
        if method_end == -1:
            method_end = content.find("\n    def ", method_start + 1)
        if method_end == -1:
            method_end = len(content)
        
        method_content = content[method_start:method_end]
        
        # 检查方法实现的关键功能
        implementation_features = [
            "pending_items = [",
            "max_concurrent = 3",
            "download_batch = pending_items[:max_concurrent]",
            "item['status'] = '下载中'",
            "self.download_voice(",
            "item['status'] = '已下载'",
            "self.update_table_display()",
            "self.save_playlist_to_file()",
            "异常处理"
        ]
        
        found_features = []
        for feature in implementation_features:
            if feature in method_content:
                found_features.append(feature)
        
        print(f"📋 方法实现特征:")
        for feature in found_features:
            print(f"  ✅ {feature}")
        
        missing_features = [feature for feature in implementation_features if feature not in found_features]
        if missing_features:
            print(f"⚠️ 缺失的实现特征:")
            for feature in missing_features:
                print(f"  ❌ {feature}")
        
        # 检查方法长度（应该有合理的实现）
        method_lines = method_content.count('\n')
        print(f"📏 方法长度: {method_lines} 行")
        
        if method_lines < 20:
            print("⚠️ 方法实现可能过于简单")
            return False
        elif method_lines > 150:
            print("⚠️ 方法实现可能过于复杂")
        else:
            print("✅ 方法长度合理")
        
        return len(found_features) >= 7
        
    except Exception as e:
        print(f"❌ 方法实现测试失败: {e}")
        return False

def test_related_methods():
    """测试相关方法"""
    print("\n🔧 测试相关下载方法...")
    
    try:
        main_file = "run_gui_qt5.py"
        
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查相关的下载方法
        related_methods = [
            "def download_all_pending_voices(self):",
            "def start_downloading_pending_voices(self):",
            "def download_playlist_voices(self, playlist_items):",
            "def download_single_voice_async(self, playlist_item"
        ]
        
        found_methods = []
        for method in related_methods:
            if method in content:
                found_methods.append(method)
        
        print(f"📋 相关下载方法:")
        for method in found_methods:
            print(f"  ✅ {method}")
        
        # 检查是否有方法冲突或重复
        method_conflicts = [
            "def check_and_download_pending_voices(self):"
        ]
        
        conflict_count = 0
        for conflict in method_conflicts:
            conflict_count += content.count(conflict)
        
        if conflict_count > 1:
            print(f"⚠️ 发现方法定义重复: {conflict_count} 次")
            return False
        else:
            print("✅ 没有发现方法定义重复")
        
        return len(found_methods) >= 3
        
    except Exception as e:
        print(f"❌ 相关方法测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始下载方法修复测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: 下载方法修复
    result1 = test_download_method_fix()
    test_results.append(("下载方法修复", result1))
    
    # 测试2: 方法实现完整性
    result2 = test_method_implementation()
    test_results.append(("方法实现完整性", result2))
    
    # 测试3: 相关方法检查
    result3 = test_related_methods()
    test_results.append(("相关方法检查", result3))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！下载方法修复完成！")
        print("\n✨ 修复内容总结:")
        print("1. ✅ 添加了 check_and_download_pending_voices 方法")
        print("2. ✅ 正确连接了定时器回调")
        print("3. ✅ 实现了批量下载限制逻辑")
        print("4. ✅ 添加了状态管理和错误处理")
        print("\n🔧 功能特点:")
        print("• 检查待下载的语音项目")
        print("• 限制同时下载数量（最多3个）")
        print("• 更新下载状态和文件名")
        print("• 自动更新表格显示")
        print("• 保存播放列表到文件")
        print("• 完善的异常处理")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
