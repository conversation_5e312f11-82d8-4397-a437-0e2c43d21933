"""
AI Broadcaster v2 - 验证工具类
提供数据验证相关的工具函数
"""

import re
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse


class Validator:
    """验证工具类"""
    
    @staticmethod
    def is_email(email: str) -> bool:
        """
        验证邮箱地址
        
        Args:
            email: 邮箱地址
            
        Returns:
            是否为有效邮箱
        """
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def is_url(url: str) -> bool:
        """
        验证URL
        
        Args:
            url: URL字符串
            
        Returns:
            是否为有效URL
        """
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    @staticmethod
    def is_ip_address(ip: str) -> bool:
        """
        验证IP地址
        
        Args:
            ip: IP地址字符串
            
        Returns:
            是否为有效IP地址
        """
        # IPv4验证
        ipv4_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
        if re.match(ipv4_pattern, ip):
            return True
        
        # IPv6验证（简化版）
        ipv6_pattern = r'^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$'
        return bool(re.match(ipv6_pattern, ip))
    
    @staticmethod
    def is_port(port: Union[str, int]) -> bool:
        """
        验证端口号
        
        Args:
            port: 端口号
            
        Returns:
            是否为有效端口号
        """
        try:
            port_num = int(port)
            return 1 <= port_num <= 65535
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def is_json(text: str) -> bool:
        """
        验证JSON字符串
        
        Args:
            text: JSON字符串
            
        Returns:
            是否为有效JSON
        """
        try:
            json.loads(text)
            return True
        except (json.JSONDecodeError, TypeError):
            return False
    
    @staticmethod
    def is_file_exists(file_path: Union[str, Path]) -> bool:
        """
        验证文件是否存在
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件是否存在
        """
        return Path(file_path).exists()
    
    @staticmethod
    def is_directory_exists(dir_path: Union[str, Path]) -> bool:
        """
        验证目录是否存在
        
        Args:
            dir_path: 目录路径
            
        Returns:
            目录是否存在
        """
        return Path(dir_path).is_dir()
    
    @staticmethod
    def is_readable_file(file_path: Union[str, Path]) -> bool:
        """
        验证文件是否可读
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件是否可读
        """
        try:
            path = Path(file_path)
            return path.exists() and path.is_file() and path.stat().st_mode & 0o444
        except Exception:
            return False
    
    @staticmethod
    def is_writable_file(file_path: Union[str, Path]) -> bool:
        """
        验证文件是否可写
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件是否可写
        """
        try:
            path = Path(file_path)
            if path.exists():
                return path.stat().st_mode & 0o222
            else:
                # 检查父目录是否可写
                return path.parent.exists() and path.parent.stat().st_mode & 0o222
        except Exception:
            return False
    
    @staticmethod
    def validate_config(config: Dict[str, Any], schema: Dict[str, Any]) -> List[str]:
        """
        验证配置字典
        
        Args:
            config: 配置字典
            schema: 验证模式
            
        Returns:
            错误信息列表
        """
        errors = []
        
        def validate_value(value, schema_item, path=""):
            if isinstance(schema_item, dict):
                if 'type' in schema_item:
                    expected_type = schema_item['type']
                    if expected_type == 'string' and not isinstance(value, str):
                        errors.append(f"{path}: 期望字符串类型，实际为 {type(value).__name__}")
                    elif expected_type == 'number' and not isinstance(value, (int, float)):
                        errors.append(f"{path}: 期望数字类型，实际为 {type(value).__name__}")
                    elif expected_type == 'boolean' and not isinstance(value, bool):
                        errors.append(f"{path}: 期望布尔类型，实际为 {type(value).__name__}")
                    elif expected_type == 'array' and not isinstance(value, list):
                        errors.append(f"{path}: 期望数组类型，实际为 {type(value).__name__}")
                    elif expected_type == 'object' and not isinstance(value, dict):
                        errors.append(f"{path}: 期望对象类型，实际为 {type(value).__name__}")
                
                if 'required' in schema_item and schema_item['required'] and value is None:
                    errors.append(f"{path}: 必填字段不能为空")
                
                if 'min' in schema_item and isinstance(value, (int, float)) and value < schema_item['min']:
                    errors.append(f"{path}: 值 {value} 小于最小值 {schema_item['min']}")
                
                if 'max' in schema_item and isinstance(value, (int, float)) and value > schema_item['max']:
                    errors.append(f"{path}: 值 {value} 大于最大值 {schema_item['max']}")
                
                if 'pattern' in schema_item and isinstance(value, str):
                    if not re.match(schema_item['pattern'], value):
                        errors.append(f"{path}: 值 '{value}' 不匹配模式 '{schema_item['pattern']}'")
                
                if 'enum' in schema_item and value not in schema_item['enum']:
                    errors.append(f"{path}: 值 '{value}' 不在允许的选项中 {schema_item['enum']}")
        
        def validate_object(obj, schema_obj, path=""):
            if not isinstance(obj, dict) or not isinstance(schema_obj, dict):
                return
            
            # 检查必填字段
            for key, schema_item in schema_obj.items():
                current_path = f"{path}.{key}" if path else key
                
                if key not in obj:
                    if isinstance(schema_item, dict) and schema_item.get('required', False):
                        errors.append(f"{current_path}: 缺少必填字段")
                    continue
                
                value = obj[key]
                
                if isinstance(schema_item, dict):
                    if schema_item.get('type') == 'object' and 'properties' in schema_item:
                        validate_object(value, schema_item['properties'], current_path)
                    else:
                        validate_value(value, schema_item, current_path)
        
        validate_object(config, schema)
        return errors
    
    @staticmethod
    def is_safe_filename(filename: str) -> bool:
        """
        验证文件名是否安全
        
        Args:
            filename: 文件名
            
        Returns:
            文件名是否安全
        """
        # 检查非法字符
        illegal_chars = '<>:"/\\|?*'
        if any(char in filename for char in illegal_chars):
            return False
        
        # 检查保留名称（Windows）
        reserved_names = {
            'CON', 'PRN', 'AUX', 'NUL',
            'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
            'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
        }
        
        name_without_ext = filename.split('.')[0].upper()
        if name_without_ext in reserved_names:
            return False
        
        # 检查长度
        if len(filename) > 255:
            return False
        
        # 检查是否以点或空格开头/结尾
        if filename.startswith('.') or filename.startswith(' ') or filename.endswith(' '):
            return False
        
        return True
    
    @staticmethod
    def sanitize_input(text: str, max_length: int = 1000) -> str:
        """
        清理用户输入
        
        Args:
            text: 输入文本
            max_length: 最大长度
            
        Returns:
            清理后的文本
        """
        if not isinstance(text, str):
            return ""
        
        # 移除控制字符
        text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
        
        # 限制长度
        if len(text) > max_length:
            text = text[:max_length]
        
        # 移除前后空白
        text = text.strip()
        
        return text
    
    @staticmethod
    def is_strong_password(password: str) -> bool:
        """
        验证密码强度
        
        Args:
            password: 密码
            
        Returns:
            密码是否足够强
        """
        if len(password) < 8:
            return False
        
        # 检查是否包含大写字母
        if not re.search(r'[A-Z]', password):
            return False
        
        # 检查是否包含小写字母
        if not re.search(r'[a-z]', password):
            return False
        
        # 检查是否包含数字
        if not re.search(r'\d', password):
            return False
        
        # 检查是否包含特殊字符
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            return False
        
        return True
