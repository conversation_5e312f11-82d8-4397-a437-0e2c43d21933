"""
AI Broadcaster v2 - 主广播器类
核心广播逻辑和状态管理
"""

import asyncio
import signal
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    print("⚠️  PyYAML未安装，将使用默认配置")

from .utils.logger import create_logger


class AIBroadcaster:
    """AI广播器主类"""

    def __init__(self, config_path: str = "config/app_config.yaml"):
        """
        初始化AI广播器

        Args:
            config_path: 配置文件路径
        """
        self.logger = create_logger("ai_broadcaster")
        self.config_path = config_path
        self.config: Dict[str, Any] = {}
        self.is_running = False
        self.is_broadcasting = False

        # 组件状态
        self.components = {
            'audio_processor': None,
            'ai_engine': None,
            'content_generator': None,
            'voice_synthesizer': None
        }

        # 统计信息
        self.stats = {
            'start_time': None,
            'broadcasts_count': 0,
            'errors_count': 0,
            'uptime_seconds': 0
        }

        self.logger.log_startup("AI广播器")

    async def initialize(self):
        """异步初始化"""
        try:
            self.logger.info("🔧 开始初始化AI广播器...")

            # 加载配置
            await self._load_config()

            # 创建必要的目录
            await self._create_directories()

            # 初始化组件
            await self._initialize_components()

            # 设置信号处理
            self._setup_signal_handlers()

            self.logger.log_success("AI广播器初始化完成")
            return True

        except Exception as e:
            self.logger.log_failure(f"初始化失败: {e}")
            self.logger.exception("初始化异常详情")
            return False

    async def _load_config(self):
        """加载配置文件"""
        try:
            config_file = Path(self.config_path)
            if not config_file.exists() or not YAML_AVAILABLE:
                if not config_file.exists():
                    self.logger.log_warning(f"配置文件不存在: {self.config_path}")
                else:
                    self.logger.log_warning("PyYAML未安装，无法加载YAML配置")
                self.config = self._get_default_config()
                return

            with open(config_file, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)

            self.logger.log_success(f"配置文件加载成功: {self.config_path}")

        except Exception as e:
            self.logger.log_failure(f"配置文件加载失败: {e}")
            self.config = self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'app': {
                'name': 'AI Broadcaster v2',
                'version': '2.0.0',
                'debug': True
            },
            'audio': {
                'sample_rate': 44100,
                'channels': 1
            },
            'broadcast': {
                'interval_seconds': 30,
                'auto_start': False
            }
        }

    async def _create_directories(self):
        """创建必要的目录"""
        directories = [
            'data',
            'data/audio',
            'data/temp',
            'logs'
        ]

        for dir_path in directories:
            Path(dir_path).mkdir(parents=True, exist_ok=True)

        self.logger.info("📁 目录结构创建完成")

    async def _initialize_components(self):
        """初始化各个组件"""
        self.logger.info("🔧 初始化组件中...")

        # TODO: 在后续阶段实现具体组件
        # self.components['audio_processor'] = AudioProcessor(self.config)
        # self.components['ai_engine'] = AIEngine(self.config)
        # self.components['content_generator'] = ContentGenerator(self.config)
        # self.components['voice_synthesizer'] = VoiceSynthesizer(self.config)

        self.logger.info("✅ 组件初始化完成（占位符）")

    def _setup_signal_handlers(self):
        """设置信号处理器"""
        if sys.platform != 'win32':
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)

        self.logger.info("📡 信号处理器设置完成")

    def _signal_handler(self, signum, frame):
        """信号处理函数"""
        self.logger.info(f"📡 收到信号 {signum}，准备关闭...")
        asyncio.create_task(self.shutdown())

    async def start(self):
        """启动广播器"""
        if self.is_running:
            self.logger.log_warning("广播器已在运行中")
            return

        try:
            self.is_running = True
            self.stats['start_time'] = datetime.now()

            self.logger.log_startup("AI广播器服务")

            # 如果配置了自动开始广播
            if self.config.get('broadcast', {}).get('auto_start', False):
                await self.start_broadcasting()

            # 主运行循环
            await self._main_loop()

        except Exception as e:
            self.logger.log_failure(f"启动失败: {e}")
            self.logger.exception("启动异常详情")
            await self.shutdown()

    async def _main_loop(self):
        """主运行循环"""
        self.logger.info("🔄 进入主运行循环")

        try:
            while self.is_running:
                # 更新统计信息
                if self.stats['start_time']:
                    self.stats['uptime_seconds'] = (
                        datetime.now() - self.stats['start_time']
                    ).total_seconds()

                # 检查组件状态
                await self._check_components_health()

                # 如果正在广播，执行广播逻辑
                if self.is_broadcasting:
                    await self._broadcast_cycle()

                # 短暂休眠
                await asyncio.sleep(1)

        except asyncio.CancelledError:
            self.logger.info("🛑 主循环被取消")
        except Exception as e:
            self.logger.log_failure(f"主循环异常: {e}")
            self.logger.exception("主循环异常详情")

    async def _check_components_health(self):
        """检查组件健康状态"""
        # TODO: 实现组件健康检查
        pass

    async def _broadcast_cycle(self):
        """执行一次广播周期"""
        try:
            self.logger.debug("🎙️ 执行广播周期")

            # TODO: 实现具体的广播逻辑
            # 1. 生成内容
            # 2. 语音合成
            # 3. 音频播放
            # 4. 更新统计

            self.stats['broadcasts_count'] += 1

            # 等待下一个广播间隔
            interval = self.config.get('broadcast', {}).get('interval_seconds', 30)
            await asyncio.sleep(interval)

        except Exception as e:
            self.stats['errors_count'] += 1
            self.logger.log_failure(f"广播周期异常: {e}")
            self.logger.exception("广播周期异常详情")

    async def start_broadcasting(self):
        """开始广播"""
        if self.is_broadcasting:
            self.logger.log_warning("广播已在进行中")
            return

        self.is_broadcasting = True
        self.logger.log_success("开始广播")

    async def stop_broadcasting(self):
        """停止广播"""
        if not self.is_broadcasting:
            self.logger.log_warning("广播未在进行中")
            return

        self.is_broadcasting = False
        self.logger.log_success("停止广播")

    async def shutdown(self):
        """关闭广播器"""
        self.logger.log_shutdown("AI广播器")

        try:
            # 停止广播
            if self.is_broadcasting:
                await self.stop_broadcasting()

            # 关闭组件
            await self._shutdown_components()

            # 停止主循环
            self.is_running = False

            self.logger.log_success("AI广播器关闭完成")

        except Exception as e:
            self.logger.log_failure(f"关闭过程中发生异常: {e}")
            self.logger.exception("关闭异常详情")

    async def _shutdown_components(self):
        """关闭所有组件"""
        for name, component in self.components.items():
            if component:
                try:
                    if hasattr(component, 'shutdown'):
                        await component.shutdown()
                    self.logger.info(f"✅ {name} 组件已关闭")
                except Exception as e:
                    self.logger.error(f"❌ {name} 组件关闭失败: {e}")

    def get_status(self) -> Dict[str, Any]:
        """获取广播器状态"""
        return {
            'is_running': self.is_running,
            'is_broadcasting': self.is_broadcasting,
            'stats': self.stats.copy(),
            'config': {
                'app_name': self.config.get('app', {}).get('name', 'Unknown'),
                'version': self.config.get('app', {}).get('version', 'Unknown')
            }
        }

    def __str__(self):
        """字符串表示"""
        status = "运行中" if self.is_running else "已停止"
        broadcast_status = "广播中" if self.is_broadcasting else "未广播"
        return f"AIBroadcaster({status}, {broadcast_status})"
