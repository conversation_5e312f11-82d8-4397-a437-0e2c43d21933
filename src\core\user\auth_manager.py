"""
AI直播系统 v2 - 认证管理器
用户认证和权限管理
"""

import jwt
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class AuthManager:
    """认证管理器"""

    def __init__(self, db_manager, secret_key: str = None):
        self.logger = create_logger("auth_manager")
        self.db_manager = db_manager
        self.secret_key = secret_key or "ai_live_system_secret_key_2024"

        # Token配置
        self.token_config = {
            'access_token_expire': 24,  # 访问令牌过期时间（小时）
            'refresh_token_expire': 30,  # 刷新令牌过期时间（天）
            'algorithm': 'HS256'
        }

        # 权限定义
        self.permissions = {
            'USER_BASIC': 'user:basic',           # 基础用户权限
            'USER_VIP': 'user:vip',               # VIP用户权限
            'USER_PREMIUM': 'user:premium',       # 高级用户权限
            'ADMIN_READ': 'admin:read',           # 管理员读权限
            'ADMIN_WRITE': 'admin:write',         # 管理员写权限
            'ADMIN_DELETE': 'admin:delete',       # 管理员删除权限
            'SYSTEM_CONFIG': 'system:config',     # 系统配置权限
        }

        # 角色权限映射
        self.role_permissions = {
            0: [self.permissions['USER_BASIC']],  # 免费用户
            1: [self.permissions['USER_BASIC'], self.permissions['USER_VIP']],  # VIP用户
            2: [self.permissions['USER_BASIC'], self.permissions['USER_VIP'],
                self.permissions['USER_PREMIUM']],  # 高级用户
            9: list(self.permissions.values())  # 管理员
        }

    @handle_exceptions("AuthManager")
    def generate_tokens(self, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成访问令牌和刷新令牌

        Args:
            user_info: 用户信息

        Returns:
            令牌信息
        """
        try:
            user_id = user_info['id']
            username = user_info['username']
            user_type = user_info['user_type']

            # 生成访问令牌
            access_token_payload = {
                'user_id': user_id,
                'username': username,
                'user_type': user_type,
                'permissions': self.role_permissions.get(user_type, []),
                'token_type': 'access',
                'exp': datetime.utcnow() + timedelta(hours=self.token_config['access_token_expire']),
                'iat': datetime.utcnow(),
                'jti': str(uuid.uuid4())
            }

            access_token = jwt.encode(
                access_token_payload,
                self.secret_key,
                algorithm=self.token_config['algorithm']
            )

            # 生成刷新令牌
            refresh_token_payload = {
                'user_id': user_id,
                'username': username,
                'token_type': 'refresh',
                'exp': datetime.utcnow() + timedelta(days=self.token_config['refresh_token_expire']),
                'iat': datetime.utcnow(),
                'jti': str(uuid.uuid4())
            }

            refresh_token = jwt.encode(
                refresh_token_payload,
                self.secret_key,
                algorithm=self.token_config['algorithm']
            )

            # 保存刷新令牌到数据库
            self._save_refresh_token(user_id, refresh_token_payload['jti'],
                                   refresh_token_payload['exp'])

            self.logger.info(f"为用户 {username} 生成令牌成功")

            return {
                'access_token': access_token,
                'refresh_token': refresh_token,
                'token_type': 'Bearer',
                'expires_in': self.token_config['access_token_expire'] * 3600,
                'permissions': access_token_payload['permissions']
            }

        except Exception as e:
            self.logger.error(f"生成令牌失败: {e}")
            return {}

    @handle_exceptions("AuthManager")
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        验证令牌

        Args:
            token: 令牌

        Returns:
            令牌载荷或None
        """
        try:
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.token_config['algorithm']]
            )

            # 检查令牌类型
            if payload.get('token_type') != 'access':
                return None

            return payload

        except jwt.ExpiredSignatureError:
            self.logger.warning("令牌已过期")
            return None
        except jwt.InvalidTokenError:
            self.logger.warning("无效令牌")
            return None
        except Exception as e:
            self.logger.error(f"验证令牌失败: {e}")
            return None

    @handle_exceptions("AuthManager")
    def refresh_access_token(self, refresh_token: str) -> Optional[Dict[str, Any]]:
        """
        使用刷新令牌获取新的访问令牌

        Args:
            refresh_token: 刷新令牌

        Returns:
            新的令牌信息或None
        """
        try:
            # 验证刷新令牌
            payload = jwt.decode(
                refresh_token,
                self.secret_key,
                algorithms=[self.token_config['algorithm']]
            )

            if payload.get('token_type') != 'refresh':
                return None

            user_id = payload['user_id']
            jti = payload['jti']

            # 检查刷新令牌是否在数据库中
            if not self._is_refresh_token_valid(user_id, jti):
                return None

            # 获取用户信息
            from .user_manager import UserManager
            user_manager = UserManager(self.db_manager)
            user_info = user_manager.get_user_by_id(user_id)

            if not user_info:
                return None

            # 生成新的访问令牌
            new_tokens = self.generate_tokens(user_info)

            self.logger.info(f"刷新令牌成功: 用户ID {user_id}")

            return new_tokens

        except jwt.ExpiredSignatureError:
            self.logger.warning("刷新令牌已过期")
            return None
        except jwt.InvalidTokenError:
            self.logger.warning("无效刷新令牌")
            return None
        except Exception as e:
            self.logger.error(f"刷新令牌失败: {e}")
            return None

    @handle_exceptions("AuthManager")
    def revoke_token(self, user_id: int, jti: str = None):
        """
        撤销令牌

        Args:
            user_id: 用户ID
            jti: 令牌ID，如果为None则撤销用户所有令牌
        """
        try:
            if jti:
                # 撤销特定令牌
                self.db_manager.execute_update(
                    "DELETE FROM refresh_tokens WHERE user_id = ? AND jti = ?",
                    (user_id, jti)
                )
            else:
                # 撤销用户所有令牌
                self.db_manager.execute_update(
                    "DELETE FROM refresh_tokens WHERE user_id = ?",
                    (user_id,)
                )

            self.logger.info(f"撤销令牌成功: 用户ID {user_id}")

        except Exception as e:
            self.logger.error(f"撤销令牌失败: {e}")

    @handle_exceptions("AuthManager")
    def check_permission(self, user_permissions: List[str], required_permission: str) -> bool:
        """
        检查权限

        Args:
            user_permissions: 用户权限列表
            required_permission: 需要的权限

        Returns:
            是否有权限
        """
        return required_permission in user_permissions

    @handle_exceptions("AuthManager")
    def get_user_permissions(self, user_type: int) -> List[str]:
        """获取用户权限列表"""
        return self.role_permissions.get(user_type, [])

    def _save_refresh_token(self, user_id: int, jti: str, expires_at: datetime):
        """保存刷新令牌到数据库"""
        try:
            self.db_manager.execute_insert(
                """INSERT OR REPLACE INTO refresh_tokens
                   (user_id, jti, expires_at, created_at)
                   VALUES (?, ?, ?, CURRENT_TIMESTAMP)""",
                (user_id, jti, expires_at.isoformat())
            )
        except Exception as e:
            self.logger.error(f"保存刷新令牌失败: {e}")

    def _is_refresh_token_valid(self, user_id: int, jti: str) -> bool:
        """检查刷新令牌是否有效"""
        try:
            tokens = self.db_manager.execute_query(
                """SELECT * FROM refresh_tokens
                   WHERE user_id = ? AND jti = ? AND expires_at > CURRENT_TIMESTAMP""",
                (user_id, jti)
            )
            return len(tokens) > 0
        except Exception as e:
            self.logger.error(f"检查刷新令牌失败: {e}")
            return False

    @handle_exceptions("AuthManager")
    def cleanup_expired_tokens(self):
        """清理过期的刷新令牌"""
        try:
            rows_affected = self.db_manager.execute_update(
                "DELETE FROM refresh_tokens WHERE expires_at <= CURRENT_TIMESTAMP"
            )

            if rows_affected > 0:
                self.logger.info(f"清理过期令牌: {rows_affected} 个")

        except Exception as e:
            self.logger.error(f"清理过期令牌失败: {e}")

    @handle_exceptions("AuthManager")
    def get_active_sessions(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户活跃会话"""
        try:
            sessions = self.db_manager.execute_query(
                """SELECT jti, created_at, expires_at FROM refresh_tokens
                   WHERE user_id = ? AND expires_at > CURRENT_TIMESTAMP
                   ORDER BY created_at DESC""",
                (user_id,)
            )

            return [dict(session) for session in sessions]

        except Exception as e:
            self.logger.error(f"获取活跃会话失败: {e}")
            return []

    @handle_exceptions("AuthManager")
    def create_api_key(self, user_id: int, name: str, permissions: List[str] = None) -> Optional[str]:
        """
        创建API密钥

        Args:
            user_id: 用户ID
            name: API密钥名称
            permissions: 权限列表

        Returns:
            API密钥
        """
        try:
            api_key = f"ak_{uuid.uuid4().hex}"

            # 默认权限为用户基础权限
            if not permissions:
                user = self.db_manager.execute_query(
                    "SELECT user_type FROM users WHERE id = ?",
                    (user_id,)
                )[0]
                permissions = self.get_user_permissions(user['user_type'])

            # 保存API密钥
            self.db_manager.execute_insert(
                """INSERT INTO api_keys
                   (user_id, api_key, name, permissions, created_at, updated_at)
                   VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)""",
                (user_id, api_key, name, ','.join(permissions))
            )

            self.logger.info(f"创建API密钥成功: {name}")
            return api_key

        except Exception as e:
            self.logger.error(f"创建API密钥失败: {e}")
            return None

    @handle_exceptions("AuthManager")
    def verify_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """验证API密钥"""
        try:
            # 检查api_keys表是否存在
            tables = self.db_manager.execute_query(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='api_keys'"
            )

            if not tables:
                self.logger.warning("api_keys表不存在")
                return None

            keys = self.db_manager.execute_query(
                """SELECT ak.*, u.username, u.user_type, u.status
                   FROM api_keys ak
                   JOIN users u ON ak.user_id = u.id
                   WHERE ak.api_key = ? AND ak.is_active = 1 AND u.status = 1""",
                (api_key,)
            )

            if keys:
                key_info = dict(keys[0])
                key_info['permissions'] = key_info['permissions'].split(',') if key_info['permissions'] else []
                return key_info

            return None

        except Exception as e:
            self.logger.error(f"验证API密钥失败: {e}")
            return None
