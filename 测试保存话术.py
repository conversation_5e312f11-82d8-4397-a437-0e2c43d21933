#!/usr/bin/env python3
"""
测试保存话术功能是否正常
"""

def test_company_code_logic():
    """测试公司代码相关逻辑"""
    print("=== 测试公司代码逻辑 ===")
    
    # 模拟用户信息
    user_info = {'username': 'jane-1'}
    
    # 提取公司代码
    def extract_company_code(username: str) -> str:
        try:
            if '-' in username:
                return username.split('-', 1)[0]
            return ""
        except Exception as e:
            print(f"[ERROR] 提取公司代码失败: {e}")
            return ""
    
    company_code = extract_company_code(user_info.get('username', ''))
    print(f"用户名: {user_info.get('username')}")
    print(f"公司代码: {company_code}")
    
    # 测试话术名称处理
    display_script_name = "测试话术"
    
    # 模拟获取原始话术名称的逻辑
    def get_full_script_name(display_name, company_code):
        """获取包含公司代码的完整话术名称"""
        try:
            # 如果没有存储原始名称，添加公司代码前缀
            if company_code and not display_name.startswith(f"{company_code}-"):
                return f"{company_code}-{display_name}"
            return display_name
        except Exception as e:
            print(f"[WARNING] 获取原始话术名称失败: {e}")
            # 如果获取失败，尝试添加公司代码前缀
            if company_code and not display_name.startswith(f"{company_code}-"):
                return f"{company_code}-{display_name}"
            return display_name
    
    full_script_name = get_full_script_name(display_script_name, company_code)
    print(f"显示名称: {display_script_name}")
    print(f"完整名称: {full_script_name}")
    
    return True

def test_save_script_logic():
    """测试保存话术的核心逻辑"""
    print("\n=== 测试保存话术逻辑 ===")
    
    # 模拟话术内容
    script_content = """欢迎来到直播间！
大家好，我是主播
请关注点赞支持
感谢观看！"""
    
    script_name = "jane-测试话术"
    
    print(f"话术名称: {script_name}")
    print(f"话术内容长度: {len(script_content)} 字符")
    print(f"话术内容预览: {script_content[:50]}...")
    
    # 模拟API调用结果
    def mock_upload_script(name, content):
        """模拟API上传话术"""
        try:
            if not name or not content:
                return {'success': False, 'message': '话术名称或内容不能为空'}
            
            if len(name) > 100:
                return {'success': False, 'message': '话术名称过长'}
            
            if len(content) > 10000:
                return {'success': False, 'message': '话术内容过长'}
            
            print(f"[MOCK] 上传话术: {name}")
            print(f"[MOCK] 内容长度: {len(content)}")
            return {'success': True, 'message': '保存成功'}
            
        except Exception as e:
            return {'success': False, 'message': f'保存异常: {str(e)}'}
    
    # 测试保存
    result = mock_upload_script(script_name, script_content)
    
    if result['success']:
        print(f"✓ 保存成功: {result['message']}")
        return True
    else:
        print(f"✗ 保存失败: {result['message']}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    # 测试各种错误情况
    test_cases = [
        ("", "测试内容", "空话术名称"),
        ("测试话术", "", "空话术内容"),
        (None, "测试内容", "None话术名称"),
        ("测试话术", None, "None话术内容"),
    ]
    
    def safe_save_script(name, content):
        """安全的保存话术方法"""
        try:
            if not name:
                return {'success': False, 'message': '请输入话术名称'}
            
            if not content:
                return {'success': False, 'message': '请输入话术内容'}
            
            # 转换为字符串
            name = str(name) if name is not None else ""
            content = str(content) if content is not None else ""
            
            if not name.strip():
                return {'success': False, 'message': '话术名称不能为空'}
            
            if not content.strip():
                return {'success': False, 'message': '话术内容不能为空'}
            
            return {'success': True, 'message': '保存成功'}
            
        except Exception as e:
            return {'success': False, 'message': f'保存异常: {str(e)}'}
    
    for name, content, description in test_cases:
        result = safe_save_script(name, content)
        status = "✓" if not result['success'] else "✗"  # 期望失败
        print(f"{status} {description}: {result['message']}")
    
    # 测试正常情况
    result = safe_save_script("jane-正常话术", "正常内容")
    status = "✓" if result['success'] else "✗"
    print(f"{status} 正常情况: {result['message']}")

def main():
    """主测试函数"""
    print("开始测试保存话术功能...")
    
    try:
        # 运行所有测试
        test1 = test_company_code_logic()
        test2 = test_save_script_logic()
        test_error_handling()
        
        print("\n=== 测试总结 ===")
        if test1 and test2:
            print("✅ 所有核心功能测试通过")
            print("保存话术功能应该可以正常工作")
        else:
            print("❌ 部分测试失败")
            print("需要进一步检查代码")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")

if __name__ == "__main__":
    main()
