#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新功能
验证更新API和更新对话框
"""

import sys
import json
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.update_service import UpdateService
from src.ui.update_dialog import UpdateDialog

class UpdateTestWindow(QMainWindow):
    """更新功能测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.update_service = UpdateService("http://43.143.225.151:12456")
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("更新功能测试")
        self.setFixedSize(400, 300)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 布局
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title_label = QLabel("更新功能测试")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #333;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 当前版本显示
        current_version = self.update_service.get_current_version_from_config()
        version_label = QLabel(f"当前版本: {current_version}")
        version_label.setStyleSheet("font-size: 14px; color: #666;")
        version_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(version_label)
        
        # 测试按钮
        test_button = QPushButton("测试检查更新")
        test_button.setFixedHeight(40)
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                border: none;
                border-radius: 6px;
                color: white;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)
        test_button.clicked.connect(self.test_check_update)
        layout.addWidget(test_button)
        
        # 测试API按钮
        api_test_button = QPushButton("测试更新API")
        api_test_button.setFixedHeight(40)
        api_test_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                border: none;
                border-radius: 6px;
                color: white;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        api_test_button.clicked.connect(self.test_api_only)
        layout.addWidget(api_test_button)
        
        # 模拟更新对话框按钮
        dialog_test_button = QPushButton("测试更新对话框")
        dialog_test_button.setFixedHeight(40)
        dialog_test_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                border: none;
                border-radius: 6px;
                color: white;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #E65100;
            }
        """)
        dialog_test_button.clicked.connect(self.test_dialog_only)
        layout.addWidget(dialog_test_button)
        
        # 状态标签
        self.status_label = QLabel("点击按钮开始测试")
        self.status_label.setStyleSheet("font-size: 12px; color: #999;")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
    def test_check_update(self):
        """测试完整的更新检查流程"""
        self.status_label.setText("正在检查更新...")
        
        try:
            # 检查更新
            update_result = self.update_service.check_for_updates()
            
            if update_result['success']:
                if update_result['has_update']:
                    self.status_label.setText(f"发现新版本: {update_result['update_info']['version']}")
                    
                    # 显示更新对话框
                    dialog = UpdateDialog(
                        update_result['update_info'],
                        update_result['current_version'],
                        self
                    )
                    
                    def on_update_confirmed():
                        new_version = update_result['update_info']['version']
                        if self.update_service.update_local_version(new_version):
                            self.status_label.setText(f"版本已更新为: {new_version}")
                        else:
                            self.status_label.setText("版本更新失败")
                    
                    def on_update_skipped():
                        self.status_label.setText("用户选择不更新")
                    
                    dialog.update_confirmed.connect(on_update_confirmed)
                    dialog.update_skipped.connect(on_update_skipped)
                    
                    dialog.exec_()
                else:
                    self.status_label.setText("当前已是最新版本")
            else:
                self.status_label.setText(f"检查更新失败: {update_result['message']}")
                
        except Exception as e:
            self.status_label.setText(f"测试失败: {str(e)}")
            print(f"[ERROR] 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    def test_api_only(self):
        """仅测试API调用"""
        self.status_label.setText("正在测试API...")
        
        try:
            update_result = self.update_service.check_for_updates()
            
            print("=" * 60)
            print("🧪 更新API测试结果")
            print("=" * 60)
            print(f"成功: {update_result['success']}")
            print(f"有更新: {update_result.get('has_update', False)}")
            print(f"当前版本: {update_result.get('current_version', '未知')}")
            
            if 'update_info' in update_result:
                info = update_result['update_info']
                print(f"最新版本: {info.get('version', '未知')}")
                print(f"发布日期: {info.get('release_date', '未知')}")
                print(f"更新内容: {info.get('description', '无')}")
            
            if 'message' in update_result:
                print(f"消息: {update_result['message']}")
            
            print("=" * 60)
            
            if update_result['success']:
                if update_result.get('has_update'):
                    self.status_label.setText("API测试成功 - 有新版本")
                else:
                    self.status_label.setText("API测试成功 - 无更新")
            else:
                self.status_label.setText("API测试失败")
                
        except Exception as e:
            self.status_label.setText(f"API测试异常: {str(e)}")
            print(f"[ERROR] API测试异常: {e}")
    
    def test_dialog_only(self):
        """仅测试更新对话框"""
        self.status_label.setText("显示模拟更新对话框")
        
        # 模拟更新信息
        mock_update_info = {
            'version': '2.1.0',
            'description': '这是一个模拟的更新内容：\n\n• 新增了更新功能\n• 修复了若干bug\n• 优化了用户界面\n• 提升了性能',
            'release_date': '2025-06-13'
        }
        
        current_version = self.update_service.get_current_version_from_config()
        
        dialog = UpdateDialog(mock_update_info, current_version, self)
        
        def on_update_confirmed():
            self.status_label.setText("用户确认已更新（模拟）")
        
        def on_update_skipped():
            self.status_label.setText("用户选择不更新（模拟）")
        
        dialog.update_confirmed.connect(on_update_confirmed)
        dialog.update_skipped.connect(on_update_skipped)
        
        dialog.exec_()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f5f5f5;
        }
        QWidget {
            font-family: 'Microsoft YaHei', sans-serif;
        }
    """)
    
    window = UpdateTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
