# 播放列表功能测试指南

## 🎯 新功能概述

我已经成功实现了您要求的播放列表功能，包括：

1. **新增"有无副视频"列**：播放列表现在有7列
2. **智能播放队列管理**：支持优先级播放（弹幕>报时>主视频）
3. **语音下载和缓存**：自动下载语音到本地voices目录
4. **变量词替换**：支持{nick}、{date}、{time}等变量
5. **弹幕触发系统**：自动处理弹幕关键词并添加到播放列表
6. **副视频切换**：检测副视频关键词并标记
7. **报时功能**：定时添加报时话术到播放列表

## 🚀 测试步骤

### 方法1：使用现有话术数据测试

1. **启动程序**
   ```bash
   python run_gui_qt5.py
   ```

2. **配置话术管理**
   - 进入"话术管理"标签页
   - 创建或选择一个话术（如"话术1"）
   - 添加时间段（如"10-20秒"）
   - 在时间段中添加话术内容，格式如下：
     ```
     1***欢迎{nick}进入直播间！
     2***现在时间是{time}，感谢观看
     3***【大家好|各位好|hello】，正在玩{gamename}
     4***在线人数：{people}人
     ```

3. **配置系统设置**
   - 进入"系统设置"标签页
   - 在"语音设置"中设置预备语音数量（如5个）
   - 设置语音播放间隔（如0-3秒）

4. **点击播放测试**
   - 返回主界面
   - 在话术选择框中选择刚才创建的话术
   - 点击"播放"按钮
   - 观察待播放列表的变化

### 方法2：使用测试数据（推荐）

如果您没有配置话术数据，程序会自动创建测试数据：

1. **启动程序**
   ```bash
   python run_gui_qt5.py
   ```

2. **直接点击播放**
   - 不需要任何配置
   - 直接点击"播放"按钮
   - 程序会自动检测到没有话术数据
   - 自动创建测试播放列表

3. **观察结果**
   - 待播放列表应该显示3个测试项目
   - 每个项目都有完整的7列信息
   - 语音状态会从"未下载"变为"已下载"或"下载失败"

## 📋 预期结果

### 播放列表显示
```
编号 | 语音类型   | 语音内容                    | 所在时间段   | 语音状态 | 语音文件名      | 有无副视频
-----|-----------|----------------------------|-------------|----------|----------------|----------
1    | 主视频话术 | 欢迎来到直播间！{nick}你好！  | 测试时间段   | 已下载   | abc123_0_100.wav | 无
2    | 主视频话术 | 感谢大家的观看，现在时间是... | 测试时间段   | 已下载   | def456_0_100.wav | 无
3    | 主视频话术 | 【大家好|各位好|hello】...   | 测试时间段   | 已下载   | ghi789_0_100.wav | 无
```

### 控制台输出
```
🎵 初始化播放列表...
🔍 调试信息:
  - 是否有voice_count: True
  - 是否有script_combo: True
  - 预备语音数量: 5
  - 当前话术: ''
  - script_time_segments总数: 0
  - script_time_segments内容: []
⚠️ 未选择话术，创建测试播放列表
🧪 创建测试播放列表数据...
✅ 播放列表显示已更新，共 3 行
🔄 开始下载播放列表语音...
✅ 测试播放列表创建完成，共 3 个项目
```

## 🧪 功能测试

### 1. 弹幕触发测试
- 配置AI对话管理，添加关键词和回复
- 模拟弹幕输入包含关键词的内容
- 观察播放列表是否自动添加"弹幕话术"类型的项目

### 2. 副视频触发测试
- 配置副视频管理，添加关键词和视频源
- 在AI对话回复中包含副视频关键词
- 观察播放列表中"有无副视频"列是否显示对应的视频源

### 3. 报时功能测试
- 在系统设置中启用报时功能
- 设置较短的报时间隔（如1分钟）
- 观察播放列表是否定时添加"报时话术"类型的项目

### 4. 变量替换测试
- 查看下载的语音文件内容
- 确认{nick}、{date}、{time}等变量是否被正确替换
- 确认【选项1|选项2】格式是否随机选择

## 🔧 故障排除

### 问题1：播放列表没有变化
**原因**：可能没有话术数据或时间段设置
**解决**：
1. 检查控制台输出的调试信息
2. 确保选择了有效的话术
3. 确保话术有时间段设置
4. 或者直接使用测试数据功能

### 问题2：语音下载失败
**原因**：网络连接或API问题
**解决**：
1. 检查网络连接
2. 确认API地址 `http://ct.scjanelife.com/voice/bert-vits2` 可访问
3. 查看控制台的详细错误信息

### 问题3：变量没有替换
**原因**：变量替换逻辑问题
**解决**：
1. 检查变量格式是否正确（如{nick}而不是{nick }）
2. 查看控制台的变量替换日志
3. 确认游戏名称、类型等设置是否正确

## 📁 文件结构

测试后会生成以下文件：
```
voices/                    # 语音文件缓存目录
├── abc123_0_100.wav      # 测试语音文件1
├── def456_0_100.wav      # 测试语音文件2
└── ghi789_0_100.wav      # 测试语音文件3

data/                      # 配置文件目录
├── user_settings.json    # 用户设置
└── system_settings.json  # 系统设置
```

## 🎉 成功标志

如果看到以下现象，说明功能正常：

1. ✅ 播放列表显示7列数据
2. ✅ 点击播放后列表有内容显示
3. ✅ 语音状态从"未下载"变为"已下载"
4. ✅ voices目录中生成了.wav文件
5. ✅ 控制台输出详细的调试信息
6. ✅ 弹幕触发时自动添加到播放列表
7. ✅ 报时功能定时添加话术

## 📞 需要帮助？

如果遇到问题，请：
1. 查看控制台的详细输出
2. 检查是否有错误信息
3. 确认网络连接正常
4. 提供具体的错误信息以便进一步诊断

现在您可以开始测试新功能了！🚀
