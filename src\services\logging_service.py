"""
AI Broadcaster v2 - 日志服务
统一的日志管理服务
"""

import logging
import logging.handlers
import json
from pathlib import Path
from typing import Dict, Any, Optional


class LoggingService:
    """日志服务管理器"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._setup_logging()
            self._initialized = True
    
    def _setup_logging(self):
        """设置日志配置"""
        # 确保日志目录存在
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 加载日志配置
        config = self._load_logging_config()
        
        # 设置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, config.get('level', 'INFO')))
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 添加控制台处理器
        if config.get('console_logging', True):
            console_handler = logging.StreamHandler()
            console_handler.setLevel(getattr(logging, config.get('level', 'INFO')))
            console_formatter = logging.Formatter(
                config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
                datefmt=config.get('date_format', '%Y-%m-%d %H:%M:%S')
            )
            console_handler.setFormatter(console_formatter)
            root_logger.addHandler(console_handler)
        
        # 添加文件处理器
        if config.get('file_logging', True):
            # 主日志文件
            file_handler = logging.handlers.RotatingFileHandler(
                'logs/app.log',
                maxBytes=config.get('max_file_size_mb', 10) * 1024 * 1024,
                backupCount=config.get('backup_count', 5),
                encoding='utf-8'
            )
            file_handler.setLevel(logging.INFO)
            file_formatter = logging.Formatter(
                config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
                datefmt=config.get('date_format', '%Y-%m-%d %H:%M:%S')
            )
            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)
            
            # 错误日志文件
            error_handler = logging.handlers.RotatingFileHandler(
                'logs/error.log',
                maxBytes=config.get('max_file_size_mb', 10) * 1024 * 1024,
                backupCount=config.get('backup_count', 5),
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(file_formatter)
            root_logger.addHandler(error_handler)

        # 🔥 新增：设置特定模块的日志级别，隐藏冗余日志
        self._configure_module_loggers()

        print("✅ 日志系统初始化完成")

    def _configure_module_loggers(self):
        """配置特定模块的日志级别"""
        # 隐藏websockets的详细日志
        logging.getLogger('websockets.client').setLevel(logging.WARNING)
        logging.getLogger('websockets.server').setLevel(logging.WARNING)
        logging.getLogger('websockets').setLevel(logging.WARNING)

        # 保持其他模块的正常日志级别
        logging.getLogger('urllib3').setLevel(logging.DEBUG)
        logging.getLogger('requests').setLevel(logging.INFO)
    
    def _load_logging_config(self) -> Dict[str, Any]:
        """加载日志配置"""
        try:
            # 尝试从应用配置中加载日志设置
            config_path = Path("config/app_config.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    app_config = json.load(f)
                    return app_config.get('logging', {})
        except Exception as e:
            print(f"⚠️  加载日志配置失败: {e}")
        
        # 返回默认配置
        return {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'date_format': '%Y-%m-%d %H:%M:%S',
            'file_logging': True,
            'console_logging': True,
            'max_file_size_mb': 10,
            'backup_count': 5
        }
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        return logging.getLogger(name)


# 全局日志服务实例
_logging_service = LoggingService()


def setup_logging():
    """初始化日志系统"""
    global _logging_service
    _logging_service = LoggingService()
    return _logging_service


def get_logger(name: str) -> logging.Logger:
    """
    获取日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logging.Logger: 日志器实例
    """
    return _logging_service.get_logger(name)


class AppLogger:
    """应用程序日志器包装类"""
    
    def __init__(self, name: str):
        self.logger = get_logger(name)
        self.name = name
    
    def debug(self, message: str, *args, **kwargs):
        """调试日志"""
        self.logger.debug(message, *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs):
        """信息日志"""
        self.logger.info(message, *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs):
        """警告日志"""
        self.logger.warning(message, *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs):
        """错误日志"""
        self.logger.error(message, *args, **kwargs)
    
    def critical(self, message: str, *args, **kwargs):
        """严重错误日志"""
        self.logger.critical(message, *args, **kwargs)
    
    def exception(self, message: str, *args, **kwargs):
        """异常日志（包含堆栈跟踪）"""
        self.logger.exception(message, *args, **kwargs)
    
    def log_startup(self, component: str):
        """记录组件启动"""
        self.info(f"🚀 {component} 启动中...")
    
    def log_shutdown(self, component: str):
        """记录组件关闭"""
        self.info(f"🛑 {component} 正在关闭...")
    
    def log_success(self, message: str):
        """记录成功操作"""
        self.info(f"✅ {message}")
    
    def log_failure(self, message: str):
        """记录失败操作"""
        self.error(f"❌ {message}")
    
    def log_warning(self, message: str):
        """记录警告"""
        self.warning(f"⚠️  {message}")


def create_logger(name: str) -> AppLogger:
    """创建应用程序日志器"""
    return AppLogger(name)
