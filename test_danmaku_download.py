#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
弹幕话术下载功能测试
测试弹幕话术的下载状态更新是否正常
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_danmaku_download():
    """测试弹幕话术下载功能"""
    print("🧪 开始测试弹幕话术下载功能...")
    
    try:
        # 导入主程序
        from run_gui_qt5 import MainWindow
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer

        # 创建应用
        app = QApplication(sys.argv)

        # 创建主窗口（需要用户信息）
        user_info = {
            'username': 'test_user',
            'user_id': 1,
            'expire_time': '2025-12-31'
        }
        window = MainWindow(user_info)
        
        # 等待界面初始化完成
        def start_test():
            print("✅ 界面初始化完成，开始测试...")
            
            # 模拟添加弹幕话术
            test_content = "测试弹幕话术内容，感谢{nick}的支持！"
            print(f"📝 添加测试弹幕话术: {test_content}")
            
            # 添加弹幕话术到播放列表
            window.add_danmaku_to_playlist(test_content)
            
            # 检查播放列表状态
            def check_status():
                print("🔍 检查播放列表状态...")

                if hasattr(window, 'playlist_items') and window.playlist_items:
                    danmaku_found = False
                    for i, item in enumerate(window.playlist_items):
                        if item.get('voice_type') == '弹幕话术':
                            danmaku_found = True
                            status = item.get('status', '未知')
                            content = item.get('content', '')[:30]
                            filename = item.get('filename', '')
                            print(f"  弹幕话术 {i+1}: {content}... - 状态: {status} - 文件: {filename}")

                            # 检查文件是否存在
                            if filename:
                                file_path = Path("voices") / filename
                                exists = file_path.exists()
                                print(f"    文件存在: {exists}")
                                if exists:
                                    file_size = file_path.stat().st_size
                                    print(f"    文件大小: {file_size} 字节")
                                    if status == '已下载':
                                        print("✅ 弹幕话术下载功能正常工作！")
                                        return

                    if not danmaku_found:
                        print("❌ 没有找到弹幕话术")
                else:
                    print("❌ 播放列表为空")

                # 5秒后再次检查
                QTimer.singleShot(5000, check_status)
            
            # 2秒后开始检查状态
            QTimer.singleShot(2000, check_status)
            
            # 30秒后退出测试
            def exit_test():
                print("🏁 测试完成，退出程序")
                app.quit()
            
            QTimer.singleShot(30000, exit_test)
        
        # 2秒后开始测试
        QTimer.singleShot(2000, start_test)
        
        # 显示窗口
        window.show()
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_danmaku_download()
