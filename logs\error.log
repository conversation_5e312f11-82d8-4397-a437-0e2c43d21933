2025-05-29 10:43:06 - database_manager - ERROR - 数据库操作失败: table scripts has no column named category
2025-05-29 10:43:06 - error_handler - ERROR - 异常发生在 DatabaseManager.execute_insert: table scripts has no column named category
2025-05-29 10:43:06 - error_handler - ERROR - 异常详情
Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\services\error_handler.py", line 84, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\data\database_manager.py", line 265, in execute_insert
    cursor.execute(query, params)
sqlite3.OperationalError: table scripts has no column named category
2025-05-29 10:43:06 - script_manager - ERROR - 创建话术失败: 测试话术
2025-05-29 10:44:50 - database_manager - ERROR - 数据库操作失败: no such column: category
2025-05-29 10:44:50 - database_manager - ERROR - ❌ 数据库初始化失败: no such column: category
2025-05-29 10:46:18 - script_scheduler - ERROR - 从计划话术中选择失败: 'sqlite3.Row' object has no attribute 'get'
2025-05-29 10:46:18 - script_scheduler - ERROR - 从计划话术中选择失败: 'sqlite3.Row' object has no attribute 'get'
2025-05-29 10:46:18 - script_scheduler - ERROR - 获取时间表预览失败: 'sqlite3.Row' object has no attribute 'get'
2025-05-29 10:46:18 - conversation_history - ERROR - 获取最近对话记录失败: 'sqlite3.Row' object does not support item assignment
2025-05-29 10:46:18 - conversation_history - ERROR - 获取最近对话记录失败: 'sqlite3.Row' object does not support item assignment
2025-05-29 10:46:18 - script_scheduler - ERROR - 从计划话术中选择失败: 'sqlite3.Row' object has no attribute 'get'
2025-05-29 11:10:18 - error_handler - ERROR - 异常发生在 SourceManager.create_source_preset: name 'datetime' is not defined
2025-05-29 11:10:18 - error_handler - ERROR - 异常详情
Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\services\error_handler.py", line 84, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\obs\source_manager.py", line 266, in create_source_preset
    'created_at': datetime.now()
                  ^^^^^^^^
NameError: name 'datetime' is not defined. Did you forget to import 'datetime'
2025-05-29 11:11:13 - database_manager - ERROR - 数据库操作失败: UNIQUE constraint failed: scripts.name
2025-05-29 11:11:13 - error_handler - ERROR - 异常发生在 DatabaseManager.execute_insert: UNIQUE constraint failed: scripts.name
2025-05-29 11:11:13 - error_handler - ERROR - 异常详情
Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\services\error_handler.py", line 84, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\data\database_manager.py", line 356, in execute_insert
    cursor.execute(query, params)
sqlite3.IntegrityError: UNIQUE constraint failed: scripts.name
2025-05-29 11:11:13 - script_manager - ERROR - 创建话术失败: 集成测试话术
2025-05-29 11:11:13 - conversation_history - ERROR - 获取最近对话记录失败: 'sqlite3.Row' object does not support item assignment
2025-05-29 11:23:38 - database_manager - ERROR - 数据库操作失败: no such column: email
2025-05-29 11:23:38 - error_handler - ERROR - 异常发生在 DatabaseManager.execute_query: no such column: email
2025-05-29 11:23:38 - error_handler - ERROR - 异常详情
Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\services\error_handler.py", line 84, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\data\database_manager.py", line 477, in execute_query
    cursor.execute(query, params)
sqlite3.OperationalError: no such column: email
2025-05-29 11:23:38 - database_manager - ERROR - 数据库操作失败: table users has no column named email
2025-05-29 11:23:38 - error_handler - ERROR - 异常发生在 DatabaseManager.execute_insert: table users has no column named email
2025-05-29 11:23:38 - error_handler - ERROR - 异常详情
Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\services\error_handler.py", line 84, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\data\database_manager.py", line 491, in execute_insert
    cursor.execute(query, params)
sqlite3.OperationalError: table users has no column named email
2025-05-29 14:16:11 - api_client - ERROR - 请求超时
2025-05-29 15:26:16 - api_test - ERROR - 测试过程中发生错误: slice(None, 3, None)
2025-05-29 15:26:27 - api_test - ERROR - 测试过程中发生错误: slice(None, 3, None)
2025-05-29 15:29:51 - api_demo - ERROR - 演示过程中发生错误: 'str' object has no attribute 'get'
2025-05-29 15:30:05 - api_demo - ERROR - 演示过程中发生错误: 'str' object has no attribute 'get'
2025-05-29 16:46:32 - script_service - ERROR - 获取话术内容失败: Extra data: line 1 column 2 (char 1)
2025-05-29 16:46:39 - script_service - ERROR - 获取话术内容失败: Extra data: line 1 column 2 (char 1)
2025-05-29 16:46:46 - script_service - ERROR - 获取话术内容失败: Extra data: line 1 column 2 (char 1)
2025-05-30 09:32:44 - system_settings - ERROR - 获取音频设备失败: 'max_outputs'
2025-05-30 09:33:18 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 09:33:18 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 09:33:28 - obs_controller - ERROR - 获取场景列表失败: 
2025-05-30 09:33:41 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 09:33:41 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 09:33:51 - obs_controller - ERROR - 获取场景列表失败: 
2025-05-30 09:35:15 - danmaku_manager - ERROR - 异步连接弹幕服务器失败: [WinError 1225] 远程计算机拒绝网络连接。
2025-05-30 09:41:01 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 09:41:01 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 09:41:11 - obs_controller - ERROR - 获取场景列表失败: 
2025-05-30 09:42:35 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 09:42:35 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 09:42:45 - obs_controller - ERROR - 获取场景列表失败: 
2025-05-30 09:53:48 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 09:53:48 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 09:53:58 - obs_controller - ERROR - 获取场景列表失败: 
2025-05-30 09:56:55 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 09:56:55 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 09:57:05 - obs_controller - ERROR - 获取场景列表失败: 
2025-05-30 10:04:19 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 10:04:19 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 10:04:29 - obs_controller - ERROR - 获取场景列表失败: 
2025-05-30 10:04:57 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 10:04:57 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 10:05:07 - obs_controller - ERROR - 获取场景列表失败: 
2025-05-30 10:20:13 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 10:20:13 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 10:28:24 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 10:28:24 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 10:29:26 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 10:29:26 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 10:38:04 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 10:38:04 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 10:38:14 - obs_controller - ERROR - 获取场景列表失败: 
2025-05-30 10:38:46 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 10:38:46 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 10:40:17 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 10:40:17 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 10:40:27 - obs_controller - ERROR - 获取场景列表失败: 
2025-05-30 10:41:34 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 10:41:34 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 10:41:44 - obs_controller - ERROR - 获取场景列表失败: 
2025-05-30 11:33:17 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 11:33:17 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 11:33:27 - obs_controller - ERROR - 获取场景列表失败: 
2025-05-30 11:37:58 - obs_controller - ERROR - 连接OBS失败: 
2025-05-30 11:37:58 - obs_controller - ERROR - 获取源列表失败: 
2025-05-30 11:38:08 - obs_controller - ERROR - 获取场景列表失败: 
2025-05-30 13:01:56 - obs_controller - ERROR - 连接OBS失败: 
2025-06-02 09:24:38 - api_client - ERROR - 无法连接到服务器
2025-06-02 09:25:17 - api_client - ERROR - 无法连接到服务器
2025-06-02 09:25:21 - api_client - ERROR - 无法连接到服务器
2025-06-02 09:44:55 - dual_video_manager - ERROR - 检查源可见性失败: 'OBSController' object has no attribute 'is_connected'
2025-06-02 09:44:55 - dual_video_manager - ERROR - 检查源可见性失败: 'OBSController' object has no attribute 'is_connected'
2025-06-02 09:44:55 - dual_video_manager - ERROR - 显示视频源失败: 'OBSController' object has no attribute 'is_connected'
2025-06-02 09:44:55 - dual_video_manager - ERROR - 隐藏视频源失败: 'OBSController' object has no attribute 'is_connected'
2025-06-02 09:44:55 - dual_video_manager - ERROR - 显示视频源失败: 'OBSController' object has no attribute 'is_connected'
2025-06-02 09:44:55 - dual_video_manager - ERROR - 播放视频源失败: 'OBSController' object has no attribute 'is_connected'
2025-06-02 09:44:55 - dual_video_manager - ERROR - 获取当前视频状态失败: 'coroutine' object does not support item assignment
2025-06-02 09:44:55 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:44:55 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:44:55 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:44:55 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:44:55 - dual_video_manager - ERROR - 播放视频源失败: 主视频源B
2025-06-02 09:44:56 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:44:56 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:44:56 - dual_video_manager - ERROR - 播放视频源失败: 主视频源A
2025-06-02 09:44:56 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:44:56 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:44:56 - dual_video_manager - ERROR - 播放视频源失败: 主视频源B
2025-06-02 09:44:56 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:44:56 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:44:56 - dual_video_manager - ERROR - 播放视频源失败: 主视频源A
2025-06-02 09:44:56 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:44:56 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:44:56 - dual_video_manager - ERROR - 播放视频源失败: 主视频源B
2025-06-02 09:44:56 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:44:56 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:44:56 - dual_video_manager - ERROR - 播放视频源失败: 主视频源A
2025-06-02 09:44:56 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:44:56 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:44:56 - dual_video_manager - ERROR - 播放视频源失败: 主视频源B
2025-06-02 09:44:56 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:44:56 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:44:56 - dual_video_manager - ERROR - 播放视频源失败: 主视频源A
2025-06-02 09:47:35 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:47:35 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:47:36 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:47:36 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:47:36 - dual_video_manager - ERROR - 播放视频源失败: 主视频源B
2025-06-02 09:47:36 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:47:36 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:47:36 - dual_video_manager - ERROR - 播放视频源失败: 主视频源A
2025-06-02 09:47:36 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:47:36 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:47:36 - dual_video_manager - ERROR - 播放视频源失败: 主视频源B
2025-06-02 09:47:36 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:47:36 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:47:36 - dual_video_manager - ERROR - 播放视频源失败: 主视频源A
2025-06-02 09:47:36 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:47:36 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:47:36 - dual_video_manager - ERROR - 播放视频源失败: 主视频源B
2025-06-02 09:47:36 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:47:36 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:47:36 - dual_video_manager - ERROR - 播放视频源失败: 主视频源A
2025-06-02 09:47:36 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:47:36 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:47:36 - dual_video_manager - ERROR - 播放视频源失败: 主视频源B
2025-06-02 09:47:36 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:47:36 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:47:36 - dual_video_manager - ERROR - 播放视频源失败: 主视频源A
2025-06-02 09:53:19 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:53:19 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:53:20 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:53:20 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:53:20 - dual_video_manager - ERROR - 播放视频源失败: 主视频源B
2025-06-02 09:53:20 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:53:20 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:53:20 - dual_video_manager - ERROR - 播放视频源失败: 主视频源A
2025-06-02 09:53:20 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:53:20 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:53:20 - dual_video_manager - ERROR - 播放视频源失败: 主视频源B
2025-06-02 09:53:20 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:53:20 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:53:20 - dual_video_manager - ERROR - 播放视频源失败: 主视频源A
2025-06-02 09:53:20 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:53:20 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:53:20 - dual_video_manager - ERROR - 播放视频源失败: 主视频源B
2025-06-02 09:53:20 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:53:20 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:53:20 - dual_video_manager - ERROR - 播放视频源失败: 主视频源A
2025-06-02 09:53:20 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:53:20 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:53:20 - dual_video_manager - ERROR - 播放视频源失败: 主视频源B
2025-06-02 09:53:20 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源B
2025-06-02 09:53:20 - dual_video_manager - ERROR - 在场景 测试场景 中找不到源 主视频源A
2025-06-02 09:53:20 - dual_video_manager - ERROR - 播放视频源失败: 主视频源A
2025-06-02 09:58:07 - dual_video_manager - ERROR - 在场景 Main Scene 中找不到源 A
2025-06-02 09:58:08 - dual_video_manager - ERROR - 在场景 Main Scene 中找不到源 A
2025-06-02 09:58:09 - dual_video_manager - ERROR - 无效的视频源: 主视频源A
2025-06-02 09:58:10 - dual_video_manager - ERROR - 无效的视频源: 主视频源B
2025-06-02 10:00:56 - dual_video_manager - ERROR - 无效的视频源: 主视频源A
2025-06-02 10:00:57 - dual_video_manager - ERROR - 无效的视频源: 主视频源B
2025-06-02 10:23:27 - dual_video_manager - ERROR - 无效的视频源: 主视频源A
2025-06-02 10:23:28 - dual_video_manager - ERROR - 无效的视频源: 主视频源B
2025-06-02 10:24:51 - dual_video_manager - ERROR - 无效的视频源: 主视频源A
2025-06-02 10:24:52 - dual_video_manager - ERROR - 无效的视频源: 主视频源B
2025-06-02 10:30:38 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<Connection.keepalive() running at D:\Program Files (x86)\python32\Lib\site-packages\websockets\asyncio\connection.py:815> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-02 10:30:38 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-3' coro=<OBSController._listen_messages() running at C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\services\obs_controller.py:325> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-02 10:30:38 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-55' coro=<OBSController._disconnect_async() done, defined at C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\services\obs_controller.py:393> wait_for=<Future pending cb=[shield.<locals>._outer_done_callback() at D:\Program Files (x86)\python32\Lib\asyncio\tasks.py:922, Task.task_wakeup()]> cb=[_chain_future.<locals>._call_set_state() at D:\Program Files (x86)\python32\Lib\asyncio\futures.py:396]>
2025-06-02 10:32:47 - test_single_source_debug - ERROR - 测试异常: 'NoneType' object is not iterable
2025-06-02 10:34:36 - test_single_source_debug - ERROR - 测试异常: 'NoneType' object is not iterable
2025-06-02 10:34:36 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-2' coro=<Connection.keepalive() done, defined at D:\Program Files (x86)\python32\Lib\site-packages\websockets\asyncio\connection.py:803> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-06-02 10:34:36 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-3' coro=<OBSController._listen_messages() running at C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\services\obs_controller.py:335> wait_for=<Future cancelled>>
2025-06-02 10:34:36 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-12' coro=<OBSController._disconnect_async() done, defined at C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\services\obs_controller.py:393> wait_for=<Future pending cb=[shield.<locals>._outer_done_callback() at D:\Program Files (x86)\python32\Lib\asyncio\tasks.py:922, Task.task_wakeup()]> cb=[_chain_future.<locals>._call_set_state() at D:\Program Files (x86)\python32\Lib\asyncio\futures.py:396]>
2025-06-02 10:35:27 - test_single_source_debug - ERROR - 测试异常: 'NoneType' object is not iterable
2025-06-02 10:38:35 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-54' coro=<OBSController._disconnect_async() done, defined at C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\services\obs_controller.py:393> wait_for=<Future finished result=None> cb=[_chain_future.<locals>._call_set_state() at D:\Program Files (x86)\python32\Lib\asyncio\futures.py:396]>
2025-06-02 10:43:55 - dual_video_manager - ERROR - 速度 3.0 超出范围 [0.5, 2.0]
2025-06-02 10:45:02 - dual_video_manager - ERROR - 速度 3.0 超出范围 [0.5, 2.0]
2025-06-02 10:59:44 - dual_video_manager - ERROR - 速度 0.3 超出范围 [0.5, 2.0]
2025-06-02 10:59:44 - dual_video_manager - ERROR - 速度 3.0 超出范围 [0.5, 2.0]
2025-06-02 10:59:44 - dual_video_manager - ERROR - 无效的视频源: test_source
2025-06-02 16:30:13 - dual_video_manager - ERROR - 速度 1.0 超出范围 [1.2, 2.0]
2025-06-02 17:05:31 - error_handler - ERROR - 异常发生在 DualVideoManager.stop_monitoring: 'DualVideoManager' object has no attribute 'logger'
2025-06-02 17:05:31 - error_handler - ERROR - 异常详情
Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 315, in stop_monitoring
    if not self.monitoring:
           ^^^^^^^^^^^^^^^
AttributeError: 'DualVideoManager' object has no attribute 'monitoring'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\services\error_handler.py", line 84, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 327, in stop_monitoring
    self.logger.error(f"停止视频监控失败: {e}")
    ^^^^^^^^^^^
AttributeError: 'DualVideoManager' object has no attribute 'logger'
2025-06-02 17:06:36 - dual_video_manager - ERROR - 获取当前场景失败: 'MockOBSController' object has no attribute 'send_request_sync'
2025-06-02 17:06:36 - dual_video_manager - ERROR - 获取当前场景失败: 'MockOBSController' object has no attribute 'send_request_sync'
2025-06-02 17:06:36 - dual_video_manager - ERROR - 获取当前场景失败: 'MockOBSController' object has no attribute 'send_request_sync'
2025-06-02 17:06:36 - dual_video_manager - ERROR - 获取当前场景失败: 'MockOBSController' object has no attribute 'send_request_sync'
2025-06-02 17:06:36 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:03:09 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:03:09 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1178, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:04:57 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:04:57 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1178, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:11:32 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_a
2025-06-03 08:18:19 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:18:19 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:18:25 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:18:25 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:18:26 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:18:26 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:18:27 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:18:27 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:18:31 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:18:31 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:18:32 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:18:32 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:18:34 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:18:34 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:18:35 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:18:35 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:18:36 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:18:36 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:18:37 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:18:37 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:18:38 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:18:38 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:18:39 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:18:39 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:18:40 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:18:40 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:18:41 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:18:41 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:19:48 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:19:48 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:20:54 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:20:54 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:22:01 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:22:01 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:23:07 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:23:07 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:24:14 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:24:14 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:25:21 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:25:21 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:26:26 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:26:26 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:27:33 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:27:33 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:28:39 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:28:39 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:29:46 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:29:46 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:30:51 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:30:51 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:31:58 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:31:58 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:33:04 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:33:04 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:34:11 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:34:11 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:35:17 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:35:17 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:36:23 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:36:23 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:37:29 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:37:29 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:39:42 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:39:42 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:40:29 - dual_video_manager - ERROR - ❌ 变化速度出错: 'OBSController' object has no attribute 'hide_all_sources'
2025-06-03 08:40:29 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1183, in _check_active_source_status_ui_design_style
    self.obs_controller.hide_all_sources()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'OBSController' object has no attribute 'hide_all_sources'

2025-06-03 08:44:49 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_a
2025-06-03 08:44:49 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_b
2025-06-03 08:44:49 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_a
2025-06-03 08:44:49 - dual_video_manager - ERROR - ❌ 检查主视频状态出错: 'DualVideoManager' object has no attribute 'on_switch_success'
2025-06-03 08:44:49 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1216, in _check_active_source_status_ui_design_style
    if self.on_switch_success:
       ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DualVideoManager' object has no attribute 'on_switch_success'

2025-06-03 08:44:49 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_a
2025-06-03 08:44:49 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_b
2025-06-03 08:44:49 - dual_video_manager - ERROR - ❌ 检查主视频状态出错: 'DualVideoManager' object has no attribute 'on_switch_success'
2025-06-03 08:44:49 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1216, in _check_active_source_status_ui_design_style
    if self.on_switch_success:
       ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DualVideoManager' object has no attribute 'on_switch_success'

2025-06-03 08:44:49 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_b
2025-06-03 08:44:49 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_a
2025-06-03 08:44:49 - dual_video_manager - ERROR - ❌ 检查主视频状态出错: 'DualVideoManager' object has no attribute 'on_switch_success'
2025-06-03 08:44:49 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1216, in _check_active_source_status_ui_design_style
    if self.on_switch_success:
       ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DualVideoManager' object has no attribute 'on_switch_success'

2025-06-03 08:44:49 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_a
2025-06-03 08:44:49 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_b
2025-06-03 08:44:49 - dual_video_manager - ERROR - ❌ 检查主视频状态出错: 'DualVideoManager' object has no attribute 'on_switch_success'
2025-06-03 08:44:49 - dual_video_manager - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\core\playback\dual_video_manager.py", line 1216, in _check_active_source_status_ui_design_style
    if self.on_switch_success:
       ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DualVideoManager' object has no attribute 'on_switch_success'

2025-06-03 08:45:25 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_a
2025-06-03 08:45:25 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_b
2025-06-03 08:45:25 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_a
2025-06-03 08:45:25 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_a
2025-06-03 08:45:25 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_b
2025-06-03 08:45:25 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_b
2025-06-03 08:45:25 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_a
2025-06-03 08:45:25 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_a
2025-06-03 08:45:25 - dual_video_manager - ERROR - 在场景 Scene 中找不到源 test_video_b
2025-06-03 08:54:51 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:54:51 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:54:51 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:54:54 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:54:54 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:54:54 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:54:55 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:54:55 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:54:55 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:54:55 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:54:55 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:54:55 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:54:55 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:55:21 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:55:22 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:55:22 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:55:25 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:55:25 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:55:25 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:55:25 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:55:25 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:55:25 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:55:25 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:55:25 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:55:25 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 08:55:25 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:02:57 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:02:57 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:02:57 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:02:57 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:02:57 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:02:57 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:02:57 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:02:57 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:03:56 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:03:56 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:03:56 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:03:56 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:03:56 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:03:56 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:03:56 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:03:56 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:17 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:17 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:17 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:17 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:17 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:17 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:17 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:17 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:17 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:54 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:54 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:54 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:54 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:54 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:54 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:55 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:55 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:10:55 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:15:54 - dual_video_manager - ERROR - 速度 1.0 超出范围 [1.2, 2.0]
2025-06-03 09:22:26 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:22:26 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:22:26 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:23:08 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:23:08 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:23:08 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:24:18 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:24:18 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:24:18 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:24:18 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:25:05 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:25:05 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:25:05 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-03 09:25:05 - dual_video_manager - ERROR - 无法获取当前场景
2025-06-04 08:34:20 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 08:46:46 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 10:45:34 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 11:09:28 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 11:42:08 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 11:45:43 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 13:40:22 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 13:43:10 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 13:54:56 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 14:13:04 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 14:17:19 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 14:17:20 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 14:47:54 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 14:57:46 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 15:13:20 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 15:38:48 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 15:39:26 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 15:47:31 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 15:50:21 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-04 16:00:27 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 08:10:40 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 08:18:25 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 08:32:51 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 08:56:05 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 08:56:05 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-481' coro=<OBSController._disconnect_async() done, defined at C:\Users\<USER>\wrzb\ai_broadcaster_v2\src\services\obs_controller.py:393> wait_for=<Future finished result=None> cb=[_chain_future.<locals>._call_set_state() at D:\Program Files (x86)\python32\Lib\asyncio\futures.py:396]>
2025-06-05 09:24:58 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 09:34:57 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 10:07:13 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 10:38:22 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 10:39:11 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 11:23:57 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 11:34:21 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 11:37:48 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 11:42:52 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 11:44:05 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 11:54:19 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 13:05:42 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 13:05:42 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 13:15:36 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 13:36:32 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 13:47:33 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 13:47:34 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 14:32:19 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 14:43:55 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 14:56:06 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 15:02:15 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 15:10:19 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 16:03:45 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 16:17:19 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 16:17:19 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 16:45:48 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-05 16:45:48 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-06 07:44:19 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-06 13:43:32 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-06 13:55:40 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-06 14:51:17 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-07 06:16:57 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-07 06:16:57 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-07 06:43:14 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-07 06:53:38 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-07 07:01:52 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-07 07:05:19 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-07 07:13:48 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-07 07:16:19 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-07 07:21:05 - audio_player - ERROR - pygame播放失败: mixer not initialized
2025-06-07 14:17:36 - danmaku_manager - ERROR - 异步连接弹幕服务器失败: [WinError 1225] 远程计算机拒绝网络连接。
2025-06-09 20:05:47 - dual_video_manager - ERROR - 获取当前场景失败: 
2025-06-11 13:16:08 - api_client - ERROR - 请求超时
2025-06-11 16:17:28 - api_client - ERROR - 无法连接到服务器
2025-06-11 19:22:04 - script_service - ERROR - 无法连接到服务器
2025-06-11 19:22:08 - dialogue_service - ERROR - 无法连接到服务器
2025-06-11 20:22:52 - api_client - ERROR - 无法连接到服务器
2025-06-11 20:23:05 - api_client - ERROR - 无法连接到服务器
2025-06-11 20:32:06 - api_client - ERROR - 无法连接到服务器
2025-06-11 21:20:36 - dialogue_service - ERROR - 请求超时
2025-06-11 21:22:01 - script_service - ERROR - 上传话术失败: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=10)
2025-06-11 21:22:11 - script_service - ERROR - 上传话术失败: HTTPConnectionPool(host='127.0.0.1', port=7890): Read timed out. (read timeout=10)
2025-06-11 21:22:18 - script_service - ERROR - 上传话术失败: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://**************:12456/ (Caused by ProxyError('Unable to connect to proxy', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)))
2025-06-11 21:22:41 - api_client - ERROR - 请求超时
2025-06-11 21:22:54 - api_client - ERROR - 请求超时
2025-06-11 21:23:42 - api_client - ERROR - 请求超时
2025-06-11 21:31:45 - api_client - ERROR - 请求超时
2025-06-11 21:55:58 - api_client - ERROR - 请求超时
2025-06-11 21:56:04 - api_client - ERROR - 无法连接到服务器
2025-06-11 22:09:41 - api_client - ERROR - 请求超时
2025-06-11 22:09:46 - api_client - ERROR - 无法连接到服务器
2025-06-11 22:22:13 - api_client - ERROR - 请求超时
2025-06-11 22:22:27 - api_client - ERROR - 请求超时
2025-06-11 22:23:05 - api_client - ERROR - 请求超时
2025-06-11 22:23:47 - api_client - ERROR - 请求超时
2025-06-11 22:24:22 - api_client - ERROR - 请求超时
2025-06-12 07:55:26 - api_client - ERROR - 请求超时
2025-06-12 08:03:36 - script_service - ERROR - 上传话术失败: HTTPConnectionPool(host='**************', port=12456): Read timed out. (read timeout=10)
2025-06-12 08:03:52 - script_service - ERROR - 上传话术失败: HTTPConnectionPool(host='**************', port=12456): Read timed out. (read timeout=10)
2025-06-12 08:28:07 - api_client - ERROR - 请求超时
2025-06-12 09:49:26 - dialogue_service - ERROR - 无法连接到服务器
2025-06-12 10:07:04 - script_service - ERROR - 上传话术失败: HTTPConnectionPool(host='**************', port=12456): Read timed out. (read timeout=10)
2025-06-12 10:07:21 - script_service - ERROR - 上传话术失败: HTTPConnectionPool(host='**************', port=12456): Read timed out. (read timeout=10)
2025-06-12 15:24:04 - script_service - ERROR - 上传话术失败: cannot access local variable 'json' where it is not associated with a value
2025-06-12 15:24:30 - script_service - ERROR - 上传话术失败: cannot access local variable 'json' where it is not associated with a value
2025-06-12 15:26:04 - script_service - ERROR - 上传话术失败: cannot access local variable 'json' where it is not associated with a value
2025-06-12 15:26:10 - script_service - ERROR - 上传话术失败: cannot access local variable 'json' where it is not associated with a value
2025-06-12 15:26:28 - script_service - ERROR - 上传话术失败: cannot access local variable 'json' where it is not associated with a value
2025-06-12 15:26:32 - script_service - ERROR - 上传话术失败: cannot access local variable 'json' where it is not associated with a value
2025-06-12 15:37:15 - script_service - ERROR - 上传话术失败: cannot access local variable 'json' where it is not associated with a value
2025-06-13 11:52:55 - script_service - ERROR - 获取话术内容失败: 'ScriptService' object has no attribute '_parse_script_content'
2025-06-13 11:52:56 - script_service - ERROR - 获取话术内容失败: 'ScriptService' object has no attribute '_parse_script_content'
2025-06-13 11:52:56 - script_service - ERROR - 获取话术内容失败: 'ScriptService' object has no attribute '_parse_script_content'
2025-06-13 11:53:06 - script_service - ERROR - 获取话术内容失败: 'ScriptService' object has no attribute '_parse_script_content'
2025-06-13 11:54:34 - script_service - ERROR - 获取话术内容失败: HTTPConnectionPool(host='localhost', port=12456): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x035EF510>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-06-14 22:32:47 - voice_service - ERROR - 无法连接到语音服务器
2025-06-14 22:34:00 - voice_service - ERROR - 无法连接到语音服务器
2025-06-14 22:34:19 - voice_service - ERROR - 无法连接到语音服务器
