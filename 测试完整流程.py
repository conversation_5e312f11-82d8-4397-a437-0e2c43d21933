#!/usr/bin/env python3
"""
测试完整的解析和保存流程
"""

import json
import re

def test_complete_workflow():
    """测试完整的解析和保存流程"""
    print("=== 测试完整的解析和保存流程 ===")
    
    # 1. 模拟从服务器获取的原始JSON数据
    original_json = """{
  "0秒 - 10秒": "1***哈喽，大家好，首先感谢【大哥|哥哥|宝子】们的捧场。\\n2***稍作停留也是爱，所能接触的都是这四个大字。\\n3***没有花里胡哨，没有弯弯绕绕。\\n4***有啥就问，新来的家人们。\\n5***云想衣裳花想容，春风拂槛露华浓。",
  "10秒 - 20秒": "6***自古套路得人心，但唯有真心得天下。\\n7***新来的家人们，相遇就是缘分。\\n8***你们的到来蓬荜生辉。\\n9***随便什么数字都是这四个大字。\\n10***俗话说的好，大哥们。",
  "20秒 - 30秒": "11***相信自己的眼睛，相信自己的耳朵。\\n12***斩青丝斩难过斩断红尘不为过。\\n13***不穿摸，不贴片，仙侠界的彭于晏。\\n14***播播间人很多，主播一个人一张嘴。\\n15***万水千山总是情，今天主播最热情。"
}"""
    
    print("1. 原始JSON数据:")
    print(original_json[:200] + "...")
    
    # 2. 解析JSON（模拟主程序的解析逻辑）
    def parse_time_segment_json_format(script_name, content):
        """模拟主程序的解析函数"""
        try:
            print(f"\n2. 开始解析时间段JSON格式，话术: {script_name}")
            
            # 解析JSON
            time_segments_data = json.loads(content)
            
            if not isinstance(time_segments_data, dict):
                print(f"❌ JSON数据不是字典格式")
                return False, {}
            
            # 检查是否为新格式
            is_new_format = all(isinstance(v, str) for v in time_segments_data.values())
            print(f"✅ 格式检测: {'新格式' if is_new_format else '旧格式'}")
            
            if not is_new_format:
                print(f"❌ 不是新的时间段格式")
                return False, {}
            
            # 初始化数据结构
            script_time_segments = {script_name: {}}
            
            # 解析每个时间段
            parsed_count = 0
            for time_key, time_data in time_segments_data.items():
                print(f"   处理时间段: '{time_key}'")
                
                if isinstance(time_data, str):
                    segment_content = time_data
                    
                    # 提取时间信息
                    pattern = r'(\d+)秒\s*-\s*(\d+)秒'
                    match = re.search(pattern, time_key)
                    
                    if match:
                        start_time = int(match.group(1))
                        end_time = int(match.group(2))
                    else:
                        start_time = parsed_count * 10
                        end_time = (parsed_count + 1) * 10
                    
                    print(f"      时间: {start_time}-{end_time}秒")
                    
                    # 统计话术行数（使用原始内容中的 \\n 分割）
                    content_lines = segment_content.split('\\n')
                    script_lines = [line for line in content_lines if line.strip() and '***' in line]
                    print(f"      行数: {len(script_lines)}/{len(content_lines)}")
                    
                    # 存储数据（保存原始内容，包含 \\n）
                    script_time_segments[script_name][time_key] = {
                        'start': start_time,
                        'end': end_time,
                        'content': segment_content  # 保存原始内容
                    }
                    
                    print(f"   ✅ 解析时间段: '{time_key}'")
                    parsed_count += 1
            
            print(f"✅ 成功解析 {parsed_count} 个时间段")
            return True, script_time_segments
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            return False, {}
        except Exception as e:
            print(f"❌ 解析异常: {e}")
            return False, {}
    
    # 3. 执行解析
    script_name = "kaer"
    success, script_time_segments = parse_time_segment_json_format(script_name, original_json)
    
    if not success:
        print("❌ 解析失败")
        return False
    
    # 4. 生成显示文本（模拟主程序的显示逻辑）
    def generate_display_text(script_name, script_time_segments):
        """生成显示文本"""
        if script_name in script_time_segments and script_time_segments[script_name]:
            time_segments_count = len(script_time_segments[script_name])
            display_text = f"# 时间段话术：{script_name}\n"
            display_text += f"# 共有 {time_segments_count} 个时间段\n\n"
            display_text += "# 时间段列表：\n"
            
            for segment_name, segment_data in script_time_segments[script_name].items():
                start_time = segment_data.get('start', 0)
                end_time = segment_data.get('end', 0)
                # 使用 \\n 分割统计行数（与独立测试保持一致）
                content_lines = len(segment_data.get('content', '').split('\\n'))
                display_text += f"# - {segment_name} ({start_time}秒-{end_time}秒) - {content_lines}行话术\n"
            
            display_text += "\n# 请在左侧时间段列表中选择具体时间段进行编辑"
            display_text += "\n# 注意：此话术已解析为时间段格式，不显示原始JSON内容"
            
            return display_text
        else:
            return "# 该话术暂无内容"
    
    display_text = generate_display_text(script_name, script_time_segments)
    print(f"\n3. 生成的显示文本:")
    print(display_text)
    
    # 5. 模拟保存流程（模拟主程序的保存逻辑）
    def save_script_time_segments(script_name, script_time_segments):
        """模拟保存时间段数据"""
        try:
            if script_name not in script_time_segments:
                return False
            
            print(f"\n4. 开始保存话术时间段数据: {script_name}")
            
            # 构造新的JSON格式数据：时间段名称 -> 话术内容
            time_segments_data = {}
            
            for segment_name, segment_data in script_time_segments[script_name].items():
                # 按照要求的格式：时间段名称 -> 话术内容
                segment_content = segment_data.get('content', '')
                time_segments_data[segment_name] = segment_content
            
            # 将时间段数据转换为JSON字符串
            upload_data_json = json.dumps(time_segments_data, ensure_ascii=False, indent=2)
            
            print(f"   保存的JSON数据:")
            print(upload_data_json[:300] + "...")
            
            # 验证保存的数据与原始数据一致
            original_data = json.loads(original_json)
            saved_data = json.loads(upload_data_json)
            
            is_identical = original_data == saved_data
            print(f"   数据一致性检查: {'✅ 一致' if is_identical else '❌ 不一致'}")
            
            if not is_identical:
                print("   原始数据键:", list(original_data.keys()))
                print("   保存数据键:", list(saved_data.keys()))
                for key in original_data:
                    if key in saved_data:
                        if original_data[key] != saved_data[key]:
                            print(f"   差异键 {key}:")
                            print(f"     原始: {original_data[key][:50]}...")
                            print(f"     保存: {saved_data[key][:50]}...")
            
            return is_identical
            
        except Exception as e:
            print(f"❌ 保存异常: {e}")
            return False
    
    # 6. 执行保存测试
    save_success = save_script_time_segments(script_name, script_time_segments)
    
    # 7. 测试结果总结
    print(f"\n=== 测试结果总结 ===")
    print(f"✅ JSON解析: {'成功' if success else '失败'}")
    print(f"✅ 显示文本生成: 成功")
    print(f"✅ 数据保存: {'成功' if save_success else '失败'}")
    print(f"✅ 数据一致性: {'保持一致' if save_success else '存在差异'}")
    
    if success and save_success:
        print(f"\n🎉 完整流程测试通过！")
        print(f"   - 解析正确识别新格式")
        print(f"   - 时间段信息提取准确")
        print(f"   - 话术行数统计正确")
        print(f"   - 显示文本格式正确")
        print(f"   - 保存数据与原始数据一致")
        return True
    else:
        print(f"\n❌ 完整流程测试失败")
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    # 测试空内容
    empty_json = '{"0秒 - 10秒": ""}'
    print(f"1. 测试空内容: {empty_json}")
    
    try:
        data = json.loads(empty_json)
        content_lines = len(data["0秒 - 10秒"].split('\\n'))
        print(f"   空内容行数: {content_lines}")
    except Exception as e:
        print(f"   空内容测试失败: {e}")
    
    # 测试单行内容
    single_line_json = '{"0秒 - 10秒": "1***单行话术"}'
    print(f"2. 测试单行内容: {single_line_json}")
    
    try:
        data = json.loads(single_line_json)
        content_lines = len(data["0秒 - 10秒"].split('\\n'))
        print(f"   单行内容行数: {content_lines}")
    except Exception as e:
        print(f"   单行内容测试失败: {e}")
    
    # 测试多行内容
    multi_line_json = '{"0秒 - 10秒": "1***第一行\\n2***第二行\\n3***第三行"}'
    print(f"3. 测试多行内容: {multi_line_json}")
    
    try:
        data = json.loads(multi_line_json)
        content_lines = len(data["0秒 - 10秒"].split('\\n'))
        print(f"   多行内容行数: {content_lines}")
    except Exception as e:
        print(f"   多行内容测试失败: {e}")

def main():
    """主测试函数"""
    print("开始测试完整的解析和保存流程...")
    
    try:
        # 测试完整流程
        success = test_complete_workflow()
        
        # 测试边界情况
        test_edge_cases()
        
        print(f"\n=== 最终结论 ===")
        if success:
            print("✅ 解析和保存逻辑完全正确")
            print("✅ 数据格式保持一致")
            print("✅ 可以安全地同步到主程序")
            
            print(f"\n📋 主程序修复要点:")
            print("1. ✅ 解析时保存原始内容（包含 \\n）")
            print("2. ✅ 显示时使用 \\n 分割统计行数")
            print("3. ✅ 保存时使用原始内容")
            print("4. ✅ 确保缓存逻辑正确")
            
            print(f"\n🔧 可能的问题:")
            print("- 缓存逻辑可能干扰显示")
            print("- 解析函数可能没有被正确调用")
            print("- 数据结构初始化可能有问题")
            
        else:
            print("❌ 解析和保存逻辑存在问题")
            print("❌ 需要进一步调试")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    main()
