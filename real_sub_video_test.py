#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实副视频测试程序
直接使用主程序的方法和数据进行副视频功能测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QTextEdit, QLabel, 
                             QLineEdit, QGroupBox, QMessageBox, QComboBox,
                             QTableWidget, QTableWidgetItem, QTabWidget)
from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QFont

class RealSubVideoTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("真实副视频功能测试程序")
        self.setGeometry(100, 100, 1000, 700)

        # 初始化主程序实例
        self.main_window = None

        # 先初始化界面，再初始化主程序
        self.init_ui()
        self.init_main_window()
        
    def init_main_window(self):
        """初始化主程序实例"""
        try:
            from run_gui_qt5 import MainWindow
            
            # 创建主程序实例
            user_info = {
                'username': 'test_user',
                'user_id': 1,
                'expire_time': '2025-12-31'
            }
            self.main_window = MainWindow(user_info)
            
            # 不显示主窗口，只使用其功能
            self.log("✅ 主程序实例创建成功")
            
            # 等待初始化完成
            QTimer.singleShot(3000, self.check_main_window_ready)
            
        except Exception as e:
            self.log(f"❌ 主程序实例创建失败: {e}")
            import traceback
            traceback.print_exc()
    
    def check_main_window_ready(self):
        """检查主程序是否准备就绪"""
        if self.main_window:
            self.log("✅ 主程序初始化完成，可以开始测试")
            self.refresh_all_data()
        else:
            self.log("❌ 主程序未准备就绪")
    
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("真实副视频功能测试程序")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title_label)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # OBS连接标签页
        obs_tab = self.create_obs_tab()
        tab_widget.addTab(obs_tab, "OBS连接")
        
        # 副视频测试标签页
        test_tab = self.create_test_tab()
        tab_widget.addTab(test_tab, "副视频测试")
        
        # 播放列表标签页
        playlist_tab = self.create_playlist_tab()
        tab_widget.addTab(playlist_tab, "播放列表")
        
        layout.addWidget(tab_widget)
        
        # 日志区域
        log_group = QGroupBox("测试日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        clear_log_btn = QPushButton("清空日志")
        clear_log_btn.clicked.connect(self.clear_log)
        log_layout.addWidget(clear_log_btn)
        
        layout.addWidget(log_group)
        
        self.log("🎬 真实副视频测试程序启动")
        
    def create_obs_tab(self):
        """创建OBS连接标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # OBS连接状态
        status_group = QGroupBox("OBS连接状态")
        status_layout = QVBoxLayout(status_group)
        
        self.obs_status_text = QTextEdit()
        self.obs_status_text.setMaximumHeight(100)
        self.obs_status_text.setReadOnly(True)
        status_layout.addWidget(self.obs_status_text)
        
        # 连接控制按钮
        control_layout = QHBoxLayout()
        
        connect_btn = QPushButton("连接OBS")
        connect_btn.clicked.connect(self.connect_obs)
        control_layout.addWidget(connect_btn)
        
        disconnect_btn = QPushButton("断开OBS")
        disconnect_btn.clicked.connect(self.disconnect_obs)
        control_layout.addWidget(disconnect_btn)
        
        check_status_btn = QPushButton("检查状态")
        check_status_btn.clicked.connect(self.check_obs_status)
        control_layout.addWidget(check_status_btn)
        
        status_layout.addLayout(control_layout)
        layout.addWidget(status_group)
        
        # 视频源列表
        sources_group = QGroupBox("OBS视频源")
        sources_layout = QVBoxLayout(sources_group)
        
        self.sources_text = QTextEdit()
        self.sources_text.setMaximumHeight(150)
        self.sources_text.setReadOnly(True)
        sources_layout.addWidget(self.sources_text)
        
        refresh_sources_btn = QPushButton("刷新视频源")
        refresh_sources_btn.clicked.connect(self.refresh_sources)
        sources_layout.addWidget(refresh_sources_btn)
        
        layout.addWidget(sources_group)
        
        # 视频源切换测试
        switch_group = QGroupBox("视频源切换测试")
        switch_layout = QVBoxLayout(switch_group)
        
        switch_control_layout = QHBoxLayout()
        switch_control_layout.addWidget(QLabel("选择视频源:"))
        self.source_combo = QComboBox()
        switch_control_layout.addWidget(self.source_combo)
        
        switch_to_btn = QPushButton("切换到副视频")
        switch_to_btn.clicked.connect(self.switch_to_sub_video)
        switch_control_layout.addWidget(switch_to_btn)
        
        switch_back_btn = QPushButton("切换回主视频")
        switch_back_btn.clicked.connect(self.switch_back_to_main)
        switch_control_layout.addWidget(switch_back_btn)
        
        switch_layout.addLayout(switch_control_layout)
        layout.addWidget(switch_group)
        
        return tab
    
    def create_test_tab(self):
        """创建副视频测试标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 副视频配置显示
        config_group = QGroupBox("副视频配置")
        config_layout = QVBoxLayout(config_group)
        
        self.config_text = QTextEdit()
        self.config_text.setMaximumHeight(100)
        self.config_text.setReadOnly(True)
        config_layout.addWidget(self.config_text)
        
        refresh_config_btn = QPushButton("刷新配置")
        refresh_config_btn.clicked.connect(self.refresh_config)
        config_layout.addWidget(refresh_config_btn)
        
        layout.addWidget(config_group)
        
        # 弹幕测试
        test_group = QGroupBox("弹幕副视频测试")
        test_layout = QVBoxLayout(test_group)
        
        # 弹幕输入
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("测试弹幕:"))
        self.danmaku_input = QLineEdit()
        self.danmaku_input.setPlaceholderText("输入包含关键词的弹幕")
        input_layout.addWidget(self.danmaku_input)
        
        test_trigger_btn = QPushButton("测试触发")
        test_trigger_btn.clicked.connect(self.test_sub_video_trigger)
        input_layout.addWidget(test_trigger_btn)
        
        add_danmaku_btn = QPushButton("添加弹幕话术")
        add_danmaku_btn.clicked.connect(self.add_danmaku_to_playlist)
        input_layout.addWidget(add_danmaku_btn)
        
        test_layout.addLayout(input_layout)
        
        # 快速测试按钮
        quick_layout = QHBoxLayout()
        
        fire_btn = QPushButton("火箭弹幕")
        fire_btn.clicked.connect(lambda: self.quick_test("感谢老板的火箭，太给力了！"))
        quick_layout.addWidget(fire_btn)
        
        gift_btn = QPushButton("礼物弹幕")
        gift_btn.clicked.connect(lambda: self.quick_test("谢谢大家的礼物支持"))
        quick_layout.addWidget(gift_btn)
        
        six_btn = QPushButton("666弹幕")
        six_btn.clicked.connect(lambda: self.quick_test("主播666，继续加油！"))
        quick_layout.addWidget(six_btn)
        
        normal_btn = QPushButton("普通弹幕")
        normal_btn.clicked.connect(lambda: self.quick_test("普通弹幕，没有关键词"))
        quick_layout.addWidget(normal_btn)
        
        test_layout.addLayout(quick_layout)
        
        # 测试结果
        result_group = QGroupBox("测试结果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(150)
        self.result_text.setReadOnly(True)
        result_layout.addWidget(self.result_text)
        
        test_layout.addWidget(result_group)
        layout.addWidget(test_group)
        
        return tab
    
    def create_playlist_tab(self):
        """创建播放列表标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 播放列表表格
        playlist_group = QGroupBox("播放列表")
        playlist_layout = QVBoxLayout(playlist_group)
        
        self.playlist_table = QTableWidget()
        self.playlist_table.setColumnCount(5)
        self.playlist_table.setHorizontalHeaderLabels(["ID", "类型", "内容", "副视频", "状态"])
        playlist_layout.addWidget(self.playlist_table)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        refresh_playlist_btn = QPushButton("刷新播放列表")
        refresh_playlist_btn.clicked.connect(self.refresh_playlist)
        control_layout.addWidget(refresh_playlist_btn)
        
        play_selected_btn = QPushButton("播放选中项目")
        play_selected_btn.clicked.connect(self.play_selected_item)
        control_layout.addWidget(play_selected_btn)
        
        test_sub_video_btn = QPushButton("测试副视频播放")
        test_sub_video_btn.clicked.connect(self.test_sub_video_playback)
        control_layout.addWidget(test_sub_video_btn)
        
        clear_playlist_btn = QPushButton("清空播放列表")
        clear_playlist_btn.clicked.connect(self.clear_playlist)
        control_layout.addWidget(clear_playlist_btn)
        
        playlist_layout.addLayout(control_layout)
        layout.addWidget(playlist_group)
        
        return tab

    def connect_obs(self):
        """连接OBS - 使用主程序的方法"""
        if not self.main_window:
            self.log("❌ 主程序未初始化")
            return

        self.log("🔗 使用主程序方法连接OBS...")

        try:
            # 使用主程序的OBS连接方法
            obs_controller = None
            if hasattr(self.main_window, 'playback_controller') and self.main_window.playback_controller:
                obs_controller = self.main_window.playback_controller.obs_controller
            elif hasattr(self.main_window, 'obs_controller'):
                obs_controller = self.main_window.obs_controller

            if obs_controller:
                # 设置连接参数
                obs_controller.host = "127.0.0.1"
                obs_controller.port = 4455

                # 连接
                if hasattr(obs_controller, 'connect'):
                    result = obs_controller.connect()
                    if result:
                        self.log("✅ OBS连接成功")
                        QTimer.singleShot(1000, self.check_obs_status)
                        QTimer.singleShot(2000, self.refresh_sources)
                    else:
                        self.log("❌ OBS连接失败")
                else:
                    self.log("❌ OBS控制器没有connect方法")
            else:
                self.log("❌ 无法获取OBS控制器")

        except Exception as e:
            self.log(f"❌ OBS连接异常: {e}")
            import traceback
            traceback.print_exc()

    def disconnect_obs(self):
        """断开OBS连接"""
        if not self.main_window:
            return

        self.log("🔗 断开OBS连接...")

        try:
            obs_controller = None
            if hasattr(self.main_window, 'playback_controller') and self.main_window.playback_controller:
                obs_controller = self.main_window.playback_controller.obs_controller
            elif hasattr(self.main_window, 'obs_controller'):
                obs_controller = self.main_window.obs_controller

            if obs_controller and hasattr(obs_controller, 'disconnect'):
                obs_controller.disconnect()
                self.log("✅ OBS已断开连接")

            self.check_obs_status()

        except Exception as e:
            self.log(f"❌ 断开连接异常: {e}")

    def check_obs_status(self):
        """检查OBS连接状态"""
        if not self.main_window:
            self.obs_status_text.setText("❌ 主程序未初始化")
            return

        try:
            obs_controllers = []

            if hasattr(self.main_window, 'obs_controller'):
                obs_controllers.append(('主OBS控制器', self.main_window.obs_controller))

            if hasattr(self.main_window, 'playback_controller') and self.main_window.playback_controller:
                obs_controllers.append(('播放控制器OBS', self.main_window.playback_controller.obs_controller))

            status_text = f"OBS控制器状态 ({len(obs_controllers)} 个):\n"

            for name, obs_controller in obs_controllers:
                if obs_controller:
                    connected = getattr(obs_controller, 'connected', False)
                    host = getattr(obs_controller, 'host', '未知')
                    port = getattr(obs_controller, 'port', '未知')

                    status_text += f"• {name}:\n"
                    status_text += f"  连接状态: {'已连接' if connected else '未连接'}\n"
                    status_text += f"  地址: {host}:{port}\n"
                else:
                    status_text += f"• {name}: 不存在\n"

            self.obs_status_text.setText(status_text)

        except Exception as e:
            self.obs_status_text.setText(f"❌ 检查状态失败: {e}")

    def refresh_sources(self):
        """刷新视频源 - 使用主程序的方法"""
        if not self.main_window:
            self.log("❌ 主程序未初始化")
            return

        self.log("🔄 使用主程序方法刷新视频源...")

        try:
            obs_controller = None
            if hasattr(self.main_window, 'playback_controller') and self.main_window.playback_controller:
                obs_controller = self.main_window.playback_controller.obs_controller
            elif hasattr(self.main_window, 'obs_controller'):
                obs_controller = self.main_window.obs_controller

            if obs_controller and hasattr(obs_controller, 'connected') and obs_controller.connected:
                if hasattr(obs_controller, 'get_source_list'):
                    sources = obs_controller.get_source_list()
                    if sources:
                        self.log(f"✅ 获取到 {len(sources)} 个视频源")

                        # 更新显示
                        sources_text = f"OBS视频源列表 ({len(sources)} 个):\n"
                        for i, source in enumerate(sources, 1):
                            sources_text += f"{i}. {source}\n"
                        self.sources_text.setText(sources_text)

                        # 更新下拉框
                        self.source_combo.clear()
                        self.source_combo.addItems(sources)

                    else:
                        self.log("⚠️ 没有获取到视频源")
                        self.sources_text.setText("没有可用的视频源")
                else:
                    self.log("❌ OBS控制器没有get_source_list方法")
            else:
                self.log("❌ OBS未连接，无法获取视频源")
                self.sources_text.setText("OBS未连接")

        except Exception as e:
            self.log(f"❌ 获取视频源失败: {e}")
            self.sources_text.setText(f"获取失败: {e}")

    def switch_to_sub_video(self):
        """切换到副视频 - 使用主程序的方法"""
        selected_source = self.source_combo.currentText()
        if not selected_source:
            QMessageBox.warning(self, "警告", "请选择要切换的视频源")
            return

        if not self.main_window:
            self.log("❌ 主程序未初始化")
            return

        self.log(f"🔄 使用主程序方法切换到副视频: {selected_source}")

        try:
            # 使用主程序的副视频切换方法
            if hasattr(self.main_window, 'switch_to_sub_video_with_obs'):
                result = self.main_window.switch_to_sub_video_with_obs(selected_source)
                if result:
                    self.log(f"✅ 成功切换到副视频: {selected_source}")
                else:
                    self.log(f"❌ 切换到副视频失败: {selected_source}")
            else:
                self.log("❌ 主程序没有switch_to_sub_video_with_obs方法")

        except Exception as e:
            self.log(f"❌ 切换副视频异常: {e}")
            import traceback
            traceback.print_exc()

    def switch_back_to_main(self):
        """切换回主视频 - 使用主程序的方法"""
        if not self.main_window:
            self.log("❌ 主程序未初始化")
            return

        self.log("🔄 使用主程序方法切换回主视频")

        try:
            # 使用主程序的主视频切换方法
            if hasattr(self.main_window, 'switch_back_to_main_video_with_obs'):
                result = self.main_window.switch_back_to_main_video_with_obs()
                if result:
                    self.log("✅ 成功切换回主视频")
                else:
                    self.log("❌ 切换回主视频失败")
            else:
                self.log("❌ 主程序没有switch_back_to_main_video_with_obs方法")

        except Exception as e:
            self.log(f"❌ 切换主视频异常: {e}")
            import traceback
            traceback.print_exc()

    def refresh_config(self):
        """刷新副视频配置"""
        if not self.main_window:
            self.config_text.setText("❌ 主程序未初始化")
            return

        try:
            if hasattr(self.main_window, 'sub_video_manager'):
                sub_video_manager = self.main_window.sub_video_manager
                if hasattr(sub_video_manager, 'sub_videos'):
                    sub_videos = sub_video_manager.sub_videos

                    if isinstance(sub_videos, dict):
                        config_text = f"副视频配置 ({len(sub_videos)} 个关键词):\n"
                        for keyword, config in sub_videos.items():
                            if hasattr(config, 'video_source'):
                                # SubVideoItem对象
                                video_source = config.video_source
                                scripts_count = len(config.scripts) if hasattr(config, 'scripts') else 0
                            elif isinstance(config, dict):
                                # 字典格式
                                video_source = config.get('video_source', '未知')
                                scripts_count = len(config.get('scripts', []))
                            else:
                                video_source = '未知格式'
                                scripts_count = 0

                            config_text += f"• {keyword} → {video_source} ({scripts_count}个话术)\n"

                        self.config_text.setText(config_text)
                        self.log(f"✅ 副视频配置已刷新: {len(sub_videos)} 个关键词")
                    else:
                        self.config_text.setText(f"⚠️ 副视频数据格式异常: {type(sub_videos)}")
                else:
                    self.config_text.setText("❌ 副视频管理器没有数据")
            else:
                self.config_text.setText("❌ 无法访问副视频管理器")
        except Exception as e:
            self.config_text.setText(f"❌ 加载配置失败: {e}")
            self.log(f"❌ 刷新配置失败: {e}")

    def test_sub_video_trigger(self):
        """测试副视频触发 - 使用主程序的方法"""
        danmaku_text = self.danmaku_input.text().strip()
        if not danmaku_text:
            QMessageBox.warning(self, "警告", "请输入测试弹幕")
            return

        if not self.main_window:
            self.log("❌ 主程序未初始化")
            return

        self.log(f"🎬 使用主程序方法测试弹幕: {danmaku_text}")
        self.result_text.append(f"\n🎬 测试弹幕: {danmaku_text}")

        try:
            # 使用主程序的副视频触发检测方法
            if hasattr(self.main_window, 'check_sub_video_trigger'):
                result = self.main_window.check_sub_video_trigger(danmaku_text)
                if result:
                    self.log(f"✅ 触发副视频: {result}")
                    self.result_text.append(f"✅ 触发副视频: {result}")
                else:
                    self.log(f"⚪ 无副视频触发")
                    self.result_text.append(f"⚪ 无副视频触发")
            else:
                self.log("❌ 主程序没有check_sub_video_trigger方法")
                self.result_text.append("❌ 主程序没有check_sub_video_trigger方法")

        except Exception as e:
            self.log(f"❌ 测试失败: {e}")
            self.result_text.append(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

    def add_danmaku_to_playlist(self):
        """添加弹幕到播放列表 - 使用主程序的方法"""
        danmaku_text = self.danmaku_input.text().strip()
        if not danmaku_text:
            QMessageBox.warning(self, "警告", "请输入测试弹幕")
            return

        if not self.main_window:
            self.log("❌ 主程序未初始化")
            return

        self.log(f"🎯 使用主程序方法添加弹幕到播放列表: {danmaku_text}")
        self.result_text.append(f"\n🎯 添加弹幕到播放列表: {danmaku_text}")

        try:
            # 先检测副视频
            sub_video_result = None
            if hasattr(self.main_window, 'check_sub_video_trigger'):
                sub_video_result = self.main_window.check_sub_video_trigger(danmaku_text)
                self.log(f"🔍 副视频检测: {sub_video_result if sub_video_result else '无'}")
                self.result_text.append(f"🔍 副视频检测: {sub_video_result if sub_video_result else '无'}")

            # 使用主程序的添加弹幕方法
            if hasattr(self.main_window, 'add_danmaku_to_playlist'):
                self.main_window.add_danmaku_to_playlist(danmaku_text, sub_video_result)
                self.log(f"✅ 弹幕已添加到播放列表")
                self.result_text.append(f"✅ 弹幕已添加到播放列表")

                # 刷新播放列表显示
                QTimer.singleShot(1000, self.refresh_playlist)
            else:
                self.log("❌ 主程序没有add_danmaku_to_playlist方法")
                self.result_text.append("❌ 主程序没有add_danmaku_to_playlist方法")

        except Exception as e:
            self.log(f"❌ 添加弹幕失败: {e}")
            self.result_text.append(f"❌ 添加弹幕失败: {e}")
            import traceback
            traceback.print_exc()

    def quick_test(self, text):
        """快速测试"""
        self.danmaku_input.setText(text)
        self.test_sub_video_trigger()

    def refresh_playlist(self):
        """刷新播放列表 - 使用主程序的数据"""
        if not self.main_window:
            self.playlist_table.setRowCount(0)
            return

        try:
            if hasattr(self.main_window, 'playlist_items'):
                playlist_items = self.main_window.playlist_items
                self.playlist_table.setRowCount(len(playlist_items))

                for i, item in enumerate(playlist_items):
                    item_id = str(item.get('id', i+1))
                    voice_type = item.get('voice_type', '未知')
                    content = item.get('content', '')[:30] + ('...' if len(item.get('content', '')) > 30 else '')
                    sub_video = item.get('sub_video', '无')
                    status = item.get('status', '未知')

                    self.playlist_table.setItem(i, 0, QTableWidgetItem(item_id))
                    self.playlist_table.setItem(i, 1, QTableWidgetItem(voice_type))
                    self.playlist_table.setItem(i, 2, QTableWidgetItem(content))
                    self.playlist_table.setItem(i, 3, QTableWidgetItem(sub_video))
                    self.playlist_table.setItem(i, 4, QTableWidgetItem(status))

                self.log(f"📋 播放列表已刷新: {len(playlist_items)} 个项目")
            else:
                self.playlist_table.setRowCount(0)
                self.log("❌ 主程序没有playlist_items属性")

        except Exception as e:
            self.log(f"❌ 刷新播放列表失败: {e}")

    def play_selected_item(self):
        """播放选中项目 - 使用主程序的方法"""
        current_row = self.playlist_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要播放的项目")
            return

        if not self.main_window or not hasattr(self.main_window, 'playlist_items'):
            self.log("❌ 主程序或播放列表不可用")
            return

        if current_row >= len(self.main_window.playlist_items):
            self.log("❌ 选中的项目索引超出范围")
            return

        item = self.main_window.playlist_items[current_row]
        content = item.get('content', '')
        sub_video = item.get('sub_video', '无')

        self.log(f"🎬 播放项目: {content[:30]}...")
        self.log(f"🎬 副视频: {sub_video}")

        # 这里可以调用主程序的播放方法
        # 由于播放涉及音频，这里只是演示副视频流程
        if sub_video and sub_video != '无':
            self.log(f"🎬 该项目包含副视频，将触发副视频播放流程")
        else:
            self.log(f"⚪ 该项目无副视频")

    def test_sub_video_playback(self):
        """测试副视频播放 - 使用主程序的方法"""
        current_row = self.playlist_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要测试的项目")
            return

        if not self.main_window or not hasattr(self.main_window, 'playlist_items'):
            self.log("❌ 主程序或播放列表不可用")
            return

        if current_row >= len(self.main_window.playlist_items):
            self.log("❌ 选中的项目索引超出范围")
            return

        item = self.main_window.playlist_items[current_row]
        sub_video = item.get('sub_video', '无')

        if sub_video and sub_video != '无':
            self.log(f"🎬 测试副视频播放流程: {sub_video}")

            try:
                # 使用主程序的副视频播放方法
                if hasattr(self.main_window, 'handle_sub_video_playback'):
                    result = self.main_window.handle_sub_video_playback(item)
                    if result:
                        self.log(f"✅ 副视频播放流程启动成功")
                    else:
                        self.log(f"❌ 副视频播放流程启动失败")
                else:
                    self.log("❌ 主程序没有handle_sub_video_playback方法")

            except Exception as e:
                self.log(f"❌ 副视频播放测试异常: {e}")
                import traceback
                traceback.print_exc()
        else:
            QMessageBox.information(self, "提示", "选中的项目没有副视频")

    def clear_playlist(self):
        """清空播放列表"""
        if self.main_window and hasattr(self.main_window, 'playlist_items'):
            self.main_window.playlist_items.clear()
            self.refresh_playlist()
            self.log(f"🗑️ 播放列表已清空")

    def refresh_all_data(self):
        """刷新所有数据"""
        self.check_obs_status()
        self.refresh_config()
        self.refresh_playlist()

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.result_text.clear()

    def log(self, message):
        """记录日志"""
        self.log_text.append(message)
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
        print(message)  # 同时输出到控制台

def main():
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName("真实副视频测试程序")
    app.setApplicationVersion("1.0")

    window = RealSubVideoTest()
    window.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
