"""
AI Broadcaster v2 - AI主播面板
AI主播选择和语音生成控制
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QSlider, QSpinBox, QTextEdit, QGroupBox,
    QProgressBar, QMessageBox, QListWidget, QListWidgetItem,
    QSplitter, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QFont

from ...services.logging_service import create_logger


class VoiceGenerationWorker(QThread):
    """语音生成工作线程"""
    generation_progress = pyqtSignal(int)  # 进度
    generation_result = pyqtSignal(bool, str, str)  # 成功, 消息, 文件路径

    def __init__(self, voice_manager, text, speaker_id, speed):
        super().__init__()
        self.voice_manager = voice_manager
        self.text = text
        self.speaker_id = speaker_id
        self.speed = speed

    def run(self):
        try:
            self.generation_progress.emit(25)

            # 生成语音（使用模拟模式）
            voice_file = self.voice_manager.generate_voice_mock(
                self.text, self.speaker_id, self.speed
            )

            self.generation_progress.emit(75)

            if voice_file:
                self.generation_progress.emit(100)
                self.generation_result.emit(True, "语音生成成功", voice_file)
            else:
                self.generation_result.emit(False, "语音生成失败", "")

        except Exception as e:
            self.generation_result.emit(False, f"生成异常: {str(e)}", "")


class AISpeakerPanel(QWidget):
    """AI主播面板"""

    def __init__(self, voice_manager):
        super().__init__()
        self.logger = create_logger("ai_speaker_panel")
        self.voice_manager = voice_manager
        self.generation_worker = None

        self.init_ui()
        self.setup_connections()
        self.load_speakers()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)

        # 上半部分：主播选择和设置
        self.create_speaker_settings(splitter)

        # 下半部分：语音生成和历史
        self.create_voice_generation(splitter)

        # 设置分割器比例
        splitter.setSizes([300, 400])

    def create_speaker_settings(self, parent):
        """创建主播设置区域"""
        settings_widget = QWidget()
        settings_layout = QVBoxLayout(settings_widget)

        # 主播选择组
        speaker_group = QGroupBox("主播选择")
        speaker_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        speaker_layout = QVBoxLayout(speaker_group)

        # 主播下拉框
        speaker_select_layout = QHBoxLayout()
        speaker_select_layout.addWidget(QLabel("当前主播:"))

        self.speaker_combo = QComboBox()
        self.speaker_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                min-width: 200px;
            }
        """)
        speaker_select_layout.addWidget(self.speaker_combo)
        speaker_select_layout.addStretch()

        speaker_layout.addLayout(speaker_select_layout)

        # 主播信息显示
        self.speaker_info_label = QLabel("请选择主播")
        self.speaker_info_label.setStyleSheet("color: #6c757d; font-style: italic;")
        speaker_layout.addWidget(self.speaker_info_label)

        settings_layout.addWidget(speaker_group)

        # 语音参数组
        params_group = QGroupBox("语音参数")
        params_group.setStyleSheet(speaker_group.styleSheet())
        params_layout = QVBoxLayout(params_group)

        # 语速设置
        speed_layout = QHBoxLayout()
        speed_layout.addWidget(QLabel("语速:"))

        self.speed_slider = QSlider(Qt.Horizontal)
        self.speed_slider.setRange(50, 200)  # 0.5x 到 2.0x
        self.speed_slider.setValue(100)  # 默认1.0x
        self.speed_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #dee2e6;
                height: 8px;
                background: #f8f9fa;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #007bff;
                border: 1px solid #007bff;
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
        """)
        speed_layout.addWidget(self.speed_slider)

        self.speed_label = QLabel("1.0x")
        self.speed_label.setMinimumWidth(40)
        speed_layout.addWidget(self.speed_label)

        params_layout.addLayout(speed_layout)

        # 预生成数量
        prepare_layout = QHBoxLayout()
        prepare_layout.addWidget(QLabel("预生成数量:"))

        self.prepare_count_spin = QSpinBox()
        self.prepare_count_spin.setRange(1, 20)
        self.prepare_count_spin.setValue(5)
        self.prepare_count_spin.setStyleSheet("""
            QSpinBox {
                padding: 5px;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                min-width: 60px;
            }
        """)
        prepare_layout.addWidget(self.prepare_count_spin)
        prepare_layout.addStretch()

        params_layout.addLayout(prepare_layout)

        settings_layout.addWidget(params_group)

        # 添加弹性空间
        settings_layout.addStretch()

        parent.addWidget(settings_widget)

    def create_voice_generation(self, parent):
        """创建语音生成区域"""
        generation_widget = QWidget()
        generation_layout = QVBoxLayout(generation_widget)

        # 语音生成组
        generation_group = QGroupBox("语音生成")
        generation_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        gen_layout = QVBoxLayout(generation_group)

        # 文本输入
        gen_layout.addWidget(QLabel("输入文本:"))
        self.text_input = QTextEdit()
        self.text_input.setMaximumHeight(100)
        self.text_input.setPlaceholderText("请输入要转换为语音的文本...")
        self.text_input.setStyleSheet("""
            QTextEdit {
                border: 1px solid #dee2e6;
                border-radius: 3px;
                padding: 5px;
                font-size: 12px;
            }
        """)
        gen_layout.addWidget(self.text_input)

        # 生成按钮和进度条
        button_layout = QHBoxLayout()

        self.generate_button = QPushButton("生成语音")
        self.generate_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)
        button_layout.addWidget(self.generate_button)

        self.test_button = QPushButton("测试播放")
        self.test_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)
        self.test_button.setEnabled(False)
        button_layout.addWidget(self.test_button)

        button_layout.addStretch()
        gen_layout.addLayout(button_layout)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #dee2e6;
                border-radius: 3px;
                text-align: center;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #007bff;
                border-radius: 2px;
            }
        """)
        gen_layout.addWidget(self.progress_bar)

        generation_layout.addWidget(generation_group)

        # 语音历史组
        history_group = QGroupBox("语音历史")
        history_group.setStyleSheet(generation_group.styleSheet())
        history_layout = QVBoxLayout(history_group)

        self.history_list = QListWidget()
        self.history_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #dee2e6;
                border-radius: 3px;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QListWidget::item {
                padding: 5px;
                border-bottom: 1px solid #f1f3f4;
            }
            QListWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        history_layout.addWidget(self.history_list)

        # 历史操作按钮
        history_button_layout = QHBoxLayout()

        self.refresh_history_button = QPushButton("刷新")
        self.refresh_history_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        history_button_layout.addWidget(self.refresh_history_button)

        self.clear_history_button = QPushButton("清空")
        self.clear_history_button.setStyleSheet(self.refresh_history_button.styleSheet())
        history_button_layout.addWidget(self.clear_history_button)

        history_button_layout.addStretch()
        history_layout.addLayout(history_button_layout)

        generation_layout.addWidget(history_group)

        parent.addWidget(generation_widget)

    def setup_connections(self):
        """设置信号连接"""
        # 主播选择变化
        self.speaker_combo.currentIndexChanged.connect(self.on_speaker_changed)

        # 语速滑块变化
        self.speed_slider.valueChanged.connect(self.on_speed_changed)

        # 按钮点击事件
        self.generate_button.clicked.connect(self.on_generate_clicked)
        self.test_button.clicked.connect(self.on_test_clicked)
        self.refresh_history_button.clicked.connect(self.refresh_history)
        self.clear_history_button.clicked.connect(self.clear_history)

    def load_speakers(self):
        """加载主播列表"""
        try:
            # 通过语音管理器获取主播管理器
            from ...core.voice.speaker_manager import SpeakerManager
            speaker_manager = SpeakerManager(self.voice_manager.db_manager)
            speakers = speaker_manager.get_all_speakers()

            self.speaker_combo.clear()

            for speaker in speakers:
                self.speaker_combo.addItem(
                    f"{speaker['name']} - {speaker['description']}",
                    speaker['id']
                )

            # 设置当前主播
            current_speaker = self.voice_manager.get_current_speaker()
            if current_speaker:
                for i in range(self.speaker_combo.count()):
                    if self.speaker_combo.itemData(i) == current_speaker['id']:
                        self.speaker_combo.setCurrentIndex(i)
                        break

            self.logger.info(f"加载了 {len(speakers)} 个主播")

        except Exception as e:
            self.logger.error(f"加载主播失败: {e}")
            QMessageBox.warning(self, "错误", f"加载主播失败: {str(e)}")

    @pyqtSlot(int)
    def on_speaker_changed(self, index):
        """主播选择变化事件"""
        if index >= 0:
            speaker_id = self.speaker_combo.itemData(index)
            if speaker_id:
                # 设置当前主播
                success = self.voice_manager.set_current_speaker(speaker_id)
                if success:
                    speaker = self.voice_manager.get_current_speaker()
                    if speaker:
                        info_text = f"语音ID: {speaker['voice_id']}, 语速范围: {speaker['speed_min']:.1f}x - {speaker['speed_max']:.1f}x"
                        self.speaker_info_label.setText(info_text)
                        self.logger.info(f"切换主播: {speaker['name']}")

    @pyqtSlot(int)
    def on_speed_changed(self, value):
        """语速变化事件"""
        speed = value / 100.0  # 转换为实际语速
        self.speed_label.setText(f"{speed:.1f}x")

    @pyqtSlot()
    def on_generate_clicked(self):
        """生成语音按钮点击事件"""
        text = self.text_input.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "提示", "请输入要转换的文本")
            return

        speaker_id = self.speaker_combo.currentData()
        if not speaker_id:
            QMessageBox.warning(self, "提示", "请选择主播")
            return

        speed = self.speed_slider.value() / 100.0

        # 禁用生成按钮
        self.generate_button.setEnabled(False)
        self.generate_button.setText("生成中...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 启动生成线程
        self.generation_worker = VoiceGenerationWorker(
            self.voice_manager, text, speaker_id, speed
        )
        self.generation_worker.generation_progress.connect(self.on_generation_progress)
        self.generation_worker.generation_result.connect(self.on_generation_result)
        self.generation_worker.start()

    @pyqtSlot()
    def on_test_clicked(self):
        """测试播放按钮点击事件"""
        QMessageBox.information(self, "提示", "测试播放功能待实现")

    @pyqtSlot(int)
    def on_generation_progress(self, value):
        """生成进度更新"""
        self.progress_bar.setValue(value)

    @pyqtSlot(bool, str, str)
    def on_generation_result(self, success, message, file_path):
        """生成结果处理"""
        # 恢复生成按钮
        self.generate_button.setEnabled(True)
        self.generate_button.setText("生成语音")
        self.progress_bar.setVisible(False)

        if success:
            QMessageBox.information(self, "成功", message)
            self.test_button.setEnabled(True)
            self.refresh_history()
            self.logger.info(f"语音生成成功: {file_path}")
        else:
            QMessageBox.warning(self, "失败", message)
            self.logger.error(f"语音生成失败: {message}")

    @pyqtSlot()
    def refresh_history(self):
        """刷新历史记录"""
        try:
            history = self.voice_manager.get_play_history(20)
            self.history_list.clear()

            for record in history:
                item_text = f"{record['content'][:50]}... ({record['voice_type']})"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, record)
                self.history_list.addItem(item)

            self.logger.debug(f"刷新历史记录: {len(history)} 条")

        except Exception as e:
            self.logger.error(f"刷新历史记录失败: {e}")

    @pyqtSlot()
    def clear_history(self):
        """清空历史记录"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空历史记录吗？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.history_list.clear()
            self.logger.info("历史记录已清空")
