"""
AI Broadcaster v2 - 主应用程序类
应用程序核心逻辑和生命周期管理
"""

import asyncio
import signal
import sys
from typing import Dict, Any, Optional
from datetime import datetime

from ..services.logging_service import create_logger
from ..services.error_handler import handle_exceptions, get_error_handler
from ..data.config_manager import ConfigManager
from ..data.file_manager import FileManager
from .event_bus import EventBus


class AIBroadcasterApp:
    """AI广播器主应用程序类"""
    
    def __init__(self, config_path: str = "config/app_config.json"):
        """
        初始化应用程序
        
        Args:
            config_path: 配置文件路径
        """
        self.logger = create_logger("ai_broadcaster_app")
        self.config_path = config_path
        
        # 核心组件
        self.config_manager = ConfigManager()
        self.file_manager = FileManager()
        self.event_bus = EventBus()
        self.error_handler = get_error_handler()
        
        # 应用状态
        self.is_running = False
        self.is_broadcasting = False
        self.is_gui_mode = False
        
        # 组件状态
        self.components = {
            'audio_processor': None,
            'ai_engine': None,
            'content_generator': None,
            'voice_synthesizer': None,
            'ui_manager': None
        }
        
        # 统计信息
        self.stats = {
            'start_time': None,
            'broadcasts_count': 0,
            'errors_count': 0,
            'uptime_seconds': 0
        }
        
        self.logger.log_startup("AI广播器应用程序")
    
    @handle_exceptions("AIBroadcasterApp")
    async def initialize(self) -> bool:
        """
        异步初始化应用程序
        
        Returns:
            是否初始化成功
        """
        try:
            self.logger.info("🔧 开始初始化AI广播器应用程序...")
            
            # 加载配置
            if not self._load_configuration():
                return False
            
            # 创建必要的目录
            self._create_directories()
            
            # 初始化事件总线
            self._setup_event_handlers()
            
            # 初始化组件
            await self._initialize_components()
            
            # 设置信号处理
            self._setup_signal_handlers()
            
            self.logger.log_success("AI广播器应用程序初始化完成")
            return True
            
        except Exception as e:
            self.logger.log_failure(f"应用程序初始化失败: {e}")
            self.logger.exception("初始化异常详情")
            return False
    
    def _load_configuration(self) -> bool:
        """加载配置"""
        try:
            # 加载合并配置
            config = self.config_manager.get_merged_config()
            if not config:
                self.logger.log_failure("无法加载配置文件")
                return False
            
            self.logger.log_success("配置加载完成")
            return True
            
        except Exception as e:
            self.logger.log_failure(f"配置加载失败: {e}")
            return False
    
    def _create_directories(self):
        """创建必要的目录"""
        config = self.config_manager.get_merged_config()
        storage_config = config.get('storage', {})
        
        directories = [
            storage_config.get('data_dir', 'data'),
            storage_config.get('audio_dir', 'data/audio'),
            storage_config.get('temp_dir', 'data/temp'),
            storage_config.get('cache_dir', 'data/cache'),
            storage_config.get('backup_dir', 'data/backup'),
            storage_config.get('logs_dir', 'logs')
        ]
        
        for dir_path in directories:
            from pathlib import Path
            Path(dir_path).mkdir(parents=True, exist_ok=True)
        
        self.logger.info("📁 目录结构创建完成")
    
    def _setup_event_handlers(self):
        """设置事件处理器"""
        # 注册错误事件处理器
        self.event_bus.subscribe('error', self._handle_error_event)
        self.event_bus.subscribe('broadcast_start', self._handle_broadcast_start)
        self.event_bus.subscribe('broadcast_stop', self._handle_broadcast_stop)
        
        self.logger.info("📡 事件处理器设置完成")
    
    async def _initialize_components(self):
        """初始化各个组件"""
        self.logger.info("🔧 初始化组件中...")
        
        # TODO: 在后续阶段实现具体组件
        # self.components['audio_processor'] = AudioProcessor(self.config_manager)
        # self.components['ai_engine'] = AIEngine(self.config_manager)
        # self.components['content_generator'] = ContentGenerator(self.config_manager)
        # self.components['voice_synthesizer'] = VoiceSynthesizer(self.config_manager)
        
        self.logger.info("✅ 组件初始化完成（占位符）")
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        if sys.platform != 'win32':
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.logger.info("📡 信号处理器设置完成")
    
    def _signal_handler(self, signum, frame):
        """信号处理函数"""
        self.logger.info(f"📡 收到信号 {signum}，准备关闭...")
        asyncio.create_task(self.shutdown())
    
    async def run(self) -> int:
        """
        运行应用程序（命令行模式）
        
        Returns:
            退出代码
        """
        try:
            self.is_running = True
            self.is_gui_mode = False
            self.stats['start_time'] = datetime.now()
            
            self.logger.log_startup("AI广播器服务（命令行模式）")
            
            # 发布应用启动事件
            await self.event_bus.publish('app_start', {'mode': 'cli'})
            
            # 主运行循环
            await self._main_loop()
            
            return 0
            
        except Exception as e:
            self.logger.log_failure(f"应用程序运行失败: {e}")
            self.logger.exception("运行异常详情")
            return 1
        finally:
            await self.shutdown()
    
    async def run_gui(self) -> int:
        """
        运行应用程序（图形界面模式）
        
        Returns:
            退出代码
        """
        try:
            self.is_running = True
            self.is_gui_mode = True
            self.stats['start_time'] = datetime.now()
            
            self.logger.log_startup("AI广播器服务（图形界面模式）")
            
            # 发布应用启动事件
            await self.event_bus.publish('app_start', {'mode': 'gui'})
            
            # TODO: 启动GUI界面
            # self.components['ui_manager'] = UIManager(self)
            # return await self.components['ui_manager'].run()
            
            self.logger.info("🖥️  图形界面模式（待实现）")
            await self._main_loop()
            
            return 0
            
        except Exception as e:
            self.logger.log_failure(f"GUI模式运行失败: {e}")
            self.logger.exception("GUI运行异常详情")
            return 1
        finally:
            await self.shutdown()
    
    async def _main_loop(self):
        """主运行循环"""
        self.logger.info("🔄 进入主运行循环")
        
        try:
            while self.is_running:
                # 更新统计信息
                if self.stats['start_time']:
                    self.stats['uptime_seconds'] = (
                        datetime.now() - self.stats['start_time']
                    ).total_seconds()
                
                # 检查组件状态
                await self._check_components_health()
                
                # 如果正在广播，执行广播逻辑
                if self.is_broadcasting:
                    await self._broadcast_cycle()
                
                # 短暂休眠
                await asyncio.sleep(1)
                
        except asyncio.CancelledError:
            self.logger.info("🛑 主循环被取消")
        except Exception as e:
            self.logger.log_failure(f"主循环异常: {e}")
            self.logger.exception("主循环异常详情")
    
    async def _check_components_health(self):
        """检查组件健康状态"""
        # TODO: 实现组件健康检查
        pass
    
    async def _broadcast_cycle(self):
        """执行一次广播周期"""
        try:
            self.logger.debug("🎙️ 执行广播周期")
            
            # TODO: 实现具体的广播逻辑
            # 1. 生成内容
            # 2. 语音合成
            # 3. 音频播放
            # 4. 更新统计
            
            self.stats['broadcasts_count'] += 1
            
            # 发布广播事件
            await self.event_bus.publish('broadcast_cycle', {
                'count': self.stats['broadcasts_count']
            })
            
            # 等待下一个广播间隔
            config = self.config_manager.get_merged_config()
            interval = config.get('broadcast', {}).get('interval_seconds', 30)
            await asyncio.sleep(interval)
            
        except Exception as e:
            self.stats['errors_count'] += 1
            self.logger.log_failure(f"广播周期异常: {e}")
            self.logger.exception("广播周期异常详情")
            
            # 发布错误事件
            await self.event_bus.publish('error', {
                'type': 'broadcast_cycle',
                'error': str(e)
            })
    
    async def start_broadcasting(self):
        """开始广播"""
        if self.is_broadcasting:
            self.logger.log_warning("广播已在进行中")
            return
        
        self.is_broadcasting = True
        self.logger.log_success("开始广播")
        
        # 发布广播开始事件
        await self.event_bus.publish('broadcast_start', {})
    
    async def stop_broadcasting(self):
        """停止广播"""
        if not self.is_broadcasting:
            self.logger.log_warning("广播未在进行中")
            return
        
        self.is_broadcasting = False
        self.logger.log_success("停止广播")
        
        # 发布广播停止事件
        await self.event_bus.publish('broadcast_stop', {})
    
    async def shutdown(self):
        """关闭应用程序"""
        self.logger.log_shutdown("AI广播器应用程序")
        
        try:
            # 停止广播
            if self.is_broadcasting:
                await self.stop_broadcasting()
            
            # 关闭组件
            await self._shutdown_components()
            
            # 停止主循环
            self.is_running = False
            
            # 发布应用关闭事件
            await self.event_bus.publish('app_shutdown', {})
            
            self.logger.log_success("AI广播器应用程序关闭完成")
            
        except Exception as e:
            self.logger.log_failure(f"应用程序关闭过程中发生异常: {e}")
            self.logger.exception("关闭异常详情")
    
    async def _shutdown_components(self):
        """关闭所有组件"""
        for name, component in self.components.items():
            if component:
                try:
                    if hasattr(component, 'shutdown'):
                        await component.shutdown()
                    self.logger.info(f"✅ {name} 组件已关闭")
                except Exception as e:
                    self.logger.error(f"❌ {name} 组件关闭失败: {e}")
    
    # 事件处理器
    async def _handle_error_event(self, data: Dict[str, Any]):
        """处理错误事件"""
        self.stats['errors_count'] += 1
        self.logger.error(f"收到错误事件: {data}")
    
    async def _handle_broadcast_start(self, data: Dict[str, Any]):
        """处理广播开始事件"""
        self.logger.info("📻 广播开始事件")
    
    async def _handle_broadcast_stop(self, data: Dict[str, Any]):
        """处理广播停止事件"""
        self.logger.info("📻 广播停止事件")
    
    def get_status(self) -> Dict[str, Any]:
        """获取应用程序状态"""
        config = self.config_manager.get_merged_config()
        
        return {
            'is_running': self.is_running,
            'is_broadcasting': self.is_broadcasting,
            'is_gui_mode': self.is_gui_mode,
            'stats': self.stats.copy(),
            'config': {
                'app_name': config.get('app', {}).get('name', 'Unknown'),
                'version': config.get('app', {}).get('version', 'Unknown'),
                'debug': config.get('app', {}).get('debug', False)
            },
            'components': {name: component is not None for name, component in self.components.items()}
        }
    
    def __str__(self):
        """字符串表示"""
        mode = "GUI" if self.is_gui_mode else "CLI"
        status = "运行中" if self.is_running else "已停止"
        broadcast_status = "广播中" if self.is_broadcasting else "未广播"
        return f"AIBroadcasterApp({mode}, {status}, {broadcast_status})"
