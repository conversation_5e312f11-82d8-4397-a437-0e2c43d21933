# AI直播系统 v2 - 用户系统API文档

## 📖 概述

本文档描述了AI直播系统v2用户管理系统的API接口和使用方法。

## 🔧 快速开始

### 初始化用户管理器

```python
from src.data.database_manager import DatabaseManager
from src.core.user import UserManager, AuthManager, PaymentManager

# 创建数据库连接
db = DatabaseManager("data/app.db")

# 初始化管理器
user_manager = UserManager(db)
auth_manager = AuthManager(db)
payment_manager = PaymentManager(db)
```

## 👤 用户管理API

### 1. 用户注册

```python
def register_user(username: str, password: str, email: str = None, phone: str = None) -> Dict[str, Any]
```

**参数:**
- `username`: 用户名 (3-20字符)
- `password`: 密码 (最少6字符)
- `email`: 邮箱 (可选)
- `phone`: 手机号 (可选)

**返回:**
```python
{
    'success': True,
    'user_id': 1,
    'user_info': {
        'id': 1,
        'username': 'testuser',
        'user_type': 0,
        'status': 1,
        'trial_days': 7,
        'created_at': '2025-05-29 11:27:34'
    },
    'message': '注册成功'
}
```

**示例:**
```python
result = user_manager.register_user(
    username="newuser",
    password="123456",
    email="<EMAIL>"
)

if result['success']:
    print(f"注册成功，用户ID: {result['user_id']}")
else:
    print(f"注册失败: {result['message']}")
```

### 2. 用户登录

```python
def login_user(username: str, password: str) -> Dict[str, Any]
```

**参数:**
- `username`: 用户名或邮箱
- `password`: 密码

**返回:**
```python
{
    'success': True,
    'user_info': {
        'id': 1,
        'username': 'testuser',
        'user_type': 1,
        'status': 1,
        'expire_at': '2025-06-28T11:28:50.729878'
    },
    'message': '登录成功'
}
```

### 3. 修改密码

```python
def change_password(user_id: int, old_password: str, new_password: str) -> Dict[str, Any]
```

### 4. 更新用户信息

```python
def update_user_info(user_id: int, **kwargs) -> bool
```

**支持的字段:**
- `nickname`: 昵称
- `email`: 邮箱
- `phone`: 手机号
- `avatar`: 头像URL

## 🔐 认证管理API

### 1. 生成令牌

```python
def generate_tokens(user_info: Dict[str, Any]) -> Dict[str, Any]
```

**返回:**
```python
{
    'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    'refresh_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    'token_type': 'Bearer',
    'expires_in': 3600,
    'permissions': ['user:basic', 'user:vip']
}
```

### 2. 验证令牌

```python
def verify_token(token: str) -> Optional[Dict[str, Any]]
```

**返回:**
```python
{
    'user_id': 1,
    'username': 'testuser',
    'user_type': 1,
    'permissions': ['user:basic', 'user:vip'],
    'exp': 1640995200,
    'iat': 1640991600
}
```

### 3. 刷新令牌

```python
def refresh_access_token(refresh_token: str) -> Optional[Dict[str, Any]]
```

### 4. 权限检查

```python
def check_permission(user_permissions: List[str], required_permission: str) -> bool
```

**示例:**
```python
# 检查用户是否有VIP权限
user_permissions = auth_manager.get_user_permissions(user_type=1)
has_vip = auth_manager.check_permission(user_permissions, 'user:vip')
```

## 💰 支付管理API

### 1. 获取充值套餐

```python
def get_recharge_packages() -> Dict[str, Any]
```

**返回:**
```python
{
    'basic': {
        'name': '基础套餐',
        'amount': 30.0,
        'days': 30,
        'user_type': 1,
        'description': '30天VIP会员'
    },
    'premium': {
        'name': '高级套餐',
        'amount': 88.0,
        'days': 90,
        'user_type': 2,
        'description': '90天高级会员'
    }
}
```

### 2. 创建充值订单

```python
def create_recharge_order(user_id: int, package_id: str, payment_method: str) -> Dict[str, Any]
```

**参数:**
- `user_id`: 用户ID
- `package_id`: 套餐ID ('basic', 'premium', 'yearly')
- `payment_method`: 支付方式 ('alipay', 'wechat', 'bank')

**返回:**
```python
{
    'success': True,
    'order_id': 1,
    'order_no': 'RC20250529112838000001',
    'amount': 30.0,
    'package_info': {
        'name': '基础套餐',
        'amount': 30.0,
        'days': 30
    },
    'payment_info': {
        'order_no': 'RC20250529112838000001',
        'amount': 30.0,
        'payment_method': 'alipay',
        'qr_code': 'alipay://pay?order_no=...',
        'pay_url': 'https://pay.alipay.com/order/...'
    }
}
```

### 3. 处理支付

```python
def process_payment(order_no: str, payment_data: Dict[str, Any] = None) -> Dict[str, Any]
```

### 4. 查询订单状态

```python
def get_order_status(order_no: str) -> Dict[str, Any]
```

### 5. 获取用户订单

```python
def get_user_orders(user_id: int, limit: int = 10) -> List[Dict[str, Any]]
```

## 🎯 使用示例

### 完整的用户注册登录流程

```python
# 1. 用户注册
register_result = user_manager.register_user(
    username="newuser",
    password="123456",
    email="<EMAIL>"
)

if register_result['success']:
    user_id = register_result['user_id']
    
    # 2. 用户登录
    login_result = user_manager.login_user("newuser", "123456")
    
    if login_result['success']:
        user_info = login_result['user_info']
        
        # 3. 生成认证令牌
        tokens = auth_manager.generate_tokens(user_info)
        
        # 4. 验证令牌
        payload = auth_manager.verify_token(tokens['access_token'])
        
        if payload:
            print(f"用户 {payload['username']} 登录成功")
            print(f"权限: {payload['permissions']}")
```

### 充值支付流程

```python
# 1. 获取充值套餐
packages = payment_manager.get_recharge_packages()

# 2. 创建充值订单
order_result = payment_manager.create_recharge_order(
    user_id=1,
    package_id='basic',
    payment_method='alipay'
)

if order_result['success']:
    order_no = order_result['order_no']
    
    # 3. 模拟支付处理
    payment_result = payment_manager.process_payment(order_no)
    
    if payment_result['success']:
        print("支付成功！")
        print(f"新用户类型: {payment_result['recharge_info']['new_user_type']}")
        print(f"到期时间: {payment_result['recharge_info']['expire_at']}")
```

## 🔒 权限系统

### 用户类型和权限

| 用户类型 | 数值 | 权限列表 |
|---------|------|----------|
| 免费用户 | 0 | `['user:basic']` |
| VIP用户 | 1 | `['user:basic', 'user:vip']` |
| 高级用户 | 2 | `['user:basic', 'user:vip', 'user:premium']` |
| 管理员 | 9 | `['user:basic', 'user:vip', 'user:premium', 'admin:all']` |

### 权限检查示例

```python
# 检查用户权限
user_permissions = auth_manager.get_user_permissions(user_type=1)
can_use_vip_features = auth_manager.check_permission(user_permissions, 'user:vip')

if can_use_vip_features:
    print("用户可以使用VIP功能")
else:
    print("用户权限不足")
```

## ⚠️ 错误处理

所有API都使用统一的错误处理机制：

```python
try:
    result = user_manager.register_user("test", "123456")
    if result['success']:
        # 处理成功情况
        pass
    else:
        # 处理业务错误
        print(f"操作失败: {result['message']}")
except Exception as e:
    # 处理系统异常
    print(f"系统错误: {e}")
```

## 📝 注意事项

1. **密码安全**: 所有密码都使用bcrypt加密存储
2. **令牌过期**: 访问令牌默认1小时过期，刷新令牌7天过期
3. **权限验证**: 所有敏感操作都需要进行权限验证
4. **数据验证**: 输入数据会进行格式和长度验证
5. **并发安全**: 数据库操作使用连接池，支持并发访问

## 🔧 配置选项

### JWT配置

```python
token_config = {
    'secret_key': 'your-secret-key',
    'algorithm': 'HS256',
    'access_token_expire': 1,  # 小时
    'refresh_token_expire': 7  # 天
}
```

### 支付配置

```python
payment_config = {
    'alipay': {
        'app_id': 'your-app-id',
        'private_key': 'your-private-key'
    },
    'wechat': {
        'mch_id': 'your-mch-id',
        'api_key': 'your-api-key'
    }
}
```

---

**文档版本**: v2.0.0  
**更新时间**: 2025年5月29日
