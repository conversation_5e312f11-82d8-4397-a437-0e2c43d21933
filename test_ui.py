#!/usr/bin/env python3
"""
AI Broadcaster v2 - UI界面测试脚本
测试PyQt6用户界面
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PyQt6.QtWidgets import QApplication, QMessageBox
    from PyQt6.QtCore import Qt
    
    from src.ui.app_launcher import launch_ui_app
    from src.services.logging_service import setup_logging, create_logger
    
    def test_ui_dependencies():
        """测试UI依赖"""
        print("🔍 检查UI依赖...")
        
        try:
            # 测试PyQt6
            app = QApplication([])
            print("  ✅ PyQt6: 可用")
            app.quit()
            
            # 测试psutil（系统信息面板需要）
            try:
                import psutil
                print("  ✅ psutil: 可用")
            except ImportError:
                print("  ⚠️  psutil: 不可用（系统信息功能受限）")
            
            return True
            
        except ImportError as e:
            print(f"  ❌ PyQt6: 不可用 - {e}")
            print("  💡 请安装PyQt6: pip install PyQt6")
            return False
            
    def test_ui_modules():
        """测试UI模块导入"""
        print("\n🧩 测试UI模块...")
        
        try:
            from src.ui.login_window import LoginWindow
            print("  ✅ 登录窗口模块: 导入成功")
            
            from src.ui.main_window import MainWindow
            print("  ✅ 主窗口模块: 导入成功")
            
            from src.ui.components.ai_speaker_panel import AISpeakerPanel
            print("  ✅ AI主播面板: 导入成功")
            
            from src.ui.components.script_panel import ScriptPanel
            print("  ✅ 话术管理面板: 导入成功")
            
            from src.ui.components.system_panel import SystemPanel
            print("  ✅ 系统设置面板: 导入成功")
            
            return True
            
        except ImportError as e:
            print(f"  ❌ UI模块导入失败: {e}")
            return False
            
    def test_ui_creation():
        """测试UI创建"""
        print("\n🖥️  测试UI创建...")
        
        try:
            # 创建QApplication
            app = QApplication([])
            
            # 测试登录窗口创建
            from src.ui.login_window import LoginWindow
            login_window = LoginWindow()
            print("  ✅ 登录窗口: 创建成功")
            login_window.close()
            
            # 测试主窗口创建
            from src.ui.main_window import MainWindow
            test_user_info = {
                'id': 1,
                'username': 'test_user',
                'phone': '13800138000'
            }
            main_window = MainWindow(test_user_info)
            print("  ✅ 主窗口: 创建成功")
            main_window.close()
            
            app.quit()
            return True
            
        except Exception as e:
            print(f"  ❌ UI创建失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    def main():
        """主函数"""
        print("🧪 AI Broadcaster v2 UI界面测试")
        print("=" * 60)
        
        # 初始化日志系统
        setup_logging()
        logger = create_logger("ui_test")
        
        try:
            # 测试依赖
            if not test_ui_dependencies():
                print("\n❌ 依赖检查失败！请安装必要的依赖包。")
                return 1
            
            # 测试模块导入
            if not test_ui_modules():
                print("\n❌ 模块导入失败！请检查代码错误。")
                return 1
            
            # 测试UI创建
            if not test_ui_creation():
                print("\n❌ UI创建失败！请检查代码错误。")
                return 1
            
            print("\n✅ 所有UI测试通过！")
            print("\n🚀 现在可以启动完整的UI应用程序:")
            print("   python test_ui.py --launch")
            print("   或者")
            print("   python -m src.ui.app_launcher")
            
            # 检查是否要启动UI
            if len(sys.argv) > 1 and sys.argv[1] == "--launch":
                print("\n🎯 启动UI应用程序...")
                return launch_ui_app()
            
            return 0
            
        except Exception as e:
            print(f"\n💥 测试过程中发生异常: {e}")
            import traceback
            traceback.print_exc()
            return 1
            
    if __name__ == "__main__":
        exit_code = main()
        sys.exit(exit_code)
        
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("💡 请确保已安装PyQt6: pip install PyQt6")
    sys.exit(1)
