#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据副视频测试程序
使用主程序的真实数据和方法进行副视频功能测试
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_real_sub_video_data():
    """使用真实数据测试副视频功能"""
    print("🎬 真实数据副视频测试程序")
    print("=" * 60)
    
    try:
        # 导入主程序
        from run_gui_qt5 import MainWindow
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        # 创建应用
        app = QApplication([])
        
        # 创建主窗口实例（使用真实的用户信息）
        user_info = {
            'username': 'real_test_user',
            'user_id': 1,
            'expire_time': '2025-12-31'
        }
        window = MainWindow(user_info)
        
        def test_real_data():
            """测试真实数据"""
            print("\n📋 步骤1：检查真实副视频配置...")
            
            # 检查副视频管理器
            if hasattr(window, 'sub_video_manager'):
                sub_video_manager = window.sub_video_manager
                print(f"✅ 副视频管理器存在: {type(sub_video_manager)}")
                
                # 获取真实的副视频数据
                if hasattr(sub_video_manager, 'sub_videos'):
                    real_sub_videos = sub_video_manager.sub_videos
                    print(f"📋 副视频数据类型: {type(real_sub_videos)}")
                    print(f"📋 副视频数据内容: {real_sub_videos}")
                    
                    if isinstance(real_sub_videos, dict):
                        print(f"✅ 真实副视频配置包含 {len(real_sub_videos)} 个关键词:")
                        for keyword, config in real_sub_videos.items():
                            if hasattr(config, 'video_source'):
                                # SubVideoItem对象
                                video_source = config.video_source
                                keyword_text = config.keyword if hasattr(config, 'keyword') else keyword
                                scripts_count = len(config.scripts) if hasattr(config, 'scripts') else 0
                            elif isinstance(config, dict):
                                # 字典格式
                                video_source = config.get('video_source', '未知')
                                keyword_text = config.get('keyword', keyword)
                                scripts_count = len(config.get('scripts', []))
                            else:
                                video_source = '未知格式'
                                keyword_text = keyword
                                scripts_count = 0
                            
                            print(f"  • {keyword_text} → {video_source} ({scripts_count}个话术)")
                    else:
                        print(f"⚠️ 副视频数据格式不是字典: {type(real_sub_videos)}")
                else:
                    print("❌ 副视频管理器没有sub_videos属性")
            else:
                print("❌ 主程序没有副视频管理器")
                return
            
            print("\n📋 步骤2：检查真实播放列表数据...")
            
            # 检查当前播放列表
            if hasattr(window, 'playlist_items'):
                real_playlist = window.playlist_items
                print(f"✅ 播放列表存在，包含 {len(real_playlist)} 个项目")
                
                sub_video_items = []
                for i, item in enumerate(real_playlist):
                    voice_type = item.get('voice_type', '未知')
                    content = item.get('content', '')[:50]
                    sub_video = item.get('sub_video', '无')
                    status = item.get('status', '未知')
                    
                    print(f"  项目 {i+1}: {voice_type}")
                    print(f"    内容: {content}...")
                    print(f"    状态: {status}")
                    print(f"    副视频: {sub_video}")
                    
                    if sub_video and sub_video != '无':
                        sub_video_items.append({
                            'index': i+1,
                            'content': content,
                            'sub_video': sub_video,
                            'voice_type': voice_type
                        })
                    print()
                
                if sub_video_items:
                    print(f"✅ 发现 {len(sub_video_items)} 个包含副视频的真实项目:")
                    for item in sub_video_items:
                        print(f"  • 项目{item['index']}: {item['content']}... → {item['sub_video']}")
                else:
                    print("⚠️ 当前播放列表中没有包含副视频的项目")
            else:
                print("❌ 主程序没有播放列表")
            
            print("\n📋 步骤3：测试真实副视频触发方法...")
            
            # 使用真实的弹幕内容进行测试
            real_test_messages = []
            
            # 从播放列表中提取真实的弹幕内容
            if hasattr(window, 'playlist_items'):
                for item in window.playlist_items:
                    if item.get('voice_type') == '弹幕话术':
                        content = item.get('content', '')
                        if content and content not in real_test_messages:
                            real_test_messages.append(content)
            
            # 如果没有真实弹幕，添加一些测试弹幕
            if not real_test_messages:
                print("⚠️ 播放列表中没有弹幕话术，使用测试弹幕")
                real_test_messages = [
                    "感谢老板的火箭，太给力了！",
                    "谢谢大家的礼物支持",
                    "主播666，继续加油！"
                ]
            
            print(f"📝 使用 {len(real_test_messages)} 条真实/测试弹幕:")
            
            # 测试真实的副视频触发方法
            if hasattr(window, 'check_sub_video_trigger'):
                for i, message in enumerate(real_test_messages, 1):
                    print(f"\n🎬 测试 {i}: {message}")
                    
                    try:
                        result = window.check_sub_video_trigger(message)
                        if result:
                            print(f"  ✅ 触发副视频: {result}")
                        else:
                            print(f"  ⚪ 无副视频触发")
                    except Exception as e:
                        print(f"  ❌ 测试失败: {e}")
            else:
                print("❌ 主程序没有check_sub_video_trigger方法")
            
            print("\n📋 步骤4：测试真实弹幕添加流程...")
            
            # 测试添加新的弹幕话术
            test_danmaku = "测试真实数据的火箭弹幕"
            print(f"🎬 添加测试弹幕: {test_danmaku}")
            
            if hasattr(window, 'check_sub_video_trigger') and hasattr(window, 'add_danmaku_to_playlist'):
                try:
                    # 检测副视频
                    sub_video_result = window.check_sub_video_trigger(test_danmaku)
                    print(f"🔍 副视频检测结果: {sub_video_result}")
                    
                    # 记录添加前的播放列表数量
                    before_count = len(window.playlist_items) if hasattr(window, 'playlist_items') else 0
                    
                    # 添加到播放列表
                    window.add_danmaku_to_playlist(test_danmaku, sub_video_result)
                    
                    # 检查添加后的播放列表
                    after_count = len(window.playlist_items) if hasattr(window, 'playlist_items') else 0
                    
                    if after_count > before_count:
                        print(f"✅ 弹幕话术添加成功，播放列表从 {before_count} 增加到 {after_count}")
                        
                        # 检查最新添加的项目
                        if hasattr(window, 'playlist_items') and window.playlist_items:
                            latest_item = window.playlist_items[-1]
                            latest_sub_video = latest_item.get('sub_video', '无')
                            latest_content = latest_item.get('content', '')
                            
                            print(f"📋 最新添加的项目:")
                            print(f"  内容: {latest_content}")
                            print(f"  副视频: {latest_sub_video}")
                            
                            if latest_sub_video and latest_sub_video != '无':
                                print(f"✅ 副视频正确设置: {latest_sub_video}")
                            else:
                                print(f"❌ 副视频未正确设置")
                    else:
                        print(f"❌ 弹幕话术添加失败")
                        
                except Exception as e:
                    print(f"❌ 弹幕添加测试失败: {e}")
                    import traceback
                    traceback.print_exc()
            
            print("\n📋 步骤5：检查表格显示...")
            
            # 检查表格中的副视频显示
            if hasattr(window, 'playlist_table'):
                table = window.playlist_table
                row_count = table.rowCount()
                print(f"📊 播放列表表格有 {row_count} 行")
                
                for row in range(min(row_count, 5)):  # 只检查前5行
                    try:
                        voice_type_item = table.item(row, 1)
                        content_item = table.item(row, 2)
                        sub_video_item = table.item(row, 6)  # 副视频列
                        
                        voice_type = voice_type_item.text() if voice_type_item else ''
                        content = content_item.text()[:30] if content_item else ''
                        sub_video = sub_video_item.text() if sub_video_item else ''
                        
                        print(f"  行 {row+1}: {voice_type} | {content}... | 副视频: {sub_video}")
                        
                    except Exception as e:
                        print(f"  行 {row+1}: 读取失败 - {e}")
            
            # 退出测试
            def exit_test():
                print(f"\n🏁 真实数据副视频测试完成!")
                print(f"📋 测试总结:")
                print(f"  ✅ 使用了主程序的真实副视频配置")
                print(f"  ✅ 使用了主程序的真实播放列表数据")
                print(f"  ✅ 使用了主程序的真实副视频检测方法")
                print(f"  ✅ 测试了真实的弹幕添加流程")
                print(f"  ✅ 检查了真实的表格显示")
                app.quit()
            
            QTimer.singleShot(3000, exit_test)
        
        # 等待界面初始化完成后开始测试
        QTimer.singleShot(3000, test_real_data)
        
        # 显示窗口（可选）
        # window.show()
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 真实数据测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_real_sub_video_data()
