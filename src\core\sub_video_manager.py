"""
AI Broadcaster v2 - 副视频管理器
管理副视频的触发关键词、视频源和话术内容
"""

import random
import re
from typing import Dict, List, Optional, Any
from pathlib import Path
import json

from ..services.logging_service import create_logger
from ..services.error_handler import handle_exceptions


class SubVideoItem:
    """副视频项"""

    def __init__(self, keyword: str, video_source: str, scripts: List[str] = None):
        self.keyword = keyword
        self.video_source = video_source
        # 🔥 修复：副视频不再需要专门的话术，话术来自触发副视频的原始内容
        self.scripts = []  # 保留字段以兼容现有数据，但不再使用
        self.used_scripts = set()  # 保留字段以兼容现有数据，但不再使用
        self.created_at = None
        self.last_triggered = None
        self.trigger_count = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'keyword': self.keyword,
            'video_source': self.video_source,
            'scripts': [],  # 🔥 修复：不再保存话术内容
            'used_scripts': [],  # 🔥 修复：不再保存已使用话术
            'created_at': self.created_at,
            'last_triggered': self.last_triggered,
            'trigger_count': self.trigger_count
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SubVideoItem':
        """从字典创建实例"""
        item = cls(
            keyword=data['keyword'],
            video_source=data['video_source'],
            scripts=[]  # 🔥 修复：不再使用话术参数
        )
        # 🔥 修复：不再加载话术相关数据
        item.used_scripts = set()
        item.created_at = data.get('created_at')
        item.last_triggered = data.get('last_triggered')
        item.trigger_count = data.get('trigger_count', 0)
        return item


class SubVideoManager:
    """副视频管理器"""
    
    def __init__(self, data_file: str = "data/sub_videos.json"):
        self.logger = create_logger("sub_video_manager")
        self.data_file = Path(data_file)
        
        # 副视频数据
        self.sub_videos: Dict[str, SubVideoItem] = {}
        
        # 确保数据目录存在
        self.data_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 加载数据
        self.load_data()
        
        self.logger.info("副视频管理器初始化完成")
    
    @handle_exceptions("SubVideoManager")
    def add_sub_video(self, keyword: str, video_source: str, scripts: List[str] = None) -> bool:
        """添加副视频"""
        try:
            if keyword in self.sub_videos:
                self.logger.warning(f"副视频关键词已存在: {keyword}")
                return False

            # 🔥 修复：副视频不再需要话术，只需要关键词和视频源
            self.sub_videos[keyword] = SubVideoItem(keyword, video_source)

            # 保存数据
            self.save_data()

            self.logger.info(f"添加副视频: {keyword} -> {video_source}")
            return True

        except Exception as e:
            self.logger.error(f"添加副视频失败: {e}")
            return False
    
    @handle_exceptions("SubVideoManager")
    def update_sub_video(self, keyword: str, video_source: str = None, 
                        scripts: List[str] = None) -> bool:
        """更新副视频"""
        try:
            if keyword not in self.sub_videos:
                self.logger.error(f"副视频不存在: {keyword}")
                return False
            
            sub_video = self.sub_videos[keyword]
            
            if video_source is not None:
                sub_video.video_source = video_source
            
            if scripts is not None:
                parsed_scripts = self._parse_scripts(scripts)
                sub_video.scripts = parsed_scripts
                sub_video.used_scripts.clear()  # 重置已使用记录
            
            # 保存数据
            self.save_data()
            
            self.logger.info(f"更新副视频: {keyword}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新副视频失败: {e}")
            return False
    
    @handle_exceptions("SubVideoManager")
    def remove_sub_video(self, keyword: str) -> bool:
        """删除副视频"""
        try:
            if keyword not in self.sub_videos:
                self.logger.error(f"副视频不存在: {keyword}")
                return False
            
            del self.sub_videos[keyword]
            
            # 保存数据
            self.save_data()
            
            self.logger.info(f"删除副视频: {keyword}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除副视频失败: {e}")
            return False
    
    def get_sub_video(self, keyword: str) -> Optional[SubVideoItem]:
        """获取副视频项"""
        return self.sub_videos.get(keyword)
    
    def get_all_sub_videos(self) -> Dict[str, SubVideoItem]:
        """获取所有副视频"""
        return self.sub_videos.copy()
    
    def check_trigger(self, text: str) -> List[str]:
        """检查文本是否触发副视频关键词"""
        triggered_keywords = []
        
        for keyword in self.sub_videos.keys():
            if keyword.lower() in text.lower():
                triggered_keywords.append(keyword)
        
        return triggered_keywords
    
    def get_random_script(self, keyword: str) -> Optional[str]:
        """🔥 修复：副视频不再需要专门的话术，返回None"""
        # 副视频的话术内容来自触发副视频的原始内容，不需要单独的话术
        if keyword not in self.sub_videos:
            return None

        # 更新触发统计
        sub_video = self.sub_videos[keyword]
        sub_video.trigger_count += 1
        sub_video.last_triggered = None  # 这里应该设置当前时间

        self.logger.info(f"副视频触发: {keyword}")
        return None  # 🔥 修复：不再返回话术内容
    
    def _parse_scripts(self, scripts: List[str]) -> List[str]:
        """解析话术内容，提取有效话术"""
        parsed_scripts = []
        
        for script in scripts:
            script = script.strip()
            # 跳过空行和注释行
            if not script or script.startswith('#'):
                continue
            
            # 匹配话术格式：数字***内容 或直接内容
            match = re.match(r'^\d+\*\*\*(.+)$', script)
            if match:
                script_content = match.group(1).strip()
                parsed_scripts.append(script_content)
            else:
                # 直接添加内容
                parsed_scripts.append(script)
        
        return parsed_scripts
    
    def _process_script_variables(self, script: str) -> str:
        """处理话术中的变量词和随机选择"""
        processed = script
        
        # 处理随机选择：【选项1|选项2|选项3】
        def replace_random_choice(match):
            choices = match.group(1).split('|')
            return random.choice(choices)
        
        processed = re.sub(r'【([^】]+)】', replace_random_choice, processed)
        
        # 这里可以添加更多变量词处理
        # 例如：{nick}, {gametype}, {gamename} 等
        # 实际使用时需要传入具体的变量值
        
        return processed
    
    def save_data(self):
        """保存数据到文件"""
        try:
            data = {
                keyword: item.to_dict() 
                for keyword, item in self.sub_videos.items()
            }
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.debug("副视频数据已保存")
            
        except Exception as e:
            self.logger.error(f"保存副视频数据失败: {e}")
    
    def load_data(self):
        """从文件加载数据"""
        try:
            if not self.data_file.exists():
                self.logger.info("副视频数据文件不存在，创建新文件")
                self.save_data()
                return
            
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.sub_videos = {
                keyword: SubVideoItem.from_dict(item_data)
                for keyword, item_data in data.items()
            }
            
            self.logger.info(f"加载副视频数据: {len(self.sub_videos)} 个副视频")
            
        except Exception as e:
            self.logger.error(f"加载副视频数据失败: {e}")
            self.sub_videos = {}
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取副视频统计信息"""
        total_videos = len(self.sub_videos)
        total_scripts = sum(len(item.scripts) for item in self.sub_videos.values())
        total_triggers = sum(item.trigger_count for item in self.sub_videos.values())
        
        return {
            'total_videos': total_videos,
            'total_scripts': total_scripts,
            'total_triggers': total_triggers,
            'videos': [
                {
                    'keyword': keyword,
                    'video_source': item.video_source,
                    'script_count': len(item.scripts),
                    'trigger_count': item.trigger_count,
                    'used_scripts': len(item.used_scripts)
                }
                for keyword, item in self.sub_videos.items()
            ]
        }


def main():
    """测试函数"""
    manager = SubVideoManager("test_sub_videos.json")
    
    # 测试添加副视频
    scripts = [
        "1***欢迎观看副视频！",
        "2***【精彩|有趣|好看】的内容来了！",
        "3***请多多支持！"
    ]
    
    manager.add_sub_video("游戏", "GameVideoSource", scripts)
    manager.add_sub_video("音乐", "MusicVideoSource", ["1***音乐时间到！", "2***享受音乐吧！"])
    
    # 测试触发检查
    triggered = manager.check_trigger("我想看游戏视频")
    print(f"触发的关键词: {triggered}")
    
    # 测试获取话术
    for i in range(5):
        script = manager.get_random_script("游戏")
        print(f"第{i+1}次获取: {script}")
    
    # 测试统计信息
    stats = manager.get_statistics()
    print(f"统计信息: {stats}")


if __name__ == "__main__":
    main()
