#!/usr/bin/env python3
"""
AI主播系统 v2 - API使用示例
展示如何使用API管理器进行各种操作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.logging_service import setup_logging, create_logger
from src.services.api_manager import APIManager


def demo_voice_operations(api_manager):
    """演示语音操作"""
    print("\n" + "="*50)
    print("🎤 语音服务操作演示")
    print("="*50)

    # 获取主播列表
    print("\n1. 获取主播列表")
    speakers_result = api_manager.get_speakers()
    if speakers_result['success']:
        print(f"✅ 共有 {len(speakers_result['speakers'])} 个主播:")
        for speaker in speakers_result['speakers']:
            if isinstance(speaker, dict):
                print(f"   - ID: {speaker.get('id')}, 名称: {speaker.get('name')}")
            else:
                print(f"   - {speaker}")
    else:
        print(f"❌ 获取失败: {speakers_result['message']}")
        return

    # 批量下载语音
    print("\n2. 批量下载语音")
    test_texts = [
        "欢迎来到AI主播系统！",
        "今天天气真不错。",
        "感谢大家的支持！"
    ]

    batch_result = api_manager.batch_download_voices(test_texts, speaker_id=0, speed=1.0)
    if batch_result['success']:
        print(f"✅ 批量下载完成: {batch_result['success_count']}/{batch_result['total']}")
        for item in batch_result['results']:
            status = "✅" if item['result']['success'] else "❌"
            cached = "（缓存）" if item['result'].get('cached') else "（新下载）"
            print(f"   {status} {item['text'][:20]}... {cached}")
    else:
        print("❌ 批量下载失败")

    # 查看缓存信息
    print("\n3. 语音缓存信息")
    cache_info = api_manager.get_voice_cache_info()
    print(f"   缓存文件数: {cache_info['file_count']}")
    print(f"   缓存大小: {cache_info['total_size_mb']} MB")


def demo_script_operations(api_manager):
    """演示话术操作"""
    print("\n" + "="*50)
    print("📝 话术服务操作演示")
    print("="*50)

    # 获取话术列表
    print("\n1. 获取话术列表")
    scripts_result = api_manager.get_script_list()
    if scripts_result['success']:
        print(f"✅ 共有 {len(scripts_result['scripts'])} 个话术:")
        for script in scripts_result['scripts']:
            print(f"   - {script.get('name')}")
    else:
        print(f"❌ 获取失败: {scripts_result['message']}")
        return

    # 获取第一个话术的内容
    if scripts_result['scripts']:
        first_script = scripts_result['scripts'][0]['name']
        print(f"\n2. 获取话术内容: {first_script}")
        content_result = api_manager.get_script_content(first_script)
        if content_result['success']:
            content = content_result['content']
            preview = content[:100] + "..." if len(content) > 100 else content
            print(f"✅ 内容预览: {preview}")
        else:
            print(f"❌ 获取失败: {content_result['message']}")

    # 创建新话术
    print("\n3. 创建新话术")
    new_script_name = "测试话术_API"
    new_script_content = "这是通过API创建的测试话术。\n包含多行内容。\n用于演示API功能。"

    create_result = api_manager.create_script(new_script_name, new_script_content)
    if create_result['success']:
        print(f"✅ 话术创建成功: {new_script_name}")
    else:
        print(f"❌ 话术创建失败: {create_result['message']}")


def demo_dialogue_operations(api_manager):
    """演示AI对话操作"""
    print("\n" + "="*50)
    print("💬 AI对话服务操作演示")
    print("="*50)

    # 获取对话列表
    print("\n1. 获取AI对话列表")
    dialogues_result = api_manager.get_dialogue_list()
    if dialogues_result['success']:
        print(f"✅ 共有 {len(dialogues_result['dialogues'])} 个对话:")
        for dialogue in dialogues_result['dialogues']:
            print(f"   - {dialogue.get('name')}")
    else:
        print(f"❌ 获取失败: {dialogues_result['message']}")
        return

    # 获取第一个对话的内容
    if dialogues_result['dialogues']:
        first_dialogue = dialogues_result['dialogues'][0]['name']
        print(f"\n2. 获取对话内容: {first_dialogue}")
        content_result = api_manager.get_dialogue_content(first_dialogue)
        if content_result['success']:
            content = content_result['content']
            preview = content[:100] + "..." if len(content) > 100 else content
            print(f"✅ 内容预览: {preview}")
        else:
            print(f"❌ 获取失败: {content_result['message']}")

    # 创建新对话
    print("\n3. 创建新AI对话")
    new_dialogue_name = "测试对话_API"
    new_dialogue_content = """
关键词: 你好
回复: 你好！欢迎来到AI主播系统！

关键词: 再见
回复: 再见！感谢您的观看！

关键词: 测试
回复: 这是通过API创建的测试对话。
"""

    create_result = api_manager.create_dialogue(new_dialogue_name, new_dialogue_content)
    if create_result['success']:
        print(f"✅ 对话创建成功: {new_dialogue_name}")
    else:
        print(f"❌ 对话创建失败: {create_result['message']}")


def demo_batch_operations(api_manager):
    """演示批量操作"""
    print("\n" + "="*50)
    print("📦 批量操作演示")
    print("="*50)

    # 获取所有内容
    print("\n1. 获取所有话术和对话内容")
    all_content = api_manager.get_all_content()

    print(f"✅ 话术数量: {len(all_content['scripts'])}")
    for name in list(all_content['scripts'].keys())[:3]:
        content_length = len(all_content['scripts'][name])
        print(f"   - {name}: {content_length} 字符")

    print(f"✅ 对话数量: {len(all_content['dialogues'])}")
    for name in list(all_content['dialogues'].keys())[:3]:
        content_length = len(all_content['dialogues'][name])
        print(f"   - {name}: {content_length} 字符")


def demo_service_status(api_manager):
    """演示服务状态查询"""
    print("\n" + "="*50)
    print("📊 服务状态查询")
    print("="*50)

    status = api_manager.get_service_status()

    for service_name, service_status in status.items():
        available = "✅" if service_status['available'] else "❌"
        print(f"\n{service_name}: {available}")

        if service_name == 'voice_service' and service_status['available']:
            cache_info = service_status['cache_info']
            print(f"   缓存文件: {cache_info['file_count']} 个")
            print(f"   缓存大小: {cache_info['total_size_mb']} MB")

        elif service_name in ['script_service', 'dialogue_service'] and service_status['available']:
            print(f"   服务器地址: {service_status['server_url']}")

        elif service_name in ['danmaku_service', 'obs_service']:
            if service_status['available']:
                connected = "✅" if service_status['connected'] else "❌"
                print(f"   连接状态: {connected}")
            else:
                print(f"   状态: 未初始化")


def main():
    """主演示函数"""
    # 初始化日志
    setup_logging()
    logger = create_logger("api_demo")

    print("🚀 AI主播系统 v2 - API使用演示")
    print("="*60)

    # 创建API管理器
    config = {
        'voice_api_url': 'http://ct.scjanelife.com/voice',
        'server_url': 'http://localhost:12456'
    }

    api_manager = APIManager(config)

    try:
        # 演示各种操作
        demo_voice_operations(api_manager)
        demo_script_operations(api_manager)
        demo_dialogue_operations(api_manager)
        demo_batch_operations(api_manager)
        demo_service_status(api_manager)

        print("\n" + "="*60)
        print("🎉 API使用演示完成！")
        print("\n💡 提示:")
        print("   - 语音服务: 支持多个主播，自动缓存")
        print("   - 话术服务: 支持CRUD操作")
        print("   - 对话服务: 支持关键词匹配")
        print("   - 批量操作: 提高效率")
        print("   - 状态监控: 实时了解服务状态")

    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        print(f"\n❌ 演示失败: {e}")
    finally:
        # 关闭API管理器
        api_manager.close()


if __name__ == "__main__":
    main()
