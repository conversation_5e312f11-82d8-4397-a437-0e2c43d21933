#!/usr/bin/env python3
"""
AI Broadcaster v2 - 新系统测试脚本
测试重构后的系统功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.app.application import AIBroadcasterApp
from src.services.logging_service import setup_logging, create_logger
from src.data.config_manager import ConfigManager
from src.data.file_manager import FileManager
from src.utils.time_utils import TimeUtils
from src.utils.file_utils import FileUtils
from src.utils.validation import Validator


async def test_basic_functionality():
    """测试基本功能"""
    print("🧪 开始测试AI Broadcaster v2重构系统...")
    print("=" * 60)
    
    # 初始化日志系统
    setup_logging()
    logger = create_logger("test")
    
    logger.info("开始系统功能测试")
    
    # 测试配置管理器
    print("📋 测试配置管理器...")
    config_manager = ConfigManager()
    
    # 加载配置
    default_config = config_manager.load_config('default')
    app_config = config_manager.load_config('app')
    user_config = config_manager.load_config('user')
    
    print(f"  ✅ 默认配置加载: {'成功' if default_config else '失败'}")
    print(f"  ✅ 应用配置加载: {'成功' if app_config else '失败'}")
    print(f"  ✅ 用户配置加载: {'成功' if user_config else '失败'}")
    
    # 测试合并配置
    merged_config = config_manager.get_merged_config()
    app_name = config_manager.get_value('app.name', 'Unknown')
    print(f"  ✅ 合并配置: {app_name}")
    
    # 测试文件管理器
    print("\n📁 测试文件管理器...")
    file_manager = FileManager()
    
    # 测试JSON保存和加载
    test_data = {"test": "data", "timestamp": TimeUtils.timestamp()}
    json_saved = file_manager.save_json(test_data, "data/test.json")
    json_loaded = file_manager.load_json("data/test.json")
    
    print(f"  ✅ JSON保存: {'成功' if json_saved else '失败'}")
    print(f"  ✅ JSON加载: {'成功' if json_loaded and json_loaded.get('test') == 'data' else '失败'}")
    
    # 测试工具类
    print("\n🔧 测试工具类...")
    
    # 时间工具
    current_time = TimeUtils.now()
    formatted_time = TimeUtils.format_datetime(current_time)
    duration = TimeUtils.format_duration(3661)  # 1小时1分1秒
    
    print(f"  ✅ 时间格式化: {formatted_time}")
    print(f"  ✅ 时长格式化: {duration}")
    
    # 文件工具
    file_size = FileUtils.format_file_size(1024 * 1024 * 5)  # 5MB
    is_audio = FileUtils.is_audio_file("test.mp3")
    clean_name = FileUtils.clean_filename("test<>file?.txt")
    
    print(f"  ✅ 文件大小格式化: {file_size}")
    print(f"  ✅ 音频文件检测: {is_audio}")
    print(f"  ✅ 文件名清理: {clean_name}")
    
    # 验证工具
    email_valid = Validator.is_email("<EMAIL>")
    url_valid = Validator.is_url("https://example.com")
    json_valid = Validator.is_json('{"key": "value"}')
    
    print(f"  ✅ 邮箱验证: {email_valid}")
    print(f"  ✅ URL验证: {url_valid}")
    print(f"  ✅ JSON验证: {json_valid}")
    
    # 测试应用程序
    print("\n🚀 测试应用程序...")
    app = AIBroadcasterApp()
    
    # 初始化应用程序
    init_success = await app.initialize()
    print(f"  ✅ 应用程序初始化: {'成功' if init_success else '失败'}")
    
    if init_success:
        # 获取状态
        status = app.get_status()
        print(f"  ✅ 应用程序状态: {status['config']['app_name']} v{status['config']['version']}")
        print(f"  ✅ 运行状态: {'运行中' if status['is_running'] else '已停止'}")
        
        # 测试事件系统
        print("\n📡 测试事件系统...")
        event_published = False
        
        async def test_event_handler(data):
            nonlocal event_published
            event_published = True
            logger.info(f"收到测试事件: {data}")
        
        # 订阅测试事件
        app.event_bus.subscribe('test_event', test_event_handler)
        
        # 发布测试事件
        await app.event_bus.publish('test_event', {'message': 'Hello World!'})
        
        # 等待事件处理
        await asyncio.sleep(0.1)
        
        print(f"  ✅ 事件发布和处理: {'成功' if event_published else '失败'}")
        
        # 获取事件统计
        event_stats = app.event_bus.get_stats()
        print(f"  ✅ 事件统计: 总事件数 {event_stats['total_events']}")
    
    print("\n" + "=" * 60)
    print("🎉 系统功能测试完成！")
    
    # 清理测试文件
    test_file = Path("data/test.json")
    if test_file.exists():
        test_file.unlink()
        print("🧹 测试文件已清理")
    
    logger.info("系统功能测试完成")


async def main():
    """主函数"""
    try:
        await test_basic_functionality()
        return 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    # 在Windows上设置事件循环策略
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
