#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整副视频测试程序
包含OBS连接、真实视频源获取、完整副视频功能测试
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QTextEdit, QLabel, 
                             QLineEdit, QTableWidget, QTableWidgetItem, 
                             QGroupBox, QMessageBox, QComboBox, QCheckBox,
                             QSpinBox, QTabWidget)
from PyQt5.QtCore import QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont

class CompleteSubVideoTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("完整副视频功能测试程序")
        self.setGeometry(100, 100, 1200, 800)
        
        # 导入主程序组件
        try:
            from run_gui_qt5 import MainWindow
            self.main_window_class = MainWindow
            self.main_window = None
            self.obs_sources = []
            self.init_main_window()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法导入主程序: {e}")
            return
        
        self.init_ui()
        
    def init_main_window(self):
        """初始化主程序窗口实例"""
        try:
            user_info = {
                'username': 'test_user',
                'user_id': 1,
                'expire_time': '2025-12-31'
            }
            self.main_window = self.main_window_class(user_info)
            self.log_message("✅ 主程序窗口初始化成功")
        except Exception as e:
            self.log_message(f"❌ 主程序窗口初始化失败: {e}")
    
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("完整副视频功能测试程序")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title_label)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # OBS连接标签页
        obs_tab = self.create_obs_tab()
        tab_widget.addTab(obs_tab, "OBS连接")
        
        # 副视频配置标签页
        config_tab = self.create_config_tab()
        tab_widget.addTab(config_tab, "副视频配置")
        
        # 弹幕测试标签页
        test_tab = self.create_test_tab()
        tab_widget.addTab(test_tab, "弹幕测试")
        
        # 播放列表标签页
        playlist_tab = self.create_playlist_tab()
        tab_widget.addTab(playlist_tab, "播放列表")
        
        layout.addWidget(tab_widget)
        
        # 日志显示区域
        log_group = QGroupBox("测试日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        clear_log_btn = QPushButton("清空日志")
        clear_log_btn.clicked.connect(self.clear_log)
        log_layout.addWidget(clear_log_btn)
        
        layout.addWidget(log_group)
        
        # 初始化数据
        self.refresh_config()
        self.refresh_playlist()
        
    def create_obs_tab(self):
        """创建OBS连接标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # OBS连接设置
        connection_group = QGroupBox("OBS连接设置")
        connection_layout = QVBoxLayout(connection_group)
        
        # 连接参数
        params_layout = QHBoxLayout()
        params_layout.addWidget(QLabel("OBS地址:"))
        self.obs_host_input = QLineEdit("127.0.0.1")
        params_layout.addWidget(self.obs_host_input)
        
        params_layout.addWidget(QLabel("端口:"))
        self.obs_port_input = QSpinBox()
        self.obs_port_input.setRange(1, 65535)
        self.obs_port_input.setValue(4455)
        params_layout.addWidget(self.obs_port_input)
        
        connection_layout.addLayout(params_layout)
        
        # 连接按钮
        connect_layout = QHBoxLayout()
        self.connect_btn = QPushButton("连接OBS")
        self.connect_btn.clicked.connect(self.connect_obs)
        connect_layout.addWidget(self.connect_btn)
        
        self.disconnect_btn = QPushButton("断开连接")
        self.disconnect_btn.clicked.connect(self.disconnect_obs)
        self.disconnect_btn.setEnabled(False)
        connect_layout.addWidget(self.disconnect_btn)
        
        self.obs_status_label = QLabel("状态: 未连接")
        connect_layout.addWidget(self.obs_status_label)
        
        connection_layout.addLayout(connect_layout)
        
        layout.addWidget(connection_group)
        
        # 视频源列表
        sources_group = QGroupBox("OBS视频源")
        sources_layout = QVBoxLayout(sources_group)
        
        refresh_sources_btn = QPushButton("刷新视频源列表")
        refresh_sources_btn.clicked.connect(self.refresh_obs_sources)
        sources_layout.addWidget(refresh_sources_btn)
        
        self.sources_text = QTextEdit()
        self.sources_text.setMaximumHeight(200)
        self.sources_text.setReadOnly(True)
        sources_layout.addWidget(self.sources_text)
        
        layout.addWidget(sources_group)
        
        # 视频源切换测试
        switch_group = QGroupBox("视频源切换测试")
        switch_layout = QVBoxLayout(switch_group)
        
        switch_control_layout = QHBoxLayout()
        switch_control_layout.addWidget(QLabel("选择视频源:"))
        self.source_combo = QComboBox()
        switch_control_layout.addWidget(self.source_combo)
        
        switch_btn = QPushButton("切换到此源")
        switch_btn.clicked.connect(self.switch_to_source)
        switch_control_layout.addWidget(switch_btn)
        
        switch_layout.addLayout(switch_control_layout)
        
        layout.addWidget(switch_group)
        
        return tab
    
    def create_config_tab(self):
        """创建副视频配置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 当前配置显示
        current_config_group = QGroupBox("当前副视频配置")
        current_config_layout = QVBoxLayout(current_config_group)
        
        self.config_text = QTextEdit()
        self.config_text.setMaximumHeight(150)
        self.config_text.setReadOnly(True)
        current_config_layout.addWidget(self.config_text)
        
        refresh_config_btn = QPushButton("刷新配置")
        refresh_config_btn.clicked.connect(self.refresh_config)
        current_config_layout.addWidget(refresh_config_btn)
        
        layout.addWidget(current_config_group)
        
        # 添加新配置
        add_config_group = QGroupBox("添加副视频配置")
        add_config_layout = QVBoxLayout(add_config_group)
        
        # 关键词输入
        keyword_layout = QHBoxLayout()
        keyword_layout.addWidget(QLabel("关键词:"))
        self.keyword_input = QLineEdit()
        self.keyword_input.setPlaceholderText("如: 火箭")
        keyword_layout.addWidget(self.keyword_input)
        add_config_layout.addLayout(keyword_layout)
        
        # 视频源选择
        video_source_layout = QHBoxLayout()
        video_source_layout.addWidget(QLabel("视频源:"))
        self.video_source_combo = QComboBox()
        video_source_layout.addWidget(self.video_source_combo)
        add_config_layout.addLayout(video_source_layout)
        
        # 话术输入
        script_layout = QHBoxLayout()
        script_layout.addWidget(QLabel("话术:"))
        self.script_input = QLineEdit()
        self.script_input.setPlaceholderText("如: 感谢{nick}的火箭支持！")
        script_layout.addWidget(self.script_input)
        add_config_layout.addLayout(script_layout)
        
        # 添加按钮
        add_btn = QPushButton("添加配置")
        add_btn.clicked.connect(self.add_sub_video_config)
        add_config_layout.addWidget(add_btn)
        
        layout.addWidget(add_config_group)
        
        return tab
    
    def create_test_tab(self):
        """创建弹幕测试标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 弹幕输入测试
        input_group = QGroupBox("弹幕输入测试")
        input_layout = QVBoxLayout(input_group)
        
        # 弹幕输入
        danmaku_layout = QHBoxLayout()
        danmaku_layout.addWidget(QLabel("测试弹幕:"))
        self.danmaku_input = QLineEdit()
        self.danmaku_input.setPlaceholderText("输入包含关键词的弹幕")
        danmaku_layout.addWidget(self.danmaku_input)
        
        test_trigger_btn = QPushButton("测试触发")
        test_trigger_btn.clicked.connect(self.test_trigger)
        danmaku_layout.addWidget(test_trigger_btn)
        
        add_to_playlist_btn = QPushButton("添加到播放列表")
        add_to_playlist_btn.clicked.connect(self.add_to_playlist)
        danmaku_layout.addWidget(add_to_playlist_btn)
        
        input_layout.addLayout(danmaku_layout)
        
        layout.addWidget(input_group)
        
        # 快速测试
        quick_group = QGroupBox("快速测试")
        quick_layout = QVBoxLayout(quick_group)
        
        quick_buttons_layout = QHBoxLayout()
        
        quick_fire_btn = QPushButton("火箭弹幕")
        quick_fire_btn.clicked.connect(lambda: self.quick_test("感谢老板的火箭，太给力了！"))
        quick_buttons_layout.addWidget(quick_fire_btn)
        
        quick_gift_btn = QPushButton("礼物弹幕")
        quick_gift_btn.clicked.connect(lambda: self.quick_test("谢谢大家的礼物支持"))
        quick_buttons_layout.addWidget(quick_gift_btn)
        
        quick_666_btn = QPushButton("666弹幕")
        quick_666_btn.clicked.connect(lambda: self.quick_test("主播666，继续加油！"))
        quick_buttons_layout.addWidget(quick_666_btn)
        
        quick_normal_btn = QPushButton("普通弹幕")
        quick_normal_btn.clicked.connect(lambda: self.quick_test("普通弹幕，没有关键词"))
        quick_buttons_layout.addWidget(quick_normal_btn)
        
        quick_layout.addLayout(quick_buttons_layout)
        
        layout.addWidget(quick_group)
        
        # 测试结果
        result_group = QGroupBox("测试结果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(200)
        self.result_text.setReadOnly(True)
        result_layout.addWidget(self.result_text)
        
        layout.addWidget(result_group)
        
        return tab
    
    def create_playlist_tab(self):
        """创建播放列表标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 播放列表表格
        playlist_group = QGroupBox("播放列表")
        playlist_layout = QVBoxLayout(playlist_group)
        
        self.playlist_table = QTableWidget()
        self.playlist_table.setColumnCount(5)
        self.playlist_table.setHorizontalHeaderLabels(["ID", "类型", "内容", "副视频", "状态"])
        playlist_layout.addWidget(self.playlist_table)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        refresh_playlist_btn = QPushButton("刷新播放列表")
        refresh_playlist_btn.clicked.connect(self.refresh_playlist)
        control_layout.addWidget(refresh_playlist_btn)
        
        play_selected_btn = QPushButton("播放选中项目")
        play_selected_btn.clicked.connect(self.play_selected)
        control_layout.addWidget(play_selected_btn)
        
        test_sub_video_btn = QPushButton("测试副视频切换")
        test_sub_video_btn.clicked.connect(self.test_sub_video_switch)
        control_layout.addWidget(test_sub_video_btn)
        
        clear_playlist_btn = QPushButton("清空播放列表")
        clear_playlist_btn.clicked.connect(self.clear_playlist)
        control_layout.addWidget(clear_playlist_btn)
        
        playlist_layout.addLayout(control_layout)
        
        layout.addWidget(playlist_group)
        
        return tab

    def connect_obs(self):
        """连接OBS"""
        host = self.obs_host_input.text().strip()
        port = self.obs_port_input.value()

        self.log_message(f"🔗 尝试连接OBS: {host}:{port}")

        if self.main_window:
            try:
                # 使用主程序的OBS控制器
                obs_controller = None
                if hasattr(self.main_window, 'playback_controller') and self.main_window.playback_controller:
                    obs_controller = self.main_window.playback_controller.obs_controller
                elif hasattr(self.main_window, 'obs_controller'):
                    obs_controller = self.main_window.obs_controller

                if obs_controller:
                    # 设置连接参数
                    obs_controller.host = host
                    obs_controller.port = port

                    # 尝试连接
                    if hasattr(obs_controller, 'connect'):
                        result = obs_controller.connect()
                        if result:
                            self.log_message("✅ OBS连接成功")
                            self.obs_status_label.setText("状态: 已连接")
                            self.connect_btn.setEnabled(False)
                            self.disconnect_btn.setEnabled(True)

                            # 自动刷新视频源
                            QTimer.singleShot(1000, self.refresh_obs_sources)
                        else:
                            self.log_message("❌ OBS连接失败")
                    else:
                        self.log_message("❌ OBS控制器没有连接方法")
                else:
                    self.log_message("❌ 无法获取OBS控制器")

            except Exception as e:
                self.log_message(f"❌ OBS连接异常: {e}")
                import traceback
                traceback.print_exc()

    def disconnect_obs(self):
        """断开OBS连接"""
        self.log_message("🔗 断开OBS连接")

        if self.main_window:
            try:
                obs_controller = None
                if hasattr(self.main_window, 'playback_controller') and self.main_window.playback_controller:
                    obs_controller = self.main_window.playback_controller.obs_controller
                elif hasattr(self.main_window, 'obs_controller'):
                    obs_controller = self.main_window.obs_controller

                if obs_controller and hasattr(obs_controller, 'disconnect'):
                    obs_controller.disconnect()

                self.log_message("✅ OBS已断开连接")
                self.obs_status_label.setText("状态: 未连接")
                self.connect_btn.setEnabled(True)
                self.disconnect_btn.setEnabled(False)
                self.sources_text.clear()
                self.source_combo.clear()
                self.video_source_combo.clear()

            except Exception as e:
                self.log_message(f"❌ 断开连接异常: {e}")

    def refresh_obs_sources(self):
        """刷新OBS视频源列表"""
        self.log_message("🔄 刷新OBS视频源列表")

        if self.main_window:
            try:
                obs_controller = None
                if hasattr(self.main_window, 'playback_controller') and self.main_window.playback_controller:
                    obs_controller = self.main_window.playback_controller.obs_controller
                elif hasattr(self.main_window, 'obs_controller'):
                    obs_controller = self.main_window.obs_controller

                if obs_controller and hasattr(obs_controller, 'connected') and obs_controller.connected:
                    if hasattr(obs_controller, 'get_source_list'):
                        sources = obs_controller.get_source_list()
                        if sources:
                            self.obs_sources = sources
                            self.log_message(f"✅ 获取到 {len(sources)} 个视频源")

                            # 更新显示
                            sources_text = f"OBS视频源列表 ({len(sources)} 个):\n"
                            for i, source in enumerate(sources, 1):
                                sources_text += f"{i}. {source}\n"
                            self.sources_text.setText(sources_text)

                            # 更新下拉框
                            self.source_combo.clear()
                            self.source_combo.addItems(sources)

                            self.video_source_combo.clear()
                            self.video_source_combo.addItems(sources)

                        else:
                            self.log_message("⚠️ 没有获取到视频源")
                            self.sources_text.setText("没有可用的视频源")
                    else:
                        self.log_message("❌ OBS控制器没有获取源列表方法")
                else:
                    self.log_message("❌ OBS未连接，无法获取视频源")

            except Exception as e:
                self.log_message(f"❌ 获取视频源失败: {e}")
                import traceback
                traceback.print_exc()

    def switch_to_source(self):
        """切换到指定视频源"""
        selected_source = self.source_combo.currentText()
        if not selected_source:
            QMessageBox.warning(self, "警告", "请选择要切换的视频源")
            return

        self.log_message(f"🔄 切换到视频源: {selected_source}")

        if self.main_window:
            try:
                # 使用主程序的副视频切换方法
                if hasattr(self.main_window, 'switch_to_sub_video_with_obs'):
                    result = self.main_window.switch_to_sub_video_with_obs(selected_source)
                    if result:
                        self.log_message(f"✅ 成功切换到视频源: {selected_source}")
                    else:
                        self.log_message(f"❌ 切换视频源失败: {selected_source}")
                else:
                    self.log_message("❌ 主程序没有视频源切换方法")

            except Exception as e:
                self.log_message(f"❌ 切换视频源异常: {e}")

    def refresh_config(self):
        """刷新副视频配置显示"""
        try:
            if self.main_window and hasattr(self.main_window, 'sub_video_manager'):
                sub_video_manager = self.main_window.sub_video_manager
                if hasattr(sub_video_manager, 'sub_videos'):
                    sub_videos = sub_video_manager.sub_videos

                    if isinstance(sub_videos, dict):
                        config_text = f"副视频配置 ({len(sub_videos)} 个关键词):\n"
                        for keyword, config in sub_videos.items():
                            if hasattr(config, 'video_source'):
                                # SubVideoItem对象
                                video_source = config.video_source
                                scripts_count = len(config.scripts) if hasattr(config, 'scripts') else 0
                            elif isinstance(config, dict):
                                # 字典格式
                                video_source = config.get('video_source', '未知')
                                scripts_count = len(config.get('scripts', []))
                            else:
                                video_source = '未知格式'
                                scripts_count = 0

                            config_text += f"• {keyword} → {video_source} ({scripts_count}个话术)\n"

                        self.config_text.setText(config_text)
                        self.log_message(f"✅ 副视频配置已刷新: {len(sub_videos)} 个关键词")
                    else:
                        self.config_text.setText(f"⚠️ 副视频数据格式异常: {type(sub_videos)}")
                else:
                    self.config_text.setText("❌ 副视频管理器没有数据")
            else:
                self.config_text.setText("❌ 无法访问副视频管理器")
        except Exception as e:
            self.config_text.setText(f"❌ 加载配置失败: {e}")
            self.log_message(f"❌ 刷新配置失败: {e}")

    def add_sub_video_config(self):
        """添加副视频配置"""
        keyword = self.keyword_input.text().strip()
        video_source = self.video_source_combo.currentText()
        script = self.script_input.text().strip()

        if not keyword:
            QMessageBox.warning(self, "警告", "请输入关键词")
            return

        if not video_source:
            QMessageBox.warning(self, "警告", "请选择视频源")
            return

        if not script:
            script = f"感谢{{nick}}的{keyword}支持！"

        self.log_message(f"➕ 添加副视频配置: {keyword} → {video_source}")

        try:
            # 读取现有配置
            config_file = Path("data/sub_videos.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {}

            # 添加新配置
            config[keyword] = {
                "keyword": keyword,
                "video_source": video_source,
                "scripts": [script],
                "used_scripts": [],
                "created_at": None,
                "last_triggered": None,
                "trigger_count": 0
            }

            # 保存配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            self.log_message(f"✅ 副视频配置已保存")

            # 清空输入
            self.keyword_input.clear()
            self.script_input.clear()

            # 刷新显示
            self.refresh_config()

            # 重新加载主程序的副视频配置
            if self.main_window and hasattr(self.main_window, 'sub_video_manager'):
                if hasattr(self.main_window.sub_video_manager, 'load_sub_videos'):
                    self.main_window.sub_video_manager.load_sub_videos()
                    self.log_message("✅ 主程序副视频配置已重新加载")

        except Exception as e:
            self.log_message(f"❌ 添加配置失败: {e}")
            QMessageBox.critical(self, "错误", f"添加配置失败: {e}")

    def test_trigger(self):
        """测试副视频触发"""
        danmaku_text = self.danmaku_input.text().strip()
        if not danmaku_text:
            QMessageBox.warning(self, "警告", "请输入测试弹幕")
            return

        self.log_message(f"🎬 测试弹幕: {danmaku_text}")
        self.result_text.append(f"\n🎬 测试弹幕: {danmaku_text}")

        if self.main_window and hasattr(self.main_window, 'check_sub_video_trigger'):
            try:
                result = self.main_window.check_sub_video_trigger(danmaku_text)
                if result:
                    self.log_message(f"✅ 触发副视频: {result}")
                    self.result_text.append(f"✅ 触发副视频: {result}")
                else:
                    self.log_message(f"⚪ 无副视频触发")
                    self.result_text.append(f"⚪ 无副视频触发")
            except Exception as e:
                self.log_message(f"❌ 测试失败: {e}")
                self.result_text.append(f"❌ 测试失败: {e}")
        else:
            self.log_message("❌ 主程序方法不可用")
            self.result_text.append("❌ 主程序方法不可用")

    def add_to_playlist(self):
        """添加弹幕到播放列表"""
        danmaku_text = self.danmaku_input.text().strip()
        if not danmaku_text:
            QMessageBox.warning(self, "警告", "请输入测试弹幕")
            return

        self.log_message(f"🎯 添加弹幕到播放列表: {danmaku_text}")
        self.result_text.append(f"\n🎯 添加弹幕到播放列表: {danmaku_text}")

        if self.main_window:
            try:
                # 检测副视频
                sub_video_result = None
                if hasattr(self.main_window, 'check_sub_video_trigger'):
                    sub_video_result = self.main_window.check_sub_video_trigger(danmaku_text)
                    self.log_message(f"🔍 副视频检测: {sub_video_result if sub_video_result else '无'}")
                    self.result_text.append(f"🔍 副视频检测: {sub_video_result if sub_video_result else '无'}")

                # 添加到播放列表
                if hasattr(self.main_window, 'add_danmaku_to_playlist'):
                    self.main_window.add_danmaku_to_playlist(danmaku_text, sub_video_result)
                    self.log_message(f"✅ 已添加到播放列表")
                    self.result_text.append(f"✅ 已添加到播放列表")

                    # 刷新显示
                    QTimer.singleShot(1000, self.refresh_playlist)
                else:
                    self.log_message(f"❌ 添加方法不可用")
                    self.result_text.append(f"❌ 添加方法不可用")

            except Exception as e:
                self.log_message(f"❌ 添加失败: {e}")
                self.result_text.append(f"❌ 添加失败: {e}")
                import traceback
                traceback.print_exc()
        else:
            self.log_message("❌ 主程序不可用")
            self.result_text.append("❌ 主程序不可用")

    def quick_test(self, text):
        """快速测试"""
        self.danmaku_input.setText(text)
        self.test_trigger()

    def refresh_playlist(self):
        """刷新播放列表显示"""
        if not self.main_window or not hasattr(self.main_window, 'playlist_items'):
            self.playlist_table.setRowCount(0)
            return

        playlist_items = self.main_window.playlist_items
        self.playlist_table.setRowCount(len(playlist_items))

        for i, item in enumerate(playlist_items):
            item_id = str(item.get('id', i+1))
            voice_type = item.get('voice_type', '未知')
            content = item.get('content', '')[:30] + ('...' if len(item.get('content', '')) > 30 else '')
            sub_video = item.get('sub_video', '无')
            status = item.get('status', '未知')

            self.playlist_table.setItem(i, 0, QTableWidgetItem(item_id))
            self.playlist_table.setItem(i, 1, QTableWidgetItem(voice_type))
            self.playlist_table.setItem(i, 2, QTableWidgetItem(content))
            self.playlist_table.setItem(i, 3, QTableWidgetItem(sub_video))
            self.playlist_table.setItem(i, 4, QTableWidgetItem(status))

        self.log_message(f"📋 播放列表已刷新: {len(playlist_items)} 个项目")

    def play_selected(self):
        """播放选中的项目"""
        current_row = self.playlist_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要播放的项目")
            return

        if not self.main_window or not hasattr(self.main_window, 'playlist_items'):
            return

        if current_row >= len(self.main_window.playlist_items):
            return

        item = self.main_window.playlist_items[current_row]
        content = item.get('content', '')
        sub_video = item.get('sub_video', '无')

        self.log_message(f"🎬 播放项目: {content[:30]}...")
        self.log_message(f"🎬 副视频: {sub_video}")

        # 测试副视频播放流程
        if sub_video and sub_video != '无':
            if hasattr(self.main_window, 'handle_sub_video_playback'):
                try:
                    result = self.main_window.handle_sub_video_playback(item)
                    if result:
                        self.log_message(f"✅ 副视频播放流程启动成功")
                    else:
                        self.log_message(f"⚠️ 副视频播放流程启动失败")
                except Exception as e:
                    self.log_message(f"❌ 副视频播放流程异常: {e}")
            else:
                self.log_message(f"❌ 副视频播放方法不可用")
        else:
            self.log_message(f"⚪ 该项目无副视频")

    def test_sub_video_switch(self):
        """测试副视频切换"""
        current_row = self.playlist_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要测试的项目")
            return

        if not self.main_window or not hasattr(self.main_window, 'playlist_items'):
            return

        if current_row >= len(self.main_window.playlist_items):
            return

        item = self.main_window.playlist_items[current_row]
        sub_video = item.get('sub_video', '无')

        if sub_video and sub_video != '无':
            self.log_message(f"🔄 测试副视频切换: {sub_video}")

            # 切换到副视频
            if hasattr(self.main_window, 'switch_to_sub_video_with_obs'):
                try:
                    result = self.main_window.switch_to_sub_video_with_obs(sub_video)
                    if result:
                        self.log_message(f"✅ 成功切换到副视频: {sub_video}")

                        # 3秒后切换回主视频
                        QTimer.singleShot(3000, self.switch_back_to_main)
                    else:
                        self.log_message(f"❌ 切换到副视频失败: {sub_video}")
                except Exception as e:
                    self.log_message(f"❌ 副视频切换异常: {e}")
            else:
                self.log_message("❌ 副视频切换方法不可用")
        else:
            QMessageBox.information(self, "提示", "选中的项目没有副视频")

    def switch_back_to_main(self):
        """切换回主视频"""
        self.log_message("🔄 切换回主视频")

        if self.main_window and hasattr(self.main_window, 'switch_back_to_main_video_with_obs'):
            try:
                result = self.main_window.switch_back_to_main_video_with_obs()
                if result:
                    self.log_message("✅ 成功切换回主视频")
                else:
                    self.log_message("❌ 切换回主视频失败")
            except Exception as e:
                self.log_message(f"❌ 切换回主视频异常: {e}")

    def clear_playlist(self):
        """清空播放列表"""
        if self.main_window and hasattr(self.main_window, 'playlist_items'):
            self.main_window.playlist_items.clear()
            self.refresh_playlist()
            self.log_message(f"🗑️ 播放列表已清空")

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.result_text.clear()

    def log_message(self, message):
        """记录日志"""
        self.log_text.append(message)
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
        print(message)  # 同时输出到控制台

def main():
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName("完整副视频测试程序")
    app.setApplicationVersion("1.0")

    window = CompleteSubVideoTestWindow()
    window.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
