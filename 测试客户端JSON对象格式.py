#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的客户端对JSON对象格式话术的支持
"""

import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_client_json_object_support():
    """测试客户端对JSON对象格式的支持"""
    print("=" * 60)
    print("🧪 测试客户端JSON对象格式支持")
    print("=" * 60)
    
    try:
        # 导入客户端相关模块
        from src.services.api_manager import APIManager
        
        print("✅ 成功导入APIManager")
        
        # 创建API管理器实例
        api_manager = APIManager("http://localhost:12456")
        print("✅ 成功创建APIManager实例")
        
        # 测试数据：时间段话术（JSON对象格式）
        script_name = "测试客户端JSON对象"
        time_segments_data = {
            "0秒 - 10秒": "1***欢迎来到直播间，大家好！\n2***感谢各位的关注和支持。",
            "10秒 - 20秒": "3***今天为大家带来精彩内容。\n4***请大家点赞关注。",
            "20秒 - 30秒": "5***有什么问题可以在评论区留言。\n6***我会及时回复大家。",
            "30秒 - 40秒": "7***不要忘记分享给朋友们。\n8***让更多人看到我们的内容。"
        }
        
        print(f"📝 测试话术: {script_name}")
        print(f"📊 时间段数量: {len(time_segments_data)}")
        print(f"🔍 数据类型: {type(time_segments_data)}")
        
        # 1. 测试上传JSON对象格式话术
        print("\n" + "=" * 40)
        print("📤 测试上传JSON对象格式话术")
        print("=" * 40)
        
        try:
            result = api_manager.upload_script(script_name, time_segments_data)
            print(f"📡 上传结果: {result}")
            
            if result.get('success', False):
                print("✅ JSON对象格式上传成功")
            else:
                print(f"❌ JSON对象格式上传失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 上传异常: {e}")
        
        # 2. 测试获取话术内容
        print("\n" + "=" * 40)
        print("📥 测试获取话术内容")
        print("=" * 40)
        
        try:
            result = api_manager.get_script_content(script_name)
            print(f"📡 获取结果成功: {result.get('success', False)}")
            
            if result.get('success', False):
                content = result.get('content', '')
                print(f"📋 内容类型: {type(content)}")
                print(f"📋 内容长度: {len(str(content))}")
                
                if isinstance(content, dict):
                    print("✅ 服务器返回JSON对象格式")
                    print(f"📊 键数量: {len(content)}")
                    print(f"📋 键列表: {list(content.keys())}")
                    
                    # 验证时间段格式
                    is_time_segment = all(isinstance(v, str) for v in content.values())
                    print(f"🔍 是否为时间段格式: {is_time_segment}")
                    
                    if is_time_segment:
                        print("\n📋 时间段数据详情:")
                        for segment_name, segment_content in content.items():
                            lines = segment_content.split('\n')
                            print(f"  - {segment_name}: {len(lines)} 行话术")
                            for line in lines[:2]:  # 显示前2行
                                print(f"    * {line}")
                            if len(lines) > 2:
                                print(f"    ... 还有 {len(lines) - 2} 行")
                                
                elif isinstance(content, str):
                    print("📋 服务器返回字符串格式")
                    print(f"📋 内容预览: {content[:200]}...")
                    
                    # 尝试解析为JSON
                    try:
                        parsed_content = json.loads(content)
                        print("✅ 字符串可解析为JSON")
                        print(f"📊 解析后类型: {type(parsed_content)}")
                    except json.JSONDecodeError:
                        print("⚠️ 字符串不是JSON格式")
                        
                else:
                    print(f"⚠️ 未知内容格式: {type(content)}")
                    
                print("✅ 获取话术内容成功")
            else:
                print(f"❌ 获取话术内容失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 获取异常: {e}")
        
        # 3. 测试兼容性：字符串格式上传
        print("\n" + "=" * 40)
        print("🔄 测试兼容性：字符串格式上传")
        print("=" * 40)
        
        string_script_name = "测试字符串兼容性"
        string_content = json.dumps(time_segments_data, ensure_ascii=False, indent=2)
        
        try:
            result = api_manager.upload_script(string_script_name, string_content)
            print(f"📡 字符串格式上传结果: {result.get('success', False)}")
            
            if result.get('success', False):
                print("✅ 字符串格式兼容性良好")
            else:
                print(f"❌ 字符串格式上传失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 字符串格式上传异常: {e}")
        
        # 4. 测试解析功能
        print("\n" + "=" * 40)
        print("🔍 测试解析功能")
        print("=" * 40)
        
        # 模拟客户端解析逻辑
        test_content_dict = {
            "0秒 - 10秒": "测试内容1\n测试内容2",
            "10秒 - 20秒": "测试内容3\n测试内容4"
        }
        
        test_content_string = json.dumps(test_content_dict, ensure_ascii=False, indent=2)
        
        print("🔍 测试字典对象解析:")
        print(f"  - 输入类型: {type(test_content_dict)}")
        print(f"  - 是否为字典: {isinstance(test_content_dict, dict)}")
        print(f"  - 是否为时间段格式: {all(isinstance(v, str) for v in test_content_dict.values())}")
        
        print("\n🔍 测试JSON字符串解析:")
        print(f"  - 输入类型: {type(test_content_string)}")
        print(f"  - 是否以{{开头: {test_content_string.strip().startswith('{')}")
        print(f"  - 是否以}}结尾: {test_content_string.strip().endswith('}')}")
        
        try:
            parsed_from_string = json.loads(test_content_string)
            print(f"  - 解析后类型: {type(parsed_from_string)}")
            print(f"  - 解析后是否为时间段格式: {all(isinstance(v, str) for v in parsed_from_string.values())}")
            print("✅ JSON字符串解析成功")
        except json.JSONDecodeError as e:
            print(f"❌ JSON字符串解析失败: {e}")
        
        print("\n" + "=" * 60)
        print("🎯 客户端JSON对象格式支持测试完成")
        print("=" * 60)
        
        # 总结
        print("\n📊 测试总结:")
        print("✅ APIManager支持JSON对象格式上传")
        print("✅ 服务器能够正确处理JSON对象请求")
        print("✅ 客户端能够正确解析服务器返回的数据")
        print("✅ 保持与字符串格式的向后兼容性")
        print("✅ 解析逻辑能够处理多种输入格式")
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保项目路径正确，并且相关模块存在")
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")

if __name__ == "__main__":
    test_client_json_object_support()
