"""
AI Broadcaster v2 - 网络服务
提供网络通信和API服务
"""

import asyncio
import json
from typing import Dict, Any, Optional, List
from pathlib import Path

try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False
    # 检查是否禁用警告
    import os
    if not os.environ.get('DISABLE_AIOHTTP_WARNING'):
        print("⚠️  aiohttp未安装，网络服务功能受限")

from .logging_service import create_logger
from .error_handler import handle_exceptions


class NetworkService:
    """网络服务管理器"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.logger = create_logger("network_service")
        self.config = config or self._load_default_config()
        self.session: Optional[aiohttp.ClientSession] = None
        self.server = None

    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认网络配置"""
        return {
            'timeout': 30,
            'retry_attempts': 3,
            'api_port': 8000,
            'websocket_port': 8001
        }

    async def initialize(self):
        """初始化网络服务"""
        try:
            if not AIOHTTP_AVAILABLE:
                self.logger.log_warning("aiohttp未安装，网络服务功能受限")
                return True

            # 创建HTTP客户端会话
            timeout = aiohttp.ClientTimeout(total=self.config.get('timeout', 30))
            self.session = aiohttp.ClientSession(timeout=timeout)

            self.logger.log_success("网络服务初始化完成")
            return True

        except Exception as e:
            self.logger.log_failure(f"网络服务初始化失败: {e}")
            return False

    async def shutdown(self):
        """关闭网络服务"""
        try:
            if self.session:
                await self.session.close()
                self.session = None

            if self.server:
                self.server.close()
                await self.server.wait_closed()
                self.server = None

            self.logger.log_success("网络服务已关闭")

        except Exception as e:
            self.logger.log_failure(f"网络服务关闭失败: {e}")

    @handle_exceptions("NetworkService")
    async def make_request(
        self,
        method: str,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """
        发送HTTP请求

        Args:
            method: HTTP方法
            url: 请求URL
            data: 请求数据
            headers: 请求头
            **kwargs: 其他参数

        Returns:
            响应数据
        """
        if not self.session:
            await self.initialize()

        retry_attempts = self.config.get('retry_attempts', 3)

        for attempt in range(retry_attempts):
            try:
                async with self.session.request(
                    method,
                    url,
                    json=data,
                    headers=headers,
                    **kwargs
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        self.logger.warning(f"请求失败，状态码: {response.status}")

            except Exception as e:
                self.logger.warning(f"请求尝试 {attempt + 1} 失败: {e}")
                if attempt == retry_attempts - 1:
                    raise
                await asyncio.sleep(1)  # 重试前等待

        return None

    async def get(self, url: str, **kwargs) -> Optional[Dict[str, Any]]:
        """发送GET请求"""
        return await self.make_request('GET', url, **kwargs)

    async def post(self, url: str, data: Optional[Dict[str, Any]] = None, **kwargs) -> Optional[Dict[str, Any]]:
        """发送POST请求"""
        return await self.make_request('POST', url, data=data, **kwargs)

    async def put(self, url: str, data: Optional[Dict[str, Any]] = None, **kwargs) -> Optional[Dict[str, Any]]:
        """发送PUT请求"""
        return await self.make_request('PUT', url, data=data, **kwargs)

    async def delete(self, url: str, **kwargs) -> Optional[Dict[str, Any]]:
        """发送DELETE请求"""
        return await self.make_request('DELETE', url, **kwargs)

    @handle_exceptions("NetworkService")
    async def download_file(self, url: str, file_path: str) -> bool:
        """
        下载文件

        Args:
            url: 文件URL
            file_path: 保存路径

        Returns:
            是否下载成功
        """
        try:
            if not self.session:
                await self.initialize()

            async with self.session.get(url) as response:
                if response.status == 200:
                    file_path_obj = Path(file_path)
                    file_path_obj.parent.mkdir(parents=True, exist_ok=True)

                    with open(file_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)

                    self.logger.log_success(f"文件下载完成: {file_path}")
                    return True
                else:
                    self.logger.log_failure(f"文件下载失败，状态码: {response.status}")
                    return False

        except Exception as e:
            self.logger.log_failure(f"文件下载异常: {e}")
            return False

    @handle_exceptions("NetworkService")
    async def upload_file(self, url: str, file_path: str, field_name: str = "file") -> Optional[Dict[str, Any]]:
        """
        上传文件

        Args:
            url: 上传URL
            file_path: 文件路径
            field_name: 表单字段名

        Returns:
            响应数据
        """
        try:
            if not self.session:
                await self.initialize()

            with open(file_path, 'rb') as f:
                data = aiohttp.FormData()
                data.add_field(field_name, f, filename=Path(file_path).name)

                async with self.session.post(url, data=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        self.logger.log_success(f"文件上传完成: {file_path}")
                        return result
                    else:
                        self.logger.log_failure(f"文件上传失败，状态码: {response.status}")
                        return None

        except Exception as e:
            self.logger.log_failure(f"文件上传异常: {e}")
            return None
