('C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\build\\AI主播系统_最终版\\PYZ-00.pyz',
 [('PyQt5',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__',
   'D:\\Program Files (x86)\\python32\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\Program Files (x86)\\python32\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Program Files (x86)\\python32\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\Program Files (x86)\\python32\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Program Files (x86)\\python32\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime',
   'D:\\Program Files (x86)\\python32\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'D:\\Program Files (x86)\\python32\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\Program Files (x86)\\python32\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_sounddevice',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\_sounddevice.py',
   'PYMODULE'),
  ('_sounddevice_data',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_sounddevice_data\\__init__.py',
   'PYMODULE'),
  ('_soundfile',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\_soundfile.py',
   'PYMODULE'),
  ('_soundfile_data',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_soundfile_data\\__init__.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\Program Files (x86)\\python32\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\Program Files (x86)\\python32\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('aiohappyeyeballs',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohappyeyeballs\\__init__.py',
   'PYMODULE'),
  ('aiohappyeyeballs._staggered',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohappyeyeballs\\_staggered.py',
   'PYMODULE'),
  ('aiohappyeyeballs.impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohappyeyeballs\\impl.py',
   'PYMODULE'),
  ('aiohappyeyeballs.types',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohappyeyeballs\\types.py',
   'PYMODULE'),
  ('aiohappyeyeballs.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohappyeyeballs\\utils.py',
   'PYMODULE'),
  ('aiohttp',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\__init__.py',
   'PYMODULE'),
  ('aiohttp._websocket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\__init__.py',
   'PYMODULE'),
  ('aiohttp._websocket.helpers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\helpers.py',
   'PYMODULE'),
  ('aiohttp._websocket.models',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\models.py',
   'PYMODULE'),
  ('aiohttp._websocket.reader',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\reader.py',
   'PYMODULE'),
  ('aiohttp._websocket.reader_py',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\reader_py.py',
   'PYMODULE'),
  ('aiohttp._websocket.writer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\writer.py',
   'PYMODULE'),
  ('aiohttp.abc',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\abc.py',
   'PYMODULE'),
  ('aiohttp.base_protocol',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\base_protocol.py',
   'PYMODULE'),
  ('aiohttp.client',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\client.py',
   'PYMODULE'),
  ('aiohttp.client_exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_exceptions.py',
   'PYMODULE'),
  ('aiohttp.client_middleware_digest_auth',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_middleware_digest_auth.py',
   'PYMODULE'),
  ('aiohttp.client_middlewares',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_middlewares.py',
   'PYMODULE'),
  ('aiohttp.client_proto',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_proto.py',
   'PYMODULE'),
  ('aiohttp.client_reqrep',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_reqrep.py',
   'PYMODULE'),
  ('aiohttp.client_ws',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_ws.py',
   'PYMODULE'),
  ('aiohttp.compression_utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\compression_utils.py',
   'PYMODULE'),
  ('aiohttp.connector',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\connector.py',
   'PYMODULE'),
  ('aiohttp.cookiejar',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\cookiejar.py',
   'PYMODULE'),
  ('aiohttp.formdata',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\formdata.py',
   'PYMODULE'),
  ('aiohttp.hdrs',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\hdrs.py',
   'PYMODULE'),
  ('aiohttp.helpers',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\helpers.py',
   'PYMODULE'),
  ('aiohttp.http',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\http.py',
   'PYMODULE'),
  ('aiohttp.http_exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\http_exceptions.py',
   'PYMODULE'),
  ('aiohttp.http_parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\http_parser.py',
   'PYMODULE'),
  ('aiohttp.http_websocket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\http_websocket.py',
   'PYMODULE'),
  ('aiohttp.http_writer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\http_writer.py',
   'PYMODULE'),
  ('aiohttp.log',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\log.py',
   'PYMODULE'),
  ('aiohttp.multipart',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\multipart.py',
   'PYMODULE'),
  ('aiohttp.payload',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\payload.py',
   'PYMODULE'),
  ('aiohttp.payload_streamer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\payload_streamer.py',
   'PYMODULE'),
  ('aiohttp.resolver',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\resolver.py',
   'PYMODULE'),
  ('aiohttp.streams',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\streams.py',
   'PYMODULE'),
  ('aiohttp.tcp_helpers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\tcp_helpers.py',
   'PYMODULE'),
  ('aiohttp.tracing',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\tracing.py',
   'PYMODULE'),
  ('aiohttp.typedefs',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\typedefs.py',
   'PYMODULE'),
  ('aiohttp.web',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\web.py',
   'PYMODULE'),
  ('aiohttp.web_app',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\web_app.py',
   'PYMODULE'),
  ('aiohttp.web_exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_exceptions.py',
   'PYMODULE'),
  ('aiohttp.web_fileresponse',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_fileresponse.py',
   'PYMODULE'),
  ('aiohttp.web_log',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\web_log.py',
   'PYMODULE'),
  ('aiohttp.web_middlewares',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_middlewares.py',
   'PYMODULE'),
  ('aiohttp.web_protocol',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_protocol.py',
   'PYMODULE'),
  ('aiohttp.web_request',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_request.py',
   'PYMODULE'),
  ('aiohttp.web_response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_response.py',
   'PYMODULE'),
  ('aiohttp.web_routedef',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_routedef.py',
   'PYMODULE'),
  ('aiohttp.web_runner',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_runner.py',
   'PYMODULE'),
  ('aiohttp.web_server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_server.py',
   'PYMODULE'),
  ('aiohttp.web_urldispatcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_urldispatcher.py',
   'PYMODULE'),
  ('aiohttp.web_ws',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\web_ws.py',
   'PYMODULE'),
  ('aiohttp.worker',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\worker.py',
   'PYMODULE'),
  ('aiosignal',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiosignal\\__init__.py',
   'PYMODULE'),
  ('argparse',
   'D:\\Program Files (x86)\\python32\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast', 'D:\\Program Files (x86)\\python32\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('backports',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\Program Files (x86)\\python32\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\Program Files (x86)\\python32\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\Program Files (x86)\\python32\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\Program Files (x86)\\python32\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar',
   'D:\\Program Files (x86)\\python32\\Lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'D:\\Program Files (x86)\\python32\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\Program Files (x86)\\python32\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Program Files (x86)\\python32\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\Program Files (x86)\\python32\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Program Files (x86)\\python32\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Program Files (x86)\\python32\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Program Files (x86)\\python32\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Program Files (x86)\\python32\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Program Files (x86)\\python32\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\Program Files (x86)\\python32\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Program Files (x86)\\python32\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\Program Files (x86)\\python32\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'D:\\Program Files (x86)\\python32\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\Program Files (x86)\\python32\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'D:\\Program Files (x86)\\python32\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal', 'D:\\Program Files (x86)\\python32\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\Program Files (x86)\\python32\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\Program Files (x86)\\python32\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\Program Files (x86)\\python32\\Lib\\doctest.py', 'PYMODULE'),
  ('email',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fileinput',
   'D:\\Program Files (x86)\\python32\\Lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\Program Files (x86)\\python32\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions',
   'D:\\Program Files (x86)\\python32\\Lib\\fractions.py',
   'PYMODULE'),
  ('frozenlist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\frozenlist\\__init__.py',
   'PYMODULE'),
  ('ftplib', 'D:\\Program Files (x86)\\python32\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Program Files (x86)\\python32\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Program Files (x86)\\python32\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Program Files (x86)\\python32\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\Program Files (x86)\\python32\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\Program Files (x86)\\python32\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Program Files (x86)\\python32\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Program Files (x86)\\python32\\Lib\\hmac.py', 'PYMODULE'),
  ('html',
   'D:\\Program Files (x86)\\python32\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\Program Files (x86)\\python32\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'D:\\Program Files (x86)\\python32\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'D:\\Program Files (x86)\\python32\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Program Files (x86)\\python32\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\Program Files (x86)\\python32\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'D:\\Program Files (x86)\\python32\\Lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\Program Files (x86)\\python32\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress',
   'D:\\Program Files (x86)\\python32\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json',
   'D:\\Program Files (x86)\\python32\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Program Files (x86)\\python32\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Program Files (x86)\\python32\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Program Files (x86)\\python32\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('jwt',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\__init__.py',
   'PYMODULE'),
  ('jwt.algorithms',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\algorithms.py',
   'PYMODULE'),
  ('jwt.api_jwk',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\api_jwk.py',
   'PYMODULE'),
  ('jwt.api_jws',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\api_jws.py',
   'PYMODULE'),
  ('jwt.api_jwt',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\api_jwt.py',
   'PYMODULE'),
  ('jwt.exceptions',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\exceptions.py',
   'PYMODULE'),
  ('jwt.jwk_set_cache',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jwt\\jwk_set_cache.py',
   'PYMODULE'),
  ('jwt.jwks_client',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\jwks_client.py',
   'PYMODULE'),
  ('jwt.types',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\types.py',
   'PYMODULE'),
  ('jwt.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\utils.py',
   'PYMODULE'),
  ('jwt.warnings',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\warnings.py',
   'PYMODULE'),
  ('logging',
   'D:\\Program Files (x86)\\python32\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   'D:\\Program Files (x86)\\python32\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma', 'D:\\Program Files (x86)\\python32\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes',
   'D:\\Program Files (x86)\\python32\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('multidict',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\multidict\\__init__.py',
   'PYMODULE'),
  ('multidict._abc',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\multidict\\_abc.py',
   'PYMODULE'),
  ('multidict._compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\multidict\\_compat.py',
   'PYMODULE'),
  ('multidict._multidict_py',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\multidict\\_multidict_py.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\Program Files (x86)\\python32\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\Program Files (x86)\\python32\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'D:\\Program Files (x86)\\python32\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\Program Files (x86)\\python32\\Lib\\opcode.py', 'PYMODULE'),
  ('packaging',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'D:\\Program Files (x86)\\python32\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\Program Files (x86)\\python32\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\Program Files (x86)\\python32\\Lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\Program Files (x86)\\python32\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform',
   'D:\\Program Files (x86)\\python32\\Lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'D:\\Program Files (x86)\\python32\\Lib\\plistlib.py',
   'PYMODULE'),
  ('pprint', 'D:\\Program Files (x86)\\python32\\Lib\\pprint.py', 'PYMODULE'),
  ('propcache',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\propcache\\__init__.py',
   'PYMODULE'),
  ('propcache._helpers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\propcache\\_helpers.py',
   'PYMODULE'),
  ('propcache._helpers_py',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\propcache\\_helpers_py.py',
   'PYMODULE'),
  ('propcache.api',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\propcache\\api.py',
   'PYMODULE'),
  ('psutil',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile',
   'D:\\Program Files (x86)\\python32\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc', 'D:\\Program Files (x86)\\python32\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\Program Files (x86)\\python32\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Program Files (x86)\\python32\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygame',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pygame\\__init__.py',
   'PYMODULE'),
  ('pygame.colordict',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pygame\\colordict.py',
   'PYMODULE'),
  ('pygame.cursors',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pygame\\cursors.py',
   'PYMODULE'),
  ('pygame.fastevent',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pygame\\fastevent.py',
   'PYMODULE'),
  ('pygame.ftfont',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pygame\\ftfont.py',
   'PYMODULE'),
  ('pygame.macosx',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pygame\\macosx.py',
   'PYMODULE'),
  ('pygame.pkgdata',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pygame\\pkgdata.py',
   'PYMODULE'),
  ('pygame.sndarray',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pygame\\sndarray.py',
   'PYMODULE'),
  ('pygame.sprite',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pygame\\sprite.py',
   'PYMODULE'),
  ('pygame.surfarray',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pygame\\surfarray.py',
   'PYMODULE'),
  ('pygame.sysfont',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pygame\\sysfont.py',
   'PYMODULE'),
  ('pygame.threads',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pygame\\threads\\__init__.py',
   'PYMODULE'),
  ('pygame.version',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pygame\\version.py',
   'PYMODULE'),
  ('queue', 'D:\\Program Files (x86)\\python32\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Program Files (x86)\\python32\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Program Files (x86)\\python32\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'D:\\Program Files (x86)\\python32\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'D:\\Program Files (x86)\\python32\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\Program Files (x86)\\python32\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors',
   'D:\\Program Files (x86)\\python32\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\Program Files (x86)\\python32\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Program Files (x86)\\python32\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Program Files (x86)\\python32\\Lib\\signal.py', 'PYMODULE'),
  ('simple_audio_player',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\simple_audio_player.py',
   'PYMODULE'),
  ('site', 'D:\\Program Files (x86)\\python32\\Lib\\site.py', 'PYMODULE'),
  ('smtplib', 'D:\\Program Files (x86)\\python32\\Lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'D:\\Program Files (x86)\\python32\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\Program Files (x86)\\python32\\Lib\\socketserver.py',
   'PYMODULE'),
  ('sounddevice',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\sounddevice.py',
   'PYMODULE'),
  ('soundfile',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\soundfile.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\Program Files (x86)\\python32\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'D:\\Program Files (x86)\\python32\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\Program Files (x86)\\python32\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\Program Files (x86)\\python32\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('src',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\__init__.py',
   'PYMODULE'),
  ('src.app',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\app\\__init__.py',
   'PYMODULE'),
  ('src.app.application',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\app\\application.py',
   'PYMODULE'),
  ('src.app.event_bus',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\app\\event_bus.py',
   'PYMODULE'),
  ('src.core',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\__init__.py',
   'PYMODULE'),
  ('src.core.auth',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\auth\\__init__.py',
   'PYMODULE'),
  ('src.core.auth.machine_code',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\auth\\machine_code.py',
   'PYMODULE'),
  ('src.core.auth.user_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\auth\\user_manager.py',
   'PYMODULE'),
  ('src.core.obs',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\obs\\__init__.py',
   'PYMODULE'),
  ('src.core.obs.obs_controller',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\obs\\obs_controller.py',
   'PYMODULE'),
  ('src.core.obs.scene_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\obs\\scene_manager.py',
   'PYMODULE'),
  ('src.core.obs.source_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\obs\\source_manager.py',
   'PYMODULE'),
  ('src.core.playback',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\playback\\__init__.py',
   'PYMODULE'),
  ('src.core.playback.audio_player',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\playback\\audio_player.py',
   'PYMODULE'),
  ('src.core.playback.dual_video_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\playback\\dual_video_manager.py',
   'PYMODULE'),
  ('src.core.playback.playback_controller',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\playback\\playback_controller.py',
   'PYMODULE'),
  ('src.core.playback.playlist_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\playback\\playlist_manager.py',
   'PYMODULE'),
  ('src.core.playback.priority_playlist_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\playback\\priority_playlist_manager.py',
   'PYMODULE'),
  ('src.core.playback_controller',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\playback_controller.py',
   'PYMODULE'),
  ('src.core.sub_video_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\sub_video_manager.py',
   'PYMODULE'),
  ('src.core.system_settings',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\system_settings.py',
   'PYMODULE'),
  ('src.core.time_report_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\time_report_manager.py',
   'PYMODULE'),
  ('src.core.time_segment_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\time_segment_manager.py',
   'PYMODULE'),
  ('src.core.user',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\user\\__init__.py',
   'PYMODULE'),
  ('src.core.user.auth_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\user\\auth_manager.py',
   'PYMODULE'),
  ('src.core.user.payment_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\user\\payment_manager.py',
   'PYMODULE'),
  ('src.core.user.user_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\user\\user_manager.py',
   'PYMODULE'),
  ('src.core.voice',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\voice\\__init__.py',
   'PYMODULE'),
  ('src.core.voice.speaker_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\voice\\speaker_manager.py',
   'PYMODULE'),
  ('src.core.voice.voice_downloader',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\voice\\voice_downloader.py',
   'PYMODULE'),
  ('src.core.voice.voice_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\core\\voice\\voice_manager.py',
   'PYMODULE'),
  ('src.data',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\data\\__init__.py',
   'PYMODULE'),
  ('src.data.config_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\data\\config_manager.py',
   'PYMODULE'),
  ('src.data.database_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\data\\database_manager.py',
   'PYMODULE'),
  ('src.data.file_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\data\\file_manager.py',
   'PYMODULE'),
  ('src.services',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\services\\__init__.py',
   'PYMODULE'),
  ('src.services.api_client',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\services\\api_client.py',
   'PYMODULE'),
  ('src.services.api_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\services\\api_manager.py',
   'PYMODULE'),
  ('src.services.audio_player',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\services\\audio_player.py',
   'PYMODULE'),
  ('src.services.danmaku_manager',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\services\\danmaku_manager.py',
   'PYMODULE'),
  ('src.services.danmaku_service',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\services\\danmaku_service.py',
   'PYMODULE'),
  ('src.services.dialogue_service',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\services\\dialogue_service.py',
   'PYMODULE'),
  ('src.services.error_handler',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\services\\error_handler.py',
   'PYMODULE'),
  ('src.services.logging_service',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\services\\logging_service.py',
   'PYMODULE'),
  ('src.services.network_service',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\services\\network_service.py',
   'PYMODULE'),
  ('src.services.obs_controller',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\services\\obs_controller.py',
   'PYMODULE'),
  ('src.services.obs_service',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\services\\obs_service.py',
   'PYMODULE'),
  ('src.services.script_service',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\services\\script_service.py',
   'PYMODULE'),
  ('src.services.update_service',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\services\\update_service.py',
   'PYMODULE'),
  ('src.services.voice_service',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\services\\voice_service.py',
   'PYMODULE'),
  ('src.services.voice_synthesis',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\services\\voice_synthesis.py',
   'PYMODULE'),
  ('src.utils',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\utils\\__init__.py',
   'PYMODULE'),
  ('src.utils.file_utils',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\utils\\file_utils.py',
   'PYMODULE'),
  ('src.utils.time_utils',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\utils\\time_utils.py',
   'PYMODULE'),
  ('src.utils.validation',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\src\\utils\\validation.py',
   'PYMODULE'),
  ('ssl', 'D:\\Program Files (x86)\\python32\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'D:\\Program Files (x86)\\python32\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'D:\\Program Files (x86)\\python32\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'D:\\Program Files (x86)\\python32\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\Program Files (x86)\\python32\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'D:\\Program Files (x86)\\python32\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile', 'D:\\Program Files (x86)\\python32\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile',
   'D:\\Program Files (x86)\\python32\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'D:\\Program Files (x86)\\python32\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'D:\\Program Files (x86)\\python32\\Lib\\threading.py',
   'PYMODULE'),
  ('token', 'D:\\Program Files (x86)\\python32\\Lib\\token.py', 'PYMODULE'),
  ('tokenize',
   'D:\\Program Files (x86)\\python32\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'D:\\Program Files (x86)\\python32\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'D:\\Program Files (x86)\\python32\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'D:\\Program Files (x86)\\python32\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'D:\\Program Files (x86)\\python32\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\Program Files (x86)\\python32\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\Program Files (x86)\\python32\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\Program Files (x86)\\python32\\Lib\\typing.py', 'PYMODULE'),
  ('unittest',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Program Files (x86)\\python32\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Program Files (x86)\\python32\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Program Files (x86)\\python32\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Program Files (x86)\\python32\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\Program Files (x86)\\python32\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid', 'D:\\Program Files (x86)\\python32\\Lib\\uuid.py', 'PYMODULE'),
  ('wave', 'D:\\Program Files (x86)\\python32\\Lib\\wave.py', 'PYMODULE'),
  ('webbrowser',
   'D:\\Program Files (x86)\\python32\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('websockets',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\__init__.py',
   'PYMODULE'),
  ('websockets.__main__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\__main__.py',
   'PYMODULE'),
  ('websockets.asyncio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\__init__.py',
   'PYMODULE'),
  ('websockets.asyncio.async_timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\async_timeout.py',
   'PYMODULE'),
  ('websockets.asyncio.client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\client.py',
   'PYMODULE'),
  ('websockets.asyncio.compatibility',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\compatibility.py',
   'PYMODULE'),
  ('websockets.asyncio.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\connection.py',
   'PYMODULE'),
  ('websockets.asyncio.messages',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\messages.py',
   'PYMODULE'),
  ('websockets.asyncio.router',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\router.py',
   'PYMODULE'),
  ('websockets.asyncio.server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\server.py',
   'PYMODULE'),
  ('websockets.auth',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websockets\\auth.py',
   'PYMODULE'),
  ('websockets.cli',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websockets\\cli.py',
   'PYMODULE'),
  ('websockets.client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\client.py',
   'PYMODULE'),
  ('websockets.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\connection.py',
   'PYMODULE'),
  ('websockets.datastructures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\datastructures.py',
   'PYMODULE'),
  ('websockets.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\exceptions.py',
   'PYMODULE'),
  ('websockets.extensions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\extensions\\__init__.py',
   'PYMODULE'),
  ('websockets.extensions.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\extensions\\base.py',
   'PYMODULE'),
  ('websockets.extensions.permessage_deflate',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\extensions\\permessage_deflate.py',
   'PYMODULE'),
  ('websockets.frames',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\frames.py',
   'PYMODULE'),
  ('websockets.headers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\headers.py',
   'PYMODULE'),
  ('websockets.http',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websockets\\http.py',
   'PYMODULE'),
  ('websockets.http11',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\http11.py',
   'PYMODULE'),
  ('websockets.imports',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\imports.py',
   'PYMODULE'),
  ('websockets.legacy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.auth',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\auth.py',
   'PYMODULE'),
  ('websockets.legacy.client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\client.py',
   'PYMODULE'),
  ('websockets.legacy.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\exceptions.py',
   'PYMODULE'),
  ('websockets.legacy.framing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\framing.py',
   'PYMODULE'),
  ('websockets.legacy.handshake',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\handshake.py',
   'PYMODULE'),
  ('websockets.legacy.http',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\http.py',
   'PYMODULE'),
  ('websockets.legacy.protocol',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\protocol.py',
   'PYMODULE'),
  ('websockets.legacy.server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\server.py',
   'PYMODULE'),
  ('websockets.protocol',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\protocol.py',
   'PYMODULE'),
  ('websockets.server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\server.py',
   'PYMODULE'),
  ('websockets.streams',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\streams.py',
   'PYMODULE'),
  ('websockets.sync',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\__init__.py',
   'PYMODULE'),
  ('websockets.sync.client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\client.py',
   'PYMODULE'),
  ('websockets.sync.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\connection.py',
   'PYMODULE'),
  ('websockets.sync.messages',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\messages.py',
   'PYMODULE'),
  ('websockets.sync.router',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\router.py',
   'PYMODULE'),
  ('websockets.sync.server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\server.py',
   'PYMODULE'),
  ('websockets.sync.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\utils.py',
   'PYMODULE'),
  ('websockets.typing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\typing.py',
   'PYMODULE'),
  ('websockets.uri',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websockets\\uri.py',
   'PYMODULE'),
  ('websockets.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\utils.py',
   'PYMODULE'),
  ('websockets.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\version.py',
   'PYMODULE'),
  ('werkzeug',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('xml',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Program Files (x86)\\python32\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Program Files (x86)\\python32\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('yaml',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('yarl',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\__init__.py',
   'PYMODULE'),
  ('yarl._parse',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_parse.py',
   'PYMODULE'),
  ('yarl._path',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_path.py',
   'PYMODULE'),
  ('yarl._query',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_query.py',
   'PYMODULE'),
  ('yarl._quoters',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_quoters.py',
   'PYMODULE'),
  ('yarl._quoting',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_quoting.py',
   'PYMODULE'),
  ('yarl._quoting_py',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\yarl\\_quoting_py.py',
   'PYMODULE'),
  ('yarl._url',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_url.py',
   'PYMODULE'),
  ('zipfile',
   'D:\\Program Files (x86)\\python32\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'D:\\Program Files (x86)\\python32\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\Program Files (x86)\\python32\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'D:\\Program Files (x86)\\python32\\Lib\\zipimport.py',
   'PYMODULE')])
