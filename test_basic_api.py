#!/usr/bin/env python3
"""
AI主播系统 v2 - 基础API测试
测试基本的API服务功能（不包含WebSocket）
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.logging_service import setup_logging, create_logger
from src.services.voice_service import VoiceService
from src.services.script_service import ScriptService
from src.services.dialogue_service import DialogueService


def test_voice_service():
    """测试语音服务"""
    print("\n" + "="*50)
    print("🎤 测试语音服务")
    print("="*50)

    voice_service = VoiceService()

    # 测试获取主播列表
    print("\n📋 获取主播列表...")
    try:
        speakers_result = voice_service.get_speakers()
        if speakers_result['success']:
            print(f"✅ 获取成功，共 {len(speakers_result['speakers'])} 个主播")
            for i, speaker in enumerate(speakers_result['speakers']):
                if i >= 3:  # 只显示前3个
                    break
                print(f"   - ID: {speaker.get('id')}, 名称: {speaker.get('name')}")
        else:
            print(f"❌ 获取失败: {speakers_result['message']}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

    # 测试语音下载
    print("\n🔊 测试语音下载...")
    try:
        test_text = "你好，欢迎来到AI主播系统！"
        download_result = voice_service.download_voice(test_text, speaker_id=0, speed=1.0)
        if download_result['success']:
            cached = "（缓存）" if download_result.get('cached') else "（新下载）"
            print(f"✅ 语音下载成功 {cached}")
            print(f"   文件路径: {download_result['file_path']}")
        else:
            print(f"❌ 语音下载失败: {download_result['message']}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

    # 测试缓存信息
    print("\n📊 语音缓存信息...")
    try:
        cache_info = voice_service.get_voice_cache_info()
        print(f"   缓存文件数: {cache_info['file_count']}")
        print(f"   缓存大小: {cache_info['total_size_mb']} MB")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

    voice_service.close()


def test_script_service():
    """测试话术服务"""
    print("\n" + "="*50)
    print("📝 测试话术服务")
    print("="*50)

    script_service = ScriptService()

    # 测试获取话术列表
    print("\n📋 获取话术列表...")
    try:
        scripts_result = script_service.get_script_list()
        if scripts_result['success']:
            print(f"✅ 获取成功，共 {len(scripts_result['scripts'])} 个话术")
            for script in scripts_result['scripts'][:3]:  # 只显示前3个
                print(f"   - {script.get('name')}")

            # 测试获取话术内容（如果有话术的话）
            if scripts_result['scripts']:
                first_script = scripts_result['scripts'][0]['name']
                print(f"\n📄 获取话术内容: {first_script}")
                content_result = script_service.get_script_content(first_script)
                if content_result['success']:
                    content = content_result['content']
                    if len(content) > 100:
                        content = content[:100] + "..."
                    print(f"✅ 获取成功: {content}")
                else:
                    print(f"❌ 获取失败: {content_result['message']}")
        else:
            print(f"❌ 获取失败: {scripts_result['message']}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

    script_service.close()


def test_dialogue_service():
    """测试AI对话服务"""
    print("\n" + "="*50)
    print("💬 测试AI对话服务")
    print("="*50)

    dialogue_service = DialogueService()

    # 测试获取对话列表
    print("\n📋 获取AI对话列表...")
    try:
        dialogues_result = dialogue_service.get_dialogue_list()
        if dialogues_result['success']:
            print(f"✅ 获取成功，共 {len(dialogues_result['dialogues'])} 个对话")
            for dialogue in dialogues_result['dialogues'][:3]:  # 只显示前3个
                print(f"   - {dialogue.get('name')}")

            # 测试获取对话内容（如果有对话的话）
            if dialogues_result['dialogues']:
                first_dialogue = dialogues_result['dialogues'][0]['name']
                print(f"\n📄 获取对话内容: {first_dialogue}")
                content_result = dialogue_service.get_dialogue_content(first_dialogue)
                if content_result['success']:
                    content = content_result['content']
                    if len(content) > 100:
                        content = content[:100] + "..."
                    print(f"✅ 获取成功: {content}")
                else:
                    print(f"❌ 获取失败: {content_result['message']}")
        else:
            print(f"❌ 获取失败: {dialogues_result['message']}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

    dialogue_service.close()


def main():
    """主测试函数"""
    # 初始化日志
    setup_logging()
    logger = create_logger("api_test")

    print("🚀 AI主播系统 v2 - 基础API服务测试")
    print("="*60)

    try:
        # 测试各个服务
        test_voice_service()
        test_script_service()
        test_dialogue_service()

        print("\n" + "="*60)
        print("🎉 基础API服务测试完成！")
        print("\n💡 提示:")
        print("   - 语音服务需要外网连接到 ct.scjanelife.com")
        print("   - 话术和对话服务需要本地服务器运行在 localhost:12456")
        print("   - WebSocket服务（弹幕、OBS）需要额外的依赖和服务")

    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        print(f"\n❌ 测试失败: {e}")


if __name__ == "__main__":
    main()
