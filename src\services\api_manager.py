"""
AI主播系统 v2 - API服务管理器
统一管理所有API服务
"""

import asyncio
from typing import Dict, Any, Optional, List
from pathlib import Path

from .logging_service import create_logger
from .error_handler import handle_exceptions
from .voice_service import VoiceService
from .script_service import ScriptService
from .dialogue_service import DialogueService


class APIManager:
    """API服务管理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.logger = create_logger("api_manager")
        self.config = config or {}
        
        # 初始化各个服务
        self._init_services()
        
    def _init_services(self):
        """初始化所有服务"""
        # 语音服务
        voice_api_url = self.config.get('voice_api_url', 'http://ct.scjanelife.com/voice')
        self.voice_service = VoiceService(voice_api_url)
        
        # 话术服务
        server_url = self.config.get('server_url', 'http://localhost:12456')
        self.script_service = ScriptService(server_url)
        
        # AI对话服务
        self.dialogue_service = DialogueService(server_url)
        
        # WebSocket服务（延迟初始化）
        self.danmaku_service = None
        self.obs_service = None
        
        self.logger.info("API服务管理器初始化完成")
        
    # ==================== 语音服务 ====================
    
    @handle_exceptions("APIManager")
    def get_speakers(self) -> Dict[str, Any]:
        """获取主播列表"""
        return self.voice_service.get_speakers()
        
    @handle_exceptions("APIManager")
    def download_voice(self, text: str, speaker_id: int = 0, speed: float = 1.0) -> Dict[str, Any]:
        """下载语音文件"""
        return self.voice_service.download_voice(text, speaker_id, speed)
        
    @handle_exceptions("APIManager")
    def get_voice_cache_info(self) -> Dict[str, Any]:
        """获取语音缓存信息"""
        return self.voice_service.get_voice_cache_info()
        
    @handle_exceptions("APIManager")
    def clear_voice_cache(self) -> bool:
        """清理语音缓存"""
        return self.voice_service.clear_voice_cache()
        
    # ==================== 话术服务 ====================
    
    @handle_exceptions("APIManager")
    def get_script_list(self) -> Dict[str, Any]:
        """获取话术列表"""
        return self.script_service.get_script_list()
        
    @handle_exceptions("APIManager")
    def get_script_content(self, script_name: str) -> Dict[str, Any]:
        """获取话术内容"""
        return self.script_service.get_script_content(script_name)
        
    @handle_exceptions("APIManager")
    def upload_script(self, script_name: str, content) -> Dict[str, Any]:
        """上传话术（支持字符串和字典对象格式）"""
        return self.script_service.upload_script(script_name, content)
        
    @handle_exceptions("APIManager")
    def create_script(self, script_name: str, content: str = "") -> Dict[str, Any]:
        """新建话术"""
        return self.script_service.create_script(script_name, content)
        
    # ==================== AI对话服务 ====================
    
    @handle_exceptions("APIManager")
    def get_dialogue_list(self) -> Dict[str, Any]:
        """获取AI对话列表"""
        return self.dialogue_service.get_dialogue_list()
        
    @handle_exceptions("APIManager")
    def get_dialogue_content(self, dialogue_name: str) -> Dict[str, Any]:
        """获取AI对话内容"""
        return self.dialogue_service.get_dialogue_content(dialogue_name)
        
    @handle_exceptions("APIManager")
    def upload_dialogue(self, dialogue_name: str, content: str) -> Dict[str, Any]:
        """上传AI对话"""
        return self.dialogue_service.upload_dialogue(dialogue_name, content)
        
    @handle_exceptions("APIManager")
    def create_dialogue(self, dialogue_name: str, content: str = "") -> Dict[str, Any]:
        """新建AI对话"""
        return self.dialogue_service.create_dialogue(dialogue_name, content)
        
    # ==================== WebSocket服务 ====================
    
    @handle_exceptions("APIManager")
    async def init_danmaku_service(self, websocket_url: str = "ws://127.0.0.1:9999"):
        """初始化弹幕服务"""
        try:
            from .danmaku_service import DanmakuService
            self.danmaku_service = DanmakuService(websocket_url)
            self.logger.info("弹幕服务初始化完成")
            return True
        except ImportError:
            self.logger.warning("弹幕服务依赖未安装")
            return False
            
    @handle_exceptions("APIManager")
    async def init_obs_service(self, host: str = "localhost", port: int = 4455, password: str = ""):
        """初始化OBS服务"""
        try:
            from .obs_service import OBSService
            self.obs_service = OBSService(host, port, password)
            self.logger.info("OBS服务初始化完成")
            return True
        except ImportError:
            self.logger.warning("OBS服务依赖未安装")
            return False
            
    @handle_exceptions("APIManager")
    async def connect_danmaku(self) -> bool:
        """连接弹幕服务"""
        if not self.danmaku_service:
            await self.init_danmaku_service()
            
        if self.danmaku_service:
            return await self.danmaku_service.connect()
        return False
        
    @handle_exceptions("APIManager")
    async def connect_obs(self) -> bool:
        """连接OBS服务"""
        if not self.obs_service:
            await self.init_obs_service()
            
        if self.obs_service:
            return await self.obs_service.connect()
        return False
        
    # ==================== 配置管理 ====================
    
    def update_config(self, config: Dict[str, Any]):
        """更新配置"""
        self.config.update(config)
        
        # 更新各服务的配置
        if 'voice_api_url' in config:
            self.voice_service.set_voice_api_url(config['voice_api_url'])
            
        if 'server_url' in config:
            self.script_service.set_server_url(config['server_url'])
            self.dialogue_service.set_server_url(config['server_url'])
            
        self.logger.info("API服务配置已更新")
        
    def get_service_status(self) -> Dict[str, Any]:
        """获取所有服务状态"""
        status = {
            'voice_service': {
                'available': True,
                'cache_info': self.voice_service.get_voice_cache_info()
            },
            'script_service': {
                'available': True,
                'server_url': self.script_service.server_url
            },
            'dialogue_service': {
                'available': True,
                'server_url': self.dialogue_service.server_url
            },
            'danmaku_service': {
                'available': self.danmaku_service is not None,
                'connected': self.danmaku_service.is_connected if self.danmaku_service else False
            },
            'obs_service': {
                'available': self.obs_service is not None,
                'connected': self.obs_service.is_connected if self.obs_service else False
            }
        }
        
        return status
        
    # ==================== 批量操作 ====================
    
    @handle_exceptions("APIManager")
    def batch_download_voices(self, texts: List[str], speaker_id: int = 0, speed: float = 1.0) -> Dict[str, Any]:
        """批量下载语音"""
        results = []
        success_count = 0
        
        for text in texts:
            result = self.download_voice(text, speaker_id, speed)
            results.append({
                'text': text,
                'result': result
            })
            
            if result['success']:
                success_count += 1
                
        return {
            'success': success_count > 0,
            'total': len(texts),
            'success_count': success_count,
            'results': results
        }
        
    @handle_exceptions("APIManager")
    def get_all_content(self) -> Dict[str, Any]:
        """获取所有话术和对话内容"""
        result = {
            'scripts': {},
            'dialogues': {}
        }
        
        # 获取所有话术
        scripts_result = self.get_script_list()
        if scripts_result['success']:
            for script in scripts_result['scripts']:
                script_name = script['name']
                content_result = self.get_script_content(script_name)
                if content_result['success']:
                    result['scripts'][script_name] = content_result['content']
                    
        # 获取所有对话
        dialogues_result = self.get_dialogue_list()
        if dialogues_result['success']:
            for dialogue in dialogues_result['dialogues']:
                dialogue_name = dialogue['name']
                content_result = self.get_dialogue_content(dialogue_name)
                if content_result['success']:
                    result['dialogues'][dialogue_name] = content_result['content']
                    
        return result
        
    def close(self):
        """关闭所有服务"""
        self.voice_service.close()
        self.script_service.close()
        self.dialogue_service.close()
        
        self.logger.info("API服务管理器已关闭")
