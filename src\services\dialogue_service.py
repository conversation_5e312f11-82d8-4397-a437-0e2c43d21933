"""
AI主播系统 v2 - AI对话管理服务
AI对话的获取、上传、新建等操作
"""

import requests
import json
from typing import Dict, Any, List, Optional

from .logging_service import create_logger
from .error_handler import handle_exceptions


class DialogueService:
    """AI对话管理服务"""

    def __init__(self, server_url: str = "http://localhost:12456"):
        self.logger = create_logger("dialogue_service")
        self.server_url = server_url.rstrip('/')
        self.session = requests.Session()

        # 设置请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'AI-Broadcaster-Client/2.0.0'
        })

        # 禁用代理，直接连接
        self.session.proxies = {'http': None, 'https': None}

        # 超时设置
        self.timeout = 10

    @handle_exceptions("DialogueService")
    def get_dialogue_list(self) -> Dict[str, Any]:
        """
        获取AI对话列表

        Returns:
            对话列表结果
        """
        try:
            self.logger.info("获取AI对话列表...")

            # 发送请求
            response = self.session.get(
                f"{self.server_url}/dialoguelist",
                timeout=self.timeout
            )

            # 检查HTTP状态码
            if response.status_code != 200:
                return {
                    'success': False,
                    'message': f'服务器错误: HTTP {response.status_code}'
                }

            # 解析响应
            result = response.json()

            # 提取对话列表
            dialogues = result.get('ai对话', [])

            self.logger.info(f"获取到 {len(dialogues)} 个AI对话")

            return {
                'success': True,
                'dialogues': dialogues
            }

        except requests.exceptions.ConnectionError:
            self.logger.error("无法连接到服务器")
            return {
                'success': False,
                'message': '无法连接到服务器，请检查网络连接'
            }
        except requests.exceptions.Timeout:
            self.logger.error("请求超时")
            return {
                'success': False,
                'message': '请求超时，请稍后重试'
            }
        except json.JSONDecodeError:
            self.logger.error("服务器响应格式错误")
            return {
                'success': False,
                'message': '服务器响应格式错误'
            }
        except Exception as e:
            self.logger.error(f"获取AI对话列表失败: {e}")
            return {
                'success': False,
                'message': f'获取AI对话列表失败: {str(e)}'
            }

    @handle_exceptions("DialogueService")
    def get_dialogue_content(self, dialogue_name: str) -> Dict[str, Any]:
        """
        获取AI对话详细内容

        Args:
            dialogue_name: 对话名称

        Returns:
            对话内容结果
        """
        try:
            self.logger.info(f"获取AI对话内容: {dialogue_name}")

            # 准备请求数据
            data = {
                "类型": "获取ai对话",
                "对话名": dialogue_name
            }

            # 发送请求
            response = self.session.post(
                f"{self.server_url}/",
                json=data,
                timeout=self.timeout
            )

            # 检查HTTP状态码
            if response.status_code != 200:
                return {
                    'success': False,
                    'message': f'服务器错误: HTTP {response.status_code}'
                }

            # AI对话内容可能是JSON格式，直接返回原始文本
            content = response.text

            return {
                'success': True,
                'content': content,
                'dialogue_name': dialogue_name
            }

        except Exception as e:
            self.logger.error(f"获取AI对话内容失败: {e}")
            return {
                'success': False,
                'message': f'获取AI对话内容失败: {str(e)}'
            }

    @handle_exceptions("DialogueService")
    def upload_dialogue(self, dialogue_name: str, content: str) -> Dict[str, Any]:
        """
        上传AI对话

        Args:
            dialogue_name: 对话名称
            content: 对话内容

        Returns:
            上传结果
        """
        try:
            self.logger.info(f"上传AI对话: {dialogue_name}")

            # 准备请求数据
            data = {
                "类型": "上传ai对话",
                "ai对话名": dialogue_name,
                "上传数据": content
            }

            # 发送请求
            response = self.session.post(
                f"{self.server_url}/",
                json=data,
                timeout=self.timeout
            )

            # 检查HTTP状态码
            if response.status_code != 200:
                return {
                    'success': False,
                    'message': f'服务器错误: HTTP {response.status_code}'
                }

            # 检查响应内容
            try:
                # 尝试解析JSON响应
                result = response.json()
                if result.get('状态') == '成功':
                    return {
                        'success': True,
                        'message': result.get('信息', 'AI对话上传成功')
                    }
                else:
                    return {
                        'success': False,
                        'message': result.get('信息', 'AI对话上传失败')
                    }
            except json.JSONDecodeError:
                # 如果不是JSON，检查文本响应
                if response.text.strip():
                    return {
                        'success': True,
                        'message': 'AI对话上传成功'
                    }
                else:
                    return {
                        'success': False,
                        'message': '服务器响应为空'
                    }

        except Exception as e:
            self.logger.error(f"上传AI对话失败: {e}")
            return {
                'success': False,
                'message': f'上传AI对话失败: {str(e)}'
            }

    @handle_exceptions("DialogueService")
    def create_dialogue(self, dialogue_name: str, content: str = "") -> Dict[str, Any]:
        """
        新建AI对话

        Args:
            dialogue_name: 对话名称
            content: 对话内容

        Returns:
            创建结果
        """
        try:
            self.logger.info(f"新建AI对话: {dialogue_name}")

            # 准备请求数据
            data = {
                "类型": "新建ai对话",
                "对话名": dialogue_name
            }

            # 发送请求
            response = self.session.post(
                f"{self.server_url}/",
                json=data,
                timeout=self.timeout
            )

            # 检查HTTP状态码
            if response.status_code != 200:
                return {
                    'success': False,
                    'message': f'服务器错误: HTTP {response.status_code}'
                }

            # 检查响应内容
            try:
                # 尝试解析JSON响应
                result = response.json()
                if result.get('状态') == '成功':
                    return {
                        'success': True,
                        'message': result.get('信息', 'AI对话创建成功')
                    }
                else:
                    return {
                        'success': False,
                        'message': result.get('信息', 'AI对话创建失败')
                    }
            except json.JSONDecodeError:
                # 如果不是JSON，检查文本响应
                if response.text.strip():
                    return {
                        'success': True,
                        'message': 'AI对话创建成功'
                    }
                else:
                    return {
                        'success': False,
                        'message': '服务器响应为空'
                    }

        except Exception as e:
            self.logger.error(f"新建AI对话失败: {e}")
            return {
                'success': False,
                'message': f'新建AI对话失败: {str(e)}'
            }

    def set_server_url(self, url: str):
        """设置服务器地址"""
        self.server_url = url.rstrip('/')
        self.logger.info(f"设置服务器地址: {self.server_url}")

    def close(self):
        """关闭会话"""
        self.session.close()
