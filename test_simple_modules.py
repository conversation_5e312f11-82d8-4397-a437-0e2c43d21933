#!/usr/bin/env python3
"""
AI Broadcaster v2 - 简单模块测试脚本
测试不依赖外部库的核心功能模块
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.logging_service import setup_logging, create_logger
from src.data.database_manager import DatabaseManager
from src.core.auth.user_manager import UserManager
from src.core.auth.machine_code import MachineCodeGenerator
from src.core.voice.speaker_manager import SpeakerManager
from src.core.voice.voice_manager import VoiceManager


def test_database_module():
    """测试数据库模块"""
    print("🗄️  测试数据库模块...")
    
    try:
        # 创建数据库管理器
        db = DatabaseManager("data/test_ai_broadcaster.db")
        
        # 测试数据库信息
        db_info = db.get_database_info()
        print(f"  ✅ 数据库创建成功: {db_info['table_count']} 个表")
        
        # 测试简单查询
        tables = db.execute_query("SELECT name FROM sqlite_master WHERE type='table'")
        print(f"  ✅ 数据库查询成功: 找到 {len(tables)} 个表")
        
        return True, db
        
    except Exception as e:
        print(f"  ❌ 数据库模块测试失败: {e}")
        return False, None


def test_auth_module(db):
    """测试认证模块"""
    print("\n🔐 测试认证模块...")
    
    try:
        # 测试机器码生成器
        machine_gen = MachineCodeGenerator()
        machine_code = machine_gen.generate_machine_code()
        hardware_info = machine_gen.get_hardware_info()
        
        print(f"  ✅ 机器码生成: {machine_code[:16]}...")
        print(f"  ✅ 硬件信息: {hardware_info['system']} {hardware_info['machine']}")
        
        # 测试用户管理器
        user_manager = UserManager(db)
        
        # 测试用户注册
        success, msg = user_manager.register_user("test_user", "test_password", "13800138000")
        print(f"  ✅ 用户注册: {msg}")
        
        # 测试用户登录
        success, msg, user_info = user_manager.login_user("test_user", "test_password")
        print(f"  ✅ 用户登录: {msg}")
        
        if user_info:
            print(f"  ✅ 用户信息: {user_info['username']}")
        
        return True, user_manager
        
    except Exception as e:
        print(f"  ❌ 认证模块测试失败: {e}")
        return False, None


def test_voice_module(db):
    """测试语音模块"""
    print("\n🎤 测试语音模块...")
    
    try:
        # 测试主播管理器
        speaker_manager = SpeakerManager(db)
        speakers = speaker_manager.get_all_speakers()
        print(f"  ✅ 主播管理器: 找到 {len(speakers)} 个主播")
        
        if speakers:
            first_speaker = speakers[0]
            print(f"  ✅ 第一个主播: {first_speaker['name']} - {first_speaker['description']}")
            
            # 测试随机语速
            random_speed = speaker_manager.get_random_speed(first_speaker['id'])
            print(f"  ✅ 随机语速: {random_speed}")
        
        # 测试语音管理器
        voice_manager = VoiceManager(db)
        current_speaker = voice_manager.get_current_speaker()
        print(f"  ✅ 当前主播: {current_speaker['name'] if current_speaker else 'None'}")
        
        # 测试模拟语音生成
        test_text = "这是一个测试语音"
        voice_file = voice_manager.generate_voice_mock(test_text)
        if voice_file:
            print(f"  ✅ 模拟语音生成成功: {Path(voice_file).name}")
        else:
            print("  ⚠️  模拟语音生成失败")
        
        # 测试缓存统计
        cache_stats = voice_manager.get_cache_stats()
        print(f"  ✅ 语音缓存: {cache_stats.get('cache_records', 0)} 条记录")
        
        return True, voice_manager
        
    except Exception as e:
        print(f"  ❌ 语音模块测试失败: {e}")
        return False, None


def test_integration():
    """集成测试"""
    print("\n🔗 集成测试...")
    
    try:
        # 测试数据库模块
        db_success, db = test_database_module()
        if not db_success:
            return False
        
        # 测试认证模块
        auth_success, user_manager = test_auth_module(db)
        if not auth_success:
            return False
        
        # 测试语音模块
        voice_success, voice_manager = test_voice_module(db)
        if not voice_success:
            return False
        
        print("\n🎉 所有核心模块测试通过！")
        
        # 清理资源
        if db:
            db.close()
        
        # 清理测试文件
        test_db_file = Path("data/test_ai_broadcaster.db")
        if test_db_file.exists():
            test_db_file.unlink()
            print("🧹 测试数据库文件已清理")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🧪 AI Broadcaster v2 核心模块测试")
    print("=" * 60)
    
    # 初始化日志系统
    setup_logging()
    logger = create_logger("test")
    
    try:
        # 运行集成测试
        success = test_integration()
        
        if success:
            print("\n✅ 所有核心测试通过！")
            print("\n📝 测试总结:")
            print("  - 数据库模块: ✅ 正常")
            print("  - 认证模块: ✅ 正常")
            print("  - 语音模块: ✅ 正常")
            print("  - 用户注册/登录: ✅ 正常")
            print("  - 机器码生成: ✅ 正常")
            print("  - 主播管理: ✅ 正常")
            print("  - 语音生成: ✅ 正常")
            print("  - 数据库操作: ✅ 正常")
            print("\n🎯 下一步可以开始开发UI界面和其他功能模块！")
            return 0
        else:
            print("\n❌ 测试失败！请检查错误信息。")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
