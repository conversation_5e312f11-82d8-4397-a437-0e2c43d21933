#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试副视频修复效果
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_sub_video_config():
    """测试副视频配置"""
    print("🧪 测试副视频配置...")
    
    config_file = Path("data/sub_videos.json")
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"✅ 副视频配置文件存在，包含 {len(config)} 个关键词:")
        for keyword, data in config.items():
            video_source = data.get('video_source', '未知')
            scripts_count = len(data.get('scripts', []))
            print(f"  • {keyword} → {video_source} ({scripts_count}个话术)")
        
        return config
    else:
        print("❌ 副视频配置文件不存在")
        return {}

def test_obs_controller():
    """测试OBS控制器"""
    print("\n🧪 测试OBS控制器...")
    
    try:
        from src.services.obs_controller import OBSController
        
        obs = OBSController()
        print("✅ OBS控制器创建成功")
        
        # 测试连接
        print("🔗 尝试连接OBS...")
        if obs.connect():
            print("✅ OBS连接成功")
            
            # 获取视频源列表
            sources = obs.get_source_list()
            print(f"✅ 获取到 {len(sources)} 个视频源:")
            for i, source in enumerate(sources[:5], 1):
                print(f"  {i}. {source}")
            if len(sources) > 5:
                print(f"  ... 还有 {len(sources) - 5} 个")
            
            # 测试源ID获取
            if sources:
                test_source = sources[0]
                source_id = obs._get_source_id(test_source)
                print(f"✅ 测试源 '{test_source}' 的ID: {source_id}")
            
            obs.disconnect()
            print("✅ OBS连接已断开")
            return True
        else:
            print("❌ OBS连接失败")
            return False
            
    except Exception as e:
        print(f"❌ OBS控制器测试失败: {e}")
        return False

def test_sub_video_trigger():
    """测试副视频触发逻辑"""
    print("\n🧪 测试副视频触发逻辑...")
    
    # 加载副视频配置
    config = test_sub_video_config()
    if not config:
        print("❌ 无副视频配置，跳过触发测试")
        return
    
    # 测试弹幕
    test_messages = [
        "感谢老板的火箭，太给力了！",
        "谢谢大家的礼物支持",
        "主播666，继续加油！",
        "普通弹幕，没有关键词"
    ]
    
    print("🎬 测试副视频触发:")
    for msg in test_messages:
        triggered = False
        triggered_keyword = None
        triggered_source = None
        
        for keyword, data in config.items():
            if keyword and keyword.lower() in msg.lower():
                triggered = True
                triggered_keyword = keyword
                triggered_source = data.get('video_source', '未知')
                break
        
        if triggered:
            print(f"  ✅ {msg} → 触发: {triggered_keyword} → {triggered_source}")
        else:
            print(f"  ⚪ {msg} → 无触发")

def test_main_program_integration():
    """测试主程序集成"""
    print("\n🧪 测试主程序集成...")
    
    try:
        # 检查主程序是否在运行
        import psutil
        
        main_program_running = False
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info['cmdline']
                if cmdline and any('run_gui_qt5.py' in arg for arg in cmdline):
                    main_program_running = True
                    print(f"✅ 主程序正在运行 (PID: {proc.info['pid']})")
                    break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if not main_program_running:
            print("⚠️ 主程序未运行，无法测试集成")
            return False
        
        # 测试主程序的副视频方法
        print("🔍 检查主程序副视频方法...")
        
        # 这里可以添加更多集成测试
        print("✅ 主程序集成测试完成")
        return True
        
    except ImportError:
        print("⚠️ psutil未安装，跳过主程序检测")
        return False
    except Exception as e:
        print(f"❌ 主程序集成测试失败: {e}")
        return False

def create_test_playlist_item():
    """创建测试播放列表项目"""
    print("\n🧪 创建测试播放列表项目...")
    
    # 加载副视频配置
    config = test_sub_video_config()
    if not config:
        print("❌ 无副视频配置，无法创建测试项目")
        return None
    
    # 选择第一个副视频配置
    first_keyword = list(config.keys())[0]
    first_config = config[first_keyword]
    
    test_item = {
        'id': 999,
        'voice_type': '弹幕话术',
        'content': f"感谢老板的{first_keyword}，太给力了！",
        'time_segment': '无',
        'status': '已下载',
        'filename': 'test_voice.mp3',  # 假设的文件名
        'sub_video': first_config.get('video_source', '无')
    }
    
    print(f"✅ 创建测试项目:")
    print(f"  内容: {test_item['content']}")
    print(f"  副视频: {test_item['sub_video']}")
    
    return test_item

def main():
    """主函数"""
    print("🎬 副视频功能修复测试")
    print("=" * 50)
    
    # 测试副视频配置
    config = test_sub_video_config()
    
    # 测试OBS控制器
    obs_ok = test_obs_controller()
    
    # 测试副视频触发逻辑
    test_sub_video_trigger()
    
    # 测试主程序集成
    integration_ok = test_main_program_integration()
    
    # 创建测试项目
    test_item = create_test_playlist_item()
    
    print("\n" + "=" * 50)
    print("🏁 测试总结:")
    print(f"  副视频配置: {'✅' if config else '❌'}")
    print(f"  OBS控制器: {'✅' if obs_ok else '❌'}")
    print(f"  主程序集成: {'✅' if integration_ok else '⚠️'}")
    print(f"  测试项目: {'✅' if test_item else '❌'}")
    
    if config and obs_ok:
        print("\n💡 建议:")
        print("1. 确保OBS Studio已启动并开启WebSocket服务器")
        print("2. 在主程序中添加包含关键词的弹幕")
        print("3. 观察播放列表中是否显示副视频信息")
        print("4. 播放时观察OBS中的视频源切换")
    
    print("\n🎯 如果副视频仍未触发，请检查:")
    print("1. 播放列表中的项目是否有副视频标识")
    print("2. OBS连接状态是否正常")
    print("3. 视频源名称是否与配置中的一致")
    print("4. 查看控制台日志中的副视频切换信息")

if __name__ == "__main__":
    main()
