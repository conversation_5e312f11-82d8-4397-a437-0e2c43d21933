#!/usr/bin/env python3
"""
调试时间段显示功能
帮助用户检查为什么时间段没有显示出来
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def check_time_segment_display_issues():
    """检查时间段显示可能的问题"""
    print("🔍 检查时间段显示功能可能的问题")
    print("=" * 60)
    
    issues_found = []
    solutions = []
    
    print("📋 可能的问题和解决方案:")
    print()
    
    # 问题1: 定时器没有启动
    print("1️⃣ 定时器问题")
    print("   问题: 时间段更新定时器没有正确启动")
    print("   检查: 在主程序启动时是否看到定时器启动信息")
    print("   解决: 确保在 __init__ 方法中正确设置了定时器")
    print("   代码位置: run_gui_qt5.py 第1069-1072行")
    print()
    
    # 问题2: OBS连接问题
    print("2️⃣ OBS连接问题")
    print("   问题: 无法从OBS获取主视频播放位置")
    print("   检查: OBS是否开启了WebSocket服务器")
    print("   解决: 在OBS中启用WebSocket服务器 (工具 -> WebSocket服务器设置)")
    print("   端口: 4455 (默认)")
    print("   密码: 可以为空或设置密码")
    print()
    
    # 问题3: 主视频源问题
    print("3️⃣ 主视频源问题")
    print("   问题: 没有设置或选择主视频源")
    print("   检查: 在OBS设置中是否正确选择了主视频源A和主视频源B")
    print("   解决: 在主程序的OBS设置中选择正确的视频源")
    print()
    
    # 问题4: 话术选择问题
    print("4️⃣ 话术选择问题")
    print("   问题: 没有选择当前话术或话术没有时间段设置")
    print("   检查: 在话术管理中是否选择了话术并添加了时间段")
    print("   解决: ")
    print("     a) 在话术管理中选择一个话术")
    print("     b) 点击'添加时间段话术'按钮")
    print("     c) 设置时间段范围 (如: 10秒-20秒)")
    print()
    
    # 问题5: 视频播放状态问题
    print("5️⃣ 视频播放状态问题")
    print("   问题: 主视频没有在播放")
    print("   检查: OBS中的主视频源是否正在播放")
    print("   解决: 确保主视频源有内容并且正在播放")
    print()
    
    return issues_found, solutions


def create_debug_checklist():
    """创建调试检查清单"""
    print("\n📝 调试检查清单")
    print("=" * 60)
    
    checklist = [
        "✅ 1. 启动主程序 (python run_gui_qt5.py)",
        "✅ 2. 登录成功进入主界面",
        "✅ 3. 检查控制台是否有定时器启动信息",
        "✅ 4. 打开OBS软件",
        "✅ 5. 在OBS中启用WebSocket服务器 (工具 -> WebSocket服务器设置)",
        "✅ 6. 在主程序中连接OBS (输入localhost:4455)",
        "✅ 7. 在OBS设置中选择主视频源A和主视频源B",
        "✅ 8. 切换到话术管理标签页",
        "✅ 9. 选择或创建一个话术",
        "✅ 10. 点击'添加时间段话术'按钮",
        "✅ 11. 设置时间段 (如: 开始时间10秒, 结束时间20秒)",
        "✅ 12. 确保主视频源在OBS中正在播放",
        "✅ 13. 观察'当前处于X时间段'标签是否更新",
        "✅ 14. 检查控制台输出的调试信息"
    ]
    
    for item in checklist:
        print(f"  {item}")
    
    print()
    return checklist


def create_debug_commands():
    """创建调试命令"""
    print("\n🛠️ 调试命令和检查方法")
    print("=" * 60)
    
    commands = [
        {
            "标题": "检查定时器是否启动",
            "方法": "查看控制台输出",
            "期望": "应该看到定时器相关的启动信息"
        },
        {
            "标题": "检查OBS连接状态",
            "方法": "在主程序中测试OBS连接",
            "期望": "连接成功后应该能获取到OBS场景信息"
        },
        {
            "标题": "检查主视频播放位置",
            "方法": "查看控制台输出",
            "期望": "应该看到类似 '🎬 主视频播放位置: X.X秒' 的信息"
        },
        {
            "标题": "检查话术时间段数据",
            "方法": "查看控制台输出",
            "期望": "应该看到类似 '📋 话术 XXX 的时间段: [...]' 的信息"
        },
        {
            "标题": "检查时间段匹配",
            "方法": "查看控制台输出",
            "期望": "应该看到类似 '✅ 匹配到时间段: 当前处于X秒-Y秒' 的信息"
        }
    ]
    
    for i, cmd in enumerate(commands, 1):
        print(f"{i}. {cmd['标题']}")
        print(f"   方法: {cmd['方法']}")
        print(f"   期望: {cmd['期望']}")
        print()
    
    return commands


def create_troubleshooting_guide():
    """创建故障排除指南"""
    print("\n🚨 常见问题故障排除")
    print("=" * 60)
    
    problems = [
        {
            "问题": "控制台显示 '⚠️ 无法获取主视频播放位置'",
            "原因": "OBS连接问题或主视频源设置问题",
            "解决": [
                "检查OBS是否开启WebSocket服务器",
                "检查主视频源A/B是否正确设置",
                "检查主视频源是否正在播放"
            ]
        },
        {
            "问题": "控制台显示 '⚠️ 未选择话术'",
            "原因": "没有在话术管理中选择话术",
            "解决": [
                "切换到话术管理标签页",
                "在话术下拉框中选择一个话术",
                "如果没有话术，先创建一个话术"
            ]
        },
        {
            "问题": "控制台显示 '⚠️ 话术 XXX 无时间段设置'",
            "原因": "选择的话术没有设置时间段",
            "解决": [
                "点击'添加时间段话术'按钮",
                "设置开始和结束时间",
                "保存时间段设置"
            ]
        },
        {
            "问题": "控制台显示 '📍 不在任何预设时间段内'",
            "原因": "当前播放位置不在设置的时间段范围内",
            "解决": [
                "检查视频当前播放位置",
                "调整时间段设置范围",
                "或等待视频播放到设置的时间段内"
            ]
        },
        {
            "问题": "时间段标签一直显示 '当前处于0-15时间段'",
            "原因": "可能还在使用旧的模拟显示",
            "解决": [
                "重启主程序",
                "确保使用最新的代码版本",
                "检查是否有缓存问题"
            ]
        }
    ]
    
    for i, problem in enumerate(problems, 1):
        print(f"{i}. 问题: {problem['问题']}")
        print(f"   原因: {problem['原因']}")
        print(f"   解决方案:")
        for solution in problem['解决']:
            print(f"     • {solution}")
        print()
    
    return problems


def main():
    """主函数"""
    print("🚀 时间段显示功能调试指南")
    print("=" * 80)
    
    # 检查问题
    issues, solutions = check_time_segment_display_issues()
    
    # 创建检查清单
    checklist = create_debug_checklist()
    
    # 创建调试命令
    commands = create_debug_commands()
    
    # 创建故障排除指南
    problems = create_troubleshooting_guide()
    
    print("\n🎯 总结")
    print("=" * 60)
    print("如果时间段没有显示出来，请按照以下步骤进行排查:")
    print()
    print("1. 📋 按照检查清单逐项检查")
    print("2. 🛠️ 使用调试命令检查各个组件状态")
    print("3. 🚨 根据控制台输出信息查找对应的故障排除方案")
    print("4. 📞 如果问题仍然存在，请提供控制台的完整输出信息")
    print()
    print("🎊 大多数情况下，问题出现在OBS连接、话术选择或时间段设置上！")
    print()
    print("现在请启动主程序并按照检查清单进行操作:")
    print("python run_gui_qt5.py")


if __name__ == "__main__":
    main()
