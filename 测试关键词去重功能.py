#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试关键词去重功能
验证每个AI对话关键词在待播放列表中只能存在一个回复内容
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_keyword_deduplication():
    """测试关键词去重功能"""
    print("🧪 测试关键词去重功能...")
    
    # 模拟播放列表数据
    playlist_items = []
    
    # 模拟process_danmaku_keywords方法的关键逻辑
    def mock_process_danmaku_keywords(content: str, user_name: str = "", dialogue_data=None):
        """模拟弹幕关键词处理方法"""
        if not dialogue_data:
            dialogue_data = {
                "进入直播间": "欢迎{nick}来到{gamename}的直播间！这是一款{gametype}游戏",
                "游戏": "我们正在玩{gamename}，这是一款{gametype}类型的游戏",
                "主播": "我是主播，正在为大家直播{gamename}",
                "时间": "现在时间是{time}，感谢大家观看"
            }
        
        print(f"🔍 【关键词匹配】开始处理弹幕: '{content}' (用户: {user_name})")
        print(f"🔍 【关键词匹配】可用关键词: {list(dialogue_data.keys())}")
        
        # 遍历所有关键词，检查是否匹配
        for keyword, reply in dialogue_data.items():
            if keyword in content:
                # 处理变量替换
                processed_reply = reply.replace("{nick}", user_name or "观众")
                processed_reply = processed_reply.replace("{gamename}", "上古")
                processed_reply = processed_reply.replace("{gametype}", "仙侠")
                processed_reply = processed_reply.replace("{time}", "08:40")
                
                # 🔥 关键：检查播放列表中是否已经存在该关键词的未播放回复
                existing_same_keyword = False
                for item in playlist_items:
                    if (item.get('voice_type') == '弹幕话术' and
                        item.get('status') in ['未下载', '下载中', '已下载', '播放中']):
                        # 🔥 新增：记录触发关键词到播放项中，用于精确匹配
                        item_keyword = item.get('trigger_keyword', '')
                        if item_keyword == keyword:
                            existing_same_keyword = True
                            print(f"⚠️ 关键词 '{keyword}' 的回复已在播放列表中，跳过生成: {item.get('content', '')[:30]}... (状态: {item.get('status')})")
                            break

                if existing_same_keyword:
                    return False  # 跳过生成
                
                print(f"🎯 弹幕匹配关键词: '{keyword}' -> {user_name}: {content}")
                print(f"📝 生成回复内容: {processed_reply[:50]}...")
                
                # 添加到播放列表
                new_id = max([item.get('id', 0) for item in playlist_items] + [0]) + 1
                playlist_item = {
                    'id': new_id,
                    'voice_type': '弹幕话术',
                    'content': processed_reply,
                    'time_segment': '无',
                    'status': '未下载',
                    'filename': '',
                    'sub_video': '无',
                    'trigger_keyword': keyword  # 🔥 新增：记录触发关键词
                }
                
                playlist_items.append(playlist_item)
                print(f"✅ 弹幕话术已添加到播放列表: {processed_reply[:30]}...")
                return True  # 成功添加
        
        return False  # 没有匹配的关键词
    
    # 测试用例
    test_cases = [
        {
            "content": "欢迎进入直播间",
            "user": "用户1",
            "description": "第一次触发'进入直播间'关键词"
        },
        {
            "content": "我也要进入直播间",
            "user": "用户2", 
            "description": "第二次触发'进入直播间'关键词（应该被跳过）"
        },
        {
            "content": "再次进入直播间",
            "user": "用户3",
            "description": "第三次触发'进入直播间'关键词（应该被跳过）"
        },
        {
            "content": "这个游戏好玩吗",
            "user": "用户4",
            "description": "第一次触发'游戏'关键词"
        },
        {
            "content": "游戏画面很棒",
            "user": "用户5",
            "description": "第二次触发'游戏'关键词（应该被跳过）"
        },
        {
            "content": "主播很厉害",
            "user": "用户6",
            "description": "第一次触发'主播'关键词"
        },
        {
            "content": "现在什么时间",
            "user": "用户7",
            "description": "第一次触发'时间'关键词"
        }
    ]
    
    print("\n📋 测试用例:")
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. {case['description']}")
        print(f"   弹幕内容: {case['content']}")
        print(f"   用户: {case['user']}")
        
        # 执行关键词匹配
        result = mock_process_danmaku_keywords(case['content'], case['user'])
        
        if result:
            print(f"   ✅ 成功添加到播放列表")
        else:
            print(f"   ⚠️ 跳过添加（重复关键词或无匹配）")
    
    # 显示最终播放列表状态
    print(f"\n📊 最终播放列表状态:")
    print(f"总项目数: {len(playlist_items)}")
    
    keyword_count = {}
    for item in playlist_items:
        keyword = item.get('trigger_keyword', '未知')
        if keyword not in keyword_count:
            keyword_count[keyword] = 0
        keyword_count[keyword] += 1
    
    print(f"关键词统计:")
    for keyword, count in keyword_count.items():
        print(f"  - {keyword}: {count} 个回复")
    
    print(f"\n📋 播放列表详情:")
    for i, item in enumerate(playlist_items, 1):
        content = item.get('content', '')[:40]
        keyword = item.get('trigger_keyword', '未知')
        status = item.get('status', '未知')
        print(f"  {i}. 关键词: {keyword} | 状态: {status} | 内容: {content}...")

def test_playlist_cleanup_scenario():
    """测试播放列表清理场景"""
    print("\n" + "=" * 60)
    print("🧹 测试播放列表清理场景")
    
    # 模拟播放列表，包含已播放和未播放的项目
    playlist_items = [
        {
            'id': 1,
            'voice_type': '弹幕话术',
            'content': '欢迎用户1来到上古的直播间！这是一款仙侠游戏',
            'status': '已播放',
            'trigger_keyword': '进入直播间'
        },
        {
            'id': 2,
            'voice_type': '弹幕话术',
            'content': '我们正在玩上古，这是一款仙侠类型的游戏',
            'status': '已播放',
            'trigger_keyword': '游戏'
        },
        {
            'id': 3,
            'voice_type': '主视频话术',
            'content': '这是主视频话术',
            'status': '已下载',
            'trigger_keyword': None
        }
    ]
    
    print(f"初始播放列表: {len(playlist_items)} 个项目")
    for item in playlist_items:
        keyword = item.get('trigger_keyword', '无')
        status = item.get('status', '未知')
        voice_type = item.get('voice_type', '未知')
        print(f"  - {voice_type} | 关键词: {keyword} | 状态: {status}")
    
    # 模拟新的弹幕触发相同关键词
    def check_keyword_exists(keyword, playlist_items):
        """检查关键词是否已存在于未播放项目中"""
        for item in playlist_items:
            if (item.get('voice_type') == '弹幕话术' and
                item.get('status') in ['未下载', '下载中', '已下载', '播放中']):
                item_keyword = item.get('trigger_keyword', '')
                if item_keyword == keyword:
                    return True, item
        return False, None
    
    # 测试场景1：触发已播放的关键词
    print(f"\n🧪 场景1：触发已播放的关键词'进入直播间'")
    exists, existing_item = check_keyword_exists('进入直播间', playlist_items)
    if exists:
        print(f"  ❌ 关键词'进入直播间'已存在未播放项目中，跳过")
    else:
        print(f"  ✅ 关键词'进入直播间'不存在未播放项目中，可以添加")
        # 添加新项目
        new_item = {
            'id': 4,
            'voice_type': '弹幕话术',
            'content': '欢迎新用户来到上古的直播间！这是一款仙侠游戏',
            'status': '未下载',
            'trigger_keyword': '进入直播间'
        }
        playlist_items.append(new_item)
        print(f"  ✅ 新项目已添加")
    
    # 测试场景2：触发已播放的关键词
    print(f"\n🧪 场景2：再次触发关键词'进入直播间'")
    exists, existing_item = check_keyword_exists('进入直播间', playlist_items)
    if exists:
        print(f"  ❌ 关键词'进入直播间'已存在未播放项目中，跳过")
        print(f"      现有项目状态: {existing_item.get('status')}")
    else:
        print(f"  ✅ 关键词'进入直播间'不存在未播放项目中，可以添加")
    
    print(f"\n📊 最终播放列表: {len(playlist_items)} 个项目")
    for item in playlist_items:
        keyword = item.get('trigger_keyword', '无')
        status = item.get('status', '未知')
        voice_type = item.get('voice_type', '未知')
        print(f"  - {voice_type} | 关键词: {keyword} | 状态: {status}")

if __name__ == "__main__":
    print("🎮 关键词去重功能测试")
    print("=" * 60)
    
    test_keyword_deduplication()
    test_playlist_cleanup_scenario()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    
    print("\n💡 功能总结：")
    print("1. ✅ 每个关键词在待播放列表中只能存在一个未播放回复")
    print("2. ✅ 通过trigger_keyword字段精确匹配关键词")
    print("3. ✅ 已播放的项目不影响新的关键词触发")
    print("4. ✅ 只检查未下载、下载中、已下载、播放中状态的项目")
    print("5. ✅ 避免了重复生成相同关键词的AI回复")
    
    print("\n🔧 实现原理：")
    print("- 在播放项目中添加trigger_keyword字段记录触发关键词")
    print("- 检查播放列表时精确匹配trigger_keyword而不是内容匹配")
    print("- 只检查未播放状态的项目，已播放项目不影响新触发")
    print("- 确保每个关键词在任何时刻只有一个回复在等待播放")
