#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
副视频界面测试程序
提供完整的界面来测试副视频触发和切换功能
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QTextEdit, QLabel, 
                             QLineEdit, QTableWidget, QTableWidgetItem, 
                             QGroupBox, QMessageBox, QComboBox)
from PyQt5.QtCore import QTimer, pyqtSignal
from PyQt5.QtGui import QFont

class SubVideoTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("副视频功能测试程序")
        self.setGeometry(100, 100, 1000, 700)
        
        # 导入主程序组件
        try:
            from run_gui_qt5 import MainWindow
            self.main_window_class = MainWindow
            self.main_window = None
            self.init_main_window()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法导入主程序: {e}")
            return
        
        self.init_ui()
        
    def init_main_window(self):
        """初始化主程序窗口实例"""
        try:
            user_info = {
                'username': 'test_user',
                'user_id': 1,
                'expire_time': '2025-12-31'
            }
            self.main_window = self.main_window_class(user_info)
            print("✅ 主程序窗口初始化成功")
        except Exception as e:
            print(f"❌ 主程序窗口初始化失败: {e}")
    
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("副视频功能测试程序")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title_label)
        
        # 副视频配置显示区域
        config_group = QGroupBox("当前副视频配置")
        config_layout = QVBoxLayout(config_group)
        
        self.config_text = QTextEdit()
        self.config_text.setMaximumHeight(120)
        self.config_text.setReadOnly(True)
        config_layout.addWidget(self.config_text)
        
        refresh_config_btn = QPushButton("刷新配置")
        refresh_config_btn.clicked.connect(self.refresh_config)
        config_layout.addWidget(refresh_config_btn)
        
        layout.addWidget(config_group)
        
        # 弹幕测试区域
        test_group = QGroupBox("弹幕测试")
        test_layout = QVBoxLayout(test_group)
        
        # 弹幕输入
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("测试弹幕:"))
        self.danmaku_input = QLineEdit()
        self.danmaku_input.setPlaceholderText("输入包含关键词的弹幕，如：感谢老板的火箭")
        input_layout.addWidget(self.danmaku_input)
        
        test_btn = QPushButton("测试触发")
        test_btn.clicked.connect(self.test_trigger)
        input_layout.addWidget(test_btn)
        
        add_btn = QPushButton("添加到播放列表")
        add_btn.clicked.connect(self.add_to_playlist)
        input_layout.addWidget(add_btn)
        
        test_layout.addLayout(input_layout)
        
        # 快速测试按钮
        quick_layout = QHBoxLayout()
        quick_layout.addWidget(QLabel("快速测试:"))
        
        quick_fire_btn = QPushButton("火箭弹幕")
        quick_fire_btn.clicked.connect(lambda: self.quick_test("感谢老板的火箭，太给力了！"))
        quick_layout.addWidget(quick_fire_btn)
        
        quick_gift_btn = QPushButton("礼物弹幕")
        quick_gift_btn.clicked.connect(lambda: self.quick_test("谢谢大家的礼物支持"))
        quick_layout.addWidget(quick_gift_btn)
        
        quick_666_btn = QPushButton("666弹幕")
        quick_666_btn.clicked.connect(lambda: self.quick_test("主播666，继续加油！"))
        quick_layout.addWidget(quick_666_btn)
        
        quick_normal_btn = QPushButton("普通弹幕")
        quick_normal_btn.clicked.connect(lambda: self.quick_test("普通弹幕，没有关键词"))
        quick_layout.addWidget(quick_normal_btn)
        
        test_layout.addLayout(quick_layout)
        
        layout.addWidget(test_group)
        
        # 结果显示区域
        result_group = QGroupBox("测试结果")
        result_layout = QVBoxLayout(result_group)
        
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(150)
        self.result_text.setReadOnly(True)
        result_layout.addWidget(self.result_text)
        
        layout.addWidget(result_group)
        
        # 播放列表显示区域
        playlist_group = QGroupBox("播放列表")
        playlist_layout = QVBoxLayout(playlist_group)
        
        # 播放列表表格
        self.playlist_table = QTableWidget()
        self.playlist_table.setColumnCount(4)
        self.playlist_table.setHorizontalHeaderLabels(["ID", "类型", "内容", "副视频"])
        playlist_layout.addWidget(self.playlist_table)
        
        # 播放控制按钮
        control_layout = QHBoxLayout()
        
        refresh_playlist_btn = QPushButton("刷新播放列表")
        refresh_playlist_btn.clicked.connect(self.refresh_playlist)
        control_layout.addWidget(refresh_playlist_btn)
        
        play_btn = QPushButton("播放选中项目")
        play_btn.clicked.connect(self.play_selected)
        control_layout.addWidget(play_btn)
        
        clear_btn = QPushButton("清空播放列表")
        clear_btn.clicked.connect(self.clear_playlist)
        control_layout.addWidget(clear_btn)
        
        playlist_layout.addLayout(control_layout)
        
        layout.addWidget(playlist_group)
        
        # 初始化数据
        self.refresh_config()
        self.refresh_playlist()
    
    def refresh_config(self):
        """刷新副视频配置显示"""
        try:
            config_file = Path("data/sub_videos.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                config_text = f"副视频配置 ({len(config)} 个关键词):\n"
                for keyword, data in config.items():
                    video_source = data.get('video_source', '未知')
                    scripts_count = len(data.get('scripts', []))
                    config_text += f"• {keyword} → {video_source} ({scripts_count}个话术)\n"
                
                self.config_text.setText(config_text)
            else:
                self.config_text.setText("❌ 副视频配置文件不存在")
        except Exception as e:
            self.config_text.setText(f"❌ 加载配置失败: {e}")
    
    def test_trigger(self):
        """测试副视频触发"""
        danmaku_text = self.danmaku_input.text().strip()
        if not danmaku_text:
            QMessageBox.warning(self, "警告", "请输入测试弹幕")
            return
        
        self.log_result(f"\n🎬 测试弹幕: {danmaku_text}")
        
        if self.main_window and hasattr(self.main_window, 'check_sub_video_trigger'):
            try:
                result = self.main_window.check_sub_video_trigger(danmaku_text)
                if result:
                    self.log_result(f"✅ 触发副视频: {result}")
                else:
                    self.log_result(f"⚪ 无副视频触发")
            except Exception as e:
                self.log_result(f"❌ 测试失败: {e}")
        else:
            self.log_result("❌ 主程序方法不可用")
    
    def add_to_playlist(self):
        """添加弹幕到播放列表"""
        danmaku_text = self.danmaku_input.text().strip()
        if not danmaku_text:
            QMessageBox.warning(self, "警告", "请输入测试弹幕")
            return
        
        self.log_result(f"\n🎯 添加弹幕到播放列表: {danmaku_text}")
        
        if self.main_window:
            try:
                # 检测副视频
                sub_video_result = None
                if hasattr(self.main_window, 'check_sub_video_trigger'):
                    sub_video_result = self.main_window.check_sub_video_trigger(danmaku_text)
                    self.log_result(f"🔍 副视频检测: {sub_video_result if sub_video_result else '无'}")
                
                # 添加到播放列表
                if hasattr(self.main_window, 'add_danmaku_to_playlist'):
                    self.main_window.add_danmaku_to_playlist(danmaku_text, sub_video_result)
                    self.log_result(f"✅ 已添加到播放列表")
                    
                    # 刷新显示
                    QTimer.singleShot(1000, self.refresh_playlist)
                else:
                    self.log_result(f"❌ 添加方法不可用")
                    
            except Exception as e:
                self.log_result(f"❌ 添加失败: {e}")
                import traceback
                traceback.print_exc()
        else:
            self.log_result("❌ 主程序不可用")
    
    def quick_test(self, text):
        """快速测试"""
        self.danmaku_input.setText(text)
        self.test_trigger()
    
    def refresh_playlist(self):
        """刷新播放列表显示"""
        if not self.main_window or not hasattr(self.main_window, 'playlist_items'):
            self.playlist_table.setRowCount(0)
            return
        
        playlist_items = self.main_window.playlist_items
        self.playlist_table.setRowCount(len(playlist_items))
        
        for i, item in enumerate(playlist_items):
            item_id = str(item.get('id', i+1))
            voice_type = item.get('voice_type', '未知')
            content = item.get('content', '')[:30] + ('...' if len(item.get('content', '')) > 30 else '')
            sub_video = item.get('sub_video', '无')
            
            self.playlist_table.setItem(i, 0, QTableWidgetItem(item_id))
            self.playlist_table.setItem(i, 1, QTableWidgetItem(voice_type))
            self.playlist_table.setItem(i, 2, QTableWidgetItem(content))
            self.playlist_table.setItem(i, 3, QTableWidgetItem(sub_video))
        
        self.log_result(f"📋 播放列表已刷新: {len(playlist_items)} 个项目")
    
    def play_selected(self):
        """播放选中的项目"""
        current_row = self.playlist_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请选择要播放的项目")
            return
        
        if not self.main_window or not hasattr(self.main_window, 'playlist_items'):
            return
        
        if current_row >= len(self.main_window.playlist_items):
            return
        
        item = self.main_window.playlist_items[current_row]
        content = item.get('content', '')
        sub_video = item.get('sub_video', '无')
        
        self.log_result(f"\n🎬 播放项目: {content[:30]}...")
        self.log_result(f"🎬 副视频: {sub_video}")
        
        # 测试副视频播放流程
        if sub_video and sub_video != '无':
            if hasattr(self.main_window, 'handle_sub_video_playback'):
                try:
                    result = self.main_window.handle_sub_video_playback(item)
                    if result:
                        self.log_result(f"✅ 副视频播放流程启动成功")
                    else:
                        self.log_result(f"⚠️ 副视频播放流程启动失败")
                except Exception as e:
                    self.log_result(f"❌ 副视频播放流程异常: {e}")
            else:
                self.log_result(f"❌ 副视频播放方法不可用")
        else:
            self.log_result(f"⚪ 该项目无副视频")
    
    def clear_playlist(self):
        """清空播放列表"""
        if self.main_window and hasattr(self.main_window, 'playlist_items'):
            self.main_window.playlist_items.clear()
            self.refresh_playlist()
            self.log_result(f"🗑️ 播放列表已清空")
    
    def log_result(self, message):
        """记录结果"""
        self.result_text.append(message)
        # 自动滚动到底部
        cursor = self.result_text.textCursor()
        cursor.movePosition(cursor.End)
        self.result_text.setTextCursor(cursor)

def main():
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("副视频测试程序")
    app.setApplicationVersion("1.0")
    
    window = SubVideoTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
