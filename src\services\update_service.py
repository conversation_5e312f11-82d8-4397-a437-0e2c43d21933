"""
AI主播系统 v2 - 更新服务
检查和处理系统更新
"""

import requests
import json
import os
import zipfile
import tempfile
import shutil
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path

# [FIX] 修复相对导入问题，使用try-except处理
try:
    from .logging_service import create_logger
    from .error_handler import handle_exceptions
except ImportError:
    # 如果相对导入失败，使用绝对导入或创建简单的替代品
    def create_logger(name):
        import logging
        return logging.getLogger(name)

    def handle_exceptions(service_name):
        def decorator(func):
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    print(f"[ERROR] {service_name}.{func.__name__}: {e}")
                    return None
            return wrapper
        return decorator


class UpdateService:
    """更新服务管理器"""
    
    def __init__(self, server_url: str = "http://localhost:12456", current_version: str = "2.0.0"):
        self.logger = create_logger("update_service")
        self.server_url = server_url.rstrip('/')
        self.current_version = current_version
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'AI-Broadcaster-Client/2.0.0'
        })

        # 禁用代理，直接连接
        self.session.proxies = {'http': None, 'https': None}

        # 设置超时
        self.timeout = 30
        
    @handle_exceptions("UpdateService")
    def check_for_updates(self) -> Dict[str, Any]:
        """
        检查是否有可用更新

        Returns:
            更新检查结果
        """
        try:
            self.logger.info("开始检查更新...")

            # [NEW] 使用新的更新API
            update_api_url = f"{self.server_url}/admin/api/updates/current"
            self.logger.info(f"请求更新API: {update_api_url}")

            # 发送GET请求获取最新版本信息
            response = self.session.get(update_api_url, timeout=self.timeout)

            # 检查HTTP状态码
            if response.status_code != 200:
                return {
                    'success': False,
                    'message': f'服务器错误: HTTP {response.status_code}',
                    'has_update': False
                }

            # 解析响应
            result = response.json()
            self.logger.info(f"服务器响应: {result}")

            # [NEW] 解析实际的API响应格式
            if result.get('状态') == '成功':
                data = result.get('数据', {})
                latest_version = data.get('version', '')
                description = data.get('description', '')
                release_date = data.get('release_date', '')
            else:
                # 如果不是成功状态，尝试直接解析
                latest_version = result.get('version', '')
                description = result.get('description', '')
                release_date = result.get('release_date', '')

            if not latest_version:
                return {
                    'success': False,
                    'message': '服务器返回的版本信息无效',
                    'has_update': False
                }

            # 获取当前版本（从配置文件读取）
            current_version = self.get_current_version_from_config()

            # 比较版本号
            has_update = self._compare_versions(current_version, latest_version)

            self.logger.info(f"当前版本: {current_version}, 最新版本: {latest_version}")

            return {
                'success': True,
                'has_update': has_update,
                'current_version': current_version,
                'update_info': {
                    'version': latest_version,
                    'description': description,
                    'release_date': release_date
                }
            }
                
        except requests.exceptions.ConnectionError:
            self.logger.error("无法连接到更新服务器")
            return {
                'success': False,
                'message': '无法连接到更新服务器，请检查网络连接',
                'has_update': False
            }
        except requests.exceptions.Timeout:
            self.logger.error("更新检查请求超时")
            return {
                'success': False,
                'message': '更新检查超时，请稍后重试',
                'has_update': False
            }
        except json.JSONDecodeError:
            self.logger.error("更新服务器响应格式错误")
            return {
                'success': False,
                'message': '更新服务器响应格式错误',
                'has_update': False
            }
        except Exception as e:
            self.logger.error(f"检查更新失败: {e}")
            return {
                'success': False,
                'message': f'检查更新失败: {str(e)}',
                'has_update': False
            }
            
    def _compare_versions(self, current: str, server: str) -> bool:
        """
        比较版本号
        
        Args:
            current: 当前版本
            server: 服务器版本
            
        Returns:
            是否有更新
        """
        try:
            # 简单的版本比较（可以根据需要改进）
            current_parts = [int(x) for x in current.split('.')]
            server_parts = [int(x) for x in server.split('.')]
            
            # 补齐版本号长度
            max_len = max(len(current_parts), len(server_parts))
            current_parts.extend([0] * (max_len - len(current_parts)))
            server_parts.extend([0] * (max_len - len(server_parts)))
            
            # 比较版本号
            for i in range(max_len):
                if server_parts[i] > current_parts[i]:
                    return True
                elif server_parts[i] < current_parts[i]:
                    return False
                    
            return False  # 版本相同
            
        except Exception as e:
            self.logger.error(f"版本比较失败: {e}")
            return False
            
    @handle_exceptions("UpdateService")
    def download_update(self, download_url: str, progress_callback=None) -> Dict[str, Any]:
        """
        下载更新文件
        
        Args:
            download_url: 下载地址
            progress_callback: 进度回调函数
            
        Returns:
            下载结果
        """
        try:
            self.logger.info(f"开始下载更新: {download_url}")
            
            # 创建临时目录
            temp_dir = tempfile.mkdtemp()
            update_file = os.path.join(temp_dir, "update.zip")
            
            # 下载文件
            response = self.session.get(download_url, stream=True, timeout=self.timeout)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(update_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 调用进度回调
                        if progress_callback and total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            progress_callback(progress)
                            
            self.logger.info("更新文件下载完成")
            
            return {
                'success': True,
                'file_path': update_file,
                'temp_dir': temp_dir
            }
            
        except Exception as e:
            self.logger.error(f"下载更新失败: {e}")
            return {
                'success': False,
                'message': f'下载更新失败: {str(e)}'
            }
            
    @handle_exceptions("UpdateService")
    def install_update(self, update_file: str) -> Dict[str, Any]:
        """
        安装更新
        
        Args:
            update_file: 更新文件路径
            
        Returns:
            安装结果
        """
        try:
            self.logger.info("开始安装更新...")
            
            # 创建备份目录
            backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 解压更新文件
            with zipfile.ZipFile(update_file, 'r') as zip_ref:
                extract_dir = tempfile.mkdtemp()
                zip_ref.extractall(extract_dir)
                
            # 这里应该实现具体的更新逻辑
            # 比如替换文件、更新配置等
            
            self.logger.info("更新安装完成")
            
            return {
                'success': True,
                'message': '更新安装完成，请重启应用程序',
                'backup_dir': backup_dir
            }
            
        except Exception as e:
            self.logger.error(f"安装更新失败: {e}")
            return {
                'success': False,
                'message': f'安装更新失败: {str(e)}'
            }
            
    def get_current_version(self) -> str:
        """获取当前版本"""
        return self.current_version

    def get_current_version_from_config(self) -> str:
        """从配置文件获取当前版本"""
        try:
            config_file = Path("config/app_config.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('app', {}).get('version', '2.0.0')
            return '2.0.0'
        except Exception as e:
            self.logger.error(f"读取配置文件版本失败: {e}")
            return '2.0.0'

    def update_local_version(self, new_version: str) -> bool:
        """
        更新本地配置文件中的版本号

        Args:
            new_version: 新版本号

        Returns:
            是否更新成功
        """
        try:
            self.logger.info(f"正在更新本地版本号: {new_version}")

            config_file = Path("config/app_config.json")

            # 读取当前配置
            config = {}
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

            # 确保app节点存在
            if 'app' not in config:
                config['app'] = {}

            # 更新版本号
            old_version = config['app'].get('version', '未知')
            config['app']['version'] = new_version

            # 创建配置目录（如果不存在）
            config_file.parent.mkdir(parents=True, exist_ok=True)

            # 保存配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            self.logger.info(f"版本号更新成功: {old_version} -> {new_version}")
            return True

        except Exception as e:
            self.logger.error(f"更新本地版本号失败: {e}")
            return False

    def get_update_info_text(self, update_info: Dict[str, Any]) -> str:
        """
        格式化更新信息文本

        Args:
            update_info: 更新信息字典

        Returns:
            格式化的更新信息文本
        """
        version = update_info.get('version', '未知版本')
        description = update_info.get('description', '暂无更新说明')
        release_date = update_info.get('release_date', '未知日期')

        # 格式化更新内容
        formatted_text = f"""版本号: {version}
发布日期: {release_date}

更新内容:
{description}

请选择是否已完成更新：
• 点击"已更新"：确认已手动更新到新版本
• 点击"不更新"：继续使用当前版本"""

        return formatted_text

    def set_server_url(self, url: str):
        """设置服务器地址"""
        self.server_url = url.rstrip('/')
        self.logger.info(f"设置更新服务器地址: {self.server_url}")

    def close(self):
        """关闭会话"""
        self.session.close()
