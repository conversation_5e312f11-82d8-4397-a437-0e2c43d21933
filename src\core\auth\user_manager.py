"""
AI Broadcaster v2 - 用户管理器
用户认证、注册、登录等功能
"""

import hashlib
import secrets
import jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tuple

from ...data.database_manager import DatabaseManager
from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions
from .machine_code import MachineCodeGenerator


class UserManager:
    """用户管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.logger = create_logger("user_manager")
        self.db = db_manager
        self.machine_code_gen = MachineCodeGenerator()
        self.jwt_secret = "ai_broadcaster_v2_secret_key"  # 在生产环境中应该使用环境变量
        self.current_user = None
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{password_hash.hex()}"
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        try:
            salt, stored_hash = password_hash.split(':')
            password_hash_check = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return password_hash_check.hex() == stored_hash
        except:
            return False
    
    def _generate_token(self, user_id: int, username: str) -> str:
        """生成JWT令牌"""
        payload = {
            'user_id': user_id,
            'username': username,
            'exp': datetime.utcnow() + timedelta(days=30),  # 30天过期
            'iat': datetime.utcnow()
        }
        return jwt.encode(payload, self.jwt_secret, algorithm='HS256')
    
    def _verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证JWT令牌"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            self.logger.warning("令牌已过期")
            return None
        except jwt.InvalidTokenError:
            self.logger.warning("无效令牌")
            return None
    
    @handle_exceptions("UserManager")
    def register_user(self, username: str, password: str, phone: str = "") -> Tuple[bool, str]:
        """
        注册用户
        
        Args:
            username: 用户名
            password: 密码
            phone: 手机号
            
        Returns:
            (是否成功, 消息)
        """
        try:
            # 检查用户名是否已存在
            existing_user = self.db.execute_query(
                "SELECT id FROM users WHERE username = ?", (username,)
            )
            if existing_user:
                return False, "用户名已存在"
            
            # 生成机器码
            machine_code = self.machine_code_gen.generate_machine_code()
            
            # 哈希密码
            password_hash = self._hash_password(password)
            
            # 插入用户
            user_id = self.db.execute_insert(
                """INSERT INTO users (username, password_hash, phone, machine_code) 
                   VALUES (?, ?, ?, ?)""",
                (username, password_hash, phone, machine_code)
            )
            
            if user_id:
                self.logger.log_success(f"用户注册成功: {username}")
                return True, "注册成功"
            else:
                return False, "注册失败"
                
        except Exception as e:
            self.logger.log_failure(f"用户注册失败: {e}")
            return False, f"注册失败: {str(e)}"
    
    @handle_exceptions("UserManager")
    def login_user(self, username: str, password: str) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        用户登录
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            (是否成功, 消息, 用户信息)
        """
        try:
            # 查询用户
            users = self.db.execute_query(
                """SELECT id, username, password_hash, phone, machine_code, 
                          token, token_expiry, expire_time 
                   FROM users WHERE username = ?""",
                (username,)
            )
            
            if not users:
                return False, "用户不存在", None
            
            user = dict(users[0])
            
            # 验证密码
            if not self._verify_password(password, user['password_hash']):
                return False, "密码错误", None
            
            # 验证机器码
            current_machine_code = self.machine_code_gen.generate_machine_code()
            if user['machine_code'] and user['machine_code'] != current_machine_code:
                self.logger.warning(f"机器码不匹配: 存储={user['machine_code']}, 当前={current_machine_code}")
                # 可以选择是否允许登录或要求重新绑定
                # return False, "设备不匹配，请联系管理员", None
            
            # 检查账户是否过期
            if user['expire_time']:
                expire_time = datetime.fromisoformat(user['expire_time'])
                if datetime.now() > expire_time:
                    return False, "账户已过期", None
            
            # 生成新令牌
            token = self._generate_token(user['id'], user['username'])
            token_expiry = datetime.now() + timedelta(days=30)
            
            # 更新令牌和机器码
            self.db.execute_update(
                """UPDATE users SET token = ?, token_expiry = ?, machine_code = ?, 
                          updated_at = CURRENT_TIMESTAMP WHERE id = ?""",
                (token, token_expiry.isoformat(), current_machine_code, user['id'])
            )
            
            # 设置当前用户
            self.current_user = {
                'id': user['id'],
                'username': user['username'],
                'phone': user['phone'],
                'token': token,
                'machine_code': current_machine_code
            }
            
            self.logger.log_success(f"用户登录成功: {username}")
            return True, "登录成功", self.current_user
            
        except Exception as e:
            self.logger.log_failure(f"用户登录失败: {e}")
            return False, f"登录失败: {str(e)}", None
    
    @handle_exceptions("UserManager")
    def logout_user(self) -> bool:
        """用户登出"""
        if self.current_user:
            self.logger.info(f"用户登出: {self.current_user['username']}")
            self.current_user = None
            return True
        return False
    
    @handle_exceptions("UserManager")
    def verify_token_login(self, token: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        通过令牌验证登录
        
        Args:
            token: JWT令牌
            
        Returns:
            (是否成功, 用户信息)
        """
        try:
            # 验证令牌
            payload = self._verify_token(token)
            if not payload:
                return False, None
            
            # 查询用户
            users = self.db.execute_query(
                """SELECT id, username, phone, machine_code, expire_time 
                   FROM users WHERE id = ? AND token = ?""",
                (payload['user_id'], token)
            )
            
            if not users:
                return False, None
            
            user = dict(users[0])
            
            # 检查账户是否过期
            if user['expire_time']:
                expire_time = datetime.fromisoformat(user['expire_time'])
                if datetime.now() > expire_time:
                    return False, None
            
            # 验证机器码
            current_machine_code = self.machine_code_gen.generate_machine_code()
            if user['machine_code'] != current_machine_code:
                self.logger.warning("机器码不匹配，令牌登录失败")
                return False, None
            
            # 设置当前用户
            self.current_user = {
                'id': user['id'],
                'username': user['username'],
                'phone': user['phone'],
                'token': token,
                'machine_code': current_machine_code
            }
            
            return True, self.current_user
            
        except Exception as e:
            self.logger.error(f"令牌验证失败: {e}")
            return False, None
    
    @handle_exceptions("UserManager")
    def change_password(self, old_password: str, new_password: str) -> Tuple[bool, str]:
        """
        修改密码
        
        Args:
            old_password: 旧密码
            new_password: 新密码
            
        Returns:
            (是否成功, 消息)
        """
        if not self.current_user:
            return False, "请先登录"
        
        try:
            # 验证旧密码
            users = self.db.execute_query(
                "SELECT password_hash FROM users WHERE id = ?",
                (self.current_user['id'],)
            )
            
            if not users or not self._verify_password(old_password, users[0]['password_hash']):
                return False, "旧密码错误"
            
            # 更新密码
            new_password_hash = self._hash_password(new_password)
            rows_affected = self.db.execute_update(
                "UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (new_password_hash, self.current_user['id'])
            )
            
            if rows_affected > 0:
                self.logger.log_success("密码修改成功")
                return True, "密码修改成功"
            else:
                return False, "密码修改失败"
                
        except Exception as e:
            self.logger.log_failure(f"密码修改失败: {e}")
            return False, f"密码修改失败: {str(e)}"
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """获取当前登录用户"""
        return self.current_user
    
    def is_logged_in(self) -> bool:
        """检查是否已登录"""
        return self.current_user is not None
    
    @handle_exceptions("UserManager")
    def get_user_info(self, user_id: int) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        users = self.db.execute_query(
            """SELECT id, username, phone, machine_code, created_at, 
                      expire_time FROM users WHERE id = ?""",
            (user_id,)
        )
        return dict(users[0]) if users else None
    
    @handle_exceptions("UserManager")
    def update_user_expire_time(self, user_id: int, expire_time: datetime) -> bool:
        """更新用户过期时间"""
        try:
            rows_affected = self.db.execute_update(
                "UPDATE users SET expire_time = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (expire_time.isoformat(), user_id)
            )
            return rows_affected > 0
        except Exception as e:
            self.logger.error(f"更新用户过期时间失败: {e}")
            return False
