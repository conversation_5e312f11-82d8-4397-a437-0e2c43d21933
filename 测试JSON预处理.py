#!/usr/bin/env python3
"""
测试JSON预处理逻辑
"""

import json

def test_json_preprocessing():
    """测试JSON预处理"""
    print("=== 测试JSON预处理 ===")
    
    # 模拟从服务器获取的包含实际换行符的JSON（这会导致解析失败）
    raw_json_with_newlines = """{
  "0秒 - 10秒": "1***哈喽，大家好，首先感谢【大哥|哥哥|宝子】们的捧场。
2***稍作停留也是爱，所能接触的都是这四个大字。
3***没有花里胡哨，没有弯弯绕绕。",
  "10秒 - 20秒": "4***自古套路得人心，但唯有真心得天下。
5***新来的家人们，相遇就是缘分。
6***你们的到来蓬荜生辉。"
}"""
    
    print(f"原始JSON（包含实际换行符）:")
    print(f"长度: {len(raw_json_with_newlines)} 字符")
    print(f"内容: {repr(raw_json_with_newlines[:150])}...")
    
    # 尝试直接解析（应该失败）
    print(f"\n1. 直接解析测试:")
    try:
        data = json.loads(raw_json_with_newlines)
        print(f"✅ 直接解析成功（意外）")
    except json.JSONDecodeError as e:
        print(f"❌ 直接解析失败（预期）: {e}")
    
    # 预处理JSON内容
    print(f"\n2. 预处理测试:")
    processed_content = raw_json_with_newlines.replace('\n', '\\n').replace('\r', '\\r').replace('\t', '\\t')
    print(f"预处理后内容: {repr(processed_content[:150])}...")
    
    # 尝试解析预处理后的内容
    try:
        data = json.loads(processed_content)
        print(f"✅ 预处理后解析成功")
        print(f"解析到 {len(data)} 个时间段")
        
        # 检查格式
        is_new_format = all(isinstance(v, str) for v in data.values())
        print(f"格式检测: {'新格式' if is_new_format else '旧格式'}")
        
        # 验证内容
        for key, value in data.items():
            print(f"\n时间段: {key}")
            print(f"原始值: {repr(value[:50])}...")
            
            # 智能检测换行符格式
            if '\\n' in value:
                lines = value.split('\\n')
                print(f"检测到转义换行符格式，分割为 {len(lines)} 行")
            else:
                lines = value.split('\n')
                print(f"检测到实际换行符格式，分割为 {len(lines)} 行")
            
            script_lines = [line for line in lines if line.strip() and '***' in line]
            print(f"话术行数: {len(script_lines)}")
            
            # 显示前3行
            for i, line in enumerate(lines[:3], 1):
                if line.strip():
                    print(f"  {i}. {line}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ 预处理后解析仍然失败: {e}")
        return False

def test_real_case_simulation():
    """测试真实情况模拟"""
    print("\n=== 测试真实情况模拟 ===")
    
    # 模拟从日志中看到的真实JSON内容（包含实际换行符）
    real_json_content = """{
  "0秒 - 10秒": "1***哈喽，大家好，首先感谢【大哥|哥哥|宝子】们的捧场。
2***稍作停留也是爱，所能接触的都是这四个大字，随便那个都是这四个大字。
3***没有花里胡哨，没有弯弯绕绕。
4***有啥就问，新来的家人们，不要藏着掖着。
5***云想衣裳花想容，春风拂槛露华浓，仙侠界的白莲花，花容月貌顶呱呱。",
  "10秒 - 20秒": "6***自古套路得人心，但唯有真心得天下，咱们心不要慌，手不要抖，跟着感觉走。
7***新来的家人们，相遇就是缘分，不要犹豫不要徘徊。
8***你们的到来蓬荜生辉，老妹我深感荣幸
9***随便什么数字都是这四个大字，目光所及都是这四个大字。
10***俗话说的好，大哥们，人生不主动，快乐少一半",
  "20秒 - 30秒": "11***相信自己的眼睛，相信自己的耳朵
12***斩青丝斩难过斩断红尘不为过，建模美姿势帅，呼风唤雨惹人爱
13***不穿摸，不贴片，仙侠界的彭于晏，（千人捧万人追）手游界的刘亦菲
14***播播间人很多，主播一个人一张嘴，忙不过来，理解下，大哥们
15***万水千山总是情，今天主播最热情"
}"""
    
    print(f"真实JSON内容:")
    print(f"长度: {len(real_json_content)} 字符")
    
    # 模拟解析函数的完整逻辑
    def parse_time_segment_json_format_simulation(script_name, content):
        """模拟解析函数"""
        try:
            import json
            import re

            print(f"[SEARCH] ⭐ 开始解析时间段JSON格式，话术: {script_name}")
            print(f"[INFO] JSON内容长度: {len(content)} 字符")

            # [FIX] 预处理JSON内容：将实际换行符转义为 \\n
            processed_content = content.replace('\n', '\\n').replace('\r', '\\r').replace('\t', '\\t')
            print(f"[FIX] 预处理JSON内容，转义控制字符")

            # 尝试解析JSON
            time_segments_data = json.loads(processed_content)

            if not isinstance(time_segments_data, dict):
                print(f"[WARNING] ❌ JSON数据不是字典格式，类型: {type(time_segments_data)}")
                return False

            print(f"[OK] ✅ JSON解析成功，包含 {len(time_segments_data)} 个顶级键")

            # 检查是否为新格式：所有值都是字符串
            is_new_format = all(isinstance(v, str) for v in time_segments_data.values())
            print(f"[INFO] 🔍 检测格式类型: {'新格式' if is_new_format else '旧格式'}")

            if not is_new_format:
                print(f"[WARNING] ❌ 不是新的时间段格式，跳过解析")
                return False

            # 初始化时间段数据结构
            script_time_segments = {script_name: {}}

            # 解析每个时间段
            parsed_count = 0
            for time_key, time_data in time_segments_data.items():
                print(f"   处理时间段: '{time_key}'")

                if isinstance(time_data, str):
                    segment_content = time_data

                    # 提取时间信息
                    pattern = r'(\d+)秒\s*-\s*(\d+)秒'
                    match = re.search(pattern, time_key)

                    if match:
                        start_time = int(match.group(1))
                        end_time = int(match.group(2))
                    else:
                        start_time = parsed_count * 10
                        end_time = (parsed_count + 1) * 10

                    print(f"      时间: {start_time}-{end_time}秒")

                    # 智能检测换行符格式并统计话术行数
                    if '\\n' in segment_content:
                        content_lines = segment_content.split('\\n')
                        print(f"      检测到转义换行符格式 (\\n)")
                    else:
                        content_lines = segment_content.split('\n')
                        print(f"      检测到实际换行符格式 (\\n)")

                    script_lines = [line for line in content_lines if line.strip() and '***' in line]
                    print(f"      行数: {len(script_lines)}/{len(content_lines)}")

                    # 存储数据
                    script_time_segments[script_name][time_key] = {
                        'start': start_time,
                        'end': end_time,
                        'content': segment_content
                    }

                    print(f"   ✅ 解析时间段: '{time_key}'")
                    parsed_count += 1

            print(f"[DART] ⭐ 成功解析 {parsed_count}/{len(time_segments_data)} 个时间段数据")
            result = parsed_count > 0
            print(f"[RESULT] 🎯 解析函数返回: {result}")
            return result

        except json.JSONDecodeError as e:
            print(f"[ERROR] ❌ JSON解析失败: {e}")
            return False
        except Exception as e:
            print(f"[ERROR] ❌ 解析异常: {e}")
            return False

    # 执行模拟解析
    success = parse_time_segment_json_format_simulation("kaer", real_json_content)
    
    return success

def main():
    """主测试函数"""
    print("开始测试JSON预处理逻辑...")
    
    try:
        # 测试JSON预处理
        success1 = test_json_preprocessing()
        
        # 测试真实情况模拟
        success2 = test_real_case_simulation()
        
        print(f"\n=== 测试总结 ===")
        if success1 and success2:
            print("✅ JSON预处理逻辑正确")
            print("✅ 能够处理包含实际换行符的JSON")
            print("✅ 解析函数应该能正常工作")
            
            print(f"\n🔧 关键修复:")
            print("1. 在JSON解析前预处理内容")
            print("2. 将实际换行符 \\n 转义为 \\\\n")
            print("3. 智能检测换行符格式")
            print("4. 正确统计话术行数")
            
            print(f"\n📋 预期效果:")
            print("- 解析函数返回 True")
            print("- 缓存被正确清空")
            print("- 显示时间段概览而不是JSON")
            
        else:
            print("❌ 测试失败")
            print("需要进一步调试")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    main()
