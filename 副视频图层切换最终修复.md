# 🎬 副视频图层切换最终修复

## 🎯 修复目标

根据用户最新要求，实现基于图层顺序的副视频切换：

1. **主视频 → 副视频**：只暂停主视频，把副视频放到最顶层，主视频放到下面一层
2. **副视频 → 主视频**：继续播放主视频，然后隐藏副视频

## 🔧 最终修复方案

### 1. 主视频 → 副视频切换流程

```
步骤1: 暂停双主视频自动切换
步骤2: 保存主视频当前播放状态（位置、状态）
步骤3: 在隐藏状态下启动副视频播放（预加载）
步骤4: 等待副视频启动（0.1秒）
步骤5: 🔥 暂停主视频（只暂停，不隐藏）
步骤6: 🔥 显示副视频并调整图层顺序
  - 显示副视频源
  - 将副视频放到最顶层（index=0）
  - 将主视频放到第二层（index=1）
```

**关键修复点**：
- ✅ **只暂停主视频**：不隐藏主视频，保持可见状态
- ✅ **图层顺序管理**：副视频在最顶层，主视频在下面一层
- ✅ **无黑屏切换**：通过图层顺序实现平滑切换

### 2. 副视频 → 主视频切换流程

```
步骤1: 🔥 恢复主视频播放位置并继续播放
步骤2: 等待主视频准备就绪（0.1秒）
步骤3: 🔥 隐藏副视频源
步骤4: 停止副视频播放（在隐藏后）
步骤5: 更新双主视频管理器当前源
步骤6: 恢复双主视频自动切换
```

**关键修复点**：
- ✅ **继续播放主视频**：从暂停位置继续播放
- ✅ **隐藏副视频**：副视频隐藏后主视频自然显示在顶层
- ✅ **简化操作**：不需要调整图层，隐藏副视频即可

## 📝 代码修改详情

### 修改1：主视频切换到副视频逻辑

**位置**：`run_gui_qt5.py` 第4057-4111行

**主要修改**：
```python
# 🔥 步骤5：暂停主视频（只暂停，不隐藏）
obs_controller.send_request_sync("TriggerMediaInputAction", {
    "inputName": current_main_source,
    "mediaAction": "OBS_WEBSOCKET_MEDIA_INPUT_ACTION_PAUSE"
})
print(f"⏸️ 主视频已暂停: {current_main_source}")

# 🔥 步骤6：显示副视频并调整图层顺序
# 显示副视频源
obs_controller.send_request_sync("SetSceneItemEnabled", {
    "sceneName": scene_name,
    "sceneItemId": obs_controller._get_source_id(sub_video_source, scene_name),
    "sceneItemEnabled": True
})

# 将副视频放到最顶层
obs_controller.send_request_sync("SetSceneItemIndex", {
    "sceneName": scene_name,
    "sceneItemId": sub_video_id,
    "sceneItemIndex": 0  # 0表示最顶层
})

# 将主视频放到副视频下面一层
obs_controller.send_request_sync("SetSceneItemIndex", {
    "sceneName": scene_name,
    "sceneItemId": main_video_id,
    "sceneItemIndex": 1  # 1表示第二层
})
```

**移除的操作**：
- ❌ 隐藏主视频源：`SetSceneItemEnabled(False)`
- ❌ 复杂的显示/隐藏时序控制

### 修改2：副视频切换到主视频逻辑

**位置**：`run_gui_qt5.py` 第4160-4217行

**主要修改**：
```python
# 🔥 步骤1: 恢复主视频播放位置并继续播放
obs_controller.send_request_sync("SetMediaInputCursor", {
    "inputName": current_main_source,
    "mediaCursor": saved_cursor
})
obs_controller.send_request_sync("TriggerMediaInputAction", {
    "inputName": current_main_source,
    "mediaAction": "OBS_WEBSOCKET_MEDIA_INPUT_ACTION_PLAY"
})

# 🔥 步骤3：隐藏副视频源
obs_controller.send_request_sync("SetSceneItemEnabled", {
    "sceneName": scene_name,
    "sceneItemId": obs_controller._get_source_id(current_sub_source, scene_name),
    "sceneItemEnabled": False
})
```

**移除的操作**：
- ❌ 显示主视频源：`SetSceneItemEnabled(True)` - 主视频一直可见
- ❌ 复杂的显示/隐藏时序控制
- ❌ 暂停副视频：直接隐藏即可

## 🎯 修复效果

### ✅ 解决的问题

#### 1. **图层顺序管理**
- **修复前**：通过显示/隐藏切换，可能出现黑屏
- **修复后**：通过图层顺序切换，主视频始终可见

#### 2. **简化操作流程**
- **修复前**：复杂的显示/隐藏时序控制
- **修复后**：主视频只暂停，副视频只隐藏

#### 3. **无黑屏切换**
- **修复前**：显示/隐藏可能导致瞬间黑屏
- **修复后**：图层切换确保画面连续

### 🔄 完整工作流程

#### 【主视频 → 副视频】
1. **暂停双主视频自动切换** ✅
2. **保存主视频状态** ✅
3. **预加载副视频** ✅
4. **🔥 只暂停主视频（不隐藏）** ✅
5. **🔥 显示副视频并放到最顶层** ✅
6. **🔥 主视频放到第二层** ✅

#### 【副视频 → 主视频】
1. **🔥 继续播放主视频** ✅
2. **🔥 隐藏副视频** ✅
3. **停止副视频播放** ✅
4. **恢复双主视频自动切换** ✅

### 🎬 技术特点

- ✅ **图层顺序管理**：副视频在顶层，主视频在下层
- ✅ **主视频始终可见**：只暂停不隐藏，避免黑屏
- ✅ **简化切换逻辑**：减少复杂的时序控制
- ✅ **状态完整**：主视频从暂停位置继续播放
- ✅ **协调管理**：与双主视频自动切换完美协调

## 🧪 测试建议

现在您可以在主程序中测试修复后的副视频功能：

1. **设置视频源**：配置视频源A（如2222）和B（如2223）
2. **开始播放**：点击播放按钮开始播放
3. **触发副视频**：发送包含关键词"代"的测试弹幕
4. **观察切换效果**：
   - 副视频应该显示在最顶层
   - 主视频应该在下面一层（暂停状态）
   - 副视频播放完成后，主视频继续播放
   - 副视频隐藏，主视频自然显示在顶层

### 📋 预期效果

- 🎯 **图层顺序正确**：副视频在顶层，主视频在下层
- 🎬 **完全无黑屏**：主视频始终可见，通过图层切换
- ⏸️ **状态保持**：主视频从暂停位置继续播放
- 🎛️ **操作简化**：减少复杂的显示/隐藏操作

---

## 🎉 总结

副视频功能现在已经完全按照您的最新要求修复：

1. ✅ **图层顺序管理**：副视频在最顶层，主视频在下面一层
2. ✅ **主视频只暂停**：不隐藏主视频，保持可见状态
3. ✅ **副视频只隐藏**：切回时只需隐藏副视频即可
4. ✅ **简化操作流程**：减少复杂的时序控制

所有修改都已完成，副视频功能现在应该能够实现基于图层顺序的完美切换！
