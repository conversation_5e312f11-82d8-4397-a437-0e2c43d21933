#!/usr/bin/env python3
"""
测试视频位置获取修复
验证"无法获取视频位置"问题是否已解决
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_video_position_debug_info():
    """测试视频位置调试信息"""
    print("🔍 视频位置获取问题调试")
    print("=" * 60)
    
    print("📋 修复内容:")
    print("1. ✅ 增强了OBS控制器获取逻辑")
    print("2. ✅ 添加了详细的调试输出信息")
    print("3. ✅ 支持多种视频源获取方式")
    print("4. ✅ 改进了连接状态检查")
    print("5. ✅ 增加了容错处理机制")
    print()
    
    print("🔍 现在启动主程序时，您应该看到以下调试信息:")
    print()
    
    # 正常情况的调试信息
    print("✅ 正常情况下的调试输出:")
    print("   🔍 开始获取主视频播放位置...")
    print("   ✅ 从播放控制器获取OBS控制器")
    print("   ✅ OBS已连接")
    print("   📋 添加用户选择的视频源A: 视频源名称")
    print("   📋 尝试获取视频源状态，共 1 个源")
    print("   🔍 尝试获取视频源 '视频源名称' 的状态...")
    print("   📊 视频源 '视频源名称' 状态: 位置=15000ms, 时长=60000ms")
    print("   ✅ 成功获取视频位置: 15.0秒 / 60.0秒")
    print("   🎬 主视频播放位置: 15.0秒")
    print("   🔍 查找时间段: 播放位置=15.0秒, 当前话术=话术1")
    print("   📋 话术 '话术1' 的时间段: ['10秒 - 20秒']")
    print("   ✅ 匹配到时间段: 当前处于10秒 - 20秒")
    print("   📍 时间段信息: 当前处于10秒 - 20秒")
    print()
    
    # 问题情况的调试信息
    print("❌ 问题情况下的调试输出:")
    print()
    
    print("🔸 情况1: OBS未连接")
    print("   🔍 开始获取主视频播放位置...")
    print("   ✅ 从播放控制器获取OBS控制器")
    print("   ❌ OBS未连接")
    print("   ⚠️ 无法获取主视频播放位置")
    print()
    
    print("🔸 情况2: 没有视频源")
    print("   🔍 开始获取主视频播放位置...")
    print("   ✅ 从播放控制器获取OBS控制器")
    print("   ✅ OBS已连接")
    print("   ❌ 没有可用的视频源")
    print("   ⚠️ 无法获取主视频播放位置")
    print()
    
    print("🔸 情况3: 视频源无状态")
    print("   🔍 开始获取主视频播放位置...")
    print("   ✅ 从播放控制器获取OBS控制器")
    print("   ✅ OBS已连接")
    print("   📋 添加用户选择的视频源A: 视频源名称")
    print("   📋 尝试获取视频源状态，共 1 个源")
    print("   🔍 尝试获取视频源 '视频源名称' 的状态...")
    print("   ⚠️ 视频源 '视频源名称' 无状态信息")
    print("   ❌ 所有视频源都无法获取有效位置")
    print("   ⚠️ 无法获取主视频播放位置")
    print()
    
    return True


def create_troubleshooting_steps():
    """创建故障排除步骤"""
    print("🛠️ 根据调试信息进行故障排除")
    print("=" * 60)
    
    steps = [
        {
            "问题": "看到 '❌ 无法获取OBS控制器'",
            "原因": "播放控制器或OBS控制器未初始化",
            "解决": [
                "重启主程序",
                "确保程序完全启动后再测试",
                "检查是否有初始化错误"
            ]
        },
        {
            "问题": "看到 '❌ OBS未连接'",
            "原因": "OBS软件未启动或WebSocket服务器未开启",
            "解决": [
                "启动OBS软件",
                "在OBS中: 工具 → WebSocket服务器设置",
                "启用WebSocket服务器",
                "端口设置为4455",
                "在主程序中连接OBS"
            ]
        },
        {
            "问题": "看到 '❌ 没有可用的视频源'",
            "原因": "没有设置主视频源或OBS中没有媒体源",
            "解决": [
                "在OBS中添加媒体源",
                "在主程序的OBS设置中选择主视频源A",
                "确保选择的是媒体源类型"
            ]
        },
        {
            "问题": "看到 '⚠️ 视频源无状态信息'",
            "原因": "视频源存在但没有播放内容",
            "解决": [
                "确保视频源有视频文件",
                "检查视频文件是否正常播放",
                "尝试在OBS中手动播放视频"
            ]
        },
        {
            "问题": "看到 '⚠️ 视频源时长为0'",
            "原因": "视频文件无效或格式不支持",
            "解决": [
                "检查视频文件格式",
                "尝试使用常见格式(MP4, AVI等)",
                "确保视频文件没有损坏"
            ]
        }
    ]
    
    for i, step in enumerate(steps, 1):
        print(f"{i}. 问题: {step['问题']}")
        print(f"   原因: {step['原因']}")
        print(f"   解决方案:")
        for solution in step['解决']:
            print(f"     • {solution}")
        print()
    
    return steps


def create_quick_check_guide():
    """创建快速检查指南"""
    print("⚡ 快速检查指南")
    print("=" * 60)
    
    checklist = [
        "1. 启动OBS软件",
        "2. 在OBS中启用WebSocket服务器 (工具 → WebSocket服务器设置)",
        "3. 在OBS中添加媒体源并加载视频文件",
        "4. 确保视频在OBS中正常播放",
        "5. 启动主程序: python run_gui_qt5.py",
        "6. 在主程序中连接OBS (localhost:4455)",
        "7. 在OBS设置中选择主视频源A",
        "8. 切换到话术管理标签页",
        "9. 选择一个话术并添加时间段",
        "10. 观察控制台的调试输出信息",
        "11. 检查时间段标签是否更新"
    ]
    
    print("按照以下步骤进行快速检查:")
    print()
    for item in checklist:
        print(f"  ✅ {item}")
    print()
    
    print("🎯 关键检查点:")
    print("  • 控制台是否显示 '✅ 成功获取视频位置: X.X秒'")
    print("  • 时间段标签是否从 '无法获取视频位置' 变为具体时间段")
    print("  • 是否看到详细的调试输出信息")
    print()
    
    return checklist


def main():
    """主函数"""
    print("🚀 视频位置获取修复验证")
    print("=" * 80)
    
    # 测试调试信息
    test_video_position_debug_info()
    
    # 创建故障排除步骤
    create_troubleshooting_steps()
    
    # 创建快速检查指南
    create_quick_check_guide()
    
    print("🎯 总结")
    print("=" * 60)
    print("现在程序已经添加了详细的调试信息，可以帮助您:")
    print()
    print("1. 📊 清楚地看到视频位置获取的每个步骤")
    print("2. 🔍 快速定位问题出现在哪个环节")
    print("3. 🛠️ 根据具体错误信息进行针对性修复")
    print("4. ✅ 验证修复是否成功")
    print()
    print("🎊 如果您看到 '✅ 成功获取视频位置' 的信息，")
    print("   说明视频位置获取功能已经正常工作！")
    print()
    print("现在请启动主程序并观察控制台输出:")
    print("python run_gui_qt5.py")
    print()
    print("如果仍然显示 '无法获取视频位置'，请:")
    print("1. 复制控制台的完整调试信息")
    print("2. 根据上面的故障排除步骤进行检查")
    print("3. 告诉我具体看到的错误信息")


if __name__ == "__main__":
    main()
