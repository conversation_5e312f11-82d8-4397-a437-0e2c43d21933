# 播放逻辑和副视频功能记录

## 📋 当前播放逻辑概述

### 核心播放流程

1. **播放启动** (`simple_start_audio`)
   - 检查音频文件是否存在
   - 检查副视频需求并切换
   - 设置播放状态变量
   - 启动音频播放

2. **播放监控** (内部音频播放器)
   - 使用pygame或其他音频后端播放
   - 监控播放状态和进度
   - 播放完成时触发回调

3. **播放完成处理** (`simple_on_audio_finished`)
   - 更新播放状态
   - 处理副视频切换回主视频
   - 清理播放项目
   - 应用播放间隔
   - 继续下一个播放项目

### 播放列表管理

- **优先级系统**: 弹幕话术 > 报时话术 > 主视频话术
- **状态管理**: 未下载 -> 下载中 -> 已下载 -> 播放中 -> 已播放
- **自动补充**: 主视频话术播放完成后自动补充同时间段的新话术
- **持久化**: 播放列表自动保存到 `data/playlist.json`

## 🎬 副视频功能实现

### 副视频检测逻辑

```python
def simple_start_audio(self, item):
    # 检查播放项目中的副视频字段
    sub_video_source = item.get('sub_video', '无')
    if sub_video_source and sub_video_source != '无':
        # 执行副视频切换
        success = self.switch_to_sub_video_with_obs(sub_video_source)
        if success:
            self.sub_video_return_callback = True
```

### 副视频切换流程

#### 切换到副视频 (`switch_to_sub_video_with_obs`)

1. **检查OBS控制器**
   - 优先使用 `playback_controller.obs_controller`
   - 回退到 `self.obs_controller`

2. **暂停主视频**
   - 调用 `pause_media_source(current_main_source)`
   - 隐藏主视频源 `set_source_visibility(current_main_source, False)`

3. **显示副视频**
   - 显示副视频源 `set_source_visibility(sub_video_source, True)`
   - 开始播放副视频 `play_media_source(sub_video_source)`

#### 切换回主视频 (`switch_back_to_main_video_with_obs`)

1. **隐藏所有副视频源**
   - 从AI对话数据中获取所有副视频源列表
   - 逐个隐藏和停止播放

2. **恢复主视频**
   - 显示主视频源 `set_source_visibility(current_main_source, True)`
   - 恢复播放 `resume_media_source(current_main_source)`

### 副视频回调机制

```python
# 播放开始时设置回调标志
if sub_video_source and sub_video_source != '无':
    self.sub_video_return_callback = True

# 播放完成时检查回调标志
if hasattr(self, 'sub_video_return_callback') and self.sub_video_return_callback:
    self.switch_back_to_main_video_with_obs()
    self.sub_video_return_callback = False
```

## 🔧 关键函数说明

### 播放控制函数

- `simple_start_audio(item)` - 开始播放音频，支持副视频切换
- `simple_on_audio_finished(item)` - 播放完成处理，包含副视频回调
- `apply_simple_playback_interval()` - 应用播放间隔

### 副视频控制函数

- `switch_to_sub_video_with_obs(sub_video_source)` - 切换到副视频
- `switch_back_to_main_video_with_obs()` - 切换回主视频
- `hide_all_sub_videos_with_obs(obs_controller)` - 隐藏所有副视频源
- `get_current_main_video_source()` - 获取当前主视频源

### 播放列表管理函数

- `get_next_playback_item_strict()` - 获取下一个播放项目（严格优先级）
- `update_table_display()` - 更新播放列表表格显示
- `save_playlist_to_file()` - 保存播放列表到文件

## 🎯 副视频功能特点

### 1. 无缝切换
- 播放语音前先切换到副视频
- 语音播放完成后自动切换回主视频
- 不影响正常的语音播放功能

### 2. 智能检测
- 基于播放列表项目的 `sub_video` 字段
- 支持动态副视频关键词检测
- 自动设置和清除回调标志

### 3. 错误处理
- OBS连接失败时继续正常播放
- 副视频切换失败时不影响语音播放
- 详细的日志记录便于调试

### 4. 兼容性
- 保持现有播放逻辑不变
- 向后兼容旧的播放列表格式
- 支持多种OBS控制器实现

## 📊 播放状态管理

### 状态变量

- `is_audio_playing` - 主要播放状态标志
- `current_playing_item` - 当前播放的项目
- `sub_video_return_callback` - 副视频回调标志
- `audio_playing` / `play_lock` - 兼容性状态变量

### 状态转换

```
未播放 -> 播放中 -> 播放完成
   |         |         |
   |         |         v
   |         |    副视频回调检查
   |         |         |
   |         |         v
   |         |    切换回主视频
   |         |         |
   |         v         v
   |    副视频切换    状态重置
   |         |         |
   v         v         v
播放间隔 -> 下一个项目 -> 循环
```

## 🔄 完整工作流程示例

### 场景：播放包含副视频的主视频话术

1. **检测阶段**
   ```
   项目: 主视频话术 - "今天我们来玩一个新游戏"
   副视频: GameVideoSource
   ```

2. **切换阶段**
   ```
   🎬 检测到副视频项目: GameVideoSource
   ⏸️ 暂停当前主视频
   🙈 隐藏主视频源
   👁️ 显示副视频源: GameVideoSource
   ▶️ 开始播放副视频
   ✅ 设置副视频回调标志
   ```

3. **播放阶段**
   ```
   🎵 开始播放语音: "今天我们来玩一个新游戏"
   🔊 设置音量
   ▶️ 启动音频播放
   ```

4. **完成阶段**
   ```
   🔔 语音播放完成
   🔍 检查副视频回调标志
   🔄 切换回主视频
   🙈 隐藏副视频源
   👁️ 显示主视频源
   ▶️ 恢复主视频播放
   ✅ 清除副视频回调标志
   ```

5. **继续阶段**
   ```
   🔄 应用播放间隔
   🎯 查找下一个播放项目
   🔄 开始下一轮播放
   ```

## ⚠️ 注意事项

### 1. 保持现有功能
- 所有现有的播放相关代码都已保留
- 副视频功能是在现有基础上的增强
- 不会影响正常的语音播放流程

### 2. OBS依赖
- 副视频功能需要OBS连接
- OBS未连接时会跳过副视频切换
- 不会因为OBS问题而中断播放

### 3. 性能考虑
- 副视频切换操作异步执行
- 错误处理不会阻塞播放流程
- 详细日志便于问题排查

### 4. 扩展性
- 支持添加更多副视频源
- 可以扩展副视频切换逻辑
- 兼容未来的OBS API变化
