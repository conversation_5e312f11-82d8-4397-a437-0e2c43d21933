"""
AI Broadcaster v2 - 播放控制器
集成音频播放、优先级管理、OBS控制等功能
"""

import time
import threading
import random
from typing import Optional, Callable, Dict, Any, List
from pathlib import Path

from .playback.priority_playlist_manager import PriorityPlaylistManager, VoiceType
from .playback.dual_video_manager import DualVideoManager
from ..services.audio_player import AudioPlayer
from ..services.voice_synthesis import VoiceSynthesis
from ..services.obs_controller import OBSController
from ..services.logging_service import create_logger
from ..services.error_handler import handle_exceptions


class PlaybackController:
    """播放控制器"""
    
    def __init__(self):
        self.logger = create_logger("playback_controller")
        
        # 核心组件
        self.playlist_manager = PriorityPlaylistManager()
        self.audio_player = AudioPlayer()
        self.voice_synthesis = VoiceSynthesis()
        self.obs_controller = OBSController()
        self.dual_video_manager = DualVideoManager(self.obs_controller)
        
        # 播放状态
        self.is_playing = False
        self.is_paused = False
        self.current_item = None
        self.main_video_position = 0.0  # 主视频当前位置（秒）
        self.main_video_duration = 3600.0  # 主视频总时长（秒）
        
        # 播放设置
        self.prepare_voice_count = 5  # 预备语音数量
        self.pause_time_min = 1.0     # 播放间隔最小时间
        self.pause_time_max = 3.0     # 播放间隔最大时间
        self.loop_mode = True         # 循环播放模式
        
        # 播放线程
        self.play_thread = None
        self.stop_event = threading.Event()
        
        # 回调函数
        self.on_playback_start: Optional[Callable] = None
        self.on_playback_end: Optional[Callable] = None
        self.on_item_change: Optional[Callable] = None
        self.on_position_change: Optional[Callable] = None
        self.on_time_segment_change: Optional[Callable] = None
        
        # 时间段管理
        self.time_segments = {}  # {时间段名: {'start': 开始时间, 'end': 结束时间}}
        self.current_time_segment = None
        
        # 设置回调
        self._setup_callbacks()

        self.logger.info("播放控制器初始化完成")
    
    def _setup_callbacks(self):
        """设置回调函数"""
        # 音频播放器回调
        self.audio_player.on_playback_start = self._on_audio_start
        self.audio_player.on_playback_end = self._on_audio_end
        self.audio_player.on_position_change = self._on_audio_position_change
        
        # 播放列表管理器回调
        self.playlist_manager.on_sub_video_start = self._on_sub_video_start
        self.playlist_manager.on_sub_video_end = self._on_sub_video_end
    
    @handle_exceptions("PlaybackController")
    def start_playback(self) -> bool:
        """开始播放"""
        try:
            if self.is_playing:
                self.logger.warning("播放已在进行中")
                return False
            
            self.logger.info("开始播放准备...")
            
            # 准备语音文件
            if not self._prepare_voices():
                self.logger.error("准备语音文件失败")
                return False
            
            # 启动播放线程
            self.is_playing = True
            self.is_paused = False
            self.stop_event.clear()
            
            self.play_thread = threading.Thread(target=self._play_loop, daemon=True)
            self.play_thread.start()
            
            self.logger.info("播放已开始")
            
            # 触发播放开始回调
            if self.on_playback_start:
                self.on_playback_start()
            
            return True
            
        except Exception as e:
            self.logger.error(f"开始播放失败: {e}")
            return False
    
    def _prepare_voices(self) -> bool:
        """准备语音文件"""
        try:
            self.logger.info(f"准备 {self.prepare_voice_count} 个预备语音...")
            
            # 为每个时间段准备语音
            for segment_name, segment_info in self.time_segments.items():
                start_time = segment_info['start']
                end_time = segment_info['end']
                
                # 生成该时间段的语音
                for i in range(self.prepare_voice_count):
                    script = self._get_time_segment_script(segment_name)
                    if script:
                        # 合成语音
                        result = self.voice_synthesis.synthesize_voice(script)
                        if result['success']:
                            # 添加到播放列表
                            self.playlist_manager.add_main_video_item(
                                result['file_path'], 
                                segment_name,
                                f"主视频语音_{i+1}"
                            )
            
            self.logger.info("预备语音准备完成")
            return True
            
        except Exception as e:
            self.logger.error(f"准备语音文件失败: {e}")
            return False
    
    def _play_loop(self):
        """播放循环"""
        try:
            while self.is_playing and not self.stop_event.is_set():
                if self.is_paused:
                    time.sleep(0.1)
                    continue

                # 如果当前有播放项，说明正在播放，等待播放完成
                if self.current_item:
                    time.sleep(0.1)
                    continue

                # 获取下一个播放项
                next_item = self.playlist_manager.get_next_item()
                if not next_item:
                    # 没有播放项，等待
                    time.sleep(1.0)
                    continue

                self.current_item = next_item

                # 触发播放项变化回调
                if self.on_item_change:
                    self.on_item_change(next_item)

                # 播放音频（播放完成后会通过回调处理）
                success = self.audio_player.play_file(next_item.file_path)
                if not success:
                    self.logger.error(f"播放失败: {next_item.title}")
                    # 播放失败，重置当前项并继续
                    self.playlist_manager.on_item_finished(next_item)
                    self.current_item = None
                    continue

                # 播放间隔（在播放开始后立即应用）
                if not self.stop_event.is_set():
                    pause_time = random.uniform(self.pause_time_min, self.pause_time_max)
                    self.logger.debug(f"播放间隔: {pause_time:.1f}秒")
                    time.sleep(pause_time)

        except Exception as e:
            self.logger.error(f"播放循环异常: {e}")
        finally:
            self.is_playing = False
            self.current_item = None
    
    def _update_main_video_position(self):
        """更新主视频位置"""
        if self.current_item and self.current_item.voice_type == VoiceType.MAIN_VIDEO:
            # 只有主视频语音播放时才更新位置
            self.main_video_position += 0.1
            
            # 检查时间段变化
            new_segment = self._get_current_time_segment()
            if new_segment != self.current_time_segment:
                self.current_time_segment = new_segment
                if self.on_time_segment_change:
                    self.on_time_segment_change(new_segment)
            
            # 触发位置变化回调
            if self.on_position_change:
                self.on_position_change(self.main_video_position)
    
    def _get_current_time_segment(self) -> Optional[str]:
        """获取当前时间段"""
        for segment_name, segment_info in self.time_segments.items():
            start_time = segment_info['start']
            end_time = segment_info['end']
            if start_time <= self.main_video_position <= end_time:
                return segment_name
        return None
    
    def _get_time_segment_script(self, segment_name: str) -> Optional[str]:
        """获取时间段话术（需要外部实现）"""
        # 这里应该调用时间段管理器获取话术
        # 暂时返回示例话术
        return f"这是{segment_name}的示例话术"
    
    def _on_audio_start(self, file_path: str):
        """音频开始播放回调"""
        self.logger.debug(f"音频开始播放: {Path(file_path).name}")
    
    def _on_audio_end(self, file_path: str):
        """音频播放结束回调"""
        self.logger.info(f"🔔 音频播放结束回调触发: {Path(file_path).name}")

        # 处理播放完成后的逻辑
        if self.current_item:
            self.playlist_manager.on_item_finished(self.current_item)
            self.logger.info(f"播放项已完成: {self.current_item.title}")

        # 重置当前播放项
        self.current_item = None
    
    def _on_audio_position_change(self, position: float):
        """音频位置变化回调"""
        # 这里可以添加音频位置相关的逻辑
        pass
    
    def _on_sub_video_start(self, item):
        """副视频开始播放回调"""
        try:
            # 暂停主视频
            if self.obs_controller.connected:
                # 切换到副视频源
                if item.sub_video_source:
                    self.logger.info(f"切换到副视频源: {item.sub_video_source}")
                    # 这里需要实现具体的OBS切换逻辑
            
        except Exception as e:
            self.logger.error(f"副视频开始播放处理异常: {e}")
    
    def _on_sub_video_end(self, item):
        """副视频播放结束回调"""
        try:
            # 切回主视频
            if self.obs_controller.connected:
                self.logger.info("切回主视频源")
                # 这里需要实现具体的OBS切换逻辑
            
        except Exception as e:
            self.logger.error(f"副视频结束播放处理异常: {e}")
    
    def pause_playback(self):
        """暂停播放"""
        if self.is_playing and not self.is_paused:
            self.is_paused = True
            self.audio_player.pause()
            self.logger.info("播放已暂停")
    
    def resume_playback(self):
        """恢复播放"""
        if self.is_playing and self.is_paused:
            self.is_paused = False
            self.audio_player.resume()
            self.logger.info("播放已恢复")
    
    def stop_playback(self):
        """停止播放"""
        if self.is_playing:
            self.stop_event.set()
            self.is_playing = False
            self.is_paused = False
            
            self.audio_player.stop()
            
            # 等待播放线程结束
            if self.play_thread and self.play_thread.is_alive():
                self.play_thread.join(timeout=2.0)
            
            self.current_item = None
            self.main_video_position = 0.0
            
            self.logger.info("播放已停止")
            
            # 触发播放结束回调
            if self.on_playback_end:
                self.on_playback_end()
    
    def add_sub_video_voice(self, file_path: str, sub_video_source: str, trigger_keyword: str):
        """添加副视频语音"""
        return self.playlist_manager.add_sub_video_item(file_path, sub_video_source, trigger_keyword)
    
    def add_ai_dialogue_voice(self, file_path: str, keyword: str):
        """添加AI对话语音"""
        return self.playlist_manager.add_ai_dialogue_item(file_path, keyword)
    
    def add_time_report_voice(self, file_path: str):
        """添加报时语音"""
        return self.playlist_manager.add_time_report_item(file_path)
    
    def clear_playlist(self, voice_type: Optional[VoiceType] = None):
        """清空播放列表"""
        self.playlist_manager.clear_queue(voice_type)
    
    def set_playback_settings(self, prepare_count: int, pause_min: float, pause_max: float, loop_mode: bool):
        """设置播放参数"""
        self.prepare_voice_count = max(1, prepare_count)
        self.pause_time_min = max(0.1, pause_min)
        self.pause_time_max = max(pause_min, pause_max)
        self.loop_mode = loop_mode
        
        self.playlist_manager.set_main_video_loop_mode(loop_mode)
        
        self.logger.info(f"播放设置已更新: 预备={prepare_count}, 间隔={pause_min}-{pause_max}s, 循环={loop_mode}")
    
    def set_time_segments(self, time_segments: Dict[str, Dict[str, Any]]):
        """设置时间段"""
        self.time_segments = time_segments
        self.logger.info(f"设置时间段: {len(time_segments)} 个")

    @handle_exceptions("PlaybackController")
    def set_dual_video_sources(self, source_a: str, source_b: str):
        """设置双主视频源"""
        try:
            self.dual_video_manager.set_video_sources(source_a, source_b)
            self.logger.info(f"设置双主视频源: A={source_a}, B={source_b}")
            return True
        except Exception as e:
            self.logger.error(f"设置双主视频源失败: {e}")
            return False

    @handle_exceptions("PlaybackController")
    def start_dual_video_monitoring(self):
        """开始双主视频监控"""
        try:
            self.dual_video_manager.start_monitoring()
            self.logger.info("双主视频监控已开始")
            return True
        except Exception as e:
            self.logger.error(f"开始双主视频监控失败: {e}")
            return False

    @handle_exceptions("PlaybackController")
    def stop_dual_video_monitoring(self):
        """停止双主视频监控"""
        try:
            self.dual_video_manager.stop_monitoring()
            self.logger.info("双主视频监控已停止")
            return True
        except Exception as e:
            self.logger.error(f"停止双主视频监控失败: {e}")
            return False

    @handle_exceptions("PlaybackController")
    def get_current_video_status(self) -> Dict[str, Any]:
        """获取当前主视频状态"""
        try:
            return self.dual_video_manager.get_current_video_status()
        except Exception as e:
            self.logger.error(f"获取当前视频状态失败: {e}")
            return {}

    @handle_exceptions("PlaybackController")
    def manual_switch_video(self):
        """手动切换主视频"""
        try:
            return self.dual_video_manager.manual_switch()
        except Exception as e:
            self.logger.error(f"手动切换主视频失败: {e}")
            return False

    @handle_exceptions("PlaybackController")
    def set_video_switch_threshold(self, seconds: float):
        """设置视频切换阈值"""
        try:
            self.dual_video_manager.set_switch_threshold(seconds)
            self.logger.info(f"设置视频切换阈值: {seconds}秒")
            return True
        except Exception as e:
            self.logger.error(f"设置视频切换阈值失败: {e}")
            return False

    @handle_exceptions("PlaybackController")
    def set_video_speed_range(self, min_speed: float, max_speed: float):
        """设置视频变速范围"""
        try:
            self.dual_video_manager.set_speed_range(min_speed, max_speed)
            self.logger.info(f"设置视频变速范围: {min_speed} - {max_speed}")
            return True
        except Exception as e:
            self.logger.error(f"设置视频变速范围失败: {e}")
            return False

    @handle_exceptions("PlaybackController")
    def ensure_single_video_source_display(self):
        """确保只有一个视频源处于显示状态"""
        try:
            return self.dual_video_manager.ensure_single_source_display()
        except Exception as e:
            self.logger.error(f"确保单一视频源显示失败: {e}")
            return False

    @handle_exceptions("PlaybackController")
    def get_all_visible_video_sources(self):
        """获取所有可见的视频源列表"""
        try:
            return self.dual_video_manager.get_all_visible_media_sources()
        except Exception as e:
            self.logger.error(f"获取可见视频源列表失败: {e}")
            return []

    @handle_exceptions("PlaybackController")
    def set_next_video_speed(self, speed: float):
        """设置下一个视频源的预设播放速度"""
        try:
            self.dual_video_manager.set_next_source_speed(speed)
            self.logger.info(f"设置下一个视频源预设速度: {speed}x")
            return True
        except Exception as e:
            self.logger.error(f"设置下一个视频源速度失败: {e}")
            return False

    @handle_exceptions("PlaybackController")
    def get_next_video_speed(self):
        """获取下一个视频源的预设播放速度"""
        try:
            status = self.dual_video_manager.get_manager_status()
            return status.get('next_source_speed', 1.0)
        except Exception as e:
            self.logger.error(f"获取下一个视频源速度失败: {e}")
            return 1.0

    @handle_exceptions("PlaybackController")
    def prepare_next_video_with_speed(self, speed: float):
        """手动准备下一个视频源并设置指定速度"""
        try:
            success = self.dual_video_manager.prepare_next_source_with_speed(speed)
            if success:
                self.logger.info(f"手动准备下一个视频源，速度: {speed}x")
            return success
        except Exception as e:
            self.logger.error(f"手动准备下一个视频源失败: {e}")
            return False

    @handle_exceptions("PlaybackController")
    def force_switch_to_video(self, source_name: str):
        """强制切换到指定视频源"""
        try:
            success = self.dual_video_manager.force_switch_to_source(source_name)
            if success:
                self.logger.info(f"强制切换到视频源: {source_name}")
            return success
        except Exception as e:
            self.logger.error(f"强制切换视频源失败: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取播放状态"""
        queue_info = self.playlist_manager.get_queue_info()
        audio_status = self.audio_player.get_status()
        dual_video_status = self.dual_video_manager.get_manager_status()

        return {
            'is_playing': self.is_playing,
            'is_paused': self.is_paused,
            'current_item': self.current_item.to_dict() if self.current_item else None,
            'main_video_position': self.main_video_position,
            'main_video_duration': self.main_video_duration,
            'current_time_segment': self.current_time_segment,
            'queue_info': queue_info,
            'audio_status': audio_status,
            'dual_video_status': dual_video_status,
            'settings': {
                'prepare_voice_count': self.prepare_voice_count,
                'pause_time_min': self.pause_time_min,
                'pause_time_max': self.pause_time_max,
                'loop_mode': self.loop_mode
            }
        }
    
    def close(self):
        """关闭播放控制器"""
        self.stop_playback()
        self.stop_dual_video_monitoring()
        self.audio_player.close()
        self.voice_synthesis.close()
        self.obs_controller.disconnect()
        self.logger.info("播放控制器已关闭")
