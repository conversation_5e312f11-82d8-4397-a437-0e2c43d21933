"""
参考ui_design.py的方法修复dual_video_manager.py
解决问题1：切换视频黑屏
解决问题2：播放完成后没有自动切换
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def apply_ui_design_fixes():
    """应用ui_design.py风格的修复"""
    print("🔧 应用ui_design.py风格的修复")
    print("=" * 50)
    
    # 修复1: 添加hide_all_sources方法到OBS控制器
    obs_controller_fixes = '''
    def hide_all_sources(self):
        """隐藏所有视频源（参考ui_design.py）"""
        try:
            # 获取当前场景
            scene_result = self.send_request_sync("GetCurrentProgramScene")
            if not scene_result:
                return False
            
            current_scene = scene_result.get("currentProgramSceneName")
            
            # 获取场景中的源列表
            sources_result = self.send_request_sync("GetSceneItemList", {
                "sceneName": current_scene
            })
            
            if not sources_result or "sceneItems" not in sources_result:
                return False
            
            scene_items = sources_result["sceneItems"]
            
            # 隐藏所有源
            for item in scene_items:
                source_id = item.get("sceneItemId")
                is_enabled = item.get("sceneItemEnabled", False)
                
                if is_enabled:
                    self.send_request_sync("SetSceneItemEnabled", {
                        "sceneName": current_scene,
                        "sceneItemId": source_id,
                        "sceneItemEnabled": False
                    })
            
            return True
            
        except Exception as e:
            print(f"隐藏所有源失败: {e}")
            return False
'''
    
    # 修复2: 参考ui_design.py的check_main_video_status方法
    dual_video_manager_fixes = '''
    def _check_active_source_status_ui_design_style(self):
        """检查当前激活源的播放状态（参考ui_design.py的实现）"""
        if not self.current_active_source:
            return

        try:
            # 获取当前状态（参考ui_design.py第3164行）
            status = self.obs_controller.get_media_status(self.current_active_source)
            
            if status:
                # 记录当前状态
                current_state = status.get('state', 'OBS_MEDIA_STATE_NONE')
                progress = status.get('progress_percent', 0.0) / 100.0  # 转换为0-1范围
                duration = status.get('duration', 0)  # 总时长（毫秒）
                position = status.get('position', 0)  # 当前位置（毫秒）

                # 保存上一次的进度和状态，用于检测循环完成（参考ui_design.py第3173行）
                if not hasattr(self, "last_video_progress"):
                    self.last_video_progress = progress
                    self.last_video_state = current_state

                # 如果进度从高变低，说明循环了一次（参考ui_design.py第3179行）
                progress_reset = (self.last_video_progress > 0.9 and progress < 0.1)

                # 记录日志，但不要太频繁（参考ui_design.py第3182行）
                if progress_reset or abs(progress - self.last_video_progress) > 0.1 or current_state != self.last_video_state:
                    remaining_time = (duration - position) / 1000.0 if duration > 0 else 0.0
                    self.logger.info(f"主视频当前状态: {current_state}, 进度: {progress*100:.2f}%, 位置: {position/1000:.2f}秒, 总时长: {duration/1000:.2f}秒")

                # 如果播放结束或停止或进度重置（参考ui_design.py第3265行）
                if current_state == "OBS_MEDIA_STATE_ENDED" or current_state == "OBS_MEDIA_STATE_STOPPED" or progress_reset:
                    # 重新播放并随机设置速度
                    if progress_reset:
                        self.logger.info("检测到主视频进度重置，循环完成一次")
                    else:
                        self.logger.info("主视频播放周期结束，重新设置速度并播放")

                    # 记录当前速度
                    old_speed = f"{self.next_source_speed:.2f}x"

                    # 使用双主视频源模式，交替切换（参考ui_design.py第3277行）
                    try:
                        # 确定当前活动的主视频源和非活动的主视频源
                        current_active = self.current_active_source
                        inactive_source = self.next_source

                        # 确保所有源都已经隐藏（参考ui_design.py第3314行）
                        self.logger.info(f"隐藏所有视频源")
                        self.obs_controller.hide_all_sources()

                        # 为非活动源设置随机速度（在隐藏状态下设置）
                        self.logger.info(f"为非活动源 {inactive_source} 设置随机速度")
                        self._generate_random_speed_for_next_source()
                        new_speed = self.next_source_speed

                        # 设置非活动源的播放速度
                        self._set_source_speed(inactive_source, new_speed)

                        # 播放非活动源（仍然是隐藏状态）（参考ui_design.py第3326行）
                        self.logger.info(f"播放非活动源 {inactive_source}")
                        self._play_source(inactive_source)

                        # 等待一小段时间确保播放已经开始并且速度已经设置好（参考ui_design.py第3332行）
                        import time
                        self.logger.info(f"等待0.2秒确保播放已经开始并且速度已经设置好")
                        time.sleep(0.2)

                        # 切换到非活动源（此时速度已经设置好）（参考ui_design.py第3336行）
                        self.logger.info(f"切换显示到非活动源 {inactive_source}")
                        self._show_source(inactive_source)

                        # 更新当前活动源
                        self.current_active_source = inactive_source
                        self.next_source = current_active

                        self.logger.info(f"主视频源已切换: {current_active} -> {inactive_source}, 速度已变化: {old_speed} -> {new_speed}x")
                        
                        return True
                        
                    except Exception as e:
                        self.logger.error(f"变化速度出错: {str(e)}")
                        import traceback
                        self.logger.error(traceback.format_exc())

                # 更新上一次的进度和状态（参考ui_design.py第3416行）
                self.last_video_progress = progress
                self.last_video_state = current_state
                
        except Exception as e:
            self.logger.error(f"检查主视频状态出错: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            
        return False
'''
    
    # 修复3: 参考ui_design.py的保存设置方法
    save_settings_fixes = '''
    def save_user_settings(self):
        """保存用户设置（参考ui_design.py的save_settings_to_config方法）"""
        try:
            # 保存OBS设置
            if "obs" not in self.user_settings:
                self.user_settings["obs"] = {}
            
            obs_config = self.user_settings["obs"]
            
            # 保存主视频源A
            if hasattr(self, "playback_controller") and self.playback_controller:
                dual_manager = self.playback_controller.dual_video_manager
                if dual_manager:
                    obs_config["main_video_source_a"] = dual_manager.video_source_a
                    obs_config["main_video_source_b"] = dual_manager.video_source_b
                    obs_config["current_active_source"] = dual_manager.current_active_source
                    obs_config["next_source"] = dual_manager.next_source
                    obs_config["speed_range"] = dual_manager.speed_range
            
            # 保存到文件
            import json
            config_file = "user_settings.json"
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(self.user_settings, f, ensure_ascii=False, indent=4)
            
            print("✅ 用户设置已保存")
            return True
            
        except Exception as e:
            print(f"❌ 保存用户设置失败: {e}")
            return False
'''
    
    print("📋 修复内容准备完成:")
    print("  1. OBS控制器添加hide_all_sources方法")
    print("  2. 双主视频管理器添加ui_design风格的状态检查")
    print("  3. 主程序添加保存设置方法")
    
    return {
        "obs_controller_fixes": obs_controller_fixes,
        "dual_video_manager_fixes": dual_video_manager_fixes,
        "save_settings_fixes": save_settings_fixes
    }


def create_integration_guide():
    """创建集成指南"""
    print("\n📋 集成指南:")
    print("=" * 40)
    
    guide = """
# 集成ui_design.py风格修复的步骤

## 1. 修改src/core/obs_controller.py
在OBSController类中添加hide_all_sources方法：
```python
def hide_all_sources(self):
    # 添加上面的hide_all_sources方法代码
```

## 2. 修改src/core/playback/dual_video_manager.py
在DualVideoManager类中添加新的状态检查方法：
```python
def _check_active_source_status_ui_design_style(self):
    # 添加上面的_check_active_source_status_ui_design_style方法代码
```

然后在_monitoring_loop中调用这个新方法：
```python
# 将原来的_check_active_source_status()调用
# 替换为_check_active_source_status_ui_design_style()
```

## 3. 修改run_gui_qt5.py
在MainWindow类中添加保存设置方法：
```python
def save_user_settings(self):
    # 添加上面的save_user_settings方法代码
```

## 4. 关键修复点

### 问题1: 切换视频黑屏
- 使用hide_all_sources()确保所有源都隐藏
- 先播放新源（隐藏状态）
- 等待0.2秒确保播放开始
- 再显示新源

### 问题2: 播放完成后没有自动切换
- 检测多种结束状态：ENDED、STOPPED、进度重置
- 使用ui_design.py的progress_reset逻辑
- 1秒间隔检查状态
- 详细的日志输出便于调试

## 5. 测试步骤
1. 启动OBS并添加两个媒体源
2. 在AI直播系统中设置双主视频源
3. 启动自动监控
4. 观察控制台日志确认状态检查正常
5. 等待视频播放完成观察自动切换
6. 测试手动切换功能
"""
    
    print(guide)
    return guide


def main():
    """主函数"""
    print("🚀 参考ui_design.py修复dual_video_manager")
    print("=" * 50)
    
    # 应用修复
    fixes = apply_ui_design_fixes()
    
    # 创建集成指南
    guide = create_integration_guide()
    
    print(f"\n🎉 修复准备完成！")
    print(f"\n💡 关键改进:")
    print(f"  ✅ 参考ui_design.py的hide_all_sources方法解决黑屏问题")
    print(f"  ✅ 参考ui_design.py的check_main_video_status方法解决自动切换问题")
    print(f"  ✅ 参考ui_design.py的save_settings_to_config方法解决设置保存问题")
    print(f"  ✅ 使用progress_reset逻辑检测视频循环完成")
    print(f"  ✅ 1秒间隔状态检查，与ui_design.py保持一致")
    
    print(f"\n🎯 请按照集成指南将修复代码添加到相应文件中")
    print(f"这样就能彻底解决黑屏和自动切换问题！")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
