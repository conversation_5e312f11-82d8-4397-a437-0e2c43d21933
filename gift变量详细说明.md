# {gift} 变量详细说明

## 📋 变量概述

`{gift}` 变量用于在AI对话、话术和报时中显示礼物信息。它会根据弹幕中的礼物消息动态更新。

## 🔍 数据来源

### 1. 弹幕消息来源

`{gift}` 变量的数据来源于 `GiftMessage` 类型的弹幕消息：

```json
{
    "type": "GiftMessage",
    "name": "用户名",
    "giftName": "礼物名称"
}
```

**字段说明**：
- `type`: 固定为 "GiftMessage"
- `name`: 送礼物的用户名
- `giftName`: **礼物的实际名称**（这就是 `{gift}` 变量的数据）

### 2. 数据流转过程

```
弹幕服务器 → 弹幕管理器 → 主界面处理 → 变量更新 → AI回复生成
```

**详细流程**：

1. **弹幕接收**：
   ```python
   # 弹幕管理器接收到礼物消息
   data = {
       "type": "GiftMessage",
       "name": "小明",
       "giftName": "火箭"  # 这是礼物的实际名称
   }
   ```

2. **消息解析**：
   ```python
   # src/services/danmaku_manager.py
   elif message_type == "GiftMessage":
       user = data.get('name', '匿名用户')
       gift_name = data.get('giftName', '礼物')  # 提取礼物名称
       
       danmaku_msg = DanmakuMessage(user, f"送出了{gift_name}", 
                                  message_type="GiftMessage", 
                                  giftName=gift_name)  # 保存礼物名称
   ```

3. **变量更新**：
   ```python
   # run_gui_qt5.py
   def _handle_gift_message(self, message, current_time):
       gift_name = getattr(message, 'giftName', '礼物')
       self.current_gift_name = gift_name  # 🔥 更新当前礼物变量
   ```

4. **变量替换**：
   ```python
   # 在AI回复生成时
   if "{gift}" in processed_reply:
       gift_name = getattr(self, "current_gift_name", "")
       processed_reply = processed_reply.replace("{gift}", gift_name)
   ```

## 📊 数据内容

### 实际礼物名称示例

根据不同直播平台，`{gift}` 变量可能包含以下实际礼物名称：

**常见礼物名称**：
- `火箭` - 高价值礼物
- `小心心` - 低价值礼物
- `小星星` - 基础礼物
- `玫瑰花` - 浪漫礼物
- `666` - 支持礼物
- `超级礼物` - 特殊礼物
- `鲜花` - 普通礼物
- `掌声` - 鼓励礼物

**平台特定礼物**：
- 抖音：`嘉年华`、`穿云箭`、`跑车`
- 快手：`火箭`、`么么哒`、`比心`
- B站：`辣条`、`牛哄哄`、`小电视`

### 数据特点

1. **实时性**：`{gift}` 变量始终显示最新收到的礼物名称
2. **准确性**：直接使用弹幕中的 `giftName` 字段，确保名称准确
3. **动态性**：每次有新礼物时会立即更新

## 🎯 使用场景

### 1. AI对话中的使用

**AI对话模板**：
```
感谢{nick}送出的{gift}！
```

**实际效果**：
- 用户"小明"送出"火箭" → `感谢小明送出的火箭！`
- 用户"小红"送出"小心心" → `感谢小红送出的小心心！`

### 2. 话术中的使用

**话术模板**：
```
哇！收到了{gift}，谢谢{nick}的支持！
```

**实际效果**：
- `哇！收到了火箭，谢谢小明的支持！`

### 3. 报时中的使用

**报时模板**：
```
现在是{time}，刚才收到了{gift}，感谢大家的支持！
```

**实际效果**：
- `现在是14:30，刚才收到了火箭，感谢大家的支持！`

## ⚙️ 技术实现

### 变量存储

```python
class AIBroadcasterApp:
    def __init__(self):
        # 初始化礼物变量
        self.current_gift_name = ""  # 存储当前礼物名称
```

### 变量更新机制

```python
def _handle_gift_message(self, message, current_time):
    """处理礼物消息"""
    try:
        # 从弹幕消息中提取礼物名称
        gift_name = getattr(message, 'giftName', '礼物')
        
        # 🔥 更新当前礼物变量
        self.current_gift_name = gift_name
        
        print(f"🎁 收到礼物: {user_name} 送出 {gift_name}")
        
        # 触发关键词匹配，可能生成AI回复
        gift_content = f"送出{gift_name}"
        self.process_danmaku_keywords(gift_content, user_name)
        
    except Exception as e:
        print(f"❌ 处理礼物消息异常: {e}")
```

### 变量替换逻辑

```python
def process_reply_variables(self, reply, user_name):
    """处理回复中的变量替换"""
    processed_reply = reply
    
    # 替换礼物变量
    if "{gift}" in processed_reply:
        gift_name = getattr(self, "current_gift_name", "")
        if not gift_name:
            # 如果没有当前礼物，使用默认礼物列表
            gifts = ["火箭", "小心心", "小星星", "超级礼物", "大礼物", "鲜花", "掌声", "666"]
            import random
            gift_name = random.choice(gifts)
        processed_reply = processed_reply.replace("{gift}", gift_name)
    
    return processed_reply
```

## 🔄 数据更新时机

### 1. 实时更新

每当收到 `GiftMessage` 类型的弹幕时，`{gift}` 变量会立即更新：

```
时间线：
10:00 - 用户A送出"火箭" → {gift} = "火箭"
10:05 - 用户B送出"小心心" → {gift} = "小心心"  
10:10 - 用户C送出"666" → {gift} = "666"
```

### 2. 优先级规则

- **最新优先**：始终使用最新收到的礼物名称
- **实时覆盖**：新礼物会立即覆盖旧礼物
- **默认兜底**：如果没有收到礼物，使用随机默认值

## 📝 使用注意事项

### 1. 数据时效性

- `{gift}` 变量显示的是**最新收到的礼物**
- 如果长时间没有礼物，可能显示很久之前的礼物名称
- 系统启动时如果没有礼物，会使用默认值

### 2. 默认值处理

当没有实际礼物数据时，系统会从以下列表中随机选择：
```python
["火箭", "小心心", "小星星", "超级礼物", "大礼物", "鲜花", "掌声", "666"]
```

### 3. 编码和显示

- 礼物名称支持中文字符
- 直接使用弹幕中的原始名称，不做转换
- 确保AI回复的自然性和准确性

## 🎉 总结

`{gift}` 变量的数据是：

1. **来源**：弹幕消息中 `GiftMessage` 类型的 `giftName` 字段
2. **内容**：实际的礼物名称（如"火箭"、"小心心"等）
3. **更新**：每次收到礼物弹幕时实时更新
4. **用途**：在AI对话、话术、报时中替换为实际礼物名称
5. **特点**：实时、准确、动态更新

这确保了AI回复能够准确地提及用户送出的具体礼物，提高了互动的真实性和个性化程度！
