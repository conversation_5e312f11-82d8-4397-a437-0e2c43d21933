# AI直播系统 v2 - 用户系统部署指南

## 🎯 部署概述

本指南将帮助您在生产环境中部署AI直播系统v2的用户管理系统。

## 📋 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 20GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Windows 10/11, Ubuntu 18.04+, CentOS 7+
- **Python**: 3.8或更高版本
- **数据库**: SQLite3 (内置) 或 PostgreSQL/MySQL (可选)

## 🔧 环境准备

### 1. Python环境安装

#### Windows
```bash
# 下载并安装Python 3.8+
# https://www.python.org/downloads/

# 验证安装
python --version
pip --version
```

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv
```

#### CentOS/RHEL
```bash
sudo yum install python3 python3-pip
```

### 2. 创建虚拟环境

```bash
# 创建项目目录
mkdir ai_broadcaster_v2
cd ai_broadcaster_v2

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

### 3. 安装依赖包

```bash
# 安装核心依赖
pip install bcrypt PyJWT

# 安装UI依赖 (可选)
pip install PyQt6

# 安装数据库依赖 (可选)
pip install psycopg2-binary  # PostgreSQL
pip install PyMySQL          # MySQL

# 安装Web框架 (可选)
pip install flask fastapi uvicorn

# 生成requirements.txt
pip freeze > requirements.txt
```

## 📁 项目部署

### 1. 下载项目代码

```bash
# 从Git仓库克隆 (如果有)
git clone https://github.com/your-repo/ai_broadcaster_v2.git

# 或者直接复制项目文件
# 确保包含以下目录结构：
ai_broadcaster_v2/
├── src/
│   ├── core/
│   ├── data/
│   ├── ui/
│   └── services/
├── data/
├── logs/
├── config/
└── docs/
```

### 2. 配置文件设置

#### 创建配置文件 `config/app_config.json`

```json
{
    "database": {
        "type": "sqlite",
        "path": "data/production.db",
        "backup_interval": 3600
    },
    "jwt": {
        "secret_key": "your-super-secret-key-change-this",
        "algorithm": "HS256",
        "access_token_expire": 1,
        "refresh_token_expire": 7
    },
    "payment": {
        "alipay": {
            "app_id": "your-alipay-app-id",
            "private_key_path": "config/alipay_private_key.pem",
            "public_key_path": "config/alipay_public_key.pem"
        },
        "wechat": {
            "mch_id": "your-wechat-mch-id",
            "api_key": "your-wechat-api-key",
            "cert_path": "config/wechat_cert.pem"
        }
    },
    "logging": {
        "level": "INFO",
        "file_path": "logs/app.log",
        "max_size": "10MB",
        "backup_count": 5
    },
    "security": {
        "password_min_length": 6,
        "max_login_attempts": 5,
        "lockout_duration": 300
    }
}
```

#### 创建环境变量文件 `.env`

```bash
# 数据库配置
DB_TYPE=sqlite
DB_PATH=data/production.db

# JWT密钥 (生产环境必须修改)
JWT_SECRET_KEY=your-super-secret-key-change-this

# 支付配置
ALIPAY_APP_ID=your-alipay-app-id
WECHAT_MCH_ID=your-wechat-mch-id

# 日志级别
LOG_LEVEL=INFO

# 运行模式
ENVIRONMENT=production
```

### 3. 数据库初始化

```bash
# 创建数据目录
mkdir -p data logs config

# 运行数据库初始化脚本
python -c "
from src.data.database_manager import DatabaseManager
db = DatabaseManager('data/production.db')
print('数据库初始化完成')
"
```

### 4. 安全配置

#### 生成JWT密钥

```python
import secrets

# 生成安全的JWT密钥
jwt_secret = secrets.token_urlsafe(32)
print(f"JWT_SECRET_KEY={jwt_secret}")
```

#### 设置文件权限

```bash
# Linux/Mac
chmod 600 config/app_config.json
chmod 600 .env
chmod 700 data/
chmod 755 logs/

# 设置数据库文件权限
chmod 600 data/production.db
```

## 🚀 启动服务

### 1. 命令行模式

```bash
# 启动用户系统演示
python demo_user_system.py
```

### 2. Web服务模式 (可选)

#### 创建Web服务 `web_server.py`

```python
from flask import Flask, request, jsonify
from src.data.database_manager import DatabaseManager
from src.core.user import UserManager, AuthManager, PaymentManager

app = Flask(__name__)
db = DatabaseManager("data/production.db")
user_manager = UserManager(db)
auth_manager = AuthManager(db)
payment_manager = PaymentManager(db)

@app.route('/api/register', methods=['POST'])
def register():
    data = request.json
    result = user_manager.register_user(
        data['username'], 
        data['password'], 
        data.get('email'), 
        data.get('phone')
    )
    return jsonify(result)

@app.route('/api/login', methods=['POST'])
def login():
    data = request.json
    result = user_manager.login_user(data['username'], data['password'])
    if result['success']:
        tokens = auth_manager.generate_tokens(result['user_info'])
        result['tokens'] = tokens
    return jsonify(result)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=False)
```

#### 启动Web服务

```bash
# 开发模式
python web_server.py

# 生产模式 (使用Gunicorn)
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 web_server:app
```

### 3. 系统服务模式

#### 创建systemd服务文件 `/etc/systemd/system/ai-broadcaster.service`

```ini
[Unit]
Description=AI Broadcaster User System
After=network.target

[Service]
Type=simple
User=ai-broadcaster
Group=ai-broadcaster
WorkingDirectory=/opt/ai_broadcaster_v2
Environment=PATH=/opt/ai_broadcaster_v2/venv/bin
ExecStart=/opt/ai_broadcaster_v2/venv/bin/python web_server.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 启动系统服务

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start ai-broadcaster

# 设置开机自启
sudo systemctl enable ai-broadcaster

# 查看服务状态
sudo systemctl status ai-broadcaster
```

## 🔒 安全配置

### 1. 防火墙设置

```bash
# Ubuntu/Debian
sudo ufw allow 5000/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --reload
```

### 2. SSL证书配置

#### 使用Let's Encrypt

```bash
# 安装Certbot
sudo apt install certbot

# 获取SSL证书
sudo certbot certonly --standalone -d your-domain.com

# 配置Nginx反向代理
sudo apt install nginx
```

#### Nginx配置 `/etc/nginx/sites-available/ai-broadcaster`

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. 数据库安全

```bash
# 定期备份数据库
#!/bin/bash
# backup_db.sh
DATE=$(date +%Y%m%d_%H%M%S)
cp data/production.db backups/production_$DATE.db
find backups/ -name "*.db" -mtime +7 -delete
```

## 📊 监控和维护

### 1. 日志监控

```bash
# 查看应用日志
tail -f logs/app.log

# 查看系统服务日志
sudo journalctl -u ai-broadcaster -f
```

### 2. 性能监控

#### 创建监控脚本 `monitor.py`

```python
import psutil
import time
from src.data.database_manager import DatabaseManager

def check_system_health():
    # CPU使用率
    cpu_percent = psutil.cpu_percent(interval=1)
    
    # 内存使用率
    memory = psutil.virtual_memory()
    
    # 磁盘使用率
    disk = psutil.disk_usage('/')
    
    # 数据库连接测试
    try:
        db = DatabaseManager("data/production.db")
        db_status = "正常"
    except Exception as e:
        db_status = f"异常: {e}"
    
    print(f"CPU: {cpu_percent}%")
    print(f"内存: {memory.percent}%")
    print(f"磁盘: {disk.percent}%")
    print(f"数据库: {db_status}")

if __name__ == "__main__":
    check_system_health()
```

### 3. 自动化维护

#### 创建维护脚本 `maintenance.sh`

```bash
#!/bin/bash

# 数据库备份
python -c "
from src.data.database_manager import DatabaseManager
db = DatabaseManager('data/production.db')
db.backup_database('backups/auto_backup_$(date +%Y%m%d).db')
"

# 清理旧日志
find logs/ -name "*.log.*" -mtime +30 -delete

# 清理临时文件
find /tmp -name "ai_broadcaster_*" -mtime +1 -delete

# 重启服务 (如果需要)
# sudo systemctl restart ai-broadcaster

echo "维护任务完成: $(date)"
```

#### 设置定时任务

```bash
# 编辑crontab
crontab -e

# 添加定时任务
# 每天凌晨2点执行维护
0 2 * * * /opt/ai_broadcaster_v2/maintenance.sh >> /var/log/ai-broadcaster-maintenance.log 2>&1

# 每小时检查系统健康
0 * * * * /opt/ai_broadcaster_v2/venv/bin/python /opt/ai_broadcaster_v2/monitor.py >> /var/log/ai-broadcaster-monitor.log 2>&1
```

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库文件权限
   ls -la data/production.db
   
   # 检查磁盘空间
   df -h
   ```

2. **JWT令牌验证失败**
   ```bash
   # 检查JWT密钥配置
   grep JWT_SECRET_KEY .env
   ```

3. **支付接口异常**
   ```bash
   # 检查支付配置
   python -c "
   import json
   with open('config/app_config.json') as f:
       config = json.load(f)
       print(config['payment'])
   "
   ```

### 日志分析

```bash
# 查看错误日志
grep ERROR logs/app.log

# 查看用户登录日志
grep "用户登录" logs/app.log

# 查看支付日志
grep "支付" logs/app.log
```

## 📈 性能优化

### 1. 数据库优化

```python
# 添加数据库索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_payment_orders_user_id ON payment_orders(user_id);
CREATE INDEX idx_payment_orders_status ON payment_orders(status);
```

### 2. 缓存优化

```python
# 使用Redis缓存用户会话
import redis

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_user_session(user_id, session_data):
    redis_client.setex(f"user_session:{user_id}", 3600, json.dumps(session_data))
```

## 🎯 部署检查清单

- [ ] Python环境安装完成
- [ ] 依赖包安装完成
- [ ] 配置文件设置正确
- [ ] 数据库初始化完成
- [ ] JWT密钥已生成
- [ ] 文件权限设置正确
- [ ] 防火墙配置完成
- [ ] SSL证书配置完成
- [ ] 监控脚本部署完成
- [ ] 备份策略配置完成
- [ ] 定时任务设置完成

---

**部署指南版本**: v2.0.0  
**更新时间**: 2025年5月29日
