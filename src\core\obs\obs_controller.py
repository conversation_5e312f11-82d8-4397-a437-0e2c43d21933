"""
AI直播系统 v2 - OBS控制器
基于测试程序的成功实现
"""

import asyncio
import websockets
import json
import hashlib
import base64
import threading
import time
from typing import Optional, Dict, Any, List, Callable
from datetime import datetime

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions

# 检查WebSocket模块
try:
    import websockets
    import asyncio
    import json
    import hashlib
    import base64
    OBS_WEBSOCKET_AVAILABLE = True
    print("✅ WebSocket模块可用，将使用原生WebSocket连接OBS")
except ImportError as e:
    OBS_WEBSOCKET_AVAILABLE = False
    print(f"⚠️ WebSocket模块不可用: {e}，OBS控制功能将不可用")


class OBSController:
    """OBS WebSocket控制器 - 基于测试程序的成功实现"""

    def __init__(self, host: str = "localhost", port: int = 4455, password: str = ""):
        self.logger = create_logger("obs_controller")

        # 连接参数
        self.host = host
        self.port = port
        self.password = password
        self.websocket_url = f"ws://{host}:{port}"

        # 连接状态
        self.websocket = None
        self.is_connected = False
        self.is_authenticated = False

        # 请求管理
        self.request_id = 0

        # 异步事件循环
        self.loop = None
        self.connection_task = None
        self.stop_event = threading.Event()

        # 回调函数
        self.on_connected: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None
        self.on_connection_change: Optional[Callable] = None
        self.on_scene_changed: Optional[Callable] = None

        # 视频源设置
        self.video_source_a = ""
        self.video_source_b = ""
        self.min_speed = 0.5
        self.max_speed = 2.0

        # 媒体源列表
        self.media_sources = []
        self.scenes = []

        self.logger.info("OBS控制器初始化完成")

    def log(self, message):
        """打印日志"""
        self.logger.info(message)

    def connect(self) -> bool:
        """连接到OBS"""
        try:
            if not OBS_WEBSOCKET_AVAILABLE:
                self.log("WebSocket模块不可用，无法连接OBS")
                return False

            if self.is_connected:
                self.log("OBS已连接")
                return True

            self.log(f"正在连接到OBS WebSocket服务器: {self.websocket_url}")

            # 启动异步连接
            self.stop_event.clear()
            self.connection_task = threading.Thread(target=self._run_async_connection, daemon=True)
            self.connection_task.start()

            # 等待连接结果
            for _ in range(50):  # 等待5秒
                if self.is_connected:
                    return True
                time.sleep(0.1)

            self.log("连接OBS超时")
            return False

        except Exception as e:
            self.log(f"连接OBS WebSocket服务器失败: {e}")
            self.is_connected = False
            return False

    def _run_async_connection(self):
        """运行异步连接并保持事件循环运行"""
        try:
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)

            # 连接到OBS
            self.loop.run_until_complete(self._async_connect())

            # 如果连接成功，保持事件循环运行
            if self.connected:
                self.log("✅ 事件循环保持运行中...")
                # 运行事件循环直到停止
                self.loop.run_until_complete(self._keep_loop_running())

        except Exception as e:
            self.log(f"异步连接异常: {e}")
            # 不要在这里抛出异常到GUI线程
        finally:
            # 只有在明确停止时才关闭事件循环
            try:
                if self.loop and not self.loop.is_closed():
                    self.loop.close()
                    self.log("事件循环已关闭")
            except Exception as e:
                self.log(f"关闭事件循环异常: {e}")

    async def _keep_loop_running(self):
        """保持事件循环运行"""
        try:
            while self.connected and not self.stop_event.is_set():
                await asyncio.sleep(0.1)
        except Exception as e:
            self.log(f"事件循环运行异常: {e}")

    async def _async_connect(self):
        """异步连接到OBS - 基于测试程序的成功实现"""
        try:
            self.log(f"正在连接到OBS: {self.websocket_url}")

            # 连接WebSocket
            self.websocket = await websockets.connect(self.websocket_url)
            self.log("WebSocket连接成功")

            # 等待Hello消息
            hello_message = await self.websocket.recv()
            hello_data = json.loads(hello_message)
            self.log(f"收到Hello消息: {hello_data}")

            if hello_data.get("op") == 0:  # Hello
                # 发送Identify消息
                await self._send_identify(hello_data.get("d", {}))

                # 等待Identified消息
                identified_message = await self.websocket.recv()
                identified_data = json.loads(identified_message)
                self.log(f"收到Identified消息: {identified_data}")

                if identified_data.get("op") == 2:  # Identified
                    self.is_connected = True
                    self.is_authenticated = True
                    self.log("✅ 成功连接到OBS WebSocket服务器")

                    # 测试基本功能
                    await self._test_basic_functions()

                    # 触发连接成功回调（安全调用）
                    try:
                        if self.on_connected:
                            self.on_connected()

                        if self.on_connection_change:
                            self.on_connection_change(True)
                    except Exception as callback_error:
                        self.log(f"连接回调异常: {callback_error}")

                    return True
                else:
                    self.log(f"❌ 身份验证失败: {identified_data}")
                    return False
            else:
                self.log(f"❌ 未收到Hello消息: {hello_data}")
                return False

        except websockets.exceptions.ConnectionRefused:
            self.log("❌ 连接被拒绝 - 请确保OBS Studio正在运行且WebSocket服务器已启用")
            return False
        except Exception as e:
            self.log(f"❌ 连接失败: {e}")
            return False
        finally:
            if not self.is_connected:
                try:
                    if self.on_disconnected:
                        self.on_disconnected()

                    if self.on_connection_change:
                        self.on_connection_change(False)
                except Exception as callback_error:
                    self.log(f"断开连接回调异常: {callback_error}")

    async def _send_identify(self, hello_data):
        """发送身份验证消息"""
        identify_data = {
            "op": 1,  # Identify
            "d": {
                "rpcVersion": 1
            }
        }

        # 如果需要密码验证
        if "authentication" in hello_data and self.password:
            auth_data = hello_data["authentication"]
            challenge = auth_data.get("challenge", "")
            salt = auth_data.get("salt", "")

            self.log("需要密码验证")

            # 生成认证字符串
            secret = base64.b64encode(
                hashlib.sha256((self.password + salt).encode()).digest()
            ).decode()

            auth_response = base64.b64encode(
                hashlib.sha256((secret + challenge).encode()).digest()
            ).decode()

            identify_data["d"]["authentication"] = auth_response
        else:
            self.log("无需密码验证")

        await self.websocket.send(json.dumps(identify_data))
        self.log("已发送Identify消息")

    async def _send_request(self, request_type, request_data=None):
        """发送请求 - 基于测试程序的成功实现"""
        if not self.is_authenticated:
            self.log("❌ 未连接到OBS")
            return None

        self.request_id += 1
        request_id = str(self.request_id)

        request = {
            "op": 6,  # Request
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }

        try:
            await self.websocket.send(json.dumps(request))
            self.log(f"发送请求: {request_type}")

            # 等待响应
            response = await asyncio.wait_for(self.websocket.recv(), timeout=5.0)
            response_data = json.loads(response)

            if response_data.get("op") == 7:  # RequestResponse
                response_d = response_data.get("d", {})
                if response_d.get("requestId") == request_id:
                    if response_d.get("requestStatus", {}).get("result", False):
                        self.log(f"✅ 请求成功: {request_type}")
                        return response_d.get("responseData", {})
                    else:
                        error_msg = response_d.get("requestStatus", {}).get("comment", "未知错误")
                        self.log(f"❌ 请求失败: {request_type} - {error_msg}")
                        return None

            return None

        except asyncio.TimeoutError:
            self.log(f"❌ 请求超时: {request_type}")
            return None
        except Exception as e:
            self.log(f"❌ 请求异常: {request_type} - {e}")
            return None

    async def _test_basic_functions(self):
        """测试基本功能 - 基于测试程序的成功实现"""
        if not self.is_authenticated:
            self.log("❌ 未连接，无法测试功能")
            return

        self.log("🔍 开始测试基本功能...")

        # 测试获取版本信息
        version_info = await self._send_request("GetVersion")
        if version_info:
            obs_version = version_info.get('obsVersion', 'Unknown')
            ws_version = version_info.get('obsWebSocketVersion', 'Unknown')
            self.log(f"✅ OBS版本: {obs_version}")
            self.log(f"✅ WebSocket版本: {ws_version}")

        # 测试获取场景列表
        scenes_info = await self._send_request("GetSceneList")
        if scenes_info:
            scenes = scenes_info.get("scenes", [])
            self.scenes = [scene.get("sceneName", "Unknown") for scene in scenes]
            self.log(f"✅ 找到 {len(scenes)} 个场景:")
            for scene_name in self.scenes:
                self.log(f"   - {scene_name}")

        # 测试获取输入源列表
        inputs_info = await self._send_request("GetInputList")
        if inputs_info:
            inputs = inputs_info.get("inputs", [])
            self.media_sources = []
            self.log(f"✅ 找到 {len(inputs)} 个输入源:")

            # 定义媒体源类型
            media_source_types = [
                'ffmpeg_source',      # 媒体源
                'vlc_source',         # VLC视频源
                'image_source',       # 图像源
                'slideshow_source',   # 图像幻灯片放映
                'color_source',       # 颜色源
                'text_gdiplus',       # 文本(GDI+)
                'text_ft2_source',    # 文本(FreeType 2)
            ]

            for inp in inputs:
                source_name = inp.get("inputName", "Unknown")
                source_kind = inp.get("inputKind", "")

                # 只添加媒体源类型，过滤掉音频设备
                if source_kind in media_source_types:
                    self.media_sources.append(source_name)
                    self.log(f"   - {source_name} ({source_kind}) [媒体源]")
                else:
                    self.log(f"   - {source_name} ({source_kind}) [已过滤]")

    async def disconnect_async(self):
        """异步断开连接"""
        if self.websocket:
            await self.websocket.close()
            self.log("已断开连接")
        self.is_connected = False
        self.is_authenticated = False

    def disconnect(self):
        """断开连接"""
        try:
            self.stop_event.set()
            self.is_connected = False
            self.is_authenticated = False

            if self.websocket and self.loop:
                asyncio.run_coroutine_threadsafe(self.disconnect_async(), self.loop)

            if self.connection_task and self.connection_task.is_alive():
                self.connection_task.join(timeout=2.0)

            # 清空媒体源列表
            self.media_sources = []
            self.scenes = []

            self.log("已断开OBS连接")

            # 安全调用回调函数
            try:
                if self.on_disconnected:
                    self.on_disconnected()

                if self.on_connection_change:
                    self.on_connection_change(False)
            except Exception as callback_error:
                self.log(f"断开连接回调异常: {callback_error}")

        except Exception as e:
            self.log(f"断开OBS连接异常: {e}")

    @handle_exceptions("OBSController")
    def get_source_list(self) -> List[str]:
        """获取所有输入源列表（包括媒体源、图像源、文本源等）"""
        try:
            if not self.is_connected:
                self.log("OBS未连接，无法获取源列表")
                return []

            # 如果缓存的媒体源列表为空，尝试实时获取
            if not self.media_sources:
                self.log("媒体源列表为空，尝试实时获取...")
                self._refresh_media_sources()

            # 返回已加载的所有输入源列表
            self.log(f"返回OBS输入源列表: {len(self.media_sources)} 个源: {self.media_sources}")
            return self.media_sources

        except Exception as e:
            self.log(f"获取输入源列表失败: {e}")
            return []

    def _refresh_media_sources(self):
        """刷新媒体源列表"""
        try:
            if not self.is_connected or not self.loop or not self.loop.is_running():
                self.log("无法刷新媒体源列表：OBS未连接或事件循环未运行")
                return

            # 使用异步方法获取最新的输入源列表
            future = asyncio.run_coroutine_threadsafe(
                self._get_inputs_async(),
                self.loop
            )
            result = future.result(timeout=3.0)

            if result:
                self.log(f"✅ 实时获取到 {len(self.media_sources)} 个媒体源")
            else:
                self.log("❌ 实时获取媒体源失败")

        except Exception as e:
            self.log(f"刷新媒体源列表异常: {e}")

    async def _get_inputs_async(self):
        """异步获取输入源列表"""
        try:
            inputs_info = await self._send_request("GetInputList")
            if inputs_info:
                inputs = inputs_info.get("inputs", [])
                self.media_sources = []

                # 定义媒体源类型
                media_source_types = [
                    'ffmpeg_source',      # 媒体源
                    'image_source',       # 图像源
                    'text_gdiplus',       # 文本源
                    'text_ft2_source',    # 文本源(FreeType 2)
                    'slideshow',          # 幻灯片放映
                    'vlc_source',         # VLC视频源
                    'browser_source',     # 浏览器源
                ]

                for inp in inputs:
                    source_name = inp.get("inputName", "Unknown")
                    source_kind = inp.get("inputKind", "")

                    # 只添加媒体源类型，过滤掉音频设备
                    if source_kind in media_source_types:
                        self.media_sources.append(source_name)
                        self.log(f"   - {source_name} ({source_kind}) [媒体源]")

                return True
            return False

        except Exception as e:
            self.log(f"异步获取输入源列表异常: {e}")
            return False

    @handle_exceptions("OBSController")
    def get_scene_list(self) -> List[str]:
        """获取场景列表"""
        try:
            if not self.is_connected:
                self.log("OBS未连接，无法获取场景列表")
                return []

            # 返回已加载的场景列表
            self.log(f"返回OBS场景列表: {len(self.scenes)} 个场景")
            return self.scenes

        except Exception as e:
            self.log(f"获取场景列表失败: {e}")
            return []

    @handle_exceptions("OBSController")
    def set_video_sources(self, source_a: str, source_b: str):
        """设置视频源A和B"""
        try:
            self.video_source_a = source_a
            self.video_source_b = source_b
            self.logger.info(f"设置视频源: A={source_a}, B={source_b}")
        except Exception as e:
            self.logger.error(f"设置视频源失败: {e}")

    @handle_exceptions("OBSController")
    def set_speed_range(self, min_speed: float, max_speed: float):
        """设置速度范围"""
        try:
            self.min_speed = min_speed
            self.max_speed = max_speed
            self.logger.info(f"设置速度范围: {min_speed} - {max_speed}")
        except Exception as e:
            self.logger.error(f"设置速度范围失败: {e}")

    @handle_exceptions("OBSController")
    def play_media(self, source_name: str) -> bool:
        """播放媒体源"""
        try:
            if not self.is_connected:
                self.log("OBS未连接，无法播放媒体")
                return False

            # TODO: 实现媒体播放功能
            self.log(f"播放媒体源: {source_name}")
            return True

        except Exception as e:
            self.log(f"播放媒体源异常: {e}")
            return False

    @handle_exceptions("OBSController")
    def pause_media(self, source_name: str) -> bool:
        """暂停媒体源"""
        try:
            if not self.is_connected:
                self.log("OBS未连接，无法暂停媒体")
                return False

            # TODO: 实现媒体暂停功能
            self.log(f"暂停媒体源: {source_name}")
            return True

        except Exception as e:
            self.log(f"暂停媒体源异常: {e}")
            return False

    @handle_exceptions("OBSController")
    def stop_media(self, source_name: str) -> bool:
        """停止媒体源"""
        try:
            if not self.is_connected:
                self.log("OBS未连接，无法停止媒体")
                return False

            # TODO: 实现媒体停止功能
            self.log(f"停止媒体源: {source_name}")
            return True

        except Exception as e:
            self.log(f"停止媒体源异常: {e}")
            return False

    @handle_exceptions("OBSController")
    def set_media_speed(self, source_name: str, speed: float) -> bool:
        """设置媒体播放速度"""
        try:
            if not self.is_connected:
                self.log("OBS未连接，无法设置媒体速度")
                return False

            # TODO: 实现媒体速度设置功能
            self.log(f"设置媒体源 {source_name} 速度为: {speed}")
            return True

        except Exception as e:
            self.log(f"设置媒体源速度异常: {e}")
            return False

    @handle_exceptions("OBSController")
    def get_media_status(self, source_name: str) -> Dict[str, Any]:
        """获取媒体源状态"""
        try:
            if not self.is_connected:
                self.log("OBS未连接，无法获取媒体状态")
                return {}

            # 使用异步方法获取媒体状态
            if self.loop and self.loop.is_running():
                future = asyncio.run_coroutine_threadsafe(
                    self._get_media_status_async(source_name),
                    self.loop
                )
                result = future.result(timeout=5.0)
                return result or {}
            else:
                self.log("异步事件循环未运行")
                return {}

        except Exception as e:
            self.log(f"获取媒体源状态异常: {e}")
            return {}

    @handle_exceptions("OBSController")
    def get_media_status_sync(self, source_name: str) -> Dict[str, Any]:
        """同步获取媒体源状态（修复协程错误）"""
        try:
            if not self.is_connected:
                self.log("OBS未连接，无法获取媒体状态")
                return {}

            # 使用异步方法获取媒体状态，确保正确处理协程
            if self.loop and self.loop.is_running():
                future = asyncio.run_coroutine_threadsafe(
                    self._get_media_status_async(source_name),
                    self.loop
                )
                try:
                    result = future.result(timeout=5.0)
                    # 确保返回的是字典而不是协程
                    if isinstance(result, dict):
                        return result
                    else:
                        self.log(f"获取媒体状态返回类型错误: {type(result)}")
                        return {}
                except Exception as e:
                    self.log(f"等待异步结果失败: {e}")
                    return {}
            else:
                self.log("异步事件循环未运行")
                return {}

        except Exception as e:
            self.log(f"同步获取媒体源状态异常: {e}")
            return {}

    async def _get_media_status_async(self, source_name: str) -> Dict[str, Any]:
        """异步获取媒体源状态"""
        try:
            # 获取媒体输入状态
            media_status = await self._send_request("GetMediaInputStatus", {
                "inputName": source_name
            })

            if not media_status:
                return {}

            # 获取输入源设置（包含文件路径等信息）
            input_settings = await self._send_request("GetInputSettings", {
                "inputName": source_name
            })

            # 组合状态信息，确保数值类型安全
            media_duration = media_status.get('mediaDuration')
            media_cursor = media_status.get('mediaCursor')

            # 安全转换为数字，处理None值
            if media_duration is None:
                media_duration = 0
            else:
                try:
                    media_duration = int(media_duration)
                except (ValueError, TypeError):
                    media_duration = 0

            if media_cursor is None:
                media_cursor = 0
            else:
                try:
                    media_cursor = int(media_cursor)
                except (ValueError, TypeError):
                    media_cursor = 0

            status_info = {
                'source_name': source_name,
                'media_state': media_status.get('mediaState', 'unknown'),
                'media_duration': media_duration,
                'media_cursor': media_cursor,
                'media_ended': media_status.get('mediaEnded', False),
                'media_playing': media_status.get('mediaState') == 'OBS_MEDIA_STATE_PLAYING',
                'media_paused': media_status.get('mediaState') == 'OBS_MEDIA_STATE_PAUSED',
                'media_stopped': media_status.get('mediaState') == 'OBS_MEDIA_STATE_STOPPED',
            }

            # 添加文件信息
            if input_settings:
                settings = input_settings.get('inputSettings', {})
                status_info.update({
                    'file_path': settings.get('local_file', ''),
                    'loop': settings.get('looping', False),
                    'restart_on_activate': settings.get('restart_on_activate', False),
                    'speed_percent': settings.get('speed_percent', 100),
                })

            # 计算进度百分比
            if status_info['media_duration'] > 0:
                status_info['progress_percent'] = (status_info['media_cursor'] / status_info['media_duration']) * 100
            else:
                status_info['progress_percent'] = 0

            # 格式化时间显示
            status_info['current_time_formatted'] = self._format_time(status_info['media_cursor'])
            status_info['total_time_formatted'] = self._format_time(status_info['media_duration'])

            self.log(f"媒体源 {source_name} 状态: {status_info['media_state']}, "
                    f"进度: {status_info['current_time_formatted']}/{status_info['total_time_formatted']} "
                    f"({status_info['progress_percent']:.1f}%)")

            return status_info

        except Exception as e:
            self.log(f"异步获取媒体源状态异常: {e}")
            return {}

    def _format_time(self, milliseconds) -> str:
        """格式化时间显示（毫秒转换为 HH:MM:SS）"""
        try:
            # 安全处理None值和非数字值
            if milliseconds is None:
                return "00:00:00"

            # 尝试转换为整数
            try:
                milliseconds = int(milliseconds)
            except (ValueError, TypeError):
                return "00:00:00"

            if milliseconds <= 0:
                return "00:00:00"

            seconds = milliseconds // 1000
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            seconds = seconds % 60

            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        except Exception as e:
            self.log(f"格式化时间异常: {e}")
            return "00:00:00"

    @handle_exceptions("OBSController")
    def get_main_video_status(self) -> Dict[str, Any]:
        """获取主视频（视频源A）的状态"""
        try:
            if not self.video_source_a:
                self.log("未设置主视频源")
                return {}

            return self.get_media_status(self.video_source_a)

        except Exception as e:
            self.log(f"获取主视频状态异常: {e}")
            return {}

    @handle_exceptions("OBSController")
    def get_sub_video_status(self) -> Dict[str, Any]:
        """获取副视频（视频源B）的状态"""
        try:
            if not self.video_source_b:
                self.log("未设置副视频源")
                return {}

            return self.get_media_status(self.video_source_b)

        except Exception as e:
            self.log(f"获取副视频状态异常: {e}")
            return {}

    @property
    def connected(self) -> bool:
        """连接状态属性"""
        return self.is_connected

    def is_connected_method(self) -> bool:
        """检查是否已连接（方法形式）"""
        return self.is_connected

    def get_version(self) -> Optional[Dict[str, Any]]:
        """获取OBS版本信息"""
        try:
            if not self.is_connected:
                return None

            # TODO: 实现版本信息获取
            return {"obsVersion": "Unknown", "obsWebSocketVersion": "Unknown"}

        except Exception as e:
            self.log(f"获取OBS版本信息失败: {e}")
            return None

    @handle_exceptions("OBSController")
    def hide_all_sources(self):
        """隐藏所有视频源（参考ui_design.py）"""
        try:
            if not self.is_connected:
                self.log("OBS未连接，无法隐藏所有源")
                return False

            # 使用异步方法隐藏所有源
            if self.loop and self.loop.is_running():
                future = asyncio.run_coroutine_threadsafe(
                    self._hide_all_sources_async(),
                    self.loop
                )
                result = future.result(timeout=5.0)
                return result
            else:
                self.log("异步事件循环未运行")
                return False

        except Exception as e:
            self.log(f"隐藏所有源失败: {e}")
            return False

    async def _hide_all_sources_async(self):
        """异步隐藏所有视频源"""
        try:
            # 获取当前场景
            scene_result = await self._send_request("GetCurrentProgramScene")
            if not scene_result:
                return False

            current_scene = scene_result.get("currentProgramSceneName")

            # 获取场景中的源列表
            sources_result = await self._send_request("GetSceneItemList", {
                "sceneName": current_scene
            })

            if not sources_result or "sceneItems" not in sources_result:
                return False

            scene_items = sources_result["sceneItems"]

            # 隐藏所有源
            hidden_count = 0
            for item in scene_items:
                source_id = item.get("sceneItemId")
                is_enabled = item.get("sceneItemEnabled", False)

                if is_enabled:
                    await self._send_request("SetSceneItemEnabled", {
                        "sceneName": current_scene,
                        "sceneItemId": source_id,
                        "sceneItemEnabled": False
                    })
                    hidden_count += 1

            self.log(f"已隐藏 {hidden_count} 个源")
            return True

        except Exception as e:
            self.log(f"异步隐藏所有源失败: {e}")
            return False
