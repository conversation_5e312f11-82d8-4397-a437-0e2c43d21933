"""
AI直播系统 v2 - 用户管理器
用户信息管理和操作
"""

import hashlib
import uuid
import platform
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class UserManager:
    """用户管理器"""
    
    def __init__(self, db_manager):
        self.logger = create_logger("user_manager")
        self.db_manager = db_manager
        
        # 当前登录用户
        self.current_user = None
        
        # 用户状态
        self.user_status = {
            'ACTIVE': 1,
            'INACTIVE': 0,
            'BANNED': -1,
            'TRIAL': 2
        }
        
        # 用户类型
        self.user_types = {
            'FREE': 0,      # 免费用户
            'VIP': 1,       # VIP用户
            'PREMIUM': 2,   # 高级用户
            'ADMIN': 9      # 管理员
        }
        
    @handle_exceptions("UserManager")
    def register_user(self, username: str, password: str, email: str = "", 
                     phone: str = "") -> Dict[str, Any]:
        """
        注册新用户
        
        Args:
            username: 用户名
            password: 密码
            email: 邮箱
            phone: 手机号
            
        Returns:
            注册结果
        """
        try:
            # 验证用户名是否已存在
            if self.get_user_by_username(username):
                return {
                    'success': False,
                    'message': '用户名已存在',
                    'code': 'USERNAME_EXISTS'
                }
                
            # 验证邮箱是否已存在
            if email and self.get_user_by_email(email):
                return {
                    'success': False,
                    'message': '邮箱已被注册',
                    'code': 'EMAIL_EXISTS'
                }
                
            # 生成机器码
            machine_code = self._generate_machine_code()
            
            # 加密密码
            password_hash = self._hash_password(password)
            
            # 创建用户
            user_id = self.db_manager.execute_insert(
                """INSERT INTO users 
                   (username, password_hash, email, phone, machine_code, 
                    user_type, status, balance, trial_days, created_at, updated_at)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)""",
                (username, password_hash, email, phone, machine_code,
                 self.user_types['FREE'], self.user_status['TRIAL'], 0.0, 7)
            )
            
            if user_id:
                self.logger.info(f"用户注册成功: {username} (ID: {user_id})")
                
                # 获取完整用户信息
                user_info = self.get_user_by_id(user_id)
                
                return {
                    'success': True,
                    'message': '注册成功',
                    'user_id': user_id,
                    'user_info': user_info
                }
            else:
                return {
                    'success': False,
                    'message': '注册失败，请重试',
                    'code': 'REGISTER_FAILED'
                }
                
        except Exception as e:
            self.logger.error(f"用户注册失败: {e}")
            return {
                'success': False,
                'message': f'注册失败: {str(e)}',
                'code': 'REGISTER_ERROR'
            }
            
    @handle_exceptions("UserManager")
    def login_user(self, username: str, password: str) -> Dict[str, Any]:
        """
        用户登录
        
        Args:
            username: 用户名或邮箱
            password: 密码
            
        Returns:
            登录结果
        """
        try:
            # 获取用户信息
            user = self.get_user_by_username(username)
            if not user:
                user = self.get_user_by_email(username)
                
            if not user:
                return {
                    'success': False,
                    'message': '用户不存在',
                    'code': 'USER_NOT_FOUND'
                }
                
            # 验证密码
            if not self._verify_password(password, user['password_hash']):
                return {
                    'success': False,
                    'message': '密码错误',
                    'code': 'INVALID_PASSWORD'
                }
                
            # 检查用户状态
            if user['status'] == self.user_status['BANNED']:
                return {
                    'success': False,
                    'message': '账户已被封禁',
                    'code': 'USER_BANNED'
                }
                
            if user['status'] == self.user_status['INACTIVE']:
                return {
                    'success': False,
                    'message': '账户未激活',
                    'code': 'USER_INACTIVE'
                }
                
            # 检查试用期
            if user['user_type'] == self.user_types['FREE'] and user['status'] == self.user_status['TRIAL']:
                trial_end = datetime.fromisoformat(user['created_at']) + timedelta(days=user['trial_days'])
                if datetime.now() > trial_end:
                    # 试用期已过期
                    self.db_manager.execute_update(
                        "UPDATE users SET status = ? WHERE id = ?",
                        (self.user_status['INACTIVE'], user['id'])
                    )
                    return {
                        'success': False,
                        'message': '试用期已过期，请充值激活',
                        'code': 'TRIAL_EXPIRED'
                    }
                    
            # 更新登录信息
            self.db_manager.execute_update(
                "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?",
                (user['id'],)
            )
            
            # 设置当前用户
            self.current_user = user
            
            self.logger.info(f"用户登录成功: {username}")
            
            return {
                'success': True,
                'message': '登录成功',
                'user_info': user
            }
            
        except Exception as e:
            self.logger.error(f"用户登录失败: {e}")
            return {
                'success': False,
                'message': f'登录失败: {str(e)}',
                'code': 'LOGIN_ERROR'
            }
            
    @handle_exceptions("UserManager")
    def logout_user(self):
        """用户登出"""
        if self.current_user:
            self.logger.info(f"用户登出: {self.current_user['username']}")
            self.current_user = None
            
    @handle_exceptions("UserManager")
    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取用户信息"""
        try:
            users = self.db_manager.execute_query(
                "SELECT * FROM users WHERE id = ?",
                (user_id,)
            )
            
            if users:
                user = dict(users[0])
                # 移除敏感信息
                user.pop('password_hash', None)
                return user
            return None
            
        except Exception as e:
            self.logger.error(f"获取用户信息失败: {e}")
            return None
            
    @handle_exceptions("UserManager")
    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """根据用户名获取用户信息"""
        try:
            users = self.db_manager.execute_query(
                "SELECT * FROM users WHERE username = ?",
                (username,)
            )
            
            if users:
                return dict(users[0])
            return None
            
        except Exception as e:
            self.logger.error(f"获取用户信息失败: {e}")
            return None
            
    @handle_exceptions("UserManager")
    def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """根据邮箱获取用户信息"""
        try:
            users = self.db_manager.execute_query(
                "SELECT * FROM users WHERE email = ?",
                (email,)
            )
            
            if users:
                return dict(users[0])
            return None
            
        except Exception as e:
            self.logger.error(f"获取用户信息失败: {e}")
            return None
            
    @handle_exceptions("UserManager")
    def update_user_info(self, user_id: int, **kwargs) -> bool:
        """更新用户信息"""
        try:
            # 允许更新的字段
            allowed_fields = ['email', 'phone', 'nickname', 'avatar']
            
            update_fields = []
            params = []
            
            for field, value in kwargs.items():
                if field in allowed_fields:
                    update_fields.append(f"{field} = ?")
                    params.append(value)
                    
            if not update_fields:
                return True
                
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            params.append(user_id)
            
            query = f"UPDATE users SET {', '.join(update_fields)} WHERE id = ?"
            
            rows_affected = self.db_manager.execute_update(query, params)
            
            if rows_affected > 0:
                self.logger.info(f"用户信息更新成功: ID {user_id}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"更新用户信息失败: {e}")
            return False
            
    @handle_exceptions("UserManager")
    def change_password(self, user_id: int, old_password: str, new_password: str) -> Dict[str, Any]:
        """修改密码"""
        try:
            # 获取用户信息
            user = self.get_user_by_id(user_id)
            if not user:
                return {
                    'success': False,
                    'message': '用户不存在'
                }
                
            # 获取完整用户信息（包含密码哈希）
            full_user = self.db_manager.execute_query(
                "SELECT password_hash FROM users WHERE id = ?",
                (user_id,)
            )[0]
            
            # 验证旧密码
            if not self._verify_password(old_password, full_user['password_hash']):
                return {
                    'success': False,
                    'message': '原密码错误'
                }
                
            # 加密新密码
            new_password_hash = self._hash_password(new_password)
            
            # 更新密码
            rows_affected = self.db_manager.execute_update(
                "UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (new_password_hash, user_id)
            )
            
            if rows_affected > 0:
                self.logger.info(f"密码修改成功: 用户ID {user_id}")
                return {
                    'success': True,
                    'message': '密码修改成功'
                }
            else:
                return {
                    'success': False,
                    'message': '密码修改失败'
                }
                
        except Exception as e:
            self.logger.error(f"修改密码失败: {e}")
            return {
                'success': False,
                'message': f'修改密码失败: {str(e)}'
            }
            
    def _hash_password(self, password: str) -> str:
        """加密密码"""
        salt = uuid.uuid4().hex
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
        return f"{salt}${password_hash}"
        
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        try:
            salt, hash_value = password_hash.split('$')
            return hashlib.sha256((password + salt).encode()).hexdigest() == hash_value
        except:
            return False
            
    def _generate_machine_code(self) -> str:
        """生成机器码"""
        try:
            # 获取系统信息
            system_info = f"{platform.system()}-{platform.machine()}-{platform.processor()}"
            machine_code = hashlib.md5(system_info.encode()).hexdigest()
            return machine_code
        except:
            return str(uuid.uuid4())
            
    @handle_exceptions("UserManager")
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """获取当前登录用户"""
        return self.current_user
        
    @handle_exceptions("UserManager")
    def is_logged_in(self) -> bool:
        """检查是否已登录"""
        return self.current_user is not None
        
    @handle_exceptions("UserManager")
    def get_user_stats(self) -> Dict[str, Any]:
        """获取用户统计信息"""
        try:
            # 总用户数
            total_users = self.db_manager.execute_query(
                "SELECT COUNT(*) as count FROM users"
            )[0]['count']
            
            # 活跃用户数
            active_users = self.db_manager.execute_query(
                "SELECT COUNT(*) as count FROM users WHERE status = ?",
                (self.user_status['ACTIVE'],)
            )[0]['count']
            
            # VIP用户数
            vip_users = self.db_manager.execute_query(
                "SELECT COUNT(*) as count FROM users WHERE user_type >= ?",
                (self.user_types['VIP'],)
            )[0]['count']
            
            # 今日注册用户数
            today_users = self.db_manager.execute_query(
                "SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = DATE('now')"
            )[0]['count']
            
            return {
                'total_users': total_users,
                'active_users': active_users,
                'vip_users': vip_users,
                'today_users': today_users
            }
            
        except Exception as e:
            self.logger.error(f"获取用户统计失败: {e}")
            return {
                'total_users': 0,
                'active_users': 0,
                'vip_users': 0,
                'today_users': 0
            }
