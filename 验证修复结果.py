#!/usr/bin/env python3
"""
验证修复结果
"""

import json
import re

def test_escape_character_handling():
    """测试转义字符处理"""
    print("=== 测试转义字符处理 ===")
    
    # 模拟原始JSON内容（包含转义字符）
    original_content = "1***哈喽，大家好\\n2***感谢大家的捧场\\n3***没有花里胡哨"
    
    print(f"原始内容: {original_content}")
    
    # 处理转义字符
    processed_content = original_content.replace('\\n', '\n')
    
    print(f"处理后内容:")
    print(processed_content)
    
    # 统计行数
    content_lines = [line.strip() for line in processed_content.split('\n') if line.strip()]
    script_lines = [line for line in content_lines if line and '***' in line]
    
    print(f"总行数: {len(content_lines)}")
    print(f"话术行数: {len(script_lines)}")
    
    return len(script_lines) == 3  # 应该有3行话术

def test_display_text_generation():
    """测试显示文本生成"""
    print("\n=== 测试显示文本生成 ===")
    
    # 模拟解析后的数据
    script_time_segments = {
        "kaer": {
            "0秒 - 10秒": {
                'start': 0,
                'end': 10,
                'content': "1***哈喽，大家好\n2***感谢大家的捧场\n3***没有花里胡哨\n4***有啥就问\n5***云想衣裳花想容"
            },
            "10秒 - 20秒": {
                'start': 10,
                'end': 20,
                'content': "6***自古套路得人心\n7***新来的家人们\n8***你们的到来蓬荜生辉\n9***随便什么数字\n10***俗话说的好"
            },
            "20秒 - 30秒": {
                'start': 20,
                'end': 30,
                'content': "11***相信自己的眼睛\n12***斩青丝斩难过\n13***不穿摸，不贴片\n14***播播间人很多\n15***万水千山总是情"
            }
        }
    }
    
    script_name = "kaer"
    
    # 生成显示文本
    if script_name in script_time_segments and script_time_segments[script_name]:
        time_segments_count = len(script_time_segments[script_name])
        display_text = f"# 时间段话术：{script_name}\n"
        display_text += f"# 共有 {time_segments_count} 个时间段\n\n"
        display_text += "# 时间段列表：\n"
        
        for segment_name, segment_data in script_time_segments[script_name].items():
            start_time = segment_data.get('start', 0)
            end_time = segment_data.get('end', 0)
            # 正确统计非空行数
            content_lines = len([line for line in segment_data.get('content', '').split('\n') if line.strip()])
            display_text += f"# - {segment_name} ({start_time}秒-{end_time}秒) - {content_lines}行话术\n"
        
        display_text += "\n# 请在左侧时间段列表中选择具体时间段进行编辑"
        display_text += "\n# 注意：此话术已解析为时间段格式，不显示原始JSON内容"
        
        print("生成的显示文本:")
        print(display_text)
        
        # 验证结果
        expected_lines = [5, 5, 5]  # 每个时间段应该有5行话术
        actual_lines = []
        for segment_name, segment_data in script_time_segments[script_name].items():
            content_lines = len([line for line in segment_data.get('content', '').split('\n') if line.strip()])
            actual_lines.append(content_lines)
        
        print(f"期望行数: {expected_lines}")
        print(f"实际行数: {actual_lines}")
        
        return expected_lines == actual_lines
    
    return False

def test_complete_parsing():
    """测试完整解析流程"""
    print("\n=== 测试完整解析流程 ===")
    
    # 模拟真实JSON数据
    test_json = """{
  "0秒 - 10秒": "1***哈喽，大家好，首先感谢【大哥|哥哥|宝子】们的捧场。\\n2***稍作停留也是爱，所能接触的都是这四个大字。\\n3***没有花里胡哨，没有弯弯绕绕。\\n4***有啥就问，新来的家人们。\\n5***云想衣裳花想容，春风拂槛露华浓。",
  "10秒 - 20秒": "6***自古套路得人心，但唯有真心得天下。\\n7***新来的家人们，相遇就是缘分。\\n8***你们的到来蓬荜生辉，老妹我深感荣幸。\\n9***随便什么数字都是这四个大字。\\n10***俗话说的好，大哥们，人生不主动。",
  "20秒 - 30秒": "11***相信自己的眼睛，相信自己的耳朵。\\n12***斩青丝斩难过斩断红尘不为过。\\n13***不穿摸，不贴片，仙侠界的彭于晏。\\n14***播播间人很多，主播一个人一张嘴。\\n15***万水千山总是情，今天主播最热情。"
}"""
    
    try:
        # 解析JSON
        time_segments_data = json.loads(test_json)
        print(f"✅ JSON解析成功，包含 {len(time_segments_data)} 个时间段")
        
        # 检查格式
        is_new_format = all(isinstance(v, str) for v in time_segments_data.values())
        print(f"✅ 格式检测: {'新格式' if is_new_format else '旧格式'}")
        
        if is_new_format:
            script_name = "kaer"
            script_time_segments = {script_name: {}}
            
            # 解析每个时间段
            parsed_count = 0
            for time_key, time_data in time_segments_data.items():
                if isinstance(time_data, str):
                    segment_content = time_data
                    
                    # 提取时间信息
                    pattern = r'(\d+)秒\s*-\s*(\d+)秒'
                    match = re.search(pattern, time_key)
                    
                    if match:
                        start_time = int(match.group(1))
                        end_time = int(match.group(2))
                    else:
                        start_time = parsed_count * 10
                        end_time = (parsed_count + 1) * 10
                    
                    # 处理转义字符
                    processed_content = segment_content.replace('\\n', '\n')
                    
                    # 统计话术行数
                    content_lines = [line.strip() for line in processed_content.split('\n') if line.strip()]
                    script_lines = [line for line in content_lines if line and '***' in line]
                    
                    print(f"时间段: {time_key}")
                    print(f"  时间: {start_time}-{end_time}秒")
                    print(f"  行数: {len(script_lines)}/{len(content_lines)}")
                    print(f"  内容预览: {processed_content[:50]}...")
                    
                    # 存储数据
                    script_time_segments[script_name][time_key] = {
                        'start': start_time,
                        'end': end_time,
                        'content': processed_content
                    }
                    
                    parsed_count += 1
            
            print(f"\n✅ 成功解析 {parsed_count} 个时间段")
            
            # 生成显示文本
            time_segments_count = len(script_time_segments[script_name])
            display_text = f"# 时间段话术：{script_name}\n"
            display_text += f"# 共有 {time_segments_count} 个时间段\n\n"
            display_text += "# 时间段列表：\n"
            
            for segment_name, segment_data in script_time_segments[script_name].items():
                start_time = segment_data.get('start', 0)
                end_time = segment_data.get('end', 0)
                content_lines = len([line for line in segment_data.get('content', '').split('\n') if line.strip()])
                display_text += f"# - {segment_name} ({start_time}秒-{end_time}秒) - {content_lines}行话术\n"
            
            display_text += "\n# 请在左侧时间段列表中选择具体时间段进行编辑"
            display_text += "\n# 注意：此话术已解析为时间段格式，不显示原始JSON内容"
            
            print(f"\n📄 最终显示文本:")
            print(display_text)
            
            return True
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始验证修复结果...")
    
    try:
        # 运行所有测试
        test1 = test_escape_character_handling()
        test2 = test_display_text_generation()
        test3 = test_complete_parsing()
        
        print("\n=== 验证总结 ===")
        if test1 and test2 and test3:
            print("✅ 所有测试通过")
            print("\n🔧 修复完成的功能:")
            print("1. ✅ 转义字符处理 (\\n -> 换行符)")
            print("2. ✅ 正确的行数统计")
            print("3. ✅ 时间段信息提取")
            print("4. ✅ 显示文本生成")
            print("5. ✅ JSON格式检测")
            
            print("\n📋 预期效果:")
            print("- 话术编辑器中显示时间段概览")
            print("- 不再显示原始JSON内容")
            print("- 左侧时间段列表正常工作")
            print("- 行数统计准确")
            
            print("\n🚀 建议:")
            print("1. 重启应用程序")
            print("2. 选择 'kaer' 话术")
            print("3. 检查是否显示时间段概览")
            print("4. 验证时间段列表是否刷新")
            
        else:
            print("❌ 部分测试失败")
            print(f"测试结果: 转义字符={test1}, 显示文本={test2}, 完整解析={test3}")
            
    except Exception as e:
        print(f"❌ 验证过程中出现异常: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    main()
