# 播放列表持久化功能说明

## 🎯 功能概述

我已经成功实现了播放列表的持久化功能，现在未播放完的语音会在下次打开程序时自动保留。

## ✅ 实现的功能

### 1. 自动保存播放列表
- **实时保存**：每次播放列表发生变化时自动保存
- **保存时机**：
  - 新增播放项目时
  - 删除播放项目时
  - 更新播放状态时
  - 弹幕话术添加时
  - 报时话术添加时

### 2. 自动加载播放列表
- **启动时加载**：程序启动时自动检查并加载保存的播放列表
- **语音文件验证**：检查语音文件是否还存在
- **状态恢复**：自动恢复播放状态和文件信息

### 3. 智能状态管理
- **文件存在检查**：验证语音文件是否还在voices目录中
- **状态重置**：如果语音文件丢失，自动重置为"未下载"状态
- **完整性保证**：确保播放列表数据的完整性和一致性

## 🔧 技术实现

### 数据存储格式
```json
[
  {
    "id": 1,
    "voice_type": "主视频话术",
    "content": "欢迎来到直播间！{nick}你好！",
    "time_segment": "10秒 - 20秒",
    "status": "已下载",
    "filename": "abc123_0_100.wav",
    "sub_video": "无"
  },
  {
    "id": 2,
    "voice_type": "弹幕话术",
    "content": "感谢{nick}的关注！",
    "time_segment": "无",
    "status": "未下载",
    "filename": "",
    "sub_video": "游戏视频源"
  }
]
```

### 保存机制
```python
def save_playlist_to_file(self, playlist_items=None):
    """保存播放列表到文件"""
    # 自动从表格获取数据或使用提供的数据
    # 保存到 data/playlist.json 文件
    # JSON格式，UTF-8编码
```

### 加载机制
```python
def load_playlist_from_file(self):
    """从文件加载播放列表"""
    # 检查文件是否存在
    # 验证语音文件完整性
    # 重置丢失文件的状态
    # 返回有效的播放列表数据
```

## 📁 文件结构

### 持久化文件
```
data/
└── playlist.json          # 播放列表数据文件

voices/
├── abc123_0_100.wav      # 语音文件1
├── def456_0_100.wav      # 语音文件2
└── ghi789_0_100.wav      # 语音文件3
```

### 文件内容示例
```json
[
  {
    "id": 1,
    "voice_type": "主视频话术",
    "content": "欢迎来到直播间！测试主播你好！",
    "time_segment": "测试时间段",
    "status": "已下载",
    "filename": "a1b2c3d4_0_100.wav",
    "sub_video": "无"
  },
  {
    "id": 2,
    "voice_type": "弹幕话术",
    "content": "感谢观看，现在时间是14:30",
    "time_segment": "无",
    "status": "已下载",
    "filename": "e5f6g7h8_0_100.wav",
    "sub_video": "无"
  }
]
```

## 🚀 使用流程

### 第一次使用
1. **启动程序**：`python run_gui_qt5.py`
2. **点击播放**：生成新的播放列表
3. **自动保存**：播放列表自动保存到文件
4. **正常使用**：播放、添加弹幕话术、报时等

### 再次启动
1. **启动程序**：`python run_gui_qt5.py`
2. **自动加载**：程序自动检测并加载保存的播放列表
3. **状态恢复**：显示之前未播放完的项目
4. **继续播放**：点击播放按钮继续之前的播放队列

## 📋 功能特点

### 1. 无缝体验
- 程序重启后播放列表自动恢复
- 保持之前的播放状态和顺序
- 无需手动操作

### 2. 数据安全
- 实时保存，防止数据丢失
- 文件完整性检查
- 自动错误恢复

### 3. 智能管理
- 自动检测语音文件是否存在
- 智能状态重置
- 避免无效播放项目

## 🧪 测试场景

### 场景1：正常保存和恢复
1. 启动程序，点击播放生成播放列表
2. 等待部分语音下载完成
3. 关闭程序
4. 重新启动程序
5. **预期结果**：播放列表自动恢复，已下载的保持"已下载"状态

### 场景2：语音文件丢失处理
1. 启动程序，生成播放列表并下载语音
2. 手动删除voices目录中的某些语音文件
3. 重新启动程序
4. **预期结果**：丢失文件的项目状态重置为"未下载"

### 场景3：弹幕和报时持久化
1. 启动程序，添加弹幕话术和报时话术
2. 关闭程序
3. 重新启动程序
4. **预期结果**：弹幕话术和报时话术也被保留

## 🔍 调试信息

### 启动时的输出
```
🔄 检查是否有保存的播放列表...
✅ 从文件加载播放列表: 3 个项目
  ✅ 语音文件存在: abc123_0_100.wav
  ✅ 语音文件存在: def456_0_100.wav
  ⚠️ 语音文件丢失，重置状态: 感谢大家的观看...
📋 发现保存的播放列表，自动加载 3 个项目
✅ 播放列表显示已更新，共 3 行
✅ 播放列表已保存: 3 个项目
🔄 发现 1 个项目需要重新下载语音
💡 提示：点击播放按钮开始下载和播放
```

### 保存时的输出
```
✅ 播放列表显示已更新，共 3 行
✅ 播放列表已保存: 3 个项目
```

### 删除项目时的输出
```
✅ 删除已播放项目: 欢迎来到直播间！测试主播你好！...
✅ 播放列表已保存: 2 个项目
```

## ⚠️ 注意事项

### 1. 文件路径
- 播放列表保存在 `data/playlist.json`
- 语音文件保存在 `voices/` 目录
- 确保程序有读写权限

### 2. 数据格式
- 使用UTF-8编码的JSON格式
- 自动处理中文字符
- 保持数据结构一致性

### 3. 错误处理
- 文件不存在时不会报错
- 语音文件丢失时自动重置状态
- 异常情况下有详细的错误日志

## 🎉 优势总结

1. **用户体验**：无需重新配置，程序重启后自动恢复
2. **数据安全**：实时保存，防止意外丢失
3. **智能管理**：自动检测和修复数据不一致
4. **无缝集成**：与现有播放系统完美配合
5. **调试友好**：详细的日志输出便于问题排查

现在您可以放心地关闭和重新打开程序，播放列表会自动保留！🚀
