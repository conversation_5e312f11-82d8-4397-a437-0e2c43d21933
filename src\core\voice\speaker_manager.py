"""
AI Broadcaster v2 - 主播管理器
AI主播的管理和配置，支持多域名和公司代码过滤
"""

from typing import Dict, List, Optional, Any
from ...data.database_manager import DatabaseManager
from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class SpeakerManager:
    """AI主播管理器 - 支持多域名和公司代码过滤"""

    def __init__(self, db_manager: DatabaseManager):
        self.logger = create_logger("speaker_manager")
        self.db = db_manager
        self._initialize_database_tables()
        self._initialize_default_speakers()
        self._initialize_default_domains()
    
    def _initialize_database_tables(self):
        """初始化数据库表结构"""
        try:
            # 创建域名配置表
            self.db.execute_update("""
                CREATE TABLE IF NOT EXISTS voice_domains (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    domain_name TEXT UNIQUE NOT NULL,
                    api_url TEXT NOT NULL,
                    description TEXT,
                    company_codes TEXT,  -- JSON格式存储公司代码列表
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 检查ai_speakers表是否需要添加domain_id字段
            columns = self.db.execute_query("PRAGMA table_info(ai_speakers)")
            has_domain_id = any(col['name'] == 'domain_id' for col in columns)
            has_company_code = any(col['name'] == 'company_code' for col in columns)

            if not has_domain_id:
                self.db.execute_update("ALTER TABLE ai_speakers ADD COLUMN domain_id INTEGER DEFAULT 1")

            if not has_company_code:
                self.db.execute_update("ALTER TABLE ai_speakers ADD COLUMN company_code TEXT DEFAULT ''")

            self.logger.info("数据库表结构初始化完成")
        except Exception as e:
            self.logger.error(f"初始化数据库表失败: {e}")

    def _initialize_default_domains(self):
        """初始化默认域名配置"""
        default_domains = [
            {
                'domain_name': 'scjanelife',
                'api_url': 'http://ct.scjanelife.com/voice',
                'description': '简生活主域名',
                'company_codes': '["jane", "test"]',  # JSON格式
                'is_active': 1
            },
            {
                'domain_name': 'backup_domain',
                'api_url': 'http://backup.example.com/voice',
                'description': '备用域名',
                'company_codes': '["backup", "demo"]',
                'is_active': 1
            }
        ]

        try:
            # 检查是否已有域名数据
            existing = self.db.execute_query("SELECT COUNT(*) as count FROM voice_domains")
            if existing and existing[0]['count'] == 0:
                # 插入默认域名
                for domain in default_domains:
                    self.db.execute_insert(
                        """INSERT OR REPLACE INTO voice_domains
                           (domain_name, api_url, description, company_codes, is_active)
                           VALUES (?, ?, ?, ?, ?)""",
                        (domain['domain_name'], domain['api_url'], domain['description'],
                         domain['company_codes'], domain['is_active'])
                    )
                self.logger.log_success("默认域名配置初始化完成")
        except Exception as e:
            self.logger.error(f"初始化默认域名失败: {e}")

    def _initialize_default_speakers(self):
        """初始化默认主播（支持多域名和公司代码）"""
        default_speakers = [
            # 简生活域名的主播
            {
                'id': 1,
                'name': 'jane-小云',
                'description': '温柔甜美的女声',
                'voice_id': 1,
                'speed_min': 0.8,
                'speed_max': 1.2,
                'volume': 0.8,
                'domain_id': 1,
                'company_code': 'jane'
            },
            {
                'id': 2,
                'name': 'jane-小宇',
                'description': '清晰标准的男声',
                'voice_id': 2,
                'speed_min': 0.8,
                'speed_max': 1.2,
                'volume': 0.8,
                'domain_id': 1,
                'company_code': 'jane'
            },
            {
                'id': 3,
                'name': 'test-小雅',
                'description': '知性优雅的女声',
                'voice_id': 3,
                'speed_min': 0.8,
                'speed_max': 1.2,
                'volume': 0.8,
                'domain_id': 1,
                'company_code': 'test'
            },
            {
                'id': 4,
                'name': 'backup-小峰',
                'description': '磁性成熟的男声',
                'voice_id': 4,
                'speed_min': 0.8,
                'speed_max': 1.2,
                'volume': 0.8,
                'domain_id': 2,
                'company_code': 'backup'
            },
            {
                'id': 5,
                'name': 'demo-小萌',
                'description': '活泼可爱的萝莉音',
                'voice_id': 5,
                'speed_min': 0.9,
                'speed_max': 1.3,
                'volume': 0.8,
                'domain_id': 2,
                'company_code': 'demo'
            }
        ]

        try:
            # 检查是否已有数据
            existing = self.db.execute_query("SELECT COUNT(*) as count FROM ai_speakers")
            if existing and existing[0]['count'] == 0:
                # 插入默认主播
                for speaker in default_speakers:
                    self.db.execute_insert(
                        """INSERT OR REPLACE INTO ai_speakers
                           (id, name, description, voice_id, speed_min, speed_max, volume, domain_id, company_code)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                        (speaker['id'], speaker['name'], speaker['description'],
                         speaker['voice_id'], speaker['speed_min'], speaker['speed_max'],
                         speaker['volume'], speaker['domain_id'], speaker['company_code'])
                    )
                self.logger.log_success("默认主播初始化完成")
        except Exception as e:
            self.logger.error(f"初始化默认主播失败: {e}")
    
    @handle_exceptions("SpeakerManager")
    def get_all_speakers(self, company_code: str = None) -> List[Dict[str, Any]]:
        """获取所有主播，支持公司代码过滤"""
        try:
            if company_code:
                # 根据公司代码过滤主播
                speakers = self.db.execute_query(
                    """SELECT s.id, s.name, s.description, s.voice_id, s.speed_min, s.speed_max,
                              s.volume, s.domain_id, s.company_code, d.api_url, d.domain_name
                       FROM ai_speakers s
                       LEFT JOIN voice_domains d ON s.domain_id = d.id
                       WHERE s.company_code = ? OR s.name LIKE ?
                       ORDER BY s.id""",
                    (company_code, f"{company_code}-%")
                )
            else:
                # 获取所有主播
                speakers = self.db.execute_query(
                    """SELECT s.id, s.name, s.description, s.voice_id, s.speed_min, s.speed_max,
                              s.volume, s.domain_id, s.company_code, d.api_url, d.domain_name
                       FROM ai_speakers s
                       LEFT JOIN voice_domains d ON s.domain_id = d.id
                       ORDER BY s.id"""
                )
            return [dict(speaker) for speaker in speakers]
        except Exception as e:
            self.logger.error(f"获取主播列表失败: {e}")
            return []

    @handle_exceptions("SpeakerManager")
    def get_speakers_by_company(self, company_code: str, display_format: bool = True) -> List[Dict[str, Any]]:
        """根据公司代码获取主播列表，支持显示格式化"""
        try:
            speakers = self.get_all_speakers(company_code)

            if display_format:
                # 格式化显示名称（隐藏公司代码前缀）
                for speaker in speakers:
                    original_name = speaker['name']
                    if original_name.startswith(f"{company_code}-"):
                        speaker['display_name'] = original_name[len(company_code)+1:]
                    else:
                        speaker['display_name'] = original_name
                    speaker['original_name'] = original_name

            return speakers
        except Exception as e:
            self.logger.error(f"根据公司代码获取主播失败: {e}")
            return []
    
    @handle_exceptions("SpeakerManager")
    def get_speaker_by_id(self, speaker_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取主播"""
        try:
            speakers = self.db.execute_query(
                """SELECT id, name, description, voice_id, speed_min, speed_max, volume 
                   FROM ai_speakers WHERE id = ?""",
                (speaker_id,)
            )
            return dict(speakers[0]) if speakers else None
        except Exception as e:
            self.logger.error(f"获取主播信息失败: {e}")
            return None
    
    @handle_exceptions("SpeakerManager")
    def get_speaker_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """根据名称获取主播"""
        try:
            speakers = self.db.execute_query(
                """SELECT id, name, description, voice_id, speed_min, speed_max, volume 
                   FROM ai_speakers WHERE name = ?""",
                (name,)
            )
            return dict(speakers[0]) if speakers else None
        except Exception as e:
            self.logger.error(f"获取主播信息失败: {e}")
            return None
    
    @handle_exceptions("SpeakerManager")
    def add_speaker(self, name: str, description: str, voice_id: int,
                   speed_min: float = 0.8, speed_max: float = 1.2,
                   volume: float = 0.8) -> Optional[int]:
        """添加新主播"""
        try:
            speaker_id = self.db.execute_insert(
                """INSERT INTO ai_speakers (name, description, voice_id, speed_min, speed_max, volume) 
                   VALUES (?, ?, ?, ?, ?, ?)""",
                (name, description, voice_id, speed_min, speed_max, volume)
            )
            if speaker_id:
                self.logger.log_success(f"添加主播成功: {name}")
            return speaker_id
        except Exception as e:
            self.logger.error(f"添加主播失败: {e}")
            return None
    
    @handle_exceptions("SpeakerManager")
    def update_speaker(self, speaker_id: int, **kwargs) -> bool:
        """更新主播信息"""
        try:
            # 构建更新语句
            update_fields = []
            params = []
            
            for field in ['name', 'description', 'voice_id', 'speed_min', 'speed_max', 'volume']:
                if field in kwargs:
                    update_fields.append(f"{field} = ?")
                    params.append(kwargs[field])
            
            if not update_fields:
                return False
            
            params.append(speaker_id)
            query = f"UPDATE ai_speakers SET {', '.join(update_fields)} WHERE id = ?"
            
            rows_affected = self.db.execute_update(query, tuple(params))
            
            if rows_affected > 0:
                self.logger.log_success(f"更新主播成功: ID={speaker_id}")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"更新主播失败: {e}")
            return False
    
    @handle_exceptions("SpeakerManager")
    def delete_speaker(self, speaker_id: int) -> bool:
        """删除主播"""
        try:
            # 检查是否为默认主播
            if speaker_id <= 5:
                self.logger.warning("不能删除默认主播")
                return False
            
            rows_affected = self.db.execute_update(
                "DELETE FROM ai_speakers WHERE id = ?",
                (speaker_id,)
            )
            
            if rows_affected > 0:
                self.logger.log_success(f"删除主播成功: ID={speaker_id}")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"删除主播失败: {e}")
            return False
    
    def get_speaker_names(self) -> List[str]:
        """获取所有主播名称列表"""
        speakers = self.get_all_speakers()
        return [speaker['name'] for speaker in speakers]
    
    def get_speaker_choices(self) -> List[tuple]:
        """获取主播选择列表（用于UI组件）"""
        speakers = self.get_all_speakers()
        return [(speaker['id'], speaker['name']) for speaker in speakers]
    
    @handle_exceptions("SpeakerManager")
    def get_random_speed(self, speaker_id: int) -> float:
        """获取主播的随机语速"""
        import random
        
        speaker = self.get_speaker_by_id(speaker_id)
        if speaker:
            speed_min = speaker.get('speed_min', 0.8)
            speed_max = speaker.get('speed_max', 1.2)
            return round(random.uniform(speed_min, speed_max), 2)
        return 1.0
    
    def validate_speaker_config(self, config: Dict[str, Any]) -> tuple[bool, str]:
        """验证主播配置"""
        required_fields = ['name', 'voice_id']
        
        for field in required_fields:
            if field not in config:
                return False, f"缺少必填字段: {field}"
        
        # 验证数值范围
        if 'speed_min' in config and (config['speed_min'] < 0.1 or config['speed_min'] > 2.0):
            return False, "最小语速必须在0.1-2.0之间"
        
        if 'speed_max' in config and (config['speed_max'] < 0.1 or config['speed_max'] > 2.0):
            return False, "最大语速必须在0.1-2.0之间"
        
        if 'volume' in config and (config['volume'] < 0.0 or config['volume'] > 1.0):
            return False, "音量必须在0.0-1.0之间"
        
        if 'speed_min' in config and 'speed_max' in config:
            if config['speed_min'] > config['speed_max']:
                return False, "最小语速不能大于最大语速"
        
        return True, "配置有效"
