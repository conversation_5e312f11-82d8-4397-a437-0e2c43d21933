#!/usr/bin/env python3
import json

# 模拟从服务器获取的包含实际换行符的JSON
raw_json = '{"0秒 - 10秒": "1***哈喽\n2***大家好\n3***感谢捧场"}'

print("原始JSON:", repr(raw_json))

# 直接解析（会失败）
try:
    data = json.loads(raw_json)
    print("直接解析成功")
except Exception as e:
    print("直接解析失败:", e)

# 预处理后解析
processed = raw_json.replace('\n', '\\n')
print("预处理后:", repr(processed))

try:
    data = json.loads(processed)
    print("预处理后解析成功:", data)
    
    # 检查内容
    content = data["0秒 - 10秒"]
    print("内容:", repr(content))
    
    # 智能分割
    if '\\n' in content:
        lines = content.split('\\n')
        print("使用 \\n 分割:", lines)
    else:
        lines = content.split('\n')
        print("使用 \\n 分割:", lines)
    
    print("行数:", len(lines))
    
except Exception as e:
    print("预处理后解析失败:", e)
