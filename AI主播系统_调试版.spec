# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 调试版本：只添加图标文件
added_files = [
    ('简生活图标.ico', '.'),
]

# 基础隐藏导入
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.QtMultimedia',
    'requests',
    'json',
    'sqlite3',
    'threading',
    'queue',
    'time',
    'datetime',
    'random',
    'os',
    'sys',
    'pathlib',
]

a = Analysis(
    ['run_gui_qt5.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AI主播系统_调试版',
    debug=True,  # 🔧 启用调试模式
    bootloader_ignore_signals=False,
    strip=False,  # 🔧 保留调试符号
    upx=False,   # 🔧 禁用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 🔧 显示控制台窗口，便于查看错误信息
    disable_windowed_traceback=False,  # 🔧 启用错误回溯
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='简生活图标.ico'
)
