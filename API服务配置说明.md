# 🌐 AI主播系统API服务配置说明

## 📋 API服务器分类

### 🎤 外部语音服务器
**服务器地址**: `http://ct.scjanelife.com`
**用途**: 语音合成和主播管理
**网络要求**: 需要外网连接

#### 语音相关API
1. **获取主播列表**
   - 接口: `GET http://ct.scjanelife.com/voice/speakers`
   - 功能: 获取可用的AI主播列表
   - 响应: JSON格式的主播数组

2. **语音下载**
   - 接口: `GET http://ct.scjanelife.com/voice/bert-vits2`
   - 参数: `id`(主播ID), `text`(文本), `length`(语速)
   - 功能: 文本转语音，返回音频文件流

### 🏠 本地服务器
**服务器地址**: `http://localhost:12456`
**用途**: 用户管理、话术管理、AI对话管理
**网络要求**: 本地运行

#### 用户认证API
1. **用户登录**
   - 接口: `POST /user/login`
   - 功能: 用户身份验证

2. **用户注册**
   - 接口: `POST /user/register`
   - 功能: 新用户注册

3. **用户充值**
   - 接口: `POST /user/recharge`
   - 功能: 账户充值

#### 话术管理API
1. **获取话术列表**
   - 接口: `GET /getscriptlist`
   - 功能: 获取所有话术文件列表

2. **话术操作**
   - 接口: `POST /`
   - 参数: `类型`(获取话术/上传话术/新建话术)
   - 功能: 话术的CRUD操作

#### AI对话管理API
1. **获取对话列表**
   - 接口: `GET /dialoguelist`
   - 功能: 获取所有AI对话列表

2. **对话操作**
   - 接口: `POST /`
   - 参数: `类型`(获取ai对话/上传ai对话/新建ai对话)
   - 功能: AI对话的CRUD操作

#### 更新检查API
1. **检查更新**
   - 接口: `POST /api/check-update`
   - 功能: 检查客户端版本更新

## 🔧 系统配置

### 主界面配置
```python
config = {
    'voice_api_url': 'http://ct.scjanelife.com/voice',  # 外部语音服务器
    'server_url': 'http://localhost:12456'  # 本地服务器
}
```

### 服务状态
- **✅ 语音服务**: 外部服务器，支持5个主播模型
- **✅ 话术服务**: 本地服务器，支持话术管理
- **✅ 对话服务**: 本地服务器，支持AI对话管理
- **⚠️ WebSocket服务**: 需要额外配置

## 📊 功能实现状态

### 🎤 AI主播功能
- **✅ 主播列表**: 从外部服务器获取真实主播数据
- **✅ 语音下载**: 支持文本转语音，自动缓存
- **✅ 语速控制**: 0.8-1.2范围调节
- **✅ 主播选择**: 下拉选择框，显示主播信息

### 📝 话术管理功能
- **✅ 话术列表**: 从本地服务器获取话术列表
- **✅ 话术编辑**: 支持内容查看和编辑
- **✅ 新建话术**: 支持创建新话术文件
- **✅ 保存话术**: 支持保存修改内容
- **⚠️ 话术内容**: 部分API需要服务器端调整

### 💬 AI对话管理功能
- **✅ 对话列表**: 从本地服务器获取对话列表
- **✅ 关键词管理**: 支持关键词的增删改查
- **✅ 回复编辑**: 支持变量词格式
- **✅ 对话解析**: 自动解析关键词和回复
- **✅ 新建对话**: 支持创建新对话文件

## 🌐 网络要求

### 外网连接
- **语音服务**: 需要访问 `ct.scjanelife.com`
- **主播列表**: 实时获取最新主播信息
- **语音合成**: 在线文本转语音服务

### 本地连接
- **本地服务器**: 需要运行在 `localhost:12456`
- **数据管理**: 话术、对话、用户数据
- **业务逻辑**: 用户认证、内容管理

## 🔍 故障排除

### 常见问题
1. **主播列表获取失败**
   - 检查外网连接
   - 确认 `ct.scjanelife.com` 可访问

2. **话术/对话操作失败**
   - 检查本地服务器是否运行
   - 确认端口12456未被占用

3. **API调用超时**
   - 检查网络连接稳定性
   - 调整超时设置

### 调试信息
- 所有API调用都有详细日志
- 错误信息会显示在界面和控制台
- 支持实时状态监控

## 🎯 使用建议

### 开发环境
- 确保本地服务器正常运行
- 测试外网连接稳定性
- 使用测试脚本验证API功能

### 生产环境
- 配置网络代理（如需要）
- 监控API调用成功率
- 定期检查服务器状态

## 📈 性能优化

### 缓存机制
- **语音文件**: 自动缓存到本地
- **主播列表**: 支持手动刷新
- **话术内容**: 实时加载和保存

### 网络优化
- **连接复用**: HTTP会话复用
- **超时控制**: 合理的超时设置
- **错误重试**: 自动重试机制

---

**注意**: 确保外部语音服务器和本地服务器都正常运行，才能享受完整的AI主播系统功能。
