# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 简化版打包配置 - 专注于解决启动问题
added_files = [
    ('简生活图标.ico', '.'),  # 图标文件
]

# 最小化的隐藏导入模块
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.QtMultimedia',
    'PyQt5.QtNetwork',
    'requests',
    'json',
    'sqlite3',
    'threading',
    'queue',
    'time',
    'datetime',
    'random',
    'os',
    'sys',
    'pathlib',
    'email',
    'email.mime',
    'email.mime.text',
    'hashlib',
    'base64',
    'logging',
    'urllib',
    'urllib.parse',
    'http',
    'ssl',
    'socket',
    'subprocess',
    'shutil',
    'tempfile',
    'collections',
    'itertools',
    'functools',
    'copy',
    'pickle',
    'io',
    'codecs',
    'traceback',
    'inspect',
    'types',
    'importlib',
]

a = Analysis(
    ['run_gui_qt5.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不必要的模块
        'tkinter',
        'matplotlib',
        'scipy',
        'pandas',
        'jupyter',
        'IPython',
        'notebook',
        'test',
        'tests',
        'unittest',
        'doctest',
        'pydoc',
        'pip',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AI主播系统_正式版',
    debug=False,  # 关闭调试模式
    bootloader_ignore_signals=False,
    strip=True,  # 启用strip，移除调试信息
    upx=True,  # 启用UPX压缩，减小文件大小
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 关闭控制台窗口
    disable_windowed_traceback=True,  # 禁用回溯信息
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='简生活图标.ico'
)
