# 双主视频管理器使用说明

## 概述

双主视频管理器（DualVideoManager）是AI直播系统中的核心组件，用于实现两个主视频源的无缝切换，避免视频切换时的黑屏问题。

## 主要功能

### 1. 智能状态检测
- **显示状态检测**：自动检测两个主视频源的显示状态
- **优先级管理**：当两个视频源都显示时，自动选择最上面的（z-order最高的）
- **自动激活**：当两个视频源都隐藏时，自动开启视频源A

### 2. 无缝切换机制
- **提前准备**：在当前视频播放完成前提前准备下一个视频源
- **变速支持**：支持设置视频播放速度范围，实现平滑过渡
- **循环播放**：A→B→A的循环切换模式

### 3. 实时监控
- **播放状态监控**：实时监控视频播放进度和状态
- **自动切换**：根据播放进度自动切换到下一个视频源
- **错误处理**：完善的错误处理和恢复机制

## 使用方法

### 1. 初始化

```python
from src.core.playback.dual_video_manager import DualVideoManager
from src.services.obs_controller import OBSController

# 创建OBS控制器
obs_controller = OBSController("localhost", 4455, "")

# 创建双主视频管理器
dual_manager = DualVideoManager(obs_controller)
```

### 2. 设置视频源

```python
# 设置两个主视频源
dual_manager.set_video_sources("主视频源A", "主视频源B")
```

### 3. 配置参数

```python
# 设置切换阈值（提前多少秒准备下一个视频）
dual_manager.set_switch_threshold(5.0)  # 提前5秒

# 设置变速范围
dual_manager.set_speed_range(0.8, 1.5)  # 0.8x - 1.5x速度
```

### 4. 开始监控

```python
# 开始自动监控和切换
dual_manager.start_monitoring()
```

### 5. 手动控制

```python
# 手动切换视频源
dual_manager.manual_switch()

# 强制切换到指定源
dual_manager.force_switch_to_source("主视频源B")

# 设置指定源的播放速度
dual_manager.set_source_speed("主视频源A", 1.2)
```

### 6. 状态查询

```python
# 获取当前视频状态
video_status = dual_manager.get_current_video_status()
print(f"当前激活源: {video_status.get('active_source')}")
print(f"播放进度: {video_status.get('progress_percent', 0):.1f}%")

# 获取管理器状态
manager_status = dual_manager.get_manager_status()
print(f"监控状态: {manager_status['monitoring']}")
print(f"OBS连接: {manager_status['obs_connected']}")
```

### 7. 停止监控

```python
# 停止监控
dual_manager.stop_monitoring()
```

## 回调函数

可以设置回调函数来响应各种事件：

```python
def on_source_switched(current_source, previous_source):
    print(f"视频源切换: {previous_source} -> {current_source}")

def on_source_prepared(source_name):
    print(f"视频源已准备: {source_name}")

def on_switch_error(error_message):
    print(f"切换错误: {error_message}")

# 设置回调函数
dual_manager.on_source_switched = on_source_switched
dual_manager.on_source_prepared = on_source_prepared
dual_manager.on_switch_error = on_switch_error
```

## 集成到播放控制器

在播放控制器中使用双主视频管理器：

```python
from src.core.playback_controller import PlaybackController

# 创建播放控制器（自动包含双主视频管理器）
controller = PlaybackController()

# 设置双主视频源
controller.set_dual_video_sources("主视频源A", "主视频源B")

# 开始双主视频监控
controller.start_dual_video_monitoring()

# 设置切换参数
controller.set_video_switch_threshold(3.0)
controller.set_video_speed_range(0.9, 1.3)

# 获取视频状态
video_status = controller.get_current_video_status()
```

## 配置要求

### OBS设置
1. **WebSocket服务器**：确保OBS WebSocket服务器已启用
2. **视频源配置**：两个主视频源必须在同一场景中
3. **媒体源类型**：支持ffmpeg_source、vlc_source等媒体源类型

### 系统要求
- Python 3.8+
- OBS Studio 28.0+
- obs-websocket 5.0+

## 注意事项

1. **连接状态**：确保OBS WebSocket连接正常
2. **源名称**：视频源名称必须与OBS中的完全一致
3. **场景配置**：两个视频源必须在当前激活的场景中
4. **性能考虑**：监控频率为每秒1次，可根据需要调整

## 故障排除

### 常见问题

1. **无法检测到视频源**
   - 检查OBS连接状态
   - 确认视频源名称正确
   - 验证视频源在当前场景中

2. **切换不及时**
   - 调整切换阈值参数
   - 检查视频播放状态
   - 确认媒体源支持状态查询

3. **切换时出现黑屏**
   - 确保两个视频源都已正确配置
   - 检查视频文件格式兼容性
   - 验证OBS场景设置

### 调试方法

启用详细日志：
```python
import logging
logging.getLogger("dual_video_manager").setLevel(logging.DEBUG)
```

查看状态信息：
```python
status = dual_manager.get_manager_status()
print(f"详细状态: {status}")
```

## 更新日志

- **v1.0.0**：初始版本，支持基本的双主视频切换
- **v1.1.0**：添加变速支持和错误处理
- **v1.2.0**：优化监控性能和状态检测
