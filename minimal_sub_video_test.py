#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小化副视频测试程序
直接测试副视频功能，不依赖完整的主程序初始化
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QTextEdit, QLabel, 
                             QLineEdit, QGroupBox, QMessageBox, QComboBox)
from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QFont

class MinimalSubVideoTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("最小化副视频测试程序")
        self.setGeometry(100, 100, 800, 600)
        
        self.obs_controller = None
        self.obs_sources = []
        
        self.init_ui()
        self.init_obs()
        
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("最小化副视频测试程序")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title_label)
        
        # OBS连接区域
        obs_group = QGroupBox("OBS连接")
        obs_layout = QVBoxLayout(obs_group)
        
        # 连接按钮
        connect_layout = QHBoxLayout()
        self.connect_btn = QPushButton("连接OBS")
        self.connect_btn.clicked.connect(self.connect_obs)
        connect_layout.addWidget(self.connect_btn)
        
        self.disconnect_btn = QPushButton("断开连接")
        self.disconnect_btn.clicked.connect(self.disconnect_obs)
        self.disconnect_btn.setEnabled(False)
        connect_layout.addWidget(self.disconnect_btn)
        
        self.obs_status_label = QLabel("状态: 未连接")
        connect_layout.addWidget(self.obs_status_label)
        
        obs_layout.addLayout(connect_layout)
        
        # 视频源显示
        self.sources_text = QTextEdit()
        self.sources_text.setMaximumHeight(100)
        self.sources_text.setReadOnly(True)
        obs_layout.addWidget(self.sources_text)
        
        refresh_btn = QPushButton("刷新视频源")
        refresh_btn.clicked.connect(self.refresh_sources)
        obs_layout.addWidget(refresh_btn)
        
        layout.addWidget(obs_group)
        
        # 副视频配置区域
        config_group = QGroupBox("副视频配置")
        config_layout = QVBoxLayout(config_group)
        
        self.config_text = QTextEdit()
        self.config_text.setMaximumHeight(100)
        self.config_text.setReadOnly(True)
        config_layout.addWidget(self.config_text)
        
        refresh_config_btn = QPushButton("刷新配置")
        refresh_config_btn.clicked.connect(self.refresh_config)
        config_layout.addWidget(refresh_config_btn)
        
        layout.addWidget(config_group)
        
        # 副视频测试区域
        test_group = QGroupBox("副视频测试")
        test_layout = QVBoxLayout(test_group)
        
        # 弹幕输入
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("测试弹幕:"))
        self.danmaku_input = QLineEdit()
        self.danmaku_input.setPlaceholderText("输入包含关键词的弹幕")
        input_layout.addWidget(self.danmaku_input)
        
        test_btn = QPushButton("测试触发")
        test_btn.clicked.connect(self.test_trigger)
        input_layout.addWidget(test_btn)
        
        test_layout.addLayout(input_layout)
        
        # 快速测试按钮
        quick_layout = QHBoxLayout()
        
        fire_btn = QPushButton("火箭弹幕")
        fire_btn.clicked.connect(lambda: self.quick_test("感谢老板的火箭，太给力了！"))
        quick_layout.addWidget(fire_btn)
        
        gift_btn = QPushButton("礼物弹幕")
        gift_btn.clicked.connect(lambda: self.quick_test("谢谢大家的礼物支持"))
        quick_layout.addWidget(gift_btn)
        
        six_btn = QPushButton("666弹幕")
        six_btn.clicked.connect(lambda: self.quick_test("主播666，继续加油！"))
        quick_layout.addWidget(six_btn)
        
        test_layout.addLayout(quick_layout)
        
        # 视频源切换测试
        switch_layout = QHBoxLayout()
        switch_layout.addWidget(QLabel("切换到:"))
        self.source_combo = QComboBox()
        switch_layout.addWidget(self.source_combo)
        
        switch_btn = QPushButton("切换视频源")
        switch_btn.clicked.connect(self.switch_source)
        switch_layout.addWidget(switch_btn)
        
        test_layout.addLayout(switch_layout)
        
        layout.addWidget(test_group)
        
        # 日志区域
        log_group = QGroupBox("测试日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        clear_btn = QPushButton("清空日志")
        clear_btn.clicked.connect(self.clear_log)
        log_layout.addWidget(clear_btn)
        
        layout.addWidget(log_group)
        
        # 初始化数据
        self.refresh_config()
    
    def init_obs(self):
        """初始化OBS控制器"""
        try:
            from src.services.obs_controller import OBSController
            self.obs_controller = OBSController()
            self.log("✅ OBS控制器初始化成功")
        except Exception as e:
            self.log(f"❌ OBS控制器初始化失败: {e}")
    
    def connect_obs(self):
        """连接OBS"""
        if not self.obs_controller:
            self.log("❌ OBS控制器未初始化")
            return
        
        self.log("🔗 尝试连接OBS...")
        
        try:
            # 设置连接参数
            self.obs_controller.host = "127.0.0.1"
            self.obs_controller.port = 4455
            
            # 连接
            if hasattr(self.obs_controller, 'connect'):
                result = self.obs_controller.connect()
                if result:
                    self.log("✅ OBS连接成功")
                    self.obs_status_label.setText("状态: 已连接")
                    self.connect_btn.setEnabled(False)
                    self.disconnect_btn.setEnabled(True)
                    
                    # 自动刷新视频源
                    QTimer.singleShot(1000, self.refresh_sources)
                else:
                    self.log("❌ OBS连接失败")
            else:
                self.log("❌ OBS控制器没有connect方法")
        except Exception as e:
            self.log(f"❌ OBS连接异常: {e}")
    
    def disconnect_obs(self):
        """断开OBS连接"""
        if not self.obs_controller:
            return
        
        try:
            if hasattr(self.obs_controller, 'disconnect'):
                self.obs_controller.disconnect()
            
            self.log("✅ OBS已断开连接")
            self.obs_status_label.setText("状态: 未连接")
            self.connect_btn.setEnabled(True)
            self.disconnect_btn.setEnabled(False)
            self.sources_text.clear()
            self.source_combo.clear()
            
        except Exception as e:
            self.log(f"❌ 断开连接异常: {e}")
    
    def refresh_sources(self):
        """刷新视频源"""
        if not self.obs_controller:
            self.log("❌ OBS控制器未初始化")
            return
        
        self.log("🔄 刷新视频源列表...")
        
        try:
            if hasattr(self.obs_controller, 'connected') and self.obs_controller.connected:
                if hasattr(self.obs_controller, 'get_source_list'):
                    sources = self.obs_controller.get_source_list()
                    if sources:
                        self.obs_sources = sources
                        self.log(f"✅ 获取到 {len(sources)} 个视频源")
                        
                        # 更新显示
                        sources_text = f"视频源列表 ({len(sources)} 个):\n"
                        for i, source in enumerate(sources, 1):
                            sources_text += f"{i}. {source}\n"
                        self.sources_text.setText(sources_text)
                        
                        # 更新下拉框
                        self.source_combo.clear()
                        self.source_combo.addItems(sources)
                    else:
                        self.log("⚠️ 没有获取到视频源")
                        self.sources_text.setText("没有可用的视频源")
                else:
                    self.log("❌ OBS控制器没有get_source_list方法")
            else:
                self.log("❌ OBS未连接")
        except Exception as e:
            self.log(f"❌ 获取视频源失败: {e}")
    
    def refresh_config(self):
        """刷新副视频配置"""
        try:
            config_file = Path("data/sub_videos.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                config_text = f"副视频配置 ({len(config)} 个关键词):\n"
                for keyword, data in config.items():
                    video_source = data.get('video_source', '未知')
                    config_text += f"• {keyword} → {video_source}\n"
                
                self.config_text.setText(config_text)
                self.log(f"✅ 副视频配置已刷新: {len(config)} 个关键词")
            else:
                self.config_text.setText("❌ 副视频配置文件不存在")
                self.log("❌ 副视频配置文件不存在")
        except Exception as e:
            self.config_text.setText(f"❌ 加载配置失败: {e}")
            self.log(f"❌ 刷新配置失败: {e}")
    
    def test_trigger(self):
        """测试副视频触发"""
        danmaku_text = self.danmaku_input.text().strip()
        if not danmaku_text:
            QMessageBox.warning(self, "警告", "请输入测试弹幕")
            return
        
        self.log(f"🎬 测试弹幕: {danmaku_text}")
        
        # 简单的关键词匹配
        try:
            config_file = Path("data/sub_videos.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                triggered = False
                for keyword, data in config.items():
                    if keyword and keyword.lower() in danmaku_text.lower():
                        video_source = data.get('video_source', '')
                        self.log(f"✅ 触发副视频: {keyword} → {video_source}")
                        triggered = True
                        break
                
                if not triggered:
                    self.log("⚪ 无副视频触发")
            else:
                self.log("❌ 副视频配置文件不存在")
        except Exception as e:
            self.log(f"❌ 测试失败: {e}")
    
    def quick_test(self, text):
        """快速测试"""
        self.danmaku_input.setText(text)
        self.test_trigger()
    
    def switch_source(self):
        """切换视频源"""
        selected_source = self.source_combo.currentText()
        if not selected_source:
            QMessageBox.warning(self, "警告", "请选择要切换的视频源")
            return
        
        self.log(f"🔄 切换到视频源: {selected_source}")
        
        if not self.obs_controller:
            self.log("❌ OBS控制器未初始化")
            return
        
        try:
            # 这里可以添加实际的视频源切换逻辑
            # 由于不同的OBS控制器实现可能不同，这里只是示例
            if hasattr(self.obs_controller, 'set_current_scene'):
                result = self.obs_controller.set_current_scene(selected_source)
                if result:
                    self.log(f"✅ 成功切换到视频源: {selected_source}")
                else:
                    self.log(f"❌ 切换视频源失败: {selected_source}")
            else:
                self.log("⚠️ OBS控制器没有切换场景方法，但视频源列表已获取")
        except Exception as e:
            self.log(f"❌ 切换视频源异常: {e}")
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
    
    def log(self, message):
        """记录日志"""
        self.log_text.append(message)
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
        print(message)  # 同时输出到控制台

def main():
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("最小化副视频测试程序")
    app.setApplicationVersion("1.0")
    
    window = MinimalSubVideoTest()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
