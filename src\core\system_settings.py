"""
AI Broadcaster v2 - 系统设置管理器
管理系统的各种配置参数
"""

import json
from pathlib import Path
from typing import Dict, Any, List, Optional
import sounddevice as sd

from ..services.logging_service import create_logger
from ..services.error_handler import handle_exceptions


class SystemSettings:
    """系统设置管理器"""

    def __init__(self, config_file: str = "data/system_settings.json"):
        self.logger = create_logger("system_settings")
        self.config_file = Path(config_file)

        # 默认设置
        self.default_settings = {
            # 游戏信息
            'game_name': '我的游戏',
            'game_type': '休闲游戏',

            # 播放设置
            'prepare_voice_count': 5,  # 预备语音数量
            'pause_time_min': 1.0,     # 播放间隔最小时间（秒）
            'pause_time_max': 3.0,     # 播放间隔最大时间（秒）
            'loop_mode': True,         # 循环播放模式

            # 音频设置
            'audio_device': '',        # 音频设备
            'volume': 80,              # 播放音量 (0-100)

            # OBS设置
            'obs_host': 'localhost',
            'obs_port': 4455,
            'obs_password': '',
            'video_source_a': '',
            'video_source_b': '',
            'min_speed': 0.5,
            'max_speed': 2.0,

            # 弹幕设置
            'danmaku_server': 'ws://127.0.0.1:9999',
            'auto_ai_response': True,  # 自动AI回复

            # 报时设置
            'time_report_enabled': False,
            'time_report_interval': 5,  # 分钟
            'time_report_scripts': [
                '现在时间是{time}，感谢大家的观看！',
                '【时间过得真快|又到了整点时间】，现在是{hour}点{minute}分'
            ],

            # 语音设置
            'voice_speed': 1.0,        # 语音速度
            'voice_speaker_id': 0,     # 默认主播ID

            # 界面设置
            'window_width': 1200,
            'window_height': 800,
            'theme': 'default'
        }

        # 当前设置
        self.settings = self.default_settings.copy()

        # 确保配置目录存在
        self.config_file.parent.mkdir(parents=True, exist_ok=True)

        # 加载设置
        self.load_settings()

        self.logger.info("系统设置管理器初始化完成")

    @handle_exceptions("SystemSettings")
    def load_settings(self):
        """加载设置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)

                # 合并设置（保留默认值，更新已有值）
                self.settings.update(loaded_settings)

                self.logger.info("系统设置加载完成")
            else:
                self.logger.info("配置文件不存在，使用默认设置")
                self.save_settings()

        except Exception as e:
            self.logger.error(f"加载系统设置失败: {e}")
            self.settings = self.default_settings.copy()

    @handle_exceptions("SystemSettings")
    def save_settings(self):
        """保存设置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)

            self.logger.info("系统设置保存完成")

        except Exception as e:
            self.logger.error(f"保存系统设置失败: {e}")

    def get(self, key: str, default: Any = None) -> Any:
        """获取设置值"""
        return self.settings.get(key, default)

    def set(self, key: str, value: Any):
        """设置值"""
        self.settings[key] = value
        self.save_settings()

    def update(self, settings_dict: Dict[str, Any]):
        """批量更新设置"""
        self.settings.update(settings_dict)
        self.save_settings()

    def reset_to_default(self):
        """重置为默认设置"""
        self.settings = self.default_settings.copy()
        self.save_settings()
        self.logger.info("设置已重置为默认值")

    # 游戏信息相关
    def set_game_info(self, game_name: str, game_type: str):
        """设置游戏信息"""
        self.settings['game_name'] = game_name
        self.settings['game_type'] = game_type
        self.save_settings()

    def get_game_info(self) -> Dict[str, str]:
        """获取游戏信息"""
        return {
            'game_name': self.get('game_name'),
            'game_type': self.get('game_type')
        }

    # 播放设置相关
    def set_playback_settings(self, prepare_count: int, pause_min: float,
                             pause_max: float, loop_mode: bool):
        """设置播放参数"""
        self.settings.update({
            'prepare_voice_count': max(1, prepare_count),
            'pause_time_min': max(0.1, pause_min),
            'pause_time_max': max(pause_min, pause_max),
            'loop_mode': loop_mode
        })
        self.save_settings()

    def get_playback_settings(self) -> Dict[str, Any]:
        """获取播放设置"""
        return {
            'prepare_voice_count': self.get('prepare_voice_count'),
            'pause_time_min': self.get('pause_time_min'),
            'pause_time_max': self.get('pause_time_max'),
            'loop_mode': self.get('loop_mode')
        }

    # 音频设置相关
    def get_audio_devices(self) -> List[Dict[str, Any]]:
        """获取可用音频设备"""
        try:
            devices = sd.query_devices()
            audio_devices = []

            for i, device in enumerate(devices):
                # 检查设备是否有输出通道
                max_outputs = device.get('max_outputs', device.get('maxOutputChannels', 0))
                if max_outputs > 0:  # 只返回输出设备
                    audio_devices.append({
                        'index': i,
                        'name': device['name'],
                        'channels': max_outputs,
                        'sample_rate': device.get('default_samplerate', device.get('defaultSampleRate', 44100))
                    })

            return audio_devices

        except Exception as e:
            self.logger.error(f"获取音频设备失败: {e}")
            return []

    def set_audio_settings(self, device_name: str, volume: int):
        """设置音频参数"""
        self.settings.update({
            'audio_device': device_name,
            'volume': max(0, min(100, volume))
        })
        self.save_settings()

    def get_audio_settings(self) -> Dict[str, Any]:
        """获取音频设置"""
        return {
            'audio_device': self.get('audio_device'),
            'volume': self.get('volume')
        }

    # OBS设置相关
    def set_obs_settings(self, host: str, port: int, password: str,
                        source_a: str, source_b: str, min_speed: float, max_speed: float):
        """设置OBS参数"""
        self.settings.update({
            'obs_host': host,
            'obs_port': max(1, port),
            'obs_password': password,
            'video_source_a': source_a,
            'video_source_b': source_b,
            'min_speed': max(0.1, min_speed),
            'max_speed': max(min_speed, max_speed)
        })
        self.save_settings()

    def get_obs_settings(self) -> Dict[str, Any]:
        """获取OBS设置"""
        return {
            'obs_host': self.get('obs_host'),
            'obs_port': self.get('obs_port'),
            'obs_password': self.get('obs_password'),
            'video_source_a': self.get('video_source_a'),
            'video_source_b': self.get('video_source_b'),
            'min_speed': self.get('min_speed'),
            'max_speed': self.get('max_speed')
        }

    # 弹幕设置相关
    def set_danmaku_settings(self, server_url: str, auto_response: bool):
        """设置弹幕参数"""
        self.settings.update({
            'danmaku_server': server_url,
            'auto_ai_response': auto_response
        })
        self.save_settings()

    def get_danmaku_settings(self) -> Dict[str, Any]:
        """获取弹幕设置"""
        return {
            'danmaku_server': self.get('danmaku_server'),
            'auto_ai_response': self.get('auto_ai_response')
        }

    # 报时设置相关
    def set_time_report_settings(self, enabled: bool, interval: int, scripts: List[str]):
        """设置报时参数"""
        self.settings.update({
            'time_report_enabled': enabled,
            'time_report_interval': max(1, interval),
            'time_report_scripts': scripts
        })
        self.save_settings()

    def get_time_report_settings(self) -> Dict[str, Any]:
        """获取报时设置"""
        return {
            'time_report_enabled': self.get('time_report_enabled'),
            'time_report_interval': self.get('time_report_interval'),
            'time_report_scripts': self.get('time_report_scripts')
        }

    # 语音设置相关
    def set_voice_settings(self, speed: float, speaker_id: int):
        """设置语音参数"""
        self.settings.update({
            'voice_speed': max(0.5, min(2.0, speed)),
            'voice_speaker_id': speaker_id
        })
        self.save_settings()

    def get_voice_settings(self) -> Dict[str, Any]:
        """获取语音设置"""
        return {
            'voice_speed': self.get('voice_speed'),
            'voice_speaker_id': self.get('voice_speaker_id')
        }

    # 界面设置相关
    def set_ui_settings(self, width: int, height: int, theme: str):
        """设置界面参数"""
        self.settings.update({
            'window_width': max(800, width),
            'window_height': max(600, height),
            'theme': theme
        })
        self.save_settings()

    def get_ui_settings(self) -> Dict[str, Any]:
        """获取界面设置"""
        return {
            'window_width': self.get('window_width'),
            'window_height': self.get('window_height'),
            'theme': self.get('theme')
        }

    def get_all_settings(self) -> Dict[str, Any]:
        """获取所有设置"""
        return self.settings.copy()

    def export_settings(self, file_path: str) -> bool:
        """导出设置到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)

            self.logger.info(f"设置已导出到: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"导出设置失败: {e}")
            return False

    def import_settings(self, file_path: str) -> bool:
        """从文件导入设置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)

            # 验证并更新设置
            self.settings.update(imported_settings)
            self.save_settings()

            self.logger.info(f"设置已从文件导入: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"导入设置失败: {e}")
            return False


def main():
    """测试函数"""
    settings = SystemSettings("test_settings.json")

    # 测试游戏信息设置
    settings.set_game_info("测试游戏", "动作游戏")
    game_info = settings.get_game_info()
    print(f"游戏信息: {game_info}")

    # 测试播放设置
    settings.set_playback_settings(3, 1.5, 4.0, True)
    playback_settings = settings.get_playback_settings()
    print(f"播放设置: {playback_settings}")

    # 测试音频设备
    devices = settings.get_audio_devices()
    print(f"音频设备: {len(devices)} 个")

    # 测试获取所有设置
    all_settings = settings.get_all_settings()
    print(f"所有设置: {len(all_settings)} 项")


if __name__ == "__main__":
    main()
