#!/usr/bin/env python3
"""
AI直播系统 v2 - 用户系统演示
演示用户注册、登录、充值功能（命令行版本）
"""

import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.logging_service import setup_logging, create_logger
from src.data.database_manager import DatabaseManager
from src.core.user import UserManager, AuthManager, PaymentManager


class UserSystemDemo:
    """用户系统演示"""
    
    def __init__(self):
        self.logger = create_logger("user_demo")
        self.db = DatabaseManager("data/user_demo.db")
        self.user_manager = UserManager(self.db)
        self.auth_manager = AuthManager(self.db)
        self.payment_manager = PaymentManager(self.db)
        
        self.current_user = None
        self.current_tokens = None
        
    def show_menu(self):
        """显示主菜单"""
        print("\n" + "="*50)
        print("🎬 AI直播系统 v2 - 用户系统演示")
        print("="*50)
        
        if self.current_user:
            print(f"当前用户: {self.current_user['username']} ({self.get_user_type_name(self.current_user['user_type'])})")
            print("1. 查看用户信息")
            print("2. 修改密码")
            print("3. 充值中心")
            print("4. 订单查询")
            print("5. 退出登录")
            print("0. 退出程序")
        else:
            print("1. 用户登录")
            print("2. 用户注册")
            print("3. 系统统计")
            print("0. 退出程序")
            
        print("="*50)
        
    def register_user(self):
        """用户注册"""
        print("\n📝 用户注册")
        print("-" * 30)
        
        username = input("请输入用户名: ").strip()
        if not username:
            print("❌ 用户名不能为空")
            return
            
        password = input("请输入密码: ").strip()
        if len(password) < 6:
            print("❌ 密码长度至少6个字符")
            return
            
        email = input("请输入邮箱（可选）: ").strip()
        phone = input("请输入手机号（可选）: ").strip()
        
        print("\n正在注册...")
        result = self.user_manager.register_user(username, password, email, phone)
        
        if result['success']:
            print(f"✅ 注册成功！用户ID: {result['user_id']}")
            print(f"✅ 试用期: {result['user_info']['trial_days']} 天")
        else:
            print(f"❌ 注册失败: {result['message']}")
            
    def login_user(self):
        """用户登录"""
        print("\n🔐 用户登录")
        print("-" * 30)
        
        username = input("请输入用户名或邮箱: ").strip()
        if not username:
            print("❌ 用户名不能为空")
            return
            
        password = input("请输入密码: ").strip()
        if not password:
            print("❌ 密码不能为空")
            return
            
        print("\n正在登录...")
        result = self.user_manager.login_user(username, password)
        
        if result['success']:
            self.current_user = result['user_info']
            
            # 生成令牌
            self.current_tokens = self.auth_manager.generate_tokens(self.current_user)
            
            print(f"✅ 登录成功！欢迎 {self.current_user['username']}")
            print(f"✅ 用户类型: {self.get_user_type_name(self.current_user['user_type'])}")
            
            if self.current_user.get('expire_at'):
                print(f"✅ 到期时间: {self.current_user['expire_at']}")
        else:
            print(f"❌ 登录失败: {result['message']}")
            
    def show_user_info(self):
        """显示用户信息"""
        if not self.current_user:
            return
            
        print("\n👤 用户信息")
        print("-" * 30)
        print(f"用户名: {self.current_user['username']}")
        print(f"用户类型: {self.get_user_type_name(self.current_user['user_type'])}")
        print(f"状态: {self.get_user_status_name(self.current_user['status'])}")
        print(f"余额: ¥{self.current_user.get('balance', 0)}")
        print(f"试用天数: {self.current_user.get('trial_days', 0)} 天")
        
        if self.current_user.get('expire_at'):
            print(f"到期时间: {self.current_user['expire_at']}")
            
        if self.current_user.get('email'):
            print(f"邮箱: {self.current_user['email']}")
            
        if self.current_user.get('phone'):
            print(f"手机: {self.current_user['phone']}")
            
        print(f"注册时间: {self.current_user['created_at']}")
        
        if self.current_user.get('last_login'):
            print(f"最后登录: {self.current_user['last_login']}")
            
    def change_password(self):
        """修改密码"""
        if not self.current_user:
            return
            
        print("\n🔑 修改密码")
        print("-" * 30)
        
        old_password = input("请输入当前密码: ").strip()
        if not old_password:
            print("❌ 当前密码不能为空")
            return
            
        new_password = input("请输入新密码: ").strip()
        if len(new_password) < 6:
            print("❌ 新密码长度至少6个字符")
            return
            
        confirm_password = input("请确认新密码: ").strip()
        if new_password != confirm_password:
            print("❌ 两次输入的密码不一致")
            return
            
        print("\n正在修改密码...")
        result = self.user_manager.change_password(
            self.current_user['id'], old_password, new_password
        )
        
        if result['success']:
            print("✅ 密码修改成功！")
        else:
            print(f"❌ 密码修改失败: {result['message']}")
            
    def show_recharge_center(self):
        """充值中心"""
        if not self.current_user:
            return
            
        print("\n💰 充值中心")
        print("-" * 30)
        
        packages = self.payment_manager.get_recharge_packages()
        
        print("可选充值套餐:")
        for i, (package_id, package_info) in enumerate(packages.items(), 1):
            print(f"{i}. {package_info['name']}")
            print(f"   价格: ¥{package_info['amount']}")
            print(f"   有效期: {package_info['days']} 天")
            print(f"   描述: {package_info['description']}")
            print()
            
        try:
            choice = input("请选择套餐（输入序号）: ").strip()
            if not choice.isdigit():
                print("❌ 请输入有效的序号")
                return
                
            choice = int(choice)
            package_ids = list(packages.keys())
            
            if choice < 1 or choice > len(package_ids):
                print("❌ 序号超出范围")
                return
                
            selected_package = package_ids[choice - 1]
            package_info = packages[selected_package]
            
            print(f"\n已选择: {package_info['name']} (¥{package_info['amount']})")
            
            # 选择支付方式
            print("\n支付方式:")
            print("1. 支付宝")
            print("2. 微信支付")
            print("3. 银行卡")
            
            payment_choice = input("请选择支付方式: ").strip()
            payment_methods = {'1': 'alipay', '2': 'wechat', '3': 'bank'}
            
            if payment_choice not in payment_methods:
                print("❌ 请选择有效的支付方式")
                return
                
            payment_method = payment_methods[payment_choice]
            
            # 创建订单
            print("\n正在创建订单...")
            order_result = self.payment_manager.create_recharge_order(
                self.current_user['id'], selected_package, payment_method
            )
            
            if order_result['success']:
                print(f"✅ 订单创建成功！")
                print(f"订单号: {order_result['order_no']}")
                print(f"金额: ¥{order_result['amount']}")
                
                # 模拟支付
                confirm = input("\n是否模拟支付？(y/n): ").strip().lower()
                if confirm == 'y':
                    print("正在处理支付...")
                    payment_result = self.payment_manager.process_payment(order_result['order_no'])
                    
                    if payment_result['success']:
                        print("✅ 支付成功！充值已到账")
                        print(f"新用户类型: {self.get_user_type_name(payment_result['recharge_info']['new_user_type'])}")
                        print(f"到期时间: {payment_result['recharge_info']['expire_at']}")
                        
                        # 更新当前用户信息
                        self.current_user = self.user_manager.get_user_by_id(self.current_user['id'])
                    else:
                        print(f"❌ 支付失败: {payment_result['message']}")
            else:
                print(f"❌ 订单创建失败: {order_result['message']}")
                
        except ValueError:
            print("❌ 请输入有效的数字")
        except Exception as e:
            print(f"❌ 充值过程中发生错误: {e}")
            
    def show_orders(self):
        """查看订单"""
        if not self.current_user:
            return
            
        print("\n📋 我的订单")
        print("-" * 30)
        
        orders = self.payment_manager.get_user_orders(self.current_user['id'])
        
        if not orders:
            print("暂无订单记录")
            return
            
        for order in orders:
            print(f"订单号: {order['order_no']}")
            print(f"套餐: {order['package_info']['name']}")
            print(f"金额: ¥{order['amount']}")
            print(f"状态: {self.get_order_status_name(order['status'])}")
            print(f"创建时间: {order['created_at']}")
            if order.get('paid_at'):
                print(f"支付时间: {order['paid_at']}")
            print("-" * 20)
            
    def show_system_stats(self):
        """显示系统统计"""
        print("\n📊 系统统计")
        print("-" * 30)
        
        user_stats = self.user_manager.get_user_stats()
        payment_stats = self.payment_manager.get_payment_stats()
        
        print(f"总用户数: {user_stats['total_users']}")
        print(f"活跃用户: {user_stats['active_users']}")
        print(f"VIP用户: {user_stats['vip_users']}")
        print(f"今日注册: {user_stats['today_users']}")
        print()
        print(f"今日收入: ¥{payment_stats['today_income']}")
        print(f"总收入: ¥{payment_stats['total_income']}")
        
    def logout(self):
        """退出登录"""
        if self.current_user:
            print(f"\n👋 再见，{self.current_user['username']}！")
            self.current_user = None
            self.current_tokens = None
            
    def get_user_type_name(self, user_type: int) -> str:
        """获取用户类型名称"""
        type_names = {0: "免费用户", 1: "VIP用户", 2: "高级用户", 9: "管理员"}
        return type_names.get(user_type, "未知")
        
    def get_user_status_name(self, status: int) -> str:
        """获取用户状态名称"""
        status_names = {0: "未激活", 1: "正常", 2: "试用中", -1: "已封禁"}
        return status_names.get(status, "未知")
        
    def get_order_status_name(self, status: int) -> str:
        """获取订单状态名称"""
        status_names = {0: "待支付", 1: "已支付", 2: "支付失败", 3: "已取消", 4: "已退款"}
        return status_names.get(status, "未知")
        
    def run(self):
        """运行演示"""
        print("🎬 AI直播系统 v2 用户系统演示启动")
        
        while True:
            self.show_menu()
            
            try:
                choice = input("\n请选择操作: ").strip()
                
                if choice == '0':
                    print("\n👋 感谢使用AI直播系统！")
                    break
                    
                elif not self.current_user:
                    # 未登录状态
                    if choice == '1':
                        self.login_user()
                    elif choice == '2':
                        self.register_user()
                    elif choice == '3':
                        self.show_system_stats()
                    else:
                        print("❌ 无效的选择")
                        
                else:
                    # 已登录状态
                    if choice == '1':
                        self.show_user_info()
                    elif choice == '2':
                        self.change_password()
                    elif choice == '3':
                        self.show_recharge_center()
                    elif choice == '4':
                        self.show_orders()
                    elif choice == '5':
                        self.logout()
                    else:
                        print("❌ 无效的选择")
                        
            except KeyboardInterrupt:
                print("\n\n👋 程序已退出")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {e}")
                
        # 清理
        if hasattr(self, 'db'):
            self.db.close()


def main():
    """主函数"""
    # 初始化日志系统
    setup_logging()
    
    # 创建并运行演示
    demo = UserSystemDemo()
    demo.run()


if __name__ == "__main__":
    main()
