"""
测试双主视频管理器功能
"""

import time
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.playback.dual_video_manager import DualVideoManager, VideoSourceState
from src.services.obs_controller import OBSController
from src.services.logging_service import create_logger


def test_dual_video_manager():
    """测试双主视频管理器"""
    logger = create_logger("test_dual_video")
    
    print("🎥 测试双主视频管理器...")
    
    try:
        # 创建OBS控制器（不实际连接）
        obs_controller = OBSController("localhost", 4455, "")
        
        # 创建双主视频管理器
        dual_manager = DualVideoManager(obs_controller)
        
        print("✅ 双主视频管理器创建成功")
        
        # 测试设置视频源
        source_a = "主视频源A"
        source_b = "主视频源B"
        dual_manager.set_video_sources(source_a, source_b)
        print(f"✅ 设置视频源: A={source_a}, B={source_b}")
        
        # 测试获取管理器状态
        status = dual_manager.get_manager_status()
        print(f"✅ 管理器状态: {status}")
        
        # 测试设置切换阈值
        dual_manager.set_switch_threshold(3.0)
        print("✅ 设置切换阈值: 3秒")
        
        # 测试设置变速范围
        dual_manager.set_speed_range(0.8, 1.5)
        print("✅ 设置变速范围: 0.8 - 1.5")
        
        # 测试手动切换（不会实际执行，因为OBS未连接）
        result = dual_manager.manual_switch()
        print(f"✅ 手动切换测试: {result}")
        
        # 测试强制切换到指定源
        result = dual_manager.force_switch_to_source(source_b)
        print(f"✅ 强制切换测试: {result}")
        
        # 测试获取当前视频状态
        video_status = dual_manager.get_current_video_status()
        print(f"✅ 当前视频状态: {video_status}")
        
        print("🎉 双主视频管理器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 双主视频管理器测试失败: {e}")
        return False


def test_video_source_states():
    """测试视频源状态枚举"""
    print("\n🔄 测试视频源状态...")
    
    try:
        # 测试所有状态
        states = [
            VideoSourceState.HIDDEN,
            VideoSourceState.VISIBLE,
            VideoSourceState.PLAYING,
            VideoSourceState.PAUSED,
            VideoSourceState.ENDED,
            VideoSourceState.PREPARING
        ]
        
        for state in states:
            print(f"  - {state.name}: {state.value}")
        
        print("✅ 视频源状态测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 视频源状态测试失败: {e}")
        return False


def test_obs_integration():
    """测试OBS集成（模拟）"""
    print("\n🔗 测试OBS集成...")
    
    try:
        # 创建模拟的OBS控制器
        class MockOBSController:
            def __init__(self):
                self.is_connected = False
                self.requests = []
            
            def send_request_sync(self, request_type, params=None):
                self.requests.append({
                    'type': request_type,
                    'params': params or {}
                })
                
                # 模拟返回结果
                if request_type == "GetCurrentProgramScene":
                    return {"currentProgramSceneName": "测试场景"}
                elif request_type == "GetSceneItemList":
                    return {
                        "sceneItems": [
                            {
                                "sourceName": "主视频源A",
                                "sceneItemId": 1,
                                "sceneItemEnabled": True,
                                "sceneItemIndex": 1
                            },
                            {
                                "sourceName": "主视频源B",
                                "sceneItemId": 2,
                                "sceneItemEnabled": False,
                                "sceneItemIndex": 2
                            }
                        ]
                    }
                elif request_type in ["SetSceneItemEnabled", "TriggerMediaInputAction", "SetInputSettings"]:
                    return {"success": True}
                
                return None
            
            def get_media_status(self, source_name):
                return {
                    'media_state': 'OBS_MEDIA_STATE_PLAYING',
                    'media_duration': 60000,  # 60秒
                    'media_cursor': 30000,    # 30秒
                    'progress_percent': 50.0
                }
        
        # 使用模拟控制器测试
        mock_obs = MockOBSController()
        mock_obs.is_connected = True
        
        dual_manager = DualVideoManager(mock_obs)
        dual_manager.set_video_sources("主视频源A", "主视频源B")
        
        # 测试源可见性检查
        visible_a = dual_manager._is_source_visible("主视频源A")
        visible_b = dual_manager._is_source_visible("主视频源B")
        print(f"  - 主视频源A可见: {visible_a}")
        print(f"  - 主视频源B可见: {visible_b}")
        
        # 测试获取最上面的源
        top_source = dual_manager._get_top_visible_source()
        print(f"  - 最上面的源: {top_source}")
        
        # 测试显示/隐藏源
        dual_manager._show_source("主视频源B")
        dual_manager._hide_source("主视频源A")
        
        # 测试播放源
        dual_manager._play_source("主视频源B")
        
        # 检查发送的请求
        print(f"  - 发送的OBS请求数量: {len(mock_obs.requests)}")
        for i, req in enumerate(mock_obs.requests):
            print(f"    {i+1}. {req['type']}: {req['params']}")
        
        print("✅ OBS集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ OBS集成测试失败: {e}")
        return False


def test_monitoring_simulation():
    """测试监控模拟"""
    print("\n⏱️ 测试监控模拟...")
    
    try:
        # 创建模拟的OBS控制器
        class MockOBSController:
            def __init__(self):
                self.is_connected = True
                self.media_cursor = 0
                self.media_duration = 10000  # 10秒
            
            def send_request_sync(self, request_type, params=None):
                if request_type == "GetCurrentProgramScene":
                    return {"currentProgramSceneName": "测试场景"}
                elif request_type == "GetSceneItemList":
                    return {"sceneItems": []}
                return None
            
            def get_media_status(self, source_name):
                # 模拟播放进度
                self.media_cursor += 1000  # 每次增加1秒
                
                if self.media_cursor >= self.media_duration:
                    state = 'OBS_MEDIA_STATE_ENDED'
                else:
                    state = 'OBS_MEDIA_STATE_PLAYING'
                
                return {
                    'media_state': state,
                    'media_duration': self.media_duration,
                    'media_cursor': self.media_cursor,
                    'progress_percent': (self.media_cursor / self.media_duration) * 100
                }
        
        mock_obs = MockOBSController()
        dual_manager = DualVideoManager(mock_obs)
        dual_manager.set_video_sources("主视频源A", "主视频源B")
        dual_manager.set_switch_threshold(2.0)  # 2秒阈值
        
        # 设置回调函数
        switch_count = 0
        def on_source_switched(current, previous):
            nonlocal switch_count
            switch_count += 1
            print(f"    切换事件 {switch_count}: {previous} -> {current}")
        
        dual_manager.on_source_switched = on_source_switched
        
        # 模拟监控循环（简化版）
        print("  - 开始监控模拟...")
        for i in range(12):  # 模拟12秒
            status = dual_manager.get_current_video_status()
            if status:
                print(f"    第{i+1}秒: 进度={status.get('progress_percent', 0):.1f}%, 状态={status.get('media_state', 'unknown')}")
            
            # 模拟检查状态
            dual_manager._check_active_source_status()
            
            time.sleep(0.1)  # 短暂延迟
        
        print(f"  - 总切换次数: {switch_count}")
        print("✅ 监控模拟测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 监控模拟测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始双主视频管理器测试\n")
    
    tests = [
        test_dual_video_manager,
        test_video_source_states,
        test_obs_integration,
        test_monitoring_simulation
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
