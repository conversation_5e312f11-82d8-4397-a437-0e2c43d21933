#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单OBS副视频测试程序
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    print("🧪 测试导入...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5导入成功")
    except Exception as e:
        print(f"❌ PyQt5导入失败: {e}")
        return False
    
    try:
        from run_gui_qt5 import MainWindow
        print("✅ 主程序导入成功")
    except Exception as e:
        print(f"❌ 主程序导入失败: {e}")
        return False
    
    return True

def test_main_window():
    """测试主窗口创建"""
    print("\n🧪 测试主窗口创建...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from run_gui_qt5 import MainWindow
        
        # 创建应用
        app = QApplication([])
        
        # 创建主窗口
        user_info = {
            'username': 'test_user',
            'user_id': 1,
            'expire_time': '2025-12-31'
        }
        window = MainWindow(user_info)
        print("✅ 主窗口创建成功")
        
        # 测试副视频管理器
        if hasattr(window, 'sub_video_manager'):
            print("✅ 副视频管理器存在")
            
            if hasattr(window.sub_video_manager, 'sub_videos'):
                sub_videos = window.sub_video_manager.sub_videos
                print(f"✅ 副视频数据: {type(sub_videos)}, 数量: {len(sub_videos) if isinstance(sub_videos, dict) else '未知'}")
            else:
                print("❌ 副视频管理器没有sub_videos属性")
        else:
            print("❌ 主窗口没有副视频管理器")
        
        # 测试OBS控制器
        obs_controllers = []
        if hasattr(window, 'obs_controller'):
            obs_controllers.append(('主OBS控制器', window.obs_controller))
        
        if hasattr(window, 'playback_controller') and window.playback_controller:
            obs_controllers.append(('播放控制器OBS', window.playback_controller.obs_controller))
        
        print(f"✅ 发现 {len(obs_controllers)} 个OBS控制器")
        
        for name, obs_controller in obs_controllers:
            if obs_controller:
                connected = getattr(obs_controller, 'connected', False)
                print(f"  • {name}: 连接状态 = {connected}")
                
                if hasattr(obs_controller, 'get_source_list'):
                    print(f"  • {name}: 有get_source_list方法")
                else:
                    print(f"  • {name}: 没有get_source_list方法")
        
        # 测试副视频方法
        methods_to_check = [
            'check_sub_video_trigger',
            'add_danmaku_to_playlist',
            'handle_sub_video_playback',
            'switch_to_sub_video_with_obs',
            'switch_back_to_main_video_with_obs'
        ]
        
        print(f"\n🔍 检查副视频方法:")
        for method_name in methods_to_check:
            if hasattr(window, method_name):
                print(f"  ✅ {method_name}")
            else:
                print(f"  ❌ {method_name}")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 主窗口创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_obs_connection():
    """测试OBS连接"""
    print("\n🧪 测试OBS连接...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from run_gui_qt5 import MainWindow
        
        # 创建应用
        app = QApplication([])
        
        # 创建主窗口
        user_info = {
            'username': 'test_user',
            'user_id': 1,
            'expire_time': '2025-12-31'
        }
        window = MainWindow(user_info)
        
        # 获取OBS控制器
        obs_controller = None
        if hasattr(window, 'playback_controller') and window.playback_controller:
            obs_controller = window.playback_controller.obs_controller
        elif hasattr(window, 'obs_controller'):
            obs_controller = window.obs_controller
        
        if obs_controller:
            print("✅ 获取到OBS控制器")
            
            # 设置连接参数
            obs_controller.host = "127.0.0.1"
            obs_controller.port = 4455
            
            print("🔗 尝试连接OBS...")
            
            if hasattr(obs_controller, 'connect'):
                try:
                    result = obs_controller.connect()
                    if result:
                        print("✅ OBS连接成功")
                        
                        # 获取视频源
                        if hasattr(obs_controller, 'get_source_list'):
                            sources = obs_controller.get_source_list()
                            if sources:
                                print(f"✅ 获取到 {len(sources)} 个视频源:")
                                for i, source in enumerate(sources[:5], 1):
                                    print(f"  {i}. {source}")
                                if len(sources) > 5:
                                    print(f"  ... 还有 {len(sources) - 5} 个")
                            else:
                                print("⚠️ 没有获取到视频源")
                        else:
                            print("❌ OBS控制器没有get_source_list方法")
                        
                        # 断开连接
                        if hasattr(obs_controller, 'disconnect'):
                            obs_controller.disconnect()
                            print("✅ OBS连接已断开")
                    else:
                        print("❌ OBS连接失败")
                except Exception as e:
                    print(f"❌ OBS连接异常: {e}")
            else:
                print("❌ OBS控制器没有connect方法")
        else:
            print("❌ 无法获取OBS控制器")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ OBS连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sub_video_trigger():
    """测试副视频触发"""
    print("\n🧪 测试副视频触发...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from run_gui_qt5 import MainWindow
        
        # 创建应用
        app = QApplication([])
        
        # 创建主窗口
        user_info = {
            'username': 'test_user',
            'user_id': 1,
            'expire_time': '2025-12-31'
        }
        window = MainWindow(user_info)
        
        # 测试弹幕
        test_messages = [
            "感谢老板的火箭，太给力了！",
            "谢谢大家的礼物支持",
            "主播666，继续加油！",
            "普通弹幕，没有关键词"
        ]
        
        print("🎬 测试副视频触发:")
        
        if hasattr(window, 'check_sub_video_trigger'):
            for msg in test_messages:
                try:
                    result = window.check_sub_video_trigger(msg)
                    if result:
                        print(f"  ✅ {msg} → {result}")
                    else:
                        print(f"  ⚪ {msg} → 无触发")
                except Exception as e:
                    print(f"  ❌ {msg} → 错误: {e}")
        else:
            print("❌ 主程序没有check_sub_video_trigger方法")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 副视频触发测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎬 简单OBS副视频测试程序")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        return
    
    # 测试主窗口
    if not test_main_window():
        return
    
    # 测试OBS连接
    test_obs_connection()
    
    # 测试副视频触发
    test_sub_video_trigger()
    
    print("\n🏁 测试完成")

if __name__ == "__main__":
    main()
