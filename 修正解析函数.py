#!/usr/bin/env python3
"""
修正后的解析函数
"""

import json
import re

def parse_time_segment_json_format_fixed(script_name, content):
    """修正后的时间段JSON格式解析函数"""
    try:
        print(f"[SEARCH] 开始解析时间段JSON格式，话术: {script_name}")
        print(f"[INFO] JSON内容长度: {len(content)} 字符")
        print(f"[DEBUG] JSON内容预览: {content[:200]}...")
        
        # 尝试解析JSON
        time_segments_data = json.loads(content)
        
        if not isinstance(time_segments_data, dict):
            print(f"[WARNING] JSON数据不是字典格式，类型: {type(time_segments_data)}")
            return False, {}
        
        print(f"[OK] JSON解析成功，包含 {len(time_segments_data)} 个顶级键")
        
        # 检查是否为新格式：所有值都是字符串
        is_new_format = all(isinstance(v, str) for v in time_segments_data.values())
        print(f"[INFO] 检测格式类型: {'新格式' if is_new_format else '旧格式'}")
        
        if not is_new_format:
            print(f"[WARNING] 不是新的时间段格式，跳过解析")
            return False, {}
        
        # 初始化时间段数据结构
        script_time_segments = {script_name: {}}
        
        # 解析每个时间段
        parsed_count = 0
        for time_key, time_data in time_segments_data.items():
            print(f"[SEARCH] 处理时间段: '{time_key}', 数据类型: {type(time_data)}")
            
            if isinstance(time_data, str):
                segment_content = time_data
                
                # 从时间段名称中提取时间信息
                pattern = r'(\d+)秒\s*-\s*(\d+)秒'
                match = re.search(pattern, time_key)
                
                if match:
                    start_time = int(match.group(1))
                    end_time = int(match.group(2))
                else:
                    # 如果无法解析时间，使用默认值
                    start_time = parsed_count * 10
                    end_time = (parsed_count + 1) * 10
                
                print(f"   [DATA] 新格式 - 提取时间信息: start={start_time}, end={end_time}")
                print(f"   [EDIT] 内容长度: {len(segment_content)} 字符")
                
                # 处理转义字符：将 \\n 转换为实际的换行符
                processed_content = segment_content.replace('\\n', '\n')
                
                # 统计话术行数
                content_lines = [line.strip() for line in processed_content.split('\n') if line.strip()]
                script_lines = [line for line in content_lines if line and '***' in line]
                print(f"   [INFO] 话术行数: {len(script_lines)}/{len(content_lines)}")
                
                print(f"   [EDIT] 处理后内容预览: {processed_content[:100]}...")
                
                # 存储到时间段数据结构
                script_time_segments[script_name][time_key] = {
                    'start': start_time,
                    'end': end_time,
                    'content': processed_content  # 使用处理后的内容
                }
                
                print(f"[OK] 解析时间段: '{time_key}' ({start_time}秒-{end_time}秒)")
                print(f"   [INFO] 总行数: {len(content_lines)}, 话术行数: {len(script_lines)}")
                
                parsed_count += 1
            else:
                print(f"[WARNING] 时间段 '{time_key}' 的数据格式不支持: {type(time_data)}")
                continue
        
        print(f"[SUCCESS] 成功解析 {parsed_count}/{len(time_segments_data)} 个时间段数据")
        print(f"[DATA] 最终数据结构: {script_name} -> {list(script_time_segments[script_name].keys())}")
        
        return parsed_count > 0, script_time_segments
        
    except json.JSONDecodeError as e:
        print(f"[WARNING] JSON解析失败: {e}")
        print(f"[INFO] 失败的内容: {content[:500]}...")
        return False, {}
    except Exception as e:
        print(f"[ERROR] 解析时间段JSON格式异常: {e}")
        import traceback
        print(f"[INFO] 详细错误: {traceback.format_exc()}")
        return False, {}

def generate_display_text_fixed(script_name, script_time_segments):
    """生成修正后的显示文本"""
    if script_name in script_time_segments and script_time_segments[script_name]:
        time_segments_count = len(script_time_segments[script_name])
        display_text = f"# 时间段话术：{script_name}\n"
        display_text += f"# 共有 {time_segments_count} 个时间段\n\n"
        display_text += "# 时间段列表：\n"
        
        for segment_name, segment_data in script_time_segments[script_name].items():
            start_time = segment_data.get('start', 0)
            end_time = segment_data.get('end', 0)
            content_lines = len([line for line in segment_data.get('content', '').split('\n') if line.strip()])
            display_text += f"# - {segment_name} ({start_time}秒-{end_time}秒) - {content_lines}行话术\n"
        
        display_text += "\n# 请在左侧时间段列表中选择具体时间段进行编辑"
        display_text += "\n# 注意：此话术已解析为时间段格式，不显示原始JSON内容"
        
        return display_text
    else:
        return "# 该话术暂无内容"

def test_fixed_parsing():
    """测试修正后的解析功能"""
    print("=== 测试修正后的解析功能 ===")
    
    # 模拟真实的JSON数据
    test_json = """{
  "0秒 - 10秒": "1***哈喽，大家好，首先感谢【大哥|哥哥|宝子】们的捧场。\\n2***稍作停留也是爱，所能接触的都是这四个大字，随便那个都是这四个大字。\\n3***没有花里胡哨，没有弯弯绕绕。\\n4***有啥就问，新来的家人们，不要藏着掖着。\\n5***云想衣裳花想容，春风拂槛露华浓，仙侠界的白莲花，花容月貌顶呱呱。",
  "10秒 - 20秒": "6***自古套路得人心，但唯有真心得天下，咱们心不要慌，手不要抖，跟着感觉走。\\n7***新来的家人们，相遇就是缘分，不要犹豫不要徘徊。\\n8***你们的到来蓬荜生辉，老妹我深感荣幸\\n9***随便什么数字都是这四个大字，目光所及都是这四个大字。\\n10***俗话说的好，大哥们，人生不主动，快乐少一半",
  "20秒 - 30秒": "11***相信自己的眼睛，相信自己的耳朵\\n12***斩青丝斩难过斩断红尘不为过，建模美 姿势帅，呼风唤雨惹人爱\\n13***不穿摸，不贴片，仙侠界的彭于晏，（千人捧万人追）手游界的刘亦菲\\n14***播播间人很多，主播一个人一张嘴，忙不过来，理解下，大哥们\\n15***万水千山总是情，今天主播最热情"
}"""
    
    print("测试JSON内容:")
    print(test_json[:200] + "...")
    
    # 执行解析
    success, parsed_data = parse_time_segment_json_format_fixed("kaer", test_json)
    
    if success:
        print(f"\n✅ 解析成功")
        
        # 显示解析结果
        script_name = "kaer"
        if script_name in parsed_data:
            print(f"\n📋 解析结果详情:")
            for segment_name, segment_data in parsed_data[script_name].items():
                print(f"\n🕐 时间段: {segment_name}")
                print(f"   ⏰ 时间: {segment_data['start']}秒 - {segment_data['end']}秒")
                
                # 显示话术内容
                content = segment_data['content']
                lines = [line.strip() for line in content.split('\n') if line.strip()]
                print(f"   📝 话术内容 ({len(lines)}行):")
                for i, line in enumerate(lines[:3], 1):  # 只显示前3行
                    print(f"      {i}. {line}")
                if len(lines) > 3:
                    print(f"      ... 还有 {len(lines) - 3} 行")
        
        # 生成显示文本
        display_text = generate_display_text_fixed(script_name, parsed_data)
        print(f"\n📄 生成的显示文本:")
        print(display_text)
        
        return True, parsed_data
    else:
        print(f"\n❌ 解析失败")
        return False, {}

def main():
    """主测试函数"""
    print("开始测试修正后的解析功能...")
    
    try:
        success, parsed_data = test_fixed_parsing()
        
        print("\n=== 测试总结 ===")
        if success:
            print("✅ 修正后的解析功能工作正常")
            print("\n🔧 关键修正:")
            print("1. 正确处理转义字符 \\n -> 换行符")
            print("2. 准确统计话术行数")
            print("3. 生成正确的显示文本")
            print("4. 完整的错误处理")
            
            print("\n📋 可以同步到主程序的代码:")
            print("- parse_time_segment_json_format_fixed 函数")
            print("- generate_display_text_fixed 函数")
            print("- 转义字符处理逻辑")
            
        else:
            print("❌ 解析功能仍有问题")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    main()
