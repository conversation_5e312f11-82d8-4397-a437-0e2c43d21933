# AI Broadcaster v2 Configuration

app:
  name: "AI Broadcaster v2"
  version: "2.0.0"
  debug: true
  log_level: "DEBUG"

# Audio settings
audio:
  input_device: null  # null for default device
  output_device: null  # null for default device
  sample_rate: 44100
  chunk_size: 1024
  channels: 1
  format: "int16"
  
  # Voice settings
  voice:
    language: "zh-CN"  # Chinese
    speed: 1.0
    volume: 0.8

# AI Engine settings
ai:
  # Primary AI provider
  primary_provider: "openai"  # openai, anthropic, local
  
  # OpenAI settings
  openai:
    model: "gpt-4"
    api_key: "${OPENAI_API_KEY}"
    max_tokens: 2000
    temperature: 0.7
    
  # Anthropic settings
  anthropic:
    model: "claude-3-sonnet-20240229"
    api_key: "${ANTHROPIC_API_KEY}"
    max_tokens: 2000
    temperature: 0.7

# Broadcasting settings
broadcast:
  auto_start: false
  interval_seconds: 30
  max_duration_minutes: 60
  
  # Content settings
  content:
    topics: ["technology", "ai", "programming"]
    style: "conversational"
    language: "chinese"

# Network settings
network:
  api_port: 8000
  websocket_port: 8001
  cors_origins: ["http://localhost:3000"]

# Storage settings
storage:
  data_dir: "data"
  audio_dir: "data/audio"
  logs_dir: "logs"
  temp_dir: "data/temp"

# Security settings
security:
  api_key_required: false
  rate_limit_per_minute: 60
