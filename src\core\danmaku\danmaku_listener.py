"""
AI直播系统 v2 - 弹幕监听器
监听直播平台的弹幕消息
"""

import json
import asyncio
import websockets
import threading
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class DanmakuListener:
    """弹幕监听器"""
    
    def __init__(self, websocket_url: str = "ws://127.0.0.1:9999"):
        self.logger = create_logger("danmaku_listener")
        
        # 连接配置
        self.websocket_url = websocket_url
        self.websocket = None
        self.is_connected = False
        self.is_listening = False
        
        # 线程控制
        self.listen_thread = None
        self.stop_event = threading.Event()
        
        # 回调函数
        self.on_danmaku_received: Optional[Callable] = None
        self.on_gift_received: Optional[Callable] = None
        self.on_follow_received: Optional[Callable] = None
        self.on_connected: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None
        self.on_error: Optional[Callable] = None
        
        # 统计信息
        self.stats = {
            'total_danmaku': 0,
            'total_gifts': 0,
            'total_follows': 0,
            'connection_count': 0,
            'last_message_time': None,
            'start_time': None
        }
        
        # 消息缓存
        self.recent_messages = []
        self.max_cache_size = 100
        
    @handle_exceptions("DanmakuListener")
    def start_listening(self) -> bool:
        """开始监听弹幕"""
        try:
            if self.is_listening:
                self.logger.warning("弹幕监听已启动")
                return True
                
            self.stop_event.clear()
            self.is_listening = True
            self.stats['start_time'] = datetime.now()
            
            self.listen_thread = threading.Thread(target=self._listen_loop, daemon=True)
            self.listen_thread.start()
            
            self.logger.info("弹幕监听已启动")
            return True
            
        except Exception as e:
            self.logger.error(f"启动弹幕监听失败: {e}")
            return False
            
    def stop_listening(self):
        """停止监听弹幕"""
        try:
            if not self.is_listening:
                return
                
            self.is_listening = False
            self.stop_event.set()
            
            if self.websocket:
                asyncio.create_task(self.websocket.close())
                
            if self.listen_thread and self.listen_thread.is_alive():
                self.listen_thread.join(timeout=3.0)
                
            self.logger.info("弹幕监听已停止")
            
        except Exception as e:
            self.logger.error(f"停止弹幕监听失败: {e}")
            
    def _listen_loop(self):
        """监听循环"""
        asyncio.run(self._async_listen_loop())
        
    async def _async_listen_loop(self):
        """异步监听循环"""
        while not self.stop_event.is_set():
            try:
                self.logger.info(f"正在连接弹幕服务器: {self.websocket_url}")
                
                async with websockets.connect(
                    self.websocket_url,
                    ping_interval=20,
                    ping_timeout=10
                ) as websocket:
                    self.websocket = websocket
                    self.is_connected = True
                    self.stats['connection_count'] += 1
                    
                    self.logger.info("弹幕服务器连接成功")
                    
                    if self.on_connected:
                        self.on_connected()
                        
                    # 消息处理循环
                    async for message in websocket:
                        if self.stop_event.is_set():
                            break
                            
                        await self._handle_message(message)
                        
            except websockets.exceptions.ConnectionClosed:
                self.logger.warning("弹幕服务器连接已关闭")
                self.is_connected = False
                
                if self.on_disconnected:
                    self.on_disconnected()
                    
            except Exception as e:
                self.logger.error(f"弹幕监听异常: {e}")
                self.is_connected = False
                
                if self.on_error:
                    self.on_error(str(e))
                    
            # 重连延迟
            if not self.stop_event.is_set():
                await asyncio.sleep(5)
                
    async def _handle_message(self, message: str):
        """处理消息"""
        try:
            data = json.loads(message)
            message_type = data.get("type", "unknown")
            message_data = data.get("data", {})
            
            self.stats['last_message_time'] = datetime.now()
            
            # 添加到缓存
            self._add_to_cache({
                'type': message_type,
                'data': message_data,
                'timestamp': datetime.now()
            })
            
            # 处理不同类型的消息
            if message_type == "danmaku":
                await self._handle_danmaku(message_data)
            elif message_type == "gift":
                await self._handle_gift(message_data)
            elif message_type == "follow":
                await self._handle_follow(message_data)
            else:
                self.logger.debug(f"收到未知类型消息: {message_type}")
                
        except json.JSONDecodeError:
            self.logger.error("弹幕消息JSON解析失败")
        except Exception as e:
            self.logger.error(f"处理弹幕消息失败: {e}")
            
    async def _handle_danmaku(self, data: Dict[str, Any]):
        """处理弹幕消息"""
        try:
            self.stats['total_danmaku'] += 1
            
            danmaku_info = {
                'username': data.get('username', '匿名用户'),
                'content': data.get('content', ''),
                'user_id': data.get('user_id', ''),
                'timestamp': datetime.now(),
                'platform': data.get('platform', 'unknown'),
                'user_level': data.get('user_level', 0),
                'is_vip': data.get('is_vip', False),
                'is_admin': data.get('is_admin', False)
            }
            
            self.logger.info(f"收到弹幕: {danmaku_info['username']}: {danmaku_info['content']}")
            
            if self.on_danmaku_received:
                if asyncio.iscoroutinefunction(self.on_danmaku_received):
                    await self.on_danmaku_received(danmaku_info)
                else:
                    self.on_danmaku_received(danmaku_info)
                    
        except Exception as e:
            self.logger.error(f"处理弹幕失败: {e}")
            
    async def _handle_gift(self, data: Dict[str, Any]):
        """处理礼物消息"""
        try:
            self.stats['total_gifts'] += 1
            
            gift_info = {
                'username': data.get('username', '匿名用户'),
                'gift_name': data.get('gift_name', ''),
                'gift_count': data.get('gift_count', 1),
                'gift_price': data.get('gift_price', 0),
                'user_id': data.get('user_id', ''),
                'timestamp': datetime.now(),
                'platform': data.get('platform', 'unknown')
            }
            
            self.logger.info(f"收到礼物: {gift_info['username']} 送出 {gift_info['gift_name']} x{gift_info['gift_count']}")
            
            if self.on_gift_received:
                if asyncio.iscoroutinefunction(self.on_gift_received):
                    await self.on_gift_received(gift_info)
                else:
                    self.on_gift_received(gift_info)
                    
        except Exception as e:
            self.logger.error(f"处理礼物失败: {e}")
            
    async def _handle_follow(self, data: Dict[str, Any]):
        """处理关注消息"""
        try:
            self.stats['total_follows'] += 1
            
            follow_info = {
                'username': data.get('username', '匿名用户'),
                'user_id': data.get('user_id', ''),
                'timestamp': datetime.now(),
                'platform': data.get('platform', 'unknown')
            }
            
            self.logger.info(f"新关注: {follow_info['username']}")
            
            if self.on_follow_received:
                if asyncio.iscoroutinefunction(self.on_follow_received):
                    await self.on_follow_received(follow_info)
                else:
                    self.on_follow_received(follow_info)
                    
        except Exception as e:
            self.logger.error(f"处理关注失败: {e}")
            
    def _add_to_cache(self, message: Dict[str, Any]):
        """添加消息到缓存"""
        self.recent_messages.append(message)
        
        # 保持缓存大小
        if len(self.recent_messages) > self.max_cache_size:
            self.recent_messages.pop(0)
            
    @handle_exceptions("DanmakuListener")
    def get_recent_messages(self, count: int = 10, message_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取最近的消息"""
        messages = self.recent_messages.copy()
        
        # 按类型过滤
        if message_type:
            messages = [msg for msg in messages if msg.get('type') == message_type]
            
        # 返回最近的消息
        return messages[-count:] if count > 0 else messages
        
    @handle_exceptions("DanmakuListener")
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        # 计算运行时间
        if stats['start_time']:
            runtime = datetime.now() - stats['start_time']
            stats['runtime_seconds'] = runtime.total_seconds()
            stats['runtime_formatted'] = str(runtime).split('.')[0]  # 去掉微秒
        else:
            stats['runtime_seconds'] = 0
            stats['runtime_formatted'] = "00:00:00"
            
        # 计算平均速率
        if stats['runtime_seconds'] > 0:
            stats['danmaku_per_minute'] = round(stats['total_danmaku'] / (stats['runtime_seconds'] / 60), 2)
            stats['gifts_per_minute'] = round(stats['total_gifts'] / (stats['runtime_seconds'] / 60), 2)
        else:
            stats['danmaku_per_minute'] = 0
            stats['gifts_per_minute'] = 0
            
        stats['is_connected'] = self.is_connected
        stats['is_listening'] = self.is_listening
        stats['cached_messages'] = len(self.recent_messages)
        
        return stats
        
    @handle_exceptions("DanmakuListener")
    def clear_cache(self):
        """清空消息缓存"""
        self.recent_messages.clear()
        self.logger.info("弹幕消息缓存已清空")
        
    @handle_exceptions("DanmakuListener")
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_danmaku': 0,
            'total_gifts': 0,
            'total_follows': 0,
            'connection_count': 0,
            'last_message_time': None,
            'start_time': datetime.now() if self.is_listening else None
        }
        self.logger.info("弹幕统计信息已重置")
        
    @handle_exceptions("DanmakuListener")
    def set_websocket_url(self, url: str):
        """设置WebSocket URL"""
        if self.is_listening:
            self.logger.warning("请先停止监听再修改WebSocket URL")
            return False
            
        self.websocket_url = url
        self.logger.info(f"WebSocket URL已更新: {url}")
        return True
        
    @handle_exceptions("DanmakuListener")
    def test_connection(self) -> Dict[str, Any]:
        """测试连接"""
        try:
            import asyncio
            
            async def test():
                try:
                    async with websockets.connect(
                        self.websocket_url,
                        ping_timeout=5
                    ) as websocket:
                        # 发送测试消息
                        await websocket.send(json.dumps({"type": "ping"}))
                        
                        # 等待响应
                        try:
                            response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                            return {"success": True, "message": "连接测试成功", "response": response}
                        except asyncio.TimeoutError:
                            return {"success": True, "message": "连接成功但无响应"}
                            
                except Exception as e:
                    return {"success": False, "message": f"连接失败: {str(e)}"}
                    
            # 运行测试
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(test())
            finally:
                loop.close()
                
        except Exception as e:
            return {"success": False, "message": f"测试异常: {str(e)}"}
