#!/usr/bin/env python3
"""
测试换行符处理逻辑
"""

import json

def test_newline_detection():
    """测试换行符检测和处理"""
    print("=== 测试换行符检测和处理 ===")
    
    # 测试用例1：转义换行符 \\n
    content_with_escaped = "1***哈喽，大家好\\n2***感谢大家的捧场\\n3***没有花里胡哨"
    print(f"测试用例1 - 转义换行符:")
    print(f"原始内容: {content_with_escaped}")
    
    if '\\n' in content_with_escaped:
        content_lines = content_with_escaped.split('\\n')
        print(f"检测到转义换行符格式 (\\n)")
    else:
        content_lines = content_with_escaped.split('\n')
        print(f"检测到实际换行符格式 (\\n)")
    
    script_lines = [line for line in content_lines if line.strip() and '***' in line]
    print(f"行数统计: {len(script_lines)}/{len(content_lines)}")
    print(f"分割结果: {content_lines}")
    print()
    
    # 测试用例2：实际换行符 \n
    content_with_actual = "1***哈喽，大家好\n2***感谢大家的捧场\n3***没有花里胡哨"
    print(f"测试用例2 - 实际换行符:")
    print(f"原始内容: {repr(content_with_actual)}")
    
    if '\\n' in content_with_actual:
        content_lines = content_with_actual.split('\\n')
        print(f"检测到转义换行符格式 (\\n)")
    else:
        content_lines = content_with_actual.split('\n')
        print(f"检测到实际换行符格式 (\\n)")
    
    script_lines = [line for line in content_lines if line.strip() and '***' in line]
    print(f"行数统计: {len(script_lines)}/{len(content_lines)}")
    print(f"分割结果: {content_lines}")
    print()

def test_json_parsing():
    """测试JSON解析"""
    print("=== 测试JSON解析 ===")
    
    # 测试用例1：包含转义换行符的JSON
    json_with_escaped = """{
  "0秒 - 10秒": "1***哈喽，大家好\\n2***感谢大家的捧场\\n3***没有花里胡哨",
  "10秒 - 20秒": "4***自古套路得人心\\n5***新来的家人们\\n6***相遇就是缘分"
}"""
    
    print(f"测试用例1 - JSON包含转义换行符:")
    print(f"JSON内容: {json_with_escaped}")
    
    try:
        data = json.loads(json_with_escaped)
        print(f"✅ JSON解析成功")
        
        for key, value in data.items():
            print(f"时间段: {key}")
            print(f"原始值: {repr(value)}")
            
            if '\\n' in value:
                lines = value.split('\\n')
                print(f"使用 \\n 分割: {lines}")
            else:
                lines = value.split('\n')
                print(f"使用 \\n 分割: {lines}")
            
            script_lines = [line for line in lines if line.strip() and '***' in line]
            print(f"话术行数: {len(script_lines)}")
            print()
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
    
    # 测试用例2：包含实际换行符的JSON（这种情况在实际中可能不会出现，但测试一下）
    json_with_actual = """{
  "0秒 - 10秒": "1***哈喽，大家好\n2***感谢大家的捧场\n3***没有花里胡哨"
}"""
    
    print(f"测试用例2 - JSON包含实际换行符:")
    print(f"JSON内容: {repr(json_with_actual)}")
    
    try:
        data = json.loads(json_with_actual)
        print(f"✅ JSON解析成功")
        
        for key, value in data.items():
            print(f"时间段: {key}")
            print(f"原始值: {repr(value)}")
            
            if '\\n' in value:
                lines = value.split('\\n')
                print(f"使用 \\n 分割: {lines}")
            else:
                lines = value.split('\n')
                print(f"使用 \\n 分割: {lines}")
            
            script_lines = [line for line in lines if line.strip() and '***' in line]
            print(f"话术行数: {len(script_lines)}")
            print()
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")

def test_real_case():
    """测试真实情况"""
    print("=== 测试真实情况 ===")
    
    # 模拟从日志中看到的真实JSON内容
    real_json = """{
  "0秒 - 10秒": "1***哈喽，大家好，首先感谢【大哥|哥哥|宝子】们的捧场。\n2***稍作停留也是爱，所能接触的都是这四个大字，随便那个都是这四个大字。\n3***没有花里胡哨，没有弯弯绕绕。\n4***有啥就问，新来的家人们，不要藏着掖着。\n5***云想衣裳花想容，春风拂槛露华浓，仙侠界的白莲花，花容月貌顶呱呱。",
  "10秒 - 20秒": "6***自古套路得人心，但唯有真心得天下，咱们心不要慌，手不要抖，跟着感觉走。\n7***新来的家人们，相遇就是缘分，不要犹豫不要徘徊。\n8***你们的到来蓬荜生辉，老妹我深感荣幸\n9***随便什么数字都是这四个大字，目光所及都是这四个大字。\n10***俗话说的好，大哥们，人生不主动，快乐少一半"
}"""
    
    print(f"真实JSON内容:")
    print(f"长度: {len(real_json)} 字符")
    print(f"开头: {real_json[:100]}...")
    
    try:
        data = json.loads(real_json)
        print(f"✅ JSON解析成功，包含 {len(data)} 个时间段")
        
        # 检查格式
        is_new_format = all(isinstance(v, str) for v in data.values())
        print(f"✅ 格式检测: {'新格式' if is_new_format else '旧格式'}")
        
        if is_new_format:
            print(f"\n📋 解析结果:")
            for segment_name, segment_content in data.items():
                print(f"\n🕐 时间段: {segment_name}")
                print(f"   原始内容: {repr(segment_content[:50])}...")
                
                # 智能检测换行符格式
                if '\\n' in segment_content:
                    content_lines = segment_content.split('\\n')
                    print(f"   检测到转义换行符格式 (\\n)")
                else:
                    content_lines = segment_content.split('\n')
                    print(f"   检测到实际换行符格式 (\\n)")
                
                script_lines = [line for line in content_lines if line.strip() and '***' in line]
                print(f"   话术行数: {len(script_lines)}/{len(content_lines)}")
                
                # 显示前3行
                for i, line in enumerate(content_lines[:3], 1):
                    if line.strip():
                        print(f"   {i}. {line}")
                
                if len(content_lines) > 3:
                    print(f"   ... 还有 {len(content_lines) - 3} 行")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        return False

def test_display_text_generation():
    """测试显示文本生成"""
    print("\n=== 测试显示文本生成 ===")
    
    # 模拟解析后的数据（包含实际换行符）
    script_time_segments = {
        "kaer": {
            "0秒 - 10秒": {
                'start': 0,
                'end': 10,
                'content': "1***哈喽，大家好\n2***感谢大家的捧场\n3***没有花里胡哨\n4***有啥就问\n5***云想衣裳花想容"
            },
            "10秒 - 20秒": {
                'start': 10,
                'end': 20,
                'content': "6***自古套路得人心\n7***新来的家人们\n8***你们的到来蓬荜生辉\n9***随便什么数字\n10***俗话说的好"
            }
        }
    }
    
    script_name = "kaer"
    segments = script_time_segments[script_name]
    
    print(f"生成显示文本:")
    display_text = f"# 时间段话术：{script_name}\n"
    display_text += f"# 共有 {len(segments)} 个时间段\n\n"
    display_text += "# 时间段列表：\n"
    
    for segment_name, segment_data in segments.items():
        start_time = segment_data.get('start', 0)
        end_time = segment_data.get('end', 0)
        
        # 智能检测换行符格式并统计行数
        content = segment_data.get('content', '')
        if '\\n' in content:
            content_lines = len(content.split('\\n'))
            print(f"   {segment_name}: 使用 \\n 分割，{content_lines} 行")
        else:
            content_lines = len([line for line in content.split('\n') if line.strip()])
            print(f"   {segment_name}: 使用 \\n 分割，{content_lines} 行")
        
        display_text += f"# - {segment_name} ({start_time}秒-{end_time}秒) - {content_lines}行话术\n"
    
    display_text += "\n# 请在左侧时间段列表中选择具体时间段进行编辑"
    display_text += "\n# 注意：此话术已解析为时间段格式，不显示原始JSON内容"
    
    print(f"\n最终显示文本:")
    print(display_text)

def main():
    """主测试函数"""
    print("开始测试换行符处理逻辑...")
    
    try:
        # 测试换行符检测
        test_newline_detection()
        
        # 测试JSON解析
        test_json_parsing()
        
        # 测试真实情况
        success = test_real_case()
        
        # 测试显示文本生成
        test_display_text_generation()
        
        print(f"\n=== 测试总结 ===")
        if success:
            print("✅ 换行符处理逻辑正确")
            print("✅ JSON解析正常")
            print("✅ 显示文本生成正确")
            
            print(f"\n🔧 关键发现:")
            print("1. 服务器返回的JSON包含实际换行符 \\n")
            print("2. 需要智能检测换行符格式")
            print("3. 统计行数时要过滤空行")
            print("4. 修复后的逻辑应该能正确处理")
            
        else:
            print("❌ 测试失败")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    main()
