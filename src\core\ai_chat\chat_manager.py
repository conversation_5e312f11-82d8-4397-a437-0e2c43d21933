"""
AI Broadcaster v2 - AI对话管理器
管理AI对话生成和处理
"""

import json
import random
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions
from .prompt_manager import PromptManager
from .conversation_history import ConversationHistory


class ChatManager:
    """AI对话管理器"""
    
    def __init__(self, db_manager):
        self.logger = create_logger("chat_manager")
        self.db_manager = db_manager
        
        # 子管理器
        self.prompt_manager = PromptManager(db_manager)
        self.conversation_history = ConversationHistory(db_manager)
        
        # AI配置
        self.ai_config = {
            'provider': 'mock',  # mock, openai, claude, etc.
            'model': 'gpt-3.5-turbo',
            'api_key': '',
            'base_url': '',
            'temperature': 0.7,
            'max_tokens': 500,
            'timeout': 30
        }
        
        # 对话设置
        self.chat_settings = {
            'enable_context': True,
            'context_length': 10,  # 保留最近10轮对话
            'enable_personality': True,
            'personality_type': 'friendly',  # friendly, professional, humorous
            'response_style': 'conversational',  # conversational, formal, casual
            'language': 'zh-CN'
        }
        
        # 回调函数
        self.on_response_generated: Optional[Callable] = None
        self.on_generation_error: Optional[Callable] = None
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_responses': 0,
            'failed_responses': 0,
            'average_response_time': 0.0,
            'last_request_time': None
        }
        
    @handle_exceptions("ChatManager")
    def generate_response(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        生成AI回复
        
        Args:
            user_input: 用户输入
            context: 上下文信息
            
        Returns:
            AI回复文本，失败返回None
        """
        try:
            start_time = datetime.now()
            self.stats['total_requests'] += 1
            self.stats['last_request_time'] = start_time
            
            # 构建提示词
            prompt = self._build_prompt(user_input, context)
            
            # 生成回复
            response = self._call_ai_api(prompt)
            
            if response:
                # 记录对话历史
                self.conversation_history.add_conversation(
                    user_input=user_input,
                    ai_response=response,
                    context=context
                )
                
                # 更新统计
                end_time = datetime.now()
                response_time = (end_time - start_time).total_seconds()
                self._update_response_stats(response_time, True)
                
                self.logger.info(f"AI回复生成成功: {len(response)} 字符")
                
                # 触发回调
                if self.on_response_generated:
                    self.on_response_generated(user_input, response, context)
                    
                return response
            else:
                self._update_response_stats(0, False)
                self.logger.error("AI回复生成失败")
                
                if self.on_generation_error:
                    self.on_generation_error("AI回复生成失败")
                    
                return None
                
        except Exception as e:
            self._update_response_stats(0, False)
            self.logger.error(f"生成AI回复异常: {e}")
            
            if self.on_generation_error:
                self.on_generation_error(str(e))
                
            return None
            
    def _build_prompt(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> str:
        """构建AI提示词"""
        try:
            # 获取系统提示词
            system_prompt = self.prompt_manager.get_system_prompt(
                personality=self.chat_settings['personality_type'],
                style=self.chat_settings['response_style']
            )
            
            # 构建对话历史
            conversation_context = ""
            if self.chat_settings['enable_context']:
                recent_conversations = self.conversation_history.get_recent_conversations(
                    limit=self.chat_settings['context_length']
                )
                
                for conv in recent_conversations:
                    conversation_context += f"用户: {conv['user_input']}\n"
                    conversation_context += f"AI: {conv['ai_response']}\n\n"
                    
            # 添加额外上下文
            extra_context = ""
            if context:
                if 'current_time' in context:
                    extra_context += f"当前时间: {context['current_time']}\n"
                if 'user_name' in context:
                    extra_context += f"用户名称: {context['user_name']}\n"
                if 'topic' in context:
                    extra_context += f"当前话题: {context['topic']}\n"
                    
            # 组合完整提示词
            full_prompt = f"{system_prompt}\n\n"
            
            if extra_context:
                full_prompt += f"上下文信息:\n{extra_context}\n"
                
            if conversation_context:
                full_prompt += f"对话历史:\n{conversation_context}\n"
                
            full_prompt += f"用户: {user_input}\nAI: "
            
            return full_prompt
            
        except Exception as e:
            self.logger.error(f"构建提示词失败: {e}")
            return f"用户: {user_input}\nAI: "
            
    def _call_ai_api(self, prompt: str) -> Optional[str]:
        """调用AI API"""
        try:
            if self.ai_config['provider'] == 'mock':
                return self._mock_ai_response(prompt)
            elif self.ai_config['provider'] == 'openai':
                return self._call_openai_api(prompt)
            else:
                self.logger.error(f"不支持的AI提供商: {self.ai_config['provider']}")
                return None
                
        except Exception as e:
            self.logger.error(f"调用AI API失败: {e}")
            return None
            
    def _mock_ai_response(self, prompt: str) -> str:
        """模拟AI回复"""
        mock_responses = [
            "这是一个很有趣的问题！让我想想...",
            "我理解您的意思，这确实值得深入讨论。",
            "从我的角度来看，这个话题有很多值得探讨的地方。",
            "您提到的这个观点很有见地，我想补充一些想法。",
            "这让我想到了一个相关的话题，我们可以聊聊。",
            "我觉得这个问题的关键在于如何平衡不同的因素。",
            "您的想法很独特，我很赞同这种思考方式。",
            "这确实是一个复杂的问题，需要从多个角度来看。",
            "我想分享一些我对这个话题的理解和看法。",
            "这个话题让我想起了很多相关的经验和思考。"
        ]
        
        # 根据输入长度和内容选择回复
        if len(prompt) > 100:
            response = random.choice(mock_responses[:5])  # 较长的回复
        else:
            response = random.choice(mock_responses[5:])  # 较短的回复
            
        # 添加一些随机性
        if random.random() < 0.3:
            response += "您觉得呢？"
        elif random.random() < 0.3:
            response += "我很想听听您的看法。"
            
        return response
        
    def _call_openai_api(self, prompt: str) -> Optional[str]:
        """调用OpenAI API（需要安装openai库）"""
        try:
            # 这里需要实际的OpenAI API调用
            # import openai
            # 
            # openai.api_key = self.ai_config['api_key']
            # if self.ai_config['base_url']:
            #     openai.api_base = self.ai_config['base_url']
            # 
            # response = openai.ChatCompletion.create(
            #     model=self.ai_config['model'],
            #     messages=[{"role": "user", "content": prompt}],
            #     temperature=self.ai_config['temperature'],
            #     max_tokens=self.ai_config['max_tokens'],
            #     timeout=self.ai_config['timeout']
            # )
            # 
            # return response.choices[0].message.content.strip()
            
            # 暂时返回模拟回复
            self.logger.warning("OpenAI API未实现，使用模拟回复")
            return self._mock_ai_response(prompt)
            
        except Exception as e:
            self.logger.error(f"OpenAI API调用失败: {e}")
            return None
            
    def _update_response_stats(self, response_time: float, success: bool):
        """更新响应统计"""
        if success:
            self.stats['successful_responses'] += 1
            
            # 更新平均响应时间
            total_successful = self.stats['successful_responses']
            current_avg = self.stats['average_response_time']
            self.stats['average_response_time'] = (
                (current_avg * (total_successful - 1) + response_time) / total_successful
            )
        else:
            self.stats['failed_responses'] += 1
            
    @handle_exceptions("ChatManager")
    def set_ai_config(self, config: Dict[str, Any]):
        """设置AI配置"""
        self.ai_config.update(config)
        self.logger.info(f"AI配置已更新: {config.keys()}")
        
    @handle_exceptions("ChatManager")
    def set_chat_settings(self, settings: Dict[str, Any]):
        """设置对话配置"""
        self.chat_settings.update(settings)
        self.logger.info(f"对话设置已更新: {settings.keys()}")
        
    @handle_exceptions("ChatManager")
    def get_ai_config(self) -> Dict[str, Any]:
        """获取AI配置"""
        # 隐藏敏感信息
        config = self.ai_config.copy()
        if 'api_key' in config and config['api_key']:
            config['api_key'] = '*' * 8
        return config
        
    @handle_exceptions("ChatManager")
    def get_chat_settings(self) -> Dict[str, Any]:
        """获取对话设置"""
        return self.chat_settings.copy()
        
    @handle_exceptions("ChatManager")
    def test_ai_connection(self) -> Dict[str, Any]:
        """测试AI连接"""
        try:
            test_prompt = "你好，这是一个连接测试。"
            start_time = datetime.now()
            
            response = self._call_ai_api(test_prompt)
            
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds()
            
            if response:
                return {
                    'success': True,
                    'response_time': response_time,
                    'response_length': len(response),
                    'message': '连接测试成功'
                }
            else:
                return {
                    'success': False,
                    'response_time': response_time,
                    'message': '连接测试失败：无响应'
                }
                
        except Exception as e:
            return {
                'success': False,
                'response_time': 0,
                'message': f'连接测试失败：{str(e)}'
            }
            
    @handle_exceptions("ChatManager")
    def get_chat_stats(self) -> Dict[str, Any]:
        """获取对话统计"""
        stats = self.stats.copy()
        
        # 计算成功率
        if stats['total_requests'] > 0:
            stats['success_rate'] = stats['successful_responses'] / stats['total_requests']
        else:
            stats['success_rate'] = 0.0
            
        return stats
        
    @handle_exceptions("ChatManager")
    def clear_conversation_history(self):
        """清空对话历史"""
        self.conversation_history.clear_history()
        self.logger.info("对话历史已清空")
        
    @handle_exceptions("ChatManager")
    def generate_topic_response(self, topic: str, style: str = "casual") -> Optional[str]:
        """生成话题回复"""
        try:
            context = {
                'topic': topic,
                'current_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'style': style
            }
            
            topic_prompts = {
                'weather': f"请聊聊关于{topic}的话题",
                'news': f"请分享一些关于{topic}的看法",
                'entertainment': f"我们来聊聊{topic}相关的内容",
                'sports': f"说说{topic}方面的话题",
                'technology': f"聊聊{topic}领域的发展",
                'life': f"分享一些关于{topic}的生活感悟"
            }
            
            prompt = topic_prompts.get(topic, f"请聊聊{topic}")
            return self.generate_response(prompt, context)
            
        except Exception as e:
            self.logger.error(f"生成话题回复失败: {e}")
            return None
