#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试弹幕回复中的副视频检测功能
验证弹幕回复内容包含副视频关键词时，播放列表是否正确显示副视频
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_danmaku_sub_video():
    """测试弹幕回复中的副视频检测"""
    print("🧪 测试弹幕回复中的副视频检测功能...")
    
    try:
        # 导入主程序
        from run_gui_qt5 import MainWindow
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建主窗口
        user_info = {
            'username': 'test_user',
            'user_id': 1,
            'expire_time': '2025-12-31'
        }
        window = MainWindow(user_info)
        
        def test_sub_video_detection():
            """测试副视频检测功能"""
            print("\n🔍 开始测试副视频检测功能...")
            
            # 步骤1：检查副视频配置
            print("\n📋 步骤1：检查副视频配置...")
            if hasattr(window, 'sub_video_manager') and hasattr(window.sub_video_manager, 'sub_videos'):
                sub_videos_data = window.sub_video_manager.sub_videos
                print(f"副视频数据类型: {type(sub_videos_data)}")
                print(f"副视频数据内容: {sub_videos_data}")
                
                # 确保有测试数据
                if isinstance(sub_videos_data, dict):
                    # 添加测试关键词
                    test_keywords = {
                        '火箭': {
                            'keyword': '火箭',
                            'video_source': '火箭特效源',
                            'scripts': ['感谢{nick}的火箭支持！'],
                            'used_scripts': [],
                            'created_at': None,
                            'last_triggered': None,
                            'trigger_count': 0
                        },
                        '礼物': {
                            'keyword': '礼物',
                            'video_source': '礼物特效源',
                            'scripts': ['谢谢{nick}的礼物！'],
                            'used_scripts': [],
                            'created_at': None,
                            'last_triggered': None,
                            'trigger_count': 0
                        }
                    }
                    
                    for keyword, config in test_keywords.items():
                        sub_videos_data[keyword] = config
                        print(f"✅ 添加测试关键词: {keyword} -> {config['video_source']}")
                
                print(f"✅ 副视频配置完成，共 {len(sub_videos_data)} 个关键词")
            else:
                print("❌ 副视频管理器不存在")
                return
            
            # 步骤2：测试副视频触发检测
            print("\n🔍 步骤2：测试副视频触发检测...")
            test_contents = [
                "感谢老板的火箭，太给力了！",
                "谢谢大家的礼物支持",
                "主播666，继续加油！",
                "普通弹幕，没有关键词"
            ]
            
            for content in test_contents:
                if hasattr(window, 'check_sub_video_trigger'):
                    result = window.check_sub_video_trigger(content)
                    print(f"📝 测试内容: {content}")
                    print(f"🔍 副视频检测结果: {result}")
                    print()
            
            # 步骤3：测试弹幕话术添加
            print("\n🎬 步骤3：测试弹幕话术添加...")
            test_danmaku_content = "感谢老板的火箭，太给力了！"
            
            # 检测副视频
            sub_video_result = window.check_sub_video_trigger(test_danmaku_content)
            print(f"📝 弹幕内容: {test_danmaku_content}")
            print(f"🔍 副视频检测: {sub_video_result}")
            
            # 添加到播放列表
            if hasattr(window, 'add_danmaku_to_playlist'):
                print(f"🎬 添加弹幕话术到播放列表...")
                window.add_danmaku_to_playlist(test_danmaku_content, sub_video_result)
                print(f"✅ 弹幕话术添加完成")
            
            # 步骤4：检查播放列表中的副视频显示
            print("\n📋 步骤4：检查播放列表中的副视频显示...")
            if hasattr(window, 'playlist_items'):
                print(f"播放列表项目数量: {len(window.playlist_items)}")
                
                for i, item in enumerate(window.playlist_items):
                    if item.get('voice_type') == '弹幕话术':
                        content = item.get('content', '')[:30]
                        sub_video = item.get('sub_video', '无')
                        status = item.get('status', '未知')
                        
                        print(f"📋 弹幕话术项目 {i+1}:")
                        print(f"  内容: {content}...")
                        print(f"  状态: {status}")
                        print(f"  副视频: {sub_video}")
                        
                        if sub_video and sub_video != '无':
                            print(f"✅ 成功检测到副视频: {sub_video}")
                        else:
                            print(f"❌ 未检测到副视频")
                        print()
            
            # 步骤5：检查表格显示
            print("\n📊 步骤5：检查表格显示...")
            if hasattr(window, 'playlist_table'):
                row_count = window.playlist_table.rowCount()
                print(f"表格行数: {row_count}")
                
                for row in range(row_count):
                    voice_type_item = window.playlist_table.item(row, 1)
                    content_item = window.playlist_table.item(row, 2)
                    sub_video_item = window.playlist_table.item(row, 6)
                    
                    if voice_type_item and voice_type_item.text() == '弹幕话术':
                        voice_type = voice_type_item.text() if voice_type_item else ''
                        content = content_item.text()[:30] if content_item else ''
                        sub_video = sub_video_item.text() if sub_video_item else ''
                        
                        print(f"📊 表格行 {row+1}:")
                        print(f"  类型: {voice_type}")
                        print(f"  内容: {content}...")
                        print(f"  副视频: {sub_video}")
                        
                        if sub_video and sub_video != '无':
                            print(f"✅ 表格中正确显示副视频: {sub_video}")
                        else:
                            print(f"❌ 表格中未显示副视频")
                        print()
            
            # 退出测试
            def exit_test():
                print("\n🏁 弹幕副视频检测测试完成")
                print("📋 测试总结:")
                print("  ✅ 副视频配置检查")
                print("  ✅ 副视频触发检测")
                print("  ✅ 弹幕话术添加")
                print("  ✅ 播放列表副视频显示")
                print("  ✅ 表格副视频显示")
                app.quit()
            
            QTimer.singleShot(3000, exit_test)
        
        # 等待界面初始化完成后开始测试
        QTimer.singleShot(3000, test_sub_video_detection)
        
        # 显示窗口
        window.show()
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_danmaku_sub_video()
