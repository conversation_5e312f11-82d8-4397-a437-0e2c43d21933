#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI直播系统测试程序
模拟主程序的完整功能进行测试
"""

import sys
import time
import json
import random
import threading
from pathlib import Path
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class AIBroadcasterTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("AI直播系统测试程序")
        self.setGeometry(100, 100, 1200, 800)
        
        # 初始化数据
        self.init_test_data()
        self.init_ui()
        self.init_audio_system()
        
        # 播放控制
        self.playback_controller_active = False
        self.current_playing_item = None
        self.playlist_items = []
        
        print("🚀 AI直播系统测试程序启动")

    def init_test_data(self):
        """初始化测试数据"""
        # 模拟话术时间段数据（包含时间段信息）
        self.script_time_segments = {
            "测试话术1": {
                "10秒 - 20秒": {
                    "start_time": 10,
                    "end_time": 20,
                    "content": "1***这是10到20秒时间段，欢迎来到直播间！\n2***10到20秒时间段，【大家好|各位好|hello】，正在玩{gamename}\n3***10到20秒时间段，感谢{nick}的关注\n4***10到20秒时间段，现在时间是{time}"
                },
                "40秒 - 50秒": {
                    "start_time": 40,
                    "end_time": 50,
                    "content": "1***这是40到50秒时间段，感谢大家的观看\n2***40到50秒时间段，谢谢【大家|各位】的支持\n3***40到50秒时间段，我们继续游戏\n4***40到50秒时间段，{gamename}很有趣"
                }
            }
        }
        
        # 模拟AI对话数据
        self.ai_dialogue_data = {
            "游戏": "正在玩{gamename}，很有趣的游戏",
            "感谢": "谢谢{nick}的支持",
            "问候": "【大家好|各位好】，欢迎来到直播间",
            "时间": "现在时间是{time}"
        }
        
        # 模拟副视频设置
        self.sub_video_settings = [
            {
                "name": "游戏画面",
                "keywords": ["游戏", "王者", "英雄"],
                "source": "游戏画面源"
            },
            {
                "name": "感谢画面", 
                "keywords": ["感谢", "谢谢", "支持"],
                "source": "感谢画面源"
            }
        ]
        
        # 模拟主视频位置
        self.current_video_position = 0  # 从0秒开始
        self.video_duration = 60  # 视频总长度60秒
        self.video_speed = 1.0  # 播放速度

        # 主视频播放定时器
        self.video_timer = QTimer()
        self.video_timer.timeout.connect(self.update_video_position)
        self.video_timer.start(1000)  # 每秒更新一次位置

    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("AI直播系统功能测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # 控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)
        
        # 播放列表
        playlist_panel = self.create_playlist_panel()
        layout.addWidget(playlist_panel)
        
        # 日志输出
        log_panel = self.create_log_panel()
        layout.addWidget(log_panel)

    def create_control_panel(self):
        """创建控制面板"""
        group = QGroupBox("控制面板")
        layout = QGridLayout(group)
        
        # 话术选择
        layout.addWidget(QLabel("当前话术:"), 0, 0)
        self.script_combo = QComboBox()
        self.script_combo.addItems(list(self.script_time_segments.keys()))
        layout.addWidget(self.script_combo, 0, 1)
        
        # 预备语音数量
        layout.addWidget(QLabel("预备语音数量:"), 0, 2)
        self.voice_count = QSpinBox()
        self.voice_count.setRange(1, 10)
        self.voice_count.setValue(3)
        layout.addWidget(self.voice_count, 0, 3)
        
        # 主视频位置
        layout.addWidget(QLabel("主视频位置:"), 1, 0)
        self.position_slider = QSlider(Qt.Horizontal)
        self.position_slider.setRange(0, 60)
        self.position_slider.setValue(15)
        self.position_slider.valueChanged.connect(self.on_position_changed)
        layout.addWidget(self.position_slider, 1, 1, 1, 2)
        
        self.position_label = QLabel("15秒")
        layout.addWidget(self.position_label, 1, 3)
        
        # 控制按钮
        self.play_btn = QPushButton("开始播放")
        self.play_btn.clicked.connect(self.start_playback_test)
        layout.addWidget(self.play_btn, 2, 0)
        
        self.stop_btn = QPushButton("停止播放")
        self.stop_btn.clicked.connect(self.stop_playback_test)
        layout.addWidget(self.stop_btn, 2, 1)
        
        # 测试按钮
        self.add_danmaku_btn = QPushButton("添加弹幕")
        self.add_danmaku_btn.clicked.connect(self.add_test_danmaku)
        layout.addWidget(self.add_danmaku_btn, 2, 2)
        
        self.add_time_btn = QPushButton("添加报时")
        self.add_time_btn.clicked.connect(self.add_test_time_announce)
        layout.addWidget(self.add_time_btn, 2, 3)

        # 播放速度控制
        layout.addWidget(QLabel("播放速度:"), 3, 0)
        self.speed_combo = QComboBox()
        self.speed_combo.addItems(["0.5x", "1.0x", "1.5x", "2.0x", "3.0x"])
        self.speed_combo.setCurrentText("1.0x")
        self.speed_combo.currentTextChanged.connect(self.on_speed_changed)
        layout.addWidget(self.speed_combo, 3, 1)

        # 暂停/继续按钮
        self.pause_btn = QPushButton("暂停主视频")
        self.pause_btn.clicked.connect(self.toggle_video_pause)
        layout.addWidget(self.pause_btn, 3, 2)

        self.video_paused = False
        
        return group

    def create_playlist_panel(self):
        """创建播放列表面板"""
        group = QGroupBox("播放列表")
        layout = QVBoxLayout(group)
        
        # 播放列表表格
        self.playlist_table = QTableWidget()
        self.playlist_table.setColumnCount(7)
        self.playlist_table.setHorizontalHeaderLabels([
            "ID", "类型", "内容", "时间段", "状态", "文件名", "副视频"
        ])
        
        # 设置列宽
        header = self.playlist_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)
        
        layout.addWidget(self.playlist_table)
        
        return group

    def create_log_panel(self):
        """创建日志面板"""
        group = QGroupBox("测试日志")
        layout = QVBoxLayout(group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        return group

    def init_audio_system(self):
        """初始化音频系统"""
        self.audio_playing = False
        self.playback_timer = QTimer()
        self.playback_timer.timeout.connect(self.process_playback_queue)

        # 初始化真实音频播放器
        self.init_real_audio_player()

        # 创建voices目录
        self.voices_dir = Path("voices")
        self.voices_dir.mkdir(exist_ok=True)

    def init_real_audio_player(self):
        """初始化真实音频播放器"""
        self.internal_audio_player = None
        self.audio_playing = False

        try:
            # 尝试使用pygame
            import pygame
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
            self.audio_backend = "pygame"
            self.log_message("✅ 使用pygame音频后端")
        except ImportError:
            try:
                # 尝试使用playsound
                import playsound
                self.audio_backend = "playsound"
                self.log_message("✅ 使用playsound音频后端")
            except ImportError:
                # 使用模拟播放
                self.audio_backend = "simulate"
                self.log_message("⚠️ 使用模拟音频播放")

                # 模拟音频播放定时器
                self.audio_timer = QTimer()
                self.audio_timer.timeout.connect(self.simulate_audio_finished)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        print(message)

    def update_video_position(self):
        """自动更新主视频位置"""
        if (hasattr(self, 'playback_controller_active') and
            self.playback_controller_active and
            not getattr(self, 'video_paused', False)):

            # 自动播放，位置递增
            self.current_video_position += self.video_speed

            # 循环播放
            if self.current_video_position >= self.video_duration:
                self.current_video_position = 0
                self.log_message(f"🔄 主视频循环播放，重新开始")

            # 更新滑动条和标签
            self.position_slider.setValue(int(self.current_video_position))
            self.position_label.setText(f"{int(self.current_video_position)}秒 ({self.video_speed}x)")

            # 获取当前时间段
            current_segment = self.get_current_time_segment()

            # 只在时间段变化时输出日志
            if not hasattr(self, 'last_segment') or self.last_segment != current_segment:
                self.log_message(f"🕐 主视频位置: {int(self.current_video_position)}秒, 时间段: {current_segment}")
                self.last_segment = current_segment

    def on_speed_changed(self, speed_text):
        """播放速度改变"""
        try:
            self.video_speed = float(speed_text.replace('x', ''))
            self.log_message(f"⚡ 主视频播放速度: {speed_text}")
        except ValueError:
            self.video_speed = 1.0

    def toggle_video_pause(self):
        """切换主视频暂停/继续"""
        self.video_paused = not self.video_paused

        if self.video_paused:
            self.pause_btn.setText("继续主视频")
            self.log_message("⏸️ 主视频已暂停")
        else:
            self.pause_btn.setText("暂停主视频")
            self.log_message("▶️ 主视频继续播放")

    def on_position_changed(self, value):
        """手动拖动位置改变"""
        self.current_video_position = value
        self.position_label.setText(f"{value}秒")

        # 获取当前时间段
        current_segment = self.get_current_time_segment()
        self.log_message(f"🎯 手动调整位置: {value}秒, 时间段: {current_segment}")

    def get_current_time_segment(self):
        """获取当前时间段"""
        current_script = self.script_combo.currentText()
        current_position = self.current_video_position
        
        if current_script in self.script_time_segments:
            time_segments = self.script_time_segments[current_script]
            
            for segment_name, segment_data in time_segments.items():
                start_time = segment_data.get('start_time', 0)
                end_time = segment_data.get('end_time', 0)
                
                if start_time <= current_position <= end_time:
                    return segment_name
        
        return "不在时间段内"

    def process_random_text_selection(self, text):
        """处理随机文本选择"""
        import re
        
        def replace_random_choice(match):
            choices = match.group(1).split('|')
            return random.choice(choices)
        
        # 处理【选项1|选项2|选项3】格式
        result = re.sub(r'【([^】]+)】', replace_random_choice, text)
        
        # 处理变量替换
        replacements = {
            '{nick}': '测试用户',
            '{gamename}': '王者荣耀',
            '{time}': time.strftime("%H:%M"),
            '{date}': time.strftime("%Y年%m月%d日"),
            '{people}': str(random.randint(100, 999))
        }
        
        for var, value in replacements.items():
            result = result.replace(var, value)
        
        return result

    def start_playback_test(self):
        """开始播放测试"""
        self.log_message("🎵 开始播放测试")
        
        # 初始化播放列表
        self.initialize_playlist()
        
        # 启动播放控制器
        self.start_playback_controller()
        
        self.play_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

    def initialize_playlist(self):
        """初始化播放列表"""
        self.log_message("📋 初始化播放列表...")
        
        # 清空播放列表
        self.playlist_table.setRowCount(0)
        self.playlist_items = []
        
        current_script = self.script_combo.currentText()
        prepare_count = self.voice_count.value()
        
        if current_script not in self.script_time_segments:
            self.log_message("⚠️ 没有选择有效的话术")
            return
        
        item_id = 1
        time_segments = self.script_time_segments[current_script]
        
        for segment_name, segment_data in time_segments.items():
            content = segment_data.get('content', '')
            
            # 解析话术内容
            scripts = []
            for line in content.split('\n'):
                line = line.strip()
                if line and not line.startswith('#'):
                    if '***' in line:
                        script_text = line.split('***', 1)[1].strip()
                        if script_text:
                            scripts.append(script_text)
                    else:
                        scripts.append(line)
            
            self.log_message(f"📝 时间段 '{segment_name}': 找到 {len(scripts)} 条话术")
            
            # 生成预备语音
            for i in range(prepare_count):
                if scripts:
                    script_content = random.choice(scripts)
                    processed_content = self.process_random_text_selection(script_content)
                    
                    playlist_item = {
                        'id': item_id,
                        'voice_type': '主视频话术',
                        'content': processed_content,
                        'time_segment': segment_name,
                        'status': '未下载',
                        'filename': '',
                        'sub_video': '无'
                    }
                    
                    self.playlist_items.append(playlist_item)
                    self.add_item_to_table(playlist_item)
                    item_id += 1
                    
                    self.log_message(f"  - 生成语音 {i+1}/{prepare_count}: {processed_content[:30]}...")
        
        self.log_message(f"✅ 播放列表初始化完成，共 {len(self.playlist_items)} 个项目")

        # 开始批量下载所有语音
        self.download_all_voices()

    def download_all_voices(self):
        """批量下载所有语音（参考主程序实现）"""
        try:
            import queue
            import threading

            self.log_message("🔄 开始异步下载播放列表语音...")

            # 筛选需要下载的项目
            download_items = [item for item in self.playlist_items if item['status'] == '未下载']

            if not download_items:
                self.log_message("✅ 所有语音都已下载")
                return

            self.log_message(f"📋 需要下载 {len(download_items)} 个语音文件")

            # 创建下载队列和结果队列
            download_queue = queue.Queue()
            result_queue = queue.Queue()

            # 将下载任务放入队列
            for i, item in enumerate(download_items):
                speaker_id = 0
                speed = 1
                download_queue.put((i, item, speaker_id, speed))

            # 创建下载工作线程
            def download_worker():
                while not download_queue.empty():
                    try:
                        item_index, item, speaker_id, speed = download_queue.get()

                        # 处理变量替换
                        processed_content = self.process_random_text_selection(item['content'])

                        # 下载语音
                        filename = self.download_voice_main_program(processed_content, speaker_id, speed)

                        # 将结果放入结果队列
                        result_queue.put((item_index, filename, processed_content))

                    except Exception as e:
                        print(f"❌ 下载工作线程异常: {e}")
                        result_queue.put((item_index, None, str(e)))
                    finally:
                        download_queue.task_done()

            # 启动多个下载线程（限制并发数避免过载）
            max_workers = 3
            threads = []
            for _ in range(min(max_workers, len(download_items))):
                thread = threading.Thread(target=download_worker)
                thread.daemon = True
                thread.start()
                threads.append(thread)

            # 创建结果处理定时器
            self.download_result_timer = QTimer()
            self.download_result_timer.timeout.connect(
                lambda: self.process_download_results(result_queue, download_items, len(download_items))
            )
            self.download_result_timer.start(500)  # 每500ms检查一次结果

            self.log_message(f"✅ 启动了 {len(threads)} 个下载线程")

        except Exception as e:
            self.log_message(f"❌ 启动异步下载失败: {e}")

    def process_download_results(self, result_queue, download_items, total_items):
        """处理下载结果（参考主程序实现）"""
        try:
            processed_count = 0

            # 处理所有可用的结果
            while not result_queue.empty():
                try:
                    import queue
                    item_index, filename, content_or_error = result_queue.get_nowait()

                    if filename:
                        download_items[item_index]['filename'] = filename
                        download_items[item_index]['status'] = '已下载'
                        print(f"✅ 语音下载成功: {filename}")
                    else:
                        download_items[item_index]['status'] = '下载失败'
                        print(f"❌ 语音下载失败: {content_or_error[:50]}...")

                    processed_count += 1

                except queue.Empty:
                    break

            # 更新显示
            if processed_count > 0:
                self.update_table_display()

            # 检查是否所有下载都完成
            completed_items = sum(1 for item in download_items if item['status'] in ['已下载', '下载失败'])

            if completed_items >= total_items:
                # 所有下载完成，停止定时器
                self.download_result_timer.stop()
                print(f"✅ 所有语音下载完成: {completed_items}/{total_items}")

                # 下载完成后不需要重新启动播放控制器，因为已经在运行了

        except Exception as e:
            print(f"❌ 处理下载结果异常: {e}")

    def download_single_voice(self, item):
        """下载单个语音（用于新生成的语音）"""
        try:
            import threading

            self.log_message(f"🔄 开始下载新语音: {item['content'][:30]}...")

            def download_thread():
                try:
                    # 处理变量替换
                    processed_content = self.process_random_text_selection(item['content'])

                    # 下载语音
                    speaker_id = 0
                    speed = 1
                    filename = self.download_voice_main_program(processed_content, speaker_id, speed)

                    if filename:
                        # 更新状态
                        item['filename'] = filename
                        item['status'] = '已下载'

                        print(f"✅ 新语音下载成功: {filename}")
                        print(f"📝 状态更新: 未下载 → 已下载")

                        # 更新表格显示
                        QTimer.singleShot(0, self.update_table_display)

                    else:
                        # 下载失败
                        item['status'] = '下载失败'
                        print(f"❌ 新语音下载失败")
                        QTimer.singleShot(0, self.update_table_display)

                except Exception as e:
                    print(f"❌ 下载新语音异常: {e}")
                    item['status'] = '下载失败'
                    QTimer.singleShot(0, self.update_table_display)

            # 启动下载线程
            thread = threading.Thread(target=download_thread)
            thread.daemon = True
            thread.start()

        except Exception as e:
            self.log_message(f"❌ 启动新语音下载失败: {e}")
            item['status'] = '下载失败'
            self.update_table_display()

    def add_item_to_table(self, item):
        """添加项目到表格"""
        row = self.playlist_table.rowCount()
        self.playlist_table.insertRow(row)
        
        self.playlist_table.setItem(row, 0, QTableWidgetItem(str(item['id'])))
        self.playlist_table.setItem(row, 1, QTableWidgetItem(item['voice_type']))
        self.playlist_table.setItem(row, 2, QTableWidgetItem(item['content'][:50] + "..." if len(item['content']) > 50 else item['content']))
        self.playlist_table.setItem(row, 3, QTableWidgetItem(item['time_segment']))
        self.playlist_table.setItem(row, 4, QTableWidgetItem(item['status']))
        self.playlist_table.setItem(row, 5, QTableWidgetItem(item['filename']))
        self.playlist_table.setItem(row, 6, QTableWidgetItem(item['sub_video']))

    def start_playback_controller(self):
        """启动播放控制器"""
        self.log_message("🎵 启动播放控制器...")
        self.playback_controller_active = True
        self.playback_timer.start(2000)  # 每2秒检查一次
        self.log_message("✅ 播放控制器已启动")

    def process_playback_queue(self):
        """处理播放队列"""
        if not self.playback_controller_active:
            return
        
        # 如果当前有正在播放的项目，跳过
        if self.current_playing_item:
            return
        
        # 获取下一个播放项目
        next_item = self.get_next_playback_item()
        
        if next_item:
            self.start_audio_playback(next_item)

    def get_next_playback_item(self):
        """获取下一个播放项目"""
        # 优先选择已下载的项目，如果没有则选择未下载的项目
        downloaded_items = [
            item for item in self.playlist_items
            if item['status'] == '已下载' and item['filename']
        ]

        undownloaded_items = [
            item for item in self.playlist_items
            if item['status'] == '未下载'
        ]

        # 优先使用已下载的项目，如果没有则使用未下载的项目
        available_items = downloaded_items if downloaded_items else undownloaded_items

        if not available_items:
            self.log_message("📋 没有可用的播放项目")
            return None

        self.log_message(f"📋 可用播放项目: {len(downloaded_items)} 个已下载, {len(undownloaded_items)} 个未下载")
        
        # 第一优先级：弹幕话术
        danmaku_items = [item for item in available_items if item['voice_type'] == '弹幕话术']
        if danmaku_items:
            item = danmaku_items[0]
            self.log_message(f"🎯 选择播放: {item['voice_type']} - {item['content'][:30]}... (弹幕优先)")
            return item
        
        # 第二优先级：报时话术
        time_items = [item for item in available_items if item['voice_type'] == '报时话术']
        if time_items:
            item = time_items[0]
            self.log_message(f"🎯 选择播放: {item['voice_type']} - {item['content'][:30]}... (报时优先)")
            return item
        
        # 第三优先级：主视频话术（按时间段匹配）
        current_segment = self.get_current_time_segment()
        
        if current_segment != "不在时间段内":
            matching_items = [
                item for item in available_items
                if item['voice_type'] == '主视频话术' and item['time_segment'] == current_segment
            ]
            
            if matching_items:
                item = matching_items[0]
                self.log_message(f"🎯 选择播放: {item['voice_type']} - 时间段:{current_segment} - {item['content'][:30]}...")
                return item
            else:
                self.log_message(f"⚠️ 当前时间段'{current_segment}'没有可用的话术")
        else:
            self.log_message("⚠️ 当前不在任何时间段内，不播放主视频话术")
        
        return None

    def start_audio_playback(self, item):
        """开始音频播放"""
        self.current_playing_item = item
        self.audio_playing = True

        self.log_message(f"🎵 开始播放: {item['voice_type']} - {item['content'][:30]}...")

        # 如果有副视频，显示切换信息
        if item.get('sub_video') and item['sub_video'] != '无':
            self.log_message(f"🎬 切换副视频: {item['sub_video']}")

        # 检查语音文件是否存在
        if item['status'] == '已下载' and item.get('filename'):
            # 更新状态为播放中
            item['status'] = '播放中'
            self.log_message(f"📝 状态更新: 已下载 → 播放中")

            # 更新表格显示
            self.update_table_display()

            # 直接播放已下载的文件
            self.play_audio_file(item['filename'])
        else:
            # 如果没有下载，跳过这个项目
            self.log_message(f"⚠️ 语音未下载，跳过播放: {item['content'][:30]}...")
            self.on_audio_playback_finished()

    def download_voice_for_item(self, item):
        """为项目下载语音（调用真实下载功能）"""
        self.log_message(f"🔄 开始下载语音: {item['content'][:30]}...")

        # 使用真实的语音下载功能
        self.real_voice_download(item)

    def real_voice_download(self, item):
        """使用主程序的真实语音下载功能（参考主程序实现）"""
        try:
            # 处理变量替换
            processed_content = self.process_random_text_selection(item['content'])

            # 使用主程序的下载方法
            speaker_id = 0  # 设置为0
            speed = 1  # 正确的速度参数

            self.log_message(f"📡 使用主程序API下载语音")
            self.log_message(f"📝 下载内容: {processed_content[:50]}...")
            self.log_message(f"🎤 说话人ID: {speaker_id}")
            self.log_message(f"⚡ 语音速度: {speed}")

            # 在新线程中下载，避免阻塞UI
            import threading
            def download_thread():
                try:
                    # 调用主程序的下载方法
                    filename = self.download_voice_main_program(processed_content, speaker_id, speed)

                    if filename:
                        # 直接更新状态（参考主程序的process_download_results）
                        item['filename'] = filename
                        item['status'] = '已下载'

                        print(f"✅ 语音下载成功: {filename}")
                        print(f"📝 状态更新: 未下载 → 已下载")

                        # 更新表格显示
                        QTimer.singleShot(0, self.update_table_display)

                    else:
                        # 下载失败
                        item['status'] = '下载失败'
                        print(f"❌ 语音下载失败")
                        QTimer.singleShot(0, self.update_table_display)

                except Exception as e:
                    print(f"❌ 下载异常: {e}")
                    item['status'] = '下载失败'
                    QTimer.singleShot(0, self.update_table_display)

            # 启动下载线程
            thread = threading.Thread(target=download_thread)
            thread.daemon = True
            thread.start()

        except Exception as e:
            self.log_message(f"❌ 语音下载初始化失败: {e}")
            item['status'] = '下载失败'
            self.update_table_display()

    def download_voice_main_program(self, text, speaker_id, speed):
        """使用主程序的语音下载方法"""
        try:
            import requests
            import hashlib
            from pathlib import Path

            # 确保voices目录存在
            voices_dir = Path("voices")
            voices_dir.mkdir(exist_ok=True)

            # 生成文件名（避免重复）
            text_hash = hashlib.md5(text.encode('utf-8')).hexdigest()[:8]
            filename = f"{text_hash}_{speaker_id}_{speed}.wav"
            filepath = voices_dir / filename

            # 如果文件已存在，直接返回
            if filepath.exists():
                self.log_message(f"✅ 语音文件已存在: {filename}")
                return filename

            # 🔥 修复：调用API下载语音（修复语速参数和代理问题）
            api_url = "http://ct.scjanelife.com/voice/bert-vits2"

            # 🔥 修复：将滑块值(80-120)转换为API需要的速度值(0.8-1.2)
            api_speed = speed / 100.0 if speed > 10 else speed

            params = {
                'id': speaker_id,
                'lang': api_speed,  # 🔥 修复：使用lang参数
                'text': text
            }

            self.log_message(f"🔄 下载语音: {text[:30]}... (ID:{speaker_id}, 速度:{api_speed})")

            # 🔥 修复：禁用代理，直接访问API
            response = requests.get(api_url, params=params, timeout=30, proxies={'http': None, 'https': None})
            response.raise_for_status()

            # 保存文件
            with open(filepath, 'wb') as f:
                f.write(response.content)

            self.log_message(f"✅ 语音下载完成: {filename}")

            # 直接调用成功回调（在下载线程中）
            self.log_message(f"🔄 准备调用下载成功回调")

            return filename

        except Exception as e:
            self.log_message(f"❌ 下载语音失败: {e}")
            return None



    def create_fallback_audio(self, item, filename):
        """创建备用音频文件"""
        self.log_message(f"🔄 创建备用音频文件: {filename}")

        filepath = self.voices_dir / filename

        # 创建简单的测试音频文件
        self.create_simple_audio_file(filepath)

        # 更新项目状态
        item['status'] = '已下载'
        item['filename'] = filename

        # 更新表格显示
        self.update_table_display()

        # 开始播放
        self.play_audio_file(filename)

    def on_voice_download_complete(self, item):
        """语音下载完成"""
        # 生成文件名
        filename = f"test_{item['id']}_{int(time.time())}.wav"
        filepath = self.voices_dir / filename

        # 创建测试音频文件
        self.create_test_audio_file(filepath, item['content'])

        # 更新项目状态
        item['status'] = '已下载'
        item['filename'] = filename

        # 更新表格显示
        self.update_table_display()

        self.log_message(f"✅ 语音下载完成: {filename}")

        # 开始播放
        self.play_audio_file(filename)

    def create_test_audio_file(self, filepath, content):
        """创建测试音频文件"""
        try:
            # 尝试使用TTS生成真实语音
            self.generate_tts_audio(filepath, content)
        except Exception as e:
            # 如果TTS失败，创建简单的音频文件
            self.create_simple_audio_file(filepath)

    def generate_tts_audio(self, filepath, content):
        """使用TTS生成语音"""
        try:
            # 尝试使用pyttsx3
            import pyttsx3
            engine = pyttsx3.init()
            engine.setProperty('rate', 150)  # 语速
            engine.setProperty('volume', 0.8)  # 音量

            # 保存为音频文件
            engine.save_to_file(content, str(filepath))
            engine.runAndWait()

            self.log_message(f"✅ 使用TTS生成语音: {filepath.name}")

        except ImportError:
            # 如果没有pyttsx3，创建简单音频文件
            self.create_simple_audio_file(filepath)

    def create_simple_audio_file(self, filepath):
        """创建简单的测试音频文件"""
        try:
            import wave
            import numpy as np

            # 生成1秒的正弦波音频
            sample_rate = 22050
            duration = 2.0  # 2秒
            frequency = 440  # A4音符

            t = np.linspace(0, duration, int(sample_rate * duration))
            audio_data = np.sin(2 * np.pi * frequency * t) * 0.3

            # 转换为16位整数
            audio_data = (audio_data * 32767).astype(np.int16)

            # 保存为WAV文件
            with wave.open(str(filepath), 'w') as wav_file:
                wav_file.setnchannels(1)  # 单声道
                wav_file.setsampwidth(2)  # 16位
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_data.tobytes())

            self.log_message(f"✅ 创建测试音频文件: {filepath.name}")

        except ImportError:
            # 如果没有numpy，创建空文件
            filepath.touch()
            self.log_message(f"⚠️ 创建空音频文件: {filepath.name}")

    def play_audio_file(self, filename):
        """播放音频文件"""
        filepath = self.voices_dir / filename

        self.log_message(f"🎵 开始播放音频文件: {filename}")
        self.log_message(f"📂 文件路径: {filepath}")

        if not filepath.exists():
            self.log_message(f"❌ 音频文件不存在: {filename}")
            self.on_audio_playback_finished()
            return

        # 检查文件大小
        file_size = filepath.stat().st_size
        self.log_message(f"📊 文件大小: {file_size} 字节")

        self.log_message(f"🎵 使用 {self.audio_backend} 后端播放")

        if self.audio_backend == "pygame":
            self.play_with_pygame(filepath)
        elif self.audio_backend == "playsound":
            self.play_with_playsound(filepath)
        else:
            # 模拟播放
            play_duration = random.randint(2000, 5000)
            self.log_message(f"🎵 模拟播放时长: {play_duration/1000}秒")
            if hasattr(self, 'audio_timer'):
                self.audio_timer.start(play_duration)

    def play_with_pygame(self, filepath):
        """使用pygame播放音频"""
        try:
            import pygame
            self.log_message(f"🎵 pygame加载音频文件: {filepath.name}")
            pygame.mixer.music.load(str(filepath))

            self.log_message(f"🎵 pygame开始播放音频")
            pygame.mixer.music.play()

            # 检查播放状态
            self.check_pygame_playback()

        except Exception as e:
            self.log_message(f"❌ pygame播放失败: {e}")
            self.on_audio_playback_finished()

    def check_pygame_playback(self):
        """检查pygame播放状态"""
        try:
            import pygame
            if not pygame.mixer.music.get_busy():
                # 播放完成
                self.on_audio_playback_finished()
            else:
                # 继续检查
                QTimer.singleShot(100, self.check_pygame_playback)
        except:
            self.on_audio_playback_finished()

    def play_with_playsound(self, filepath):
        """使用playsound播放音频"""
        try:
            import playsound
            # 在新线程中播放，避免阻塞UI
            def play_thread():
                try:
                    playsound.playsound(str(filepath))
                    # 播放完成后通知主线程
                    QTimer.singleShot(0, self.on_audio_playback_finished)
                except Exception as e:
                    self.log_message(f"❌ playsound播放失败: {e}")
                    QTimer.singleShot(0, self.on_audio_playback_finished)

            import threading
            thread = threading.Thread(target=play_thread)
            thread.daemon = True
            thread.start()

        except Exception as e:
            self.log_message(f"❌ playsound播放失败: {e}")
            self.on_audio_playback_finished()

    def on_audio_playback_finished(self):
        """音频播放完成"""
        if self.current_playing_item:
            self.log_message(f"🎵 播放完成: {self.current_playing_item['content'][:30]}...")

            # 更新状态为已播放
            self.current_playing_item['status'] = '已播放'
            self.log_message(f"📝 状态更新: 播放中 → 已播放")

            # 从播放列表中移除
            if self.current_playing_item in self.playlist_items:
                self.playlist_items.remove(self.current_playing_item)
                self.update_table_display()

            # 补充新语音
            self.replenish_voice(self.current_playing_item)

            self.current_playing_item = None
            self.audio_playing = False

    def simulate_audio_finished(self):
        """模拟音频播放完成"""
        self.audio_timer.stop()
        
        if self.current_playing_item:
            self.log_message(f"🎵 播放完成: {self.current_playing_item['content'][:30]}...")
            
            # 从播放列表中移除
            self.playlist_items.remove(self.current_playing_item)
            self.update_table_display()
            
            # 补充新语音
            self.replenish_voice(self.current_playing_item)
            
            self.current_playing_item = None
            self.audio_playing = False

    def replenish_voice(self, played_item):
        """补充新语音"""
        if played_item['voice_type'] != '主视频话术':
            return
        
        time_segment = played_item['time_segment']
        self.log_message(f"🔄 补充主视频话术: {time_segment}")
        
        current_script = self.script_combo.currentText()
        if current_script in self.script_time_segments:
            segment_data = self.script_time_segments[current_script].get(time_segment)
            if segment_data:
                content = segment_data.get('content', '')
                scripts = []
                for line in content.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if '***' in line:
                            script_text = line.split('***', 1)[1].strip()
                            if script_text:
                                scripts.append(script_text)
                
                if scripts:
                    script_content = random.choice(scripts)
                    processed_content = self.process_random_text_selection(script_content)
                    
                    new_item = {
                        'id': max([item['id'] for item in self.playlist_items], default=0) + 1,
                        'voice_type': '主视频话术',
                        'content': processed_content,
                        'time_segment': time_segment,
                        'status': '未下载',
                        'filename': '',
                        'sub_video': '无'
                    }
                    
                    self.playlist_items.append(new_item)
                    self.add_item_to_table(new_item)

                    self.log_message(f"✅ 补充新语音: {processed_content[:30]}...")

                    # 立即下载新补充的语音
                    self.download_single_voice(new_item)

    def update_table_display(self):
        """更新表格显示"""
        self.playlist_table.setRowCount(0)
        for item in self.playlist_items:
            self.add_item_to_table(item)

    def add_test_danmaku(self):
        """添加测试弹幕"""
        test_messages = [
            "主播在玩什么游戏？",
            "感谢主播的精彩直播",
            "666，主播很厉害",
            "现在几点了？"
        ]
        
        message = random.choice(test_messages)
        self.log_message(f"💬 收到弹幕: {message}")
        
        # 查找匹配的AI对话
        matched_reply = None
        matched_sub_video = None
        
        for keyword, reply in self.ai_dialogue_data.items():
            if keyword in message:
                matched_reply = self.process_random_text_selection(reply)
                
                # 查找对应的副视频
                for sub_video in self.sub_video_settings:
                    if any(kw in keyword for kw in sub_video["keywords"]):
                        matched_sub_video = sub_video["source"]
                        break
                break
        
        if matched_reply:
            danmaku_item = {
                'id': max([item['id'] for item in self.playlist_items], default=0) + 1,
                'voice_type': '弹幕话术',
                'content': matched_reply,
                'time_segment': '无',
                'status': '未下载',
                'filename': '',
                'sub_video': matched_sub_video or '无'
            }
            
            self.playlist_items.append(danmaku_item)
            self.add_item_to_table(danmaku_item)

            self.log_message(f"🤖 生成AI回复: {matched_reply[:30]}...")
            if matched_sub_video:
                self.log_message(f"🎬 关联副视频: {matched_sub_video}")

            # 立即下载新生成的弹幕语音
            self.download_single_voice(danmaku_item)

    def add_test_time_announce(self):
        """添加测试报时"""
        current_time = time.strftime("%H:%M")
        time_content = f"现在时间是{current_time}"
        
        time_item = {
            'id': max([item['id'] for item in self.playlist_items], default=0) + 1,
            'voice_type': '报时话术',
            'content': time_content,
            'time_segment': '无',
            'status': '未下载',
            'filename': '',
            'sub_video': '无'
        }
        
        self.playlist_items.append(time_item)
        self.add_item_to_table(time_item)

        self.log_message(f"⏰ 添加报时: {time_content}")

        # 立即下载新生成的报时语音
        self.download_single_voice(time_item)

    def stop_playback_test(self):
        """停止播放测试"""
        self.log_message("⏹️ 停止播放测试")
        
        self.playback_controller_active = False
        self.playback_timer.stop()
        self.audio_timer.stop()
        
        if self.current_playing_item:
            self.log_message(f"⏹️ 中断播放: {self.current_playing_item['content'][:30]}...")
            self.current_playing_item = None
        
        self.audio_playing = False
        self.play_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

def main():
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    window = AIBroadcasterTest()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
