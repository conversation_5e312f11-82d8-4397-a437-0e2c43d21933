#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速副视频触发测试
"""

import json
from pathlib import Path

def main():
    print("🎬 快速副视频触发测试")
    print("=" * 40)
    
    # 检查配置文件
    config_file = Path("data/sub_videos.json")
    if not config_file.exists():
        print("❌ 副视频配置文件不存在")
        return
    
    # 加载配置
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"✅ 加载配置成功: {len(config)} 个关键词")
        
        for keyword, data in config.items():
            video_source = data.get('video_source', '未知')
            print(f"  • {keyword} → {video_source}")
    except Exception as e:
        print(f"❌ 加载配置失败: {e}")
        return
    
    # 测试弹幕
    test_messages = [
        "感谢老板的火箭，太给力了！",
        "谢谢大家的礼物支持", 
        "主播666，继续加油！",
        "普通弹幕，没有关键词"
    ]
    
    print(f"\n🧪 测试弹幕触发:")
    for msg in test_messages:
        triggered = False
        for keyword in config.keys():
            if keyword and keyword.lower() in msg.lower():
                video_source = config[keyword].get('video_source', '')
                print(f"✅ {msg} → 触发 {keyword} → {video_source}")
                triggered = True
                break
        
        if not triggered:
            print(f"⚪ {msg} → 无触发")
    
    print(f"\n🏁 测试完成")

if __name__ == "__main__":
    main()
