#!/usr/bin/env python3
"""
OBS WebSocket连接测试程序
简单版本，用于测试OBS连接
"""

import asyncio
import websockets
import json
import hashlib
import base64
import sys
from datetime import datetime


class SimpleOBSTest:
    """简单的OBS WebSocket测试"""
    
    def __init__(self, host="localhost", port=4455, password=""):
        self.host = host
        self.port = port
        self.password = password
        self.websocket_url = f"ws://{host}:{port}"
        self.websocket = None
        self.is_connected = False
        self.request_id = 0
        
    def log(self, message):
        """打印日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    async def connect(self):
        """连接到OBS"""
        try:
            self.log(f"正在连接到OBS WebSocket服务器: {self.websocket_url}")
            
            # 连接WebSocket
            self.websocket = await websockets.connect(self.websocket_url)
            self.log("WebSocket连接成功")
            
            # 等待Hello消息
            hello_message = await self.websocket.recv()
            hello_data = json.loads(hello_message)
            self.log(f"收到Hello消息: {hello_data}")
            
            if hello_data.get("op") == 0:  # Hello
                # 发送Identify消息
                await self.send_identify(hello_data.get("d", {}))
                
                # 等待Identified消息
                identified_message = await self.websocket.recv()
                identified_data = json.loads(identified_message)
                self.log(f"收到Identified消息: {identified_data}")
                
                if identified_data.get("op") == 2:  # Identified
                    self.is_connected = True
                    self.log("✅ 成功连接到OBS WebSocket服务器")
                    return True
                else:
                    self.log(f"❌ 身份验证失败: {identified_data}")
                    return False
            else:
                self.log(f"❌ 未收到Hello消息: {hello_data}")
                return False
                
        except websockets.exceptions.ConnectionRefused:
            self.log("❌ 连接被拒绝 - 请确保OBS Studio正在运行且WebSocket服务器已启用")
            return False
        except Exception as e:
            self.log(f"❌ 连接失败: {e}")
            return False
    
    async def send_identify(self, hello_data):
        """发送身份验证消息"""
        identify_data = {
            "op": 1,  # Identify
            "d": {
                "rpcVersion": 1
            }
        }
        
        # 如果需要密码验证
        if "authentication" in hello_data and self.password:
            auth_data = hello_data["authentication"]
            challenge = auth_data.get("challenge", "")
            salt = auth_data.get("salt", "")
            
            self.log("需要密码验证")
            
            # 生成认证字符串
            secret = base64.b64encode(
                hashlib.sha256((self.password + salt).encode()).digest()
            ).decode()
            
            auth_response = base64.b64encode(
                hashlib.sha256((secret + challenge).encode()).digest()
            ).decode()
            
            identify_data["d"]["authentication"] = auth_response
        else:
            self.log("无需密码验证")
        
        await self.websocket.send(json.dumps(identify_data))
        self.log("已发送Identify消息")
    
    async def send_request(self, request_type, request_data=None):
        """发送请求"""
        if not self.is_connected:
            self.log("❌ 未连接到OBS")
            return None
        
        self.request_id += 1
        request_id = str(self.request_id)
        
        request = {
            "op": 6,  # Request
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data or {}
            }
        }
        
        try:
            await self.websocket.send(json.dumps(request))
            self.log(f"发送请求: {request_type}")
            
            # 等待响应
            response = await asyncio.wait_for(self.websocket.recv(), timeout=5.0)
            response_data = json.loads(response)
            
            if response_data.get("op") == 7:  # RequestResponse
                response_d = response_data.get("d", {})
                if response_d.get("requestId") == request_id:
                    if response_d.get("requestStatus", {}).get("result", False):
                        self.log(f"✅ 请求成功: {request_type}")
                        return response_d.get("responseData", {})
                    else:
                        error_msg = response_d.get("requestStatus", {}).get("comment", "未知错误")
                        self.log(f"❌ 请求失败: {request_type} - {error_msg}")
                        return None
            
            return None
            
        except asyncio.TimeoutError:
            self.log(f"❌ 请求超时: {request_type}")
            return None
        except Exception as e:
            self.log(f"❌ 请求异常: {request_type} - {e}")
            return None
    
    async def test_basic_functions(self):
        """测试基本功能"""
        if not self.is_connected:
            self.log("❌ 未连接，无法测试功能")
            return
        
        self.log("\n🔍 开始测试基本功能...")
        
        # 测试获取版本信息
        version_info = await self.send_request("GetVersion")
        if version_info:
            self.log(f"✅ OBS版本: {version_info.get('obsVersion', 'Unknown')}")
            self.log(f"✅ WebSocket版本: {version_info.get('obsWebSocketVersion', 'Unknown')}")
        
        # 测试获取场景列表
        scenes_info = await self.send_request("GetSceneList")
        if scenes_info:
            scenes = scenes_info.get("scenes", [])
            self.log(f"✅ 找到 {len(scenes)} 个场景:")
            for scene in scenes:
                scene_name = scene.get("sceneName", "Unknown")
                self.log(f"   - {scene_name}")
        
        # 测试获取输入源列表
        inputs_info = await self.send_request("GetInputList")
        if inputs_info:
            inputs = inputs_info.get("inputs", [])
            self.log(f"✅ 找到 {len(inputs)} 个输入源:")
            for input_source in inputs:
                input_name = input_source.get("inputName", "Unknown")
                input_kind = input_source.get("inputKind", "Unknown")
                self.log(f"   - {input_name} ({input_kind})")
    
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            self.log("已断开连接")
        self.is_connected = False


async def main():
    """主函数"""
    print("=" * 60)
    print("OBS WebSocket连接测试程序")
    print("=" * 60)
    
    # 获取连接参数
    host = input("请输入OBS主机地址 (默认: localhost): ").strip() or "localhost"
    port_input = input("请输入OBS WebSocket端口 (默认: 4455): ").strip()
    port = int(port_input) if port_input else 4455
    password = input("请输入WebSocket密码 (如果没有设置密码请直接回车): ").strip()
    
    print(f"\n连接参数:")
    print(f"主机: {host}")
    print(f"端口: {port}")
    print(f"密码: {'已设置' if password else '未设置'}")
    print("-" * 60)
    
    # 创建测试实例
    obs_test = SimpleOBSTest(host, port, password)
    
    try:
        # 尝试连接
        if await obs_test.connect():
            # 测试基本功能
            await obs_test.test_basic_functions()
        else:
            print("\n❌ 连接失败，请检查:")
            print("1. OBS Studio是否正在运行")
            print("2. WebSocket服务器是否已启用 (工具 -> WebSocket服务器设置)")
            print("3. 端口号是否正确")
            print("4. 密码是否正确")
    
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
    finally:
        await obs_test.disconnect()
    
    print("\n测试完成")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被中断")
    except Exception as e:
        print(f"程序异常: {e}")
    
    input("\n按回车键退出...")
