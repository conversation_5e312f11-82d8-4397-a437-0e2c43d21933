#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缺失方法修复测试脚本
测试 update_main_video_position 方法是否已正确添加
"""

import sys
import os
from pathlib import Path

def test_missing_method_fix():
    """测试缺失方法修复"""
    print("🔧 测试缺失方法修复...")
    
    try:
        main_file = "run_gui_qt5.py"
        
        if not Path(main_file).exists():
            print(f"⚠️ 主文件不存在: {main_file}")
            return False
        
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否存在 update_main_video_position 方法
        method_patterns = [
            "def update_main_video_position(self):",
            "更新主视频位置",
            "self.get_main_video_actual_position()",
            "self.update_time_segment(",
            "self.main_video_position"
        ]
        
        found_patterns = []
        for pattern in method_patterns:
            if pattern in content:
                found_patterns.append(pattern)
        
        print(f"📋 检查到的方法特征:")
        for pattern in found_patterns:
            print(f"  ✅ {pattern}")
        
        missing_patterns = [pattern for pattern in method_patterns if pattern not in found_patterns]
        if missing_patterns:
            print(f"⚠️ 缺失的特征:")
            for pattern in missing_patterns:
                print(f"  ❌ {pattern}")
        
        # 检查方法调用是否正确连接
        connection_patterns = [
            "self.video_position_timer.timeout.connect(self.update_main_video_position)",
            "video_position_timer.start(500)"
        ]
        
        found_connections = []
        for pattern in connection_patterns:
            if pattern in content:
                found_connections.append(pattern)
        
        print(f"📋 检查到的连接:")
        for pattern in found_connections:
            print(f"  ✅ {pattern}")
        
        # 检查是否还有其他缺失的方法调用
        potential_issues = [
            "AttributeError: 'MainWindow' object has no attribute",
            "Did you mean:"
        ]
        
        found_issues = []
        for issue in potential_issues:
            if issue in content:
                found_issues.append(issue)
        
        if found_issues:
            print(f"⚠️ 发现潜在问题:")
            for issue in found_issues:
                print(f"  ❌ {issue}")
        else:
            print("✅ 没有发现明显的属性错误")
        
        return len(found_patterns) >= 4 and len(found_connections) >= 1 and len(found_issues) == 0
        
    except Exception as e:
        print(f"❌ 缺失方法修复测试失败: {e}")
        return False

def test_method_implementation():
    """测试方法实现的完整性"""
    print("\n🔧 测试方法实现的完整性...")
    
    try:
        main_file = "run_gui_qt5.py"
        
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找 update_main_video_position 方法的实现
        method_start = content.find("def update_main_video_position(self):")
        if method_start == -1:
            print("❌ 未找到 update_main_video_position 方法")
            return False
        
        # 查找方法结束位置（下一个同级方法或类结束）
        method_end = content.find("\n        def ", method_start + 1)
        if method_end == -1:
            method_end = content.find("\n    def ", method_start + 1)
        if method_end == -1:
            method_end = len(content)
        
        method_content = content[method_start:method_end]
        
        # 检查方法实现的关键功能
        implementation_features = [
            "get_main_video_actual_position()",
            "position_seconds",
            "duration_seconds", 
            "progress_bar.setValue",
            "update_time_segment",
            "main_video_position",
            "模拟播放",
            "异常处理"
        ]
        
        found_features = []
        for feature in implementation_features:
            if feature in method_content:
                found_features.append(feature)
        
        print(f"📋 方法实现特征:")
        for feature in found_features:
            print(f"  ✅ {feature}")
        
        missing_features = [feature for feature in implementation_features if feature not in found_features]
        if missing_features:
            print(f"⚠️ 缺失的实现特征:")
            for feature in missing_features:
                print(f"  ❌ {feature}")
        
        # 检查方法长度（应该有合理的实现）
        method_lines = method_content.count('\n')
        print(f"📏 方法长度: {method_lines} 行")
        
        if method_lines < 10:
            print("⚠️ 方法实现可能过于简单")
            return False
        elif method_lines > 100:
            print("⚠️ 方法实现可能过于复杂")
        else:
            print("✅ 方法长度合理")
        
        return len(found_features) >= 6
        
    except Exception as e:
        print(f"❌ 方法实现测试失败: {e}")
        return False

def test_timer_connections():
    """测试定时器连接"""
    print("\n🔧 测试定时器连接...")
    
    try:
        main_file = "run_gui_qt5.py"
        
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查定时器相关的代码
        timer_patterns = [
            "video_position_timer = QTimer()",
            "video_position_timer.timeout.connect",
            "video_position_timer.start(500)",
            "download_check_timer = QTimer()",
            "simple_play_timer = QTimer()"
        ]
        
        found_timers = []
        for pattern in timer_patterns:
            if pattern in content:
                found_timers.append(pattern)
        
        print(f"📋 定时器设置:")
        for timer in found_timers:
            print(f"  ✅ {timer}")
        
        # 检查是否有定时器冲突或重复
        timer_conflicts = [
            "video_position_timer.timeout.connect(self.update_main_video_position)",
            "video_position_timer.timeout.connect(self.update_main_video_position)"
        ]
        
        conflict_count = 0
        for conflict in timer_conflicts:
            conflict_count += content.count(conflict)
        
        if conflict_count > 1:
            print(f"⚠️ 发现定时器连接重复: {conflict_count} 次")
            return False
        else:
            print("✅ 没有发现定时器连接重复")
        
        return len(found_timers) >= 3
        
    except Exception as e:
        print(f"❌ 定时器连接测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始缺失方法修复测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: 缺失方法修复
    result1 = test_missing_method_fix()
    test_results.append(("缺失方法修复", result1))
    
    # 测试2: 方法实现完整性
    result2 = test_method_implementation()
    test_results.append(("方法实现完整性", result2))
    
    # 测试3: 定时器连接
    result3 = test_timer_connections()
    test_results.append(("定时器连接", result3))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！缺失方法修复完成！")
        print("\n✨ 修复内容总结:")
        print("1. ✅ 添加了 update_main_video_position 方法")
        print("2. ✅ 正确连接了定时器回调")
        print("3. ✅ 实现了主视频位置更新逻辑")
        print("4. ✅ 添加了异常处理和回退机制")
        print("\n🔧 功能特点:")
        print("• 获取OBS主视频实际播放位置")
        print("• 更新进度条显示")
        print("• 更新时间段显示")
        print("• 模拟播放位置（OBS未连接时）")
        print("• 完善的异常处理")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
