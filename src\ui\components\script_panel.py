"""
AI Broadcaster v2 - 话术管理面板
话术的增删改查和时间段管理
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QTextEdit, QListWidget, QListWidgetItem,
    QGroupBox, QMessageBox, QInputDialog, QSplitter,
    QFrame, QComboBox, QSpinBox, QTimeEdit
)
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot, QTime
from PyQt5.QtGui import QFont

from ...services.logging_service import create_logger


class ScriptPanel(QWidget):
    """话术管理面板"""
    
    def __init__(self, db_manager, user_info):
        super().__init__()
        self.logger = create_logger("script_panel")
        self.db_manager = db_manager
        self.user_info = user_info
        self.current_script_id = None
        
        self.init_ui()
        self.setup_connections()
        self.load_scripts()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：话术列表
        self.create_script_list(splitter)
        
        # 右侧：话术编辑
        self.create_script_editor(splitter)
        
        # 设置分割器比例
        splitter.setSizes([300, 500])
        
    def create_script_list(self, parent):
        """创建话术列表"""
        list_widget = QWidget()
        list_layout = QVBoxLayout(list_widget)
        
        # 话术列表组
        list_group = QGroupBox("话术列表")
        list_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        group_layout = QVBoxLayout(list_group)
        
        # 搜索框
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("搜索:"))
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入话术名称...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 5px;
                border: 1px solid #dee2e6;
                border-radius: 3px;
            }
        """)
        search_layout.addWidget(self.search_input)
        
        group_layout.addLayout(search_layout)
        
        # 话术列表
        self.script_list = QListWidget()
        self.script_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #dee2e6;
                border-radius: 3px;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f1f3f4;
            }
            QListWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #e9ecef;
            }
        """)
        group_layout.addWidget(self.script_list)
        
        # 列表操作按钮
        list_button_layout = QHBoxLayout()
        
        self.add_script_button = QPushButton("新建")
        self.add_script_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        list_button_layout.addWidget(self.add_script_button)
        
        self.delete_script_button = QPushButton("删除")
        self.delete_script_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        self.delete_script_button.setEnabled(False)
        list_button_layout.addWidget(self.delete_script_button)
        
        list_button_layout.addStretch()
        group_layout.addLayout(list_button_layout)
        
        list_layout.addWidget(list_group)
        parent.addWidget(list_widget)
        
    def create_script_editor(self, parent):
        """创建话术编辑器"""
        editor_widget = QWidget()
        editor_layout = QVBoxLayout(editor_widget)
        
        # 话术编辑组
        editor_group = QGroupBox("话术编辑")
        editor_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        group_layout = QVBoxLayout(editor_group)
        
        # 话术名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("话术名称:"))
        
        self.script_name_input = QLineEdit()
        self.script_name_input.setPlaceholderText("请输入话术名称...")
        self.script_name_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                font-size: 14px;
            }
        """)
        name_layout.addWidget(self.script_name_input)
        
        group_layout.addLayout(name_layout)
        
        # 话术内容
        group_layout.addWidget(QLabel("话术内容:"))
        
        self.script_content_input = QTextEdit()
        self.script_content_input.setPlaceholderText("请输入话术内容...\n支持多行文本，每行一个话术")
        self.script_content_input.setStyleSheet("""
            QTextEdit {
                border: 1px solid #dee2e6;
                border-radius: 3px;
                padding: 8px;
                font-size: 12px;
                font-family: 'Microsoft YaHei', sans-serif;
            }
        """)
        self.script_content_input.setMinimumHeight(200)
        group_layout.addWidget(self.script_content_input)
        
        # 编辑操作按钮
        edit_button_layout = QHBoxLayout()
        
        self.save_script_button = QPushButton("保存")
        self.save_script_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)
        self.save_script_button.setEnabled(False)
        edit_button_layout.addWidget(self.save_script_button)
        
        self.cancel_edit_button = QPushButton("取消")
        self.cancel_edit_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        self.cancel_edit_button.setEnabled(False)
        edit_button_layout.addWidget(self.cancel_edit_button)
        
        edit_button_layout.addStretch()
        
        # 测试按钮
        self.test_script_button = QPushButton("测试话术")
        self.test_script_button.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        self.test_script_button.setEnabled(False)
        edit_button_layout.addWidget(self.test_script_button)
        
        group_layout.addLayout(edit_button_layout)
        
        editor_layout.addWidget(editor_group)
        
        # 时间段设置组
        time_group = QGroupBox("时间段设置")
        time_group.setStyleSheet(editor_group.styleSheet())
        time_layout = QVBoxLayout(time_group)
        
        # 时间段说明
        time_info = QLabel("可以为话术设置特定的播放时间段，系统会在对应时间自动选择相应话术")
        time_info.setStyleSheet("color: #6c757d; font-style: italic; margin: 5px 0;")
        time_info.setWordWrap(True)
        time_layout.addWidget(time_info)
        
        # 时间段设置按钮
        time_button_layout = QHBoxLayout()
        
        self.add_time_segment_button = QPushButton("添加时间段")
        self.add_time_segment_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        self.add_time_segment_button.setEnabled(False)
        time_button_layout.addWidget(self.add_time_segment_button)
        
        self.manage_time_segments_button = QPushButton("管理时间段")
        self.manage_time_segments_button.setStyleSheet(self.add_time_segment_button.styleSheet())
        self.manage_time_segments_button.setEnabled(False)
        time_button_layout.addWidget(self.manage_time_segments_button)
        
        time_button_layout.addStretch()
        time_layout.addLayout(time_button_layout)
        
        editor_layout.addWidget(time_group)
        
        # 添加弹性空间
        editor_layout.addStretch()
        
        parent.addWidget(editor_widget)
        
    def setup_connections(self):
        """设置信号连接"""
        # 搜索框
        self.search_input.textChanged.connect(self.filter_scripts)
        
        # 列表选择
        self.script_list.currentItemChanged.connect(self.on_script_selected)
        
        # 按钮点击事件
        self.add_script_button.clicked.connect(self.add_new_script)
        self.delete_script_button.clicked.connect(self.delete_script)
        self.save_script_button.clicked.connect(self.save_script)
        self.cancel_edit_button.clicked.connect(self.cancel_edit)
        self.test_script_button.clicked.connect(self.test_script)
        self.add_time_segment_button.clicked.connect(self.add_time_segment)
        self.manage_time_segments_button.clicked.connect(self.manage_time_segments)
        
        # 内容变化
        self.script_name_input.textChanged.connect(self.on_content_changed)
        self.script_content_input.textChanged.connect(self.on_content_changed)
        
    def load_scripts(self):
        """加载话术列表"""
        try:
            # 查询用户的话术
            user_id = self.user_info['id'] if self.user_info else None
            scripts = self.db_manager.execute_query(
                "SELECT id, name, content FROM scripts WHERE user_id = ? OR user_id IS NULL ORDER BY name",
                (user_id,)
            )
            
            self.script_list.clear()
            for script in scripts:
                item = QListWidgetItem(script['name'])
                item.setData(Qt.ItemDataRole.UserRole, {
                    'id': script['id'],
                    'name': script['name'],
                    'content': script['content']
                })
                self.script_list.addItem(item)
                
            self.logger.info(f"加载了 {len(scripts)} 个话术")
            
        except Exception as e:
            self.logger.error(f"加载话术失败: {e}")
            QMessageBox.warning(self, "错误", f"加载话术失败: {str(e)}")
            
    @pyqtSlot(str)
    def filter_scripts(self, text):
        """过滤话术列表"""
        for i in range(self.script_list.count()):
            item = self.script_list.item(i)
            item.setHidden(text.lower() not in item.text().lower())
            
    @pyqtSlot(QListWidgetItem, QListWidgetItem)
    def on_script_selected(self, current, previous):
        """话术选择事件"""
        if current:
            script_data = current.data(Qt.ItemDataRole.UserRole)
            self.current_script_id = script_data['id']
            
            # 填充编辑器
            self.script_name_input.setText(script_data['name'])
            self.script_content_input.setPlainText(script_data['content'] or "")
            
            # 启用相关按钮
            self.delete_script_button.setEnabled(True)
            self.test_script_button.setEnabled(True)
            self.add_time_segment_button.setEnabled(True)
            self.manage_time_segments_button.setEnabled(True)
            
            self.logger.debug(f"选择话术: {script_data['name']}")
        else:
            self.current_script_id = None
            self.clear_editor()
            
    def clear_editor(self):
        """清空编辑器"""
        self.script_name_input.clear()
        self.script_content_input.clear()
        self.delete_script_button.setEnabled(False)
        self.save_script_button.setEnabled(False)
        self.cancel_edit_button.setEnabled(False)
        self.test_script_button.setEnabled(False)
        self.add_time_segment_button.setEnabled(False)
        self.manage_time_segments_button.setEnabled(False)
        
    @pyqtSlot()
    def on_content_changed(self):
        """内容变化事件"""
        has_content = bool(self.script_name_input.text().strip())
        self.save_script_button.setEnabled(has_content)
        self.cancel_edit_button.setEnabled(has_content)
        
    @pyqtSlot()
    def add_new_script(self):
        """添加新话术"""
        name, ok = QInputDialog.getText(self, "新建话术", "请输入话术名称:")
        if ok and name.strip():
            try:
                user_id = self.user_info['id'] if self.user_info else None
                script_id = self.db_manager.execute_insert(
                    "INSERT INTO scripts (name, content, user_id) VALUES (?, ?, ?)",
                    (name.strip(), "", user_id)
                )
                
                if script_id:
                    # 添加到列表
                    item = QListWidgetItem(name.strip())
                    item.setData(Qt.ItemDataRole.UserRole, {
                        'id': script_id,
                        'name': name.strip(),
                        'content': ""
                    })
                    self.script_list.addItem(item)
                    self.script_list.setCurrentItem(item)
                    
                    self.logger.info(f"创建新话术: {name.strip()}")
                    QMessageBox.information(self, "成功", "话术创建成功")
                else:
                    QMessageBox.warning(self, "错误", "话术创建失败")
                    
            except Exception as e:
                self.logger.error(f"创建话术失败: {e}")
                QMessageBox.warning(self, "错误", f"创建话术失败: {str(e)}")
                
    @pyqtSlot()
    def delete_script(self):
        """删除话术"""
        if not self.current_script_id:
            return
            
        current_item = self.script_list.currentItem()
        if not current_item:
            return
            
        script_name = current_item.text()
        reply = QMessageBox.question(
            self, "确认删除", f"确定要删除话术 '{script_name}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 删除数据库记录
                self.db_manager.execute_update(
                    "DELETE FROM scripts WHERE id = ?",
                    (self.current_script_id,)
                )
                
                # 删除时间段
                self.db_manager.execute_update(
                    "DELETE FROM time_segments WHERE script_id = ?",
                    (self.current_script_id,)
                )
                
                # 从列表中移除
                row = self.script_list.row(current_item)
                self.script_list.takeItem(row)
                
                self.logger.info(f"删除话术: {script_name}")
                QMessageBox.information(self, "成功", "话术删除成功")
                
            except Exception as e:
                self.logger.error(f"删除话术失败: {e}")
                QMessageBox.warning(self, "错误", f"删除话术失败: {str(e)}")
                
    @pyqtSlot()
    def save_script(self):
        """保存话术"""
        if not self.current_script_id:
            return
            
        name = self.script_name_input.text().strip()
        content = self.script_content_input.toPlainText()
        
        if not name:
            QMessageBox.warning(self, "提示", "请输入话术名称")
            return
            
        try:
            # 更新数据库
            self.db_manager.execute_update(
                "UPDATE scripts SET name = ?, content = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (name, content, self.current_script_id)
            )
            
            # 更新列表项
            current_item = self.script_list.currentItem()
            if current_item:
                current_item.setText(name)
                script_data = current_item.data(Qt.ItemDataRole.UserRole)
                script_data['name'] = name
                script_data['content'] = content
                current_item.setData(Qt.ItemDataRole.UserRole, script_data)
            
            self.save_script_button.setEnabled(False)
            self.cancel_edit_button.setEnabled(False)
            
            self.logger.info(f"保存话术: {name}")
            QMessageBox.information(self, "成功", "话术保存成功")
            
        except Exception as e:
            self.logger.error(f"保存话术失败: {e}")
            QMessageBox.warning(self, "错误", f"保存话术失败: {str(e)}")
            
    @pyqtSlot()
    def cancel_edit(self):
        """取消编辑"""
        if self.current_script_id:
            # 恢复原始内容
            current_item = self.script_list.currentItem()
            if current_item:
                script_data = current_item.data(Qt.ItemDataRole.UserRole)
                self.script_name_input.setText(script_data['name'])
                self.script_content_input.setPlainText(script_data['content'] or "")
        else:
            self.clear_editor()
            
        self.save_script_button.setEnabled(False)
        self.cancel_edit_button.setEnabled(False)
        
    @pyqtSlot()
    def test_script(self):
        """测试话术"""
        content = self.script_content_input.toPlainText().strip()
        if not content:
            QMessageBox.warning(self, "提示", "话术内容为空")
            return
            
        # 随机选择一行进行测试
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        if lines:
            import random
            test_line = random.choice(lines)
            QMessageBox.information(self, "测试话术", f"随机选择的话术:\n\n{test_line}")
        else:
            QMessageBox.warning(self, "提示", "没有有效的话术内容")
            
    @pyqtSlot()
    def add_time_segment(self):
        """添加时间段"""
        QMessageBox.information(self, "提示", "时间段设置功能待实现")
        
    @pyqtSlot()
    def manage_time_segments(self):
        """管理时间段"""
        QMessageBox.information(self, "提示", "时间段管理功能待实现")
