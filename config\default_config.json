{"app": {"name": "AI Broadcaster v2", "version": "2.0.0", "debug": false, "log_level": "INFO", "language": "zh-CN"}, "audio": {"input_device": null, "output_device": null, "sample_rate": 44100, "chunk_size": 1024, "channels": 1, "format": "int16", "voice": {"language": "zh-CN", "speed": 1.0, "volume": 0.8, "pitch": 0.0}}, "ai": {"primary_provider": "openai", "openai": {"model": "gpt-4", "api_key": "", "max_tokens": 2000, "temperature": 0.7, "base_url": "https://api.openai.com/v1"}, "anthropic": {"model": "claude-3-sonnet-20240229", "api_key": "", "max_tokens": 2000, "temperature": 0.7}, "local": {"model_path": "", "device": "auto"}}, "broadcast": {"auto_start": false, "interval_seconds": 30, "max_duration_minutes": 60, "content": {"topics": ["technology", "ai", "programming"], "style": "conversational", "language": "chinese", "max_length": 500}}, "ui": {"theme": "default", "window": {"width": 1200, "height": 800, "resizable": true, "center": true}, "auto_save": true, "auto_save_interval": 300}, "network": {"api_port": 8000, "websocket_port": 8001, "cors_origins": ["http://localhost:3000"], "timeout": 30, "retry_attempts": 3}, "storage": {"data_dir": "data", "audio_dir": "data/audio", "logs_dir": "logs", "temp_dir": "data/temp", "cache_dir": "data/cache", "backup_dir": "data/backup"}, "security": {"api_key_required": false, "rate_limit_per_minute": 60, "max_file_size_mb": 100}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "date_format": "%Y-%m-%d %H:%M:%S", "file_logging": true, "console_logging": true, "max_file_size_mb": 10, "backup_count": 5}}