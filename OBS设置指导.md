# OBS设置指导 - 双主视频管理器

## 概述

本指导将帮助您正确配置OBS Studio，以便与双主视频管理器配合使用，实现无缝视频切换功能。

## 步骤1: 安装和启动OBS Studio

### 下载安装
1. 访问 [OBS Studio官网](https://obsproject.com/)
2. 下载适合您操作系统的版本
3. 安装OBS Studio（建议版本28.0或更高）

### 启动OBS
1. 启动OBS Studio
2. 如果是首次使用，可以跳过自动配置向导
3. 确保OBS正常运行

## 步骤2: 启用WebSocket服务器

### 打开WebSocket设置
1. 在OBS中点击菜单栏的 **工具**
2. 选择 **WebSocket服务器设置**

### 配置WebSocket服务器
1. **勾选** "启用WebSocket服务器"
2. **服务器端口**: 保持默认的 `4455`
3. **服务器密码**: 
   - 可以留空（推荐用于测试）
   - 或设置一个密码（用于生产环境）
4. 点击 **确定** 保存设置

### 验证设置
- 在OBS底部状态栏应该显示WebSocket服务器状态
- 如果显示绿色图标，表示服务器已启用

## 步骤3: 创建场景和视频源

### 创建或选择场景
1. 在 **场景** 面板中，确保有一个活动场景
2. 如果没有，点击 **+** 创建新场景
3. 给场景起一个有意义的名称，如"双主视频场景"

### 添加第一个视频源
1. 在 **来源** 面板中，点击 **+**
2. 选择 **媒体源**
3. 创建新的媒体源，命名为 `主视频源A`
4. 在属性中：
   - **本地文件**: 勾选
   - **本地文件路径**: 选择一个视频文件
   - **循环**: 勾选（用于测试）
   - **重启播放当媒体结束时**: 勾选
5. 点击 **确定**

### 添加第二个视频源
1. 重复上述步骤，创建第二个媒体源
2. 命名为 `主视频源B`
3. 选择另一个视频文件
4. 确保两个视频源都在同一个场景中

### 调整视频源位置
1. 在预览窗口中调整两个视频源的位置
2. 可以让它们重叠，因为程序会控制显示/隐藏
3. 确保两个视频源的大小和位置合适

## 步骤4: 测试视频源

### 播放测试
1. 右键点击 `主视频源A`，选择 **重启**
2. 确保视频开始播放
3. 重复测试 `主视频源B`

### 显示/隐藏测试
1. 点击视频源旁边的 **眼睛图标** 来隐藏/显示
2. 确保两个视频源都可以正常显示和隐藏
3. 这模拟了程序的自动切换功能

## 步骤5: 运行测试程序

### 运行示例程序
```bash
python dual_video_example.py
```

### 程序交互
1. 程序会检查OBS连接状态
2. 如果连接成功，会显示可用的媒体源
3. 输入您创建的视频源名称：
   - 视频源A: `主视频源A`
   - 视频源B: `主视频源B`
4. 程序将开始监控和演示切换功能

## 故障排除

### 连接问题

**问题**: 程序显示"OBS连接失败"
**解决方案**:
1. 确认OBS已启动
2. 检查WebSocket服务器是否已启用
3. 确认端口号为4455
4. 检查防火墙设置
5. 尝试重启OBS

**问题**: 找不到视频源
**解决方案**:
1. 确认视频源名称拼写正确
2. 检查视频源是否在当前激活的场景中
3. 确认视频源类型为"媒体源"

### 播放问题

**问题**: 视频不播放
**解决方案**:
1. 检查视频文件路径是否正确
2. 确认视频文件格式被OBS支持
3. 尝试重启媒体源
4. 检查视频文件是否损坏

**问题**: 切换不流畅
**解决方案**:
1. 确保两个视频源都已预加载
2. 检查计算机性能
3. 调整视频质量设置
4. 确保视频文件在本地存储

### 性能优化

**建议设置**:
1. **视频格式**: 使用MP4格式，H.264编码
2. **分辨率**: 统一两个视频的分辨率
3. **帧率**: 使用相同的帧率
4. **硬件加速**: 在OBS设置中启用硬件加速

## 高级配置

### 自定义端口
如果需要使用非默认端口：
1. 在OBS WebSocket设置中修改端口
2. 在程序中相应修改连接参数

### 密码保护
生产环境建议设置密码：
1. 在OBS WebSocket设置中设置密码
2. 在程序连接时提供密码

### 多场景支持
如果使用多个场景：
1. 确保视频源在正确的场景中
2. 程序会自动检测当前激活的场景

## 测试清单

在运行双主视频管理器之前，请确认：

- [ ] OBS Studio已启动
- [ ] WebSocket服务器已启用（端口4455）
- [ ] 创建了两个媒体源（主视频源A和主视频源B）
- [ ] 两个视频源都在当前场景中
- [ ] 视频文件可以正常播放
- [ ] 可以手动显示/隐藏视频源
- [ ] 防火墙允许端口4455的连接

## 联系支持

如果遇到问题：
1. 查看程序控制台的详细日志
2. 检查OBS日志文件
3. 确认系统要求满足
4. 参考OBS官方文档

完成以上设置后，您就可以体验双主视频管理器的无缝切换功能了！
