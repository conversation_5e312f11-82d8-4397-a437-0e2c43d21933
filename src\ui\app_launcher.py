"""
AI Broadcaster v2 - UI应用程序启动器
管理UI应用程序的启动和生命周期
"""

import sys
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt, pyqtSlot
from PyQt6.QtGui import QIcon

from ..services.logging_service import create_logger, setup_logging
from .login_window import LoginWindow
from .main_window import MainWindow


class UIAppLauncher:
    """UI应用程序启动器"""
    
    def __init__(self):
        self.logger = create_logger("ui_launcher")
        self.app = None
        self.login_window = None
        self.main_window = None
        self.user_info = None
        
    def initialize_app(self):
        """初始化QApplication"""
        try:
            # 创建QApplication实例
            self.app = QApplication(sys.argv)
            
            # 设置应用程序属性
            self.app.setApplicationName("AI Broadcaster v2")
            self.app.setApplicationVersion("2.0.0")
            self.app.setOrganizationName("AI Broadcaster Team")
            
            # 设置应用程序图标（如果有的话）
            # self.app.setWindowIcon(QIcon("resources/icons/app_icon.png"))
            
            # 设置应用程序样式
            self.set_application_style()
            
            self.logger.info("QApplication初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"QApplication初始化失败: {e}")
            return False
            
    def set_application_style(self):
        """设置应用程序样式"""
        try:
            # 设置全局样式表
            style_sheet = """
                QApplication {
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                    font-size: 12px;
                }
                
                QMainWindow {
                    background-color: #f8f9fa;
                }
                
                QWidget {
                    background-color: white;
                }
                
                QMessageBox {
                    background-color: white;
                    font-size: 12px;
                }
                
                QMessageBox QLabel {
                    color: #212529;
                }
                
                QMessageBox QPushButton {
                    background-color: #007bff;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    padding: 8px 16px;
                    font-weight: bold;
                    min-width: 80px;
                }
                
                QMessageBox QPushButton:hover {
                    background-color: #0056b3;
                }
                
                QMessageBox QPushButton:pressed {
                    background-color: #004085;
                }
                
                /* 滚动条样式 */
                QScrollBar:vertical {
                    background-color: #f8f9fa;
                    width: 12px;
                    border-radius: 6px;
                }
                
                QScrollBar::handle:vertical {
                    background-color: #dee2e6;
                    border-radius: 6px;
                    min-height: 20px;
                }
                
                QScrollBar::handle:vertical:hover {
                    background-color: #adb5bd;
                }
                
                QScrollBar::add-line:vertical,
                QScrollBar::sub-line:vertical {
                    border: none;
                    background: none;
                }
                
                QScrollBar:horizontal {
                    background-color: #f8f9fa;
                    height: 12px;
                    border-radius: 6px;
                }
                
                QScrollBar::handle:horizontal {
                    background-color: #dee2e6;
                    border-radius: 6px;
                    min-width: 20px;
                }
                
                QScrollBar::handle:horizontal:hover {
                    background-color: #adb5bd;
                }
                
                QScrollBar::add-line:horizontal,
                QScrollBar::sub-line:horizontal {
                    border: none;
                    background: none;
                }
            """
            
            self.app.setStyleSheet(style_sheet)
            self.logger.debug("应用程序样式设置完成")
            
        except Exception as e:
            self.logger.warning(f"设置应用程序样式失败: {e}")
            
    def show_login_window(self):
        """显示登录窗口"""
        try:
            self.login_window = LoginWindow()
            
            # 连接登录成功信号
            self.login_window.login_success.connect(self.on_login_success)
            
            # 显示登录窗口
            self.login_window.show()
            
            self.logger.info("登录窗口已显示")
            
        except Exception as e:
            self.logger.error(f"显示登录窗口失败: {e}")
            QMessageBox.critical(None, "错误", f"无法显示登录窗口: {str(e)}")
            
    @pyqtSlot(object)
    def on_login_success(self, user_info):
        """登录成功处理"""
        try:
            self.user_info = user_info
            self.logger.info(f"用户登录成功: {user_info['username']}")
            
            # 显示主窗口
            self.show_main_window()
            
        except Exception as e:
            self.logger.error(f"登录成功处理失败: {e}")
            QMessageBox.critical(None, "错误", f"登录后处理失败: {str(e)}")
            
    def show_main_window(self):
        """显示主窗口"""
        try:
            self.main_window = MainWindow(self.user_info)
            
            # 显示主窗口
            self.main_window.show()
            
            # 关闭登录窗口
            if self.login_window:
                self.login_window.close()
                self.login_window = None
                
            self.logger.info("主窗口已显示")
            
        except Exception as e:
            self.logger.error(f"显示主窗口失败: {e}")
            QMessageBox.critical(None, "错误", f"无法显示主窗口: {str(e)}")
            
    def run(self):
        """运行应用程序"""
        try:
            if not self.initialize_app():
                return 1
                
            # 显示登录窗口
            self.show_login_window()
            
            # 运行事件循环
            exit_code = self.app.exec()
            
            self.logger.info(f"应用程序退出，退出码: {exit_code}")
            return exit_code
            
        except Exception as e:
            self.logger.error(f"运行应用程序失败: {e}")
            if self.app:
                QMessageBox.critical(None, "严重错误", f"应用程序运行失败: {str(e)}")
            return 1
            
        finally:
            self.cleanup()
            
    def cleanup(self):
        """清理资源"""
        try:
            # 关闭窗口
            if self.main_window:
                self.main_window.close()
                self.main_window = None
                
            if self.login_window:
                self.login_window.close()
                self.login_window = None
                
            # 清理应用程序
            if self.app:
                self.app.quit()
                self.app = None
                
            self.logger.info("资源清理完成")
            
        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")


def launch_ui_app():
    """启动UI应用程序的便捷函数"""
    # 初始化日志系统
    setup_logging()
    
    # 创建并运行应用程序
    launcher = UIAppLauncher()
    return launcher.run()


if __name__ == "__main__":
    exit_code = launch_ui_app()
    sys.exit(exit_code)
