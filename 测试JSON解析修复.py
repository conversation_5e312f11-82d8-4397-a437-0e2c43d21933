#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON解析修复
"""

import json

# 模拟从服务器获取的内容（包含换行符和空格）
test_content = """{
  "0秒 - 10秒": "1***哈喽，大家好，首先感谢【大哥|哥哥|宝子】们的捧场。\\n2***稍作停留也是爱，所能接触的都是这四个大字，随便那个都是这四个大字。\\n3***没有花里胡哨，没有弯弯绕绕。\\n4***有啥就问，新来的家人们，不要藏着掖着。\\n5***云想衣裳花想容，春风拂槛露华浓，仙侠界的白莲花，花容月貌顶呱呱。",
  "10秒 - 20秒": "6***自古套路得人心，但唯有真心得天下，咱们心不要慌，手不要抖，跟着感觉走。\\n7***新来的家人们，相遇就是缘分，不要犹豫不要徘徊。\\n8***你们的到来蓬荜生辉，老妹我深感荣幸\\n9***随便什么数字都是这四个大字，目光所及都是这四个大字。\\n10***俗话说的好，大哥们，人生不主动，快乐少一半",
  "20秒 - 30秒": "11***相信自己的眼睛，相信自己的耳朵\\n12***斩青丝斩难过斩断红尘不为过，建模美姿势帅，呼风唤雨惹人爱\\n13***不穿摸，不贴片，仙侠界的彭于晏，（千人捧万人追）手游界的刘亦菲\\n14***播播间人很多，主播一个人一张嘴，忙不过来，理解下，大哥们\\n15***万水千山总是情，今天主播最热情",
  "30秒 - 40秒": "16***今天只为做人气，玩归玩，闹归闹，不拿真诚开玩笑\\n17***我一说，你一笑，【大哥|哥哥|宝子】们开心比啥都重要。\\n18***落地人上人，只管开心，只管快乐。\\n19***千里姻缘一线牵，欢迎大哥们来到播播间\\n20***山外青山楼外楼，播播间里无忧愁。",
  "40秒 - 50秒": "21***只管开心只管爽，都是这四个大字都是这四个大字\\n22***走过南呀闯过北，主播认识大家不后悔。\\n23***有啥就说有啥就问，相遇就是缘分。人生何处不相逢啊。\\n24***给个面子捧个场，大哥们，不要到处乱跑了\\n25***真真实实，实实在在，简简又单单。",
  "50秒 - 60秒": "26***没有花里胡哨，没有弯弯绕绕，啥啥都是这四个大字。\\n27***相信自己的眼睛，相信自己的耳朵\\n28***老弟正儿八经，诚意满满，爱意满满（今天呢，老弟也是带着诚意来的）\\n29***新来的大哥们，心别慌手别抖，不要着急离开\\n30***万水千山总是情，今天主播最热情"
}"""

print("🧪 测试JSON解析修复")
print("=" * 60)

# 测试1：检查原始内容格式
print("📋 测试1：检查原始内容格式")
stripped_content = test_content.strip()
print(f"内容长度: {len(test_content)}, 去空格后长度: {len(stripped_content)}")
print(f"开头字符: '{stripped_content[:5]}', 结尾字符: '{stripped_content[-5:]}'")
print(f"旧方法检查 - 开头: {stripped_content.startswith('{')}, 结尾: {stripped_content.endswith('}')}")

# 测试2：新的解析方法
print("\n📋 测试2：新的解析方法（直接尝试JSON解析）")
try:
    parsed_data = json.loads(test_content)
    print(f"✅ JSON解析成功！")
    print(f"📊 包含 {len(parsed_data)} 个键")
    print(f"🔍 键列表: {list(parsed_data.keys())}")
    
    # 检查格式类型
    is_new_format = all(isinstance(v, str) for v in parsed_data.values())
    print(f"🎯 格式类型: {'新格式' if is_new_format else '旧格式'}")
    
    if is_new_format:
        print("✅ 这是正确的新格式时间段数据！")
        print("✅ 应该能被解析函数正确处理！")
    
except json.JSONDecodeError as e:
    print(f"❌ JSON解析失败: {e}")

print("\n" + "=" * 60)
print("🎯 结论：新的解析方法可以正确处理包含换行符的JSON内容")
print("🚀 修复应该生效了！")
