# AI Broadcaster v2 🎙️

智能AI广播系统第二版 - 基于Python的现代化AI广播解决方案

## 📋 项目概述

AI Broadcaster v2 是一个功能强大的智能广播系统，集成了先进的AI技术，支持自动内容生成、语音合成和实时广播功能。

## ✨ 主要特性

- 🤖 **智能内容生成**: 基于AI的自动内容创作
- 🎵 **高质量语音合成**: 支持多种语音引擎
- 📡 **实时广播**: 自动化广播流程
- 🔧 **模块化设计**: 易于扩展和维护
- 📊 **完整日志系统**: 详细的运行日志和错误追踪
- ⚙️ **灵活配置**: YAML配置文件支持

## 🏗️ 项目结构

```
ai_broadcaster_v2/
├── main.py                      # 主入口点
├── src/                         # 源代码目录
│   ├── __init__.py
│   ├── app/                     # 应用程序核心
│   │   ├── __init__.py
│   │   ├── application.py       # 主应用程序类
│   │   └── event_bus.py         # 事件总线
│   ├── ui/                      # 用户界面 (待实现)
│   │   └── __init__.py
│   ├── core/                    # 核心业务逻辑 (待实现)
│   │   └── __init__.py
│   ├── data/                    # 数据访问层
│   │   ├── __init__.py
│   │   ├── config_manager.py    # 配置管理器
│   │   └── file_manager.py      # 文件管理器
│   ├── services/                # 基础服务
│   │   ├── __init__.py
│   │   ├── logging_service.py   # 日志服务
│   │   ├── error_handler.py     # 错误处理
│   │   └── network_service.py   # 网络服务
│   └── utils/                   # 工具类
│       ├── __init__.py
│       ├── time_utils.py        # 时间工具
│       ├── file_utils.py        # 文件工具
│       └── validation.py        # 验证工具
├── config/                      # 配置文件目录
│   ├── default_config.json      # 默认配置
│   ├── app_config.json          # 应用配置
│   └── user_config.json         # 用户配置
├── data/                        # 数据文件
├── logs/                        # 日志文件
├── tests/                       # 测试代码
├── docs/                        # 文档
├── resources/                   # 资源文件
├── requirements.txt             # Python依赖
├── run_dev.bat                  # 开发运行脚本
└── README.md                    # 项目说明
```

## 🚀 快速开始

### 1. 环境准备

确保你的系统已安装Python 3.8+：

```bash
python --version
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置设置

编辑 `config/app_config.json` 文件，配置你的AI API密钥和其他设置：

```json
{
  "ai": {
    "openai": {
      "api_key": "your-openai-api-key"
    },
    "anthropic": {
      "api_key": "your-anthropic-api-key"
    }
  }
}
```

### 4. 运行应用

```bash
# 基本启动
python main.py

# 启动并自动开始广播
python main.py --auto-broadcast

# 启动图形界面
python main.py --ui

# 使用自定义配置文件
python main.py --config custom_config.json

# 启用调试模式
python main.py --debug

# 或者使用开发脚本（Windows）
run_dev.bat
```

## 📖 使用说明

### 命令行选项

- `--config, -c`: 指定配置文件路径
- `--auto-broadcast, -a`: 启动后自动开始广播
- `--ui, -u`: 启动图形用户界面
- `--debug, -d`: 启用调试模式
- `--version, -v`: 显示版本信息

### 配置文件

项目使用分层配置系统：

- **default_config.json**: 默认配置模板
- **app_config.json**: 应用程序配置
- **user_config.json**: 用户个人配置

配置项包括：

- **应用设置**: 基本应用信息和调试选项
- **音频设置**: 音频输入输出配置
- **AI引擎**: AI服务提供商配置
- **广播设置**: 广播间隔和内容配置
- **网络设置**: API和WebSocket端口配置
- **UI设置**: 界面主题和窗口配置

### 日志系统

新的日志系统特性：

- 自动日志配置
- 彩色控制台输出
- 文件日志记录（按级别分离）
- 日志轮转和压缩
- 详细的错误追踪

## 🔧 开发指南

### 项目架构

采用分层架构设计：

1. **应用层** (`src/app/`): 应用程序核心和事件总线
2. **服务层** (`src/services/`): 基础服务（日志、错误处理、网络）
3. **数据层** (`src/data/`): 数据访问和配置管理
4. **工具层** (`src/utils/`): 通用工具函数
5. **业务层** (`src/core/`): 核心业务逻辑（待实现）
6. **界面层** (`src/ui/`): 用户界面（待实现）

### 开发阶段

- ✅ **第一阶段**: 基础框架重构（分层架构、配置系统、日志服务）
- 🔄 **第二阶段**: 核心业务逻辑（音频处理、AI引擎）
- 🔄 **第三阶段**: 用户界面开发
- 🔄 **第四阶段**: 高级功能集成
- 🔄 **第五阶段**: 测试和优化
- 🔄 **第六阶段**: 部署和文档

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_broadcaster.py

# 运行测试并显示覆盖率
pytest --cov=src
```

## 📝 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 创建Issue
- 发送邮件
- 提交Pull Request

---

**AI Broadcaster v2** - 让AI为你的声音插上翅膀 🎙️✨
