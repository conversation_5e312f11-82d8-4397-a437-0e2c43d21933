('C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\build\\test_simple\\test_simple.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\build\\test_simple\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\build\\test_simple\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\build\\test_simple\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\build\\test_simple\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\build\\test_simple\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\build\\test_simple\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('test_simple',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\test_simple.py',
   'PYSOURCE'),
  ('python312.dll',
   'D:\\Program Files (x86)\\python32\\python312.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('select.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\sip.cp312-win32.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_cffi_backend.cp312-win32.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win32.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\md.cp312-win32.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'D:\\Program Files (x86)\\python32\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Program Files (x86)\\python32\\python3.dll', 'BINARY'),
  ('sqlite3.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\wrzb\\ai_broadcaster_v2\\build\\test_simple\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
