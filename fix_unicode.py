#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Unicode字符问题的脚本
将所有emoji和特殊Unicode字符替换为ASCII字符
"""

import re
import os

def fix_unicode_in_file(file_path):
    """修复文件中的Unicode字符"""
    print(f"正在处理文件: {file_path}")
    
    # Unicode字符映射表
    unicode_map = {
        '✅': '[OK]',
        '❌': '[ERROR]',
        '⚠️': '[WARNING]',
        '🔄': '[LOADING]',
        '🔧': '[CONFIG]',
        '🔒': '[SECURE]',
        '🎯': '[TARGET]',
        '📋': '[INFO]',
        '📊': '[DATA]',
        '📁': '[FOLDER]',
        '🚀': '[START]',
        '💡': '[TIP]',
        '🎉': '[SUCCESS]',
        '🔍': '[SEARCH]',
        '📤': '[UPLOAD]',
        '📥': '[DOWNLOAD]',
        '🗑️': '[DELETE]',
        '🎨': '[STYLE]',
        '⏰': '[TIME]',
        '🔥': '[HOT]',
        '💾': '[SAVE]',
        '📝': '[EDIT]',
        '🛡️': '[SHIELD]',
        '🔗': '[LINK]',
        '📞': '[PHONE]',
        '💻': '[COMPUTER]',
        '🌐': '[NETWORK]',
        '🎵': '[MUSIC]',
        '🎤': '[MIC]',
        '🔊': '[SOUND]',
        '📺': '[TV]',
        '📱': '[MOBILE]',
        '⭐': '[STAR]',
        '🏆': '[TROPHY]',
        '🎪': '[CIRCUS]',
        '🎭': '[MASK]',
        '🎬': '[MOVIE]',
        '📸': '[CAMERA]',
        '🎮': '[GAME]',
        '🎲': '[DICE]',
        '🃏': '[CARD]',
        '🎯': '[DART]',
        '🏹': '[ARROW]',
        '⚡': '[LIGHTNING]',
        '🌟': '[SHINE]',
        '💫': '[SPARKLE]',
        '🌈': '[RAINBOW]',
        '🔮': '[CRYSTAL]',
        '💎': '[DIAMOND]',
        '👑': '[CROWN]',
        '🏅': '[MEDAL]',
        '🎖️': '[MILITARY]',
        '🏵️': '[ROSETTE]',
        '🎗️': '[RIBBON]',
        '🎀': '[BOW]',
        '🎁': '[GIFT]',
        '🎊': '[CONFETTI]',
        '🎈': '[BALLOON]',
        '🎂': '[CAKE]',
        '🍰': '[DESSERT]',
        '🧁': '[CUPCAKE]',
        '🍭': '[CANDY]',
        '🍬': '[SWEET]',
        '🍫': '[CHOCOLATE]',
        '🍪': '[COOKIE]',
        '🥇': '[GOLD]',
        '🥈': '[SILVER]',
        '🥉': '[BRONZE]',
    }
    
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 记录原始内容长度
        original_length = len(content)
        
        # 替换Unicode字符
        replaced_count = 0
        for unicode_char, ascii_replacement in unicode_map.items():
            if unicode_char in content:
                count = content.count(unicode_char)
                content = content.replace(unicode_char, ascii_replacement)
                replaced_count += count
                print(f"  替换 '{unicode_char}' -> '{ascii_replacement}' ({count}次)")
        
        # 写回文件
        if replaced_count > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  完成! 共替换 {replaced_count} 个Unicode字符")
        else:
            print(f"  无需替换")
            
        return replaced_count
        
    except Exception as e:
        print(f"  错误: {e}")
        return 0

def main():
    """主函数"""
    print("Unicode字符修复工具")
    print("=" * 40)
    
    # 需要处理的文件
    files_to_fix = [
        'run_gui_qt5.py',
        'build_exe.py',
        'debug_build.py'
    ]
    
    total_replaced = 0
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            replaced = fix_unicode_in_file(file_path)
            total_replaced += replaced
        else:
            print(f"文件不存在: {file_path}")
    
    print("\n" + "=" * 40)
    print(f"修复完成! 总共替换了 {total_replaced} 个Unicode字符")
    print("现在可以重新打包程序了")

if __name__ == "__main__":
    main()
