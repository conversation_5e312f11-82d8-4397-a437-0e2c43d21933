#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI主播系统调试打包脚本
用于诊断exe无法打开的问题
"""

import os
import sys
import subprocess
from pathlib import Path

def create_debug_spec():
    """创建调试版本的PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 调试版本：只添加图标文件
added_files = [
    ('简生活图标.ico', '.'),
]

# 基础隐藏导入
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.QtMultimedia',
    'requests',
    'json',
    'sqlite3',
    'threading',
    'queue',
    'time',
    'datetime',
    'random',
    'os',
    'sys',
    'pathlib',
]

a = Analysis(
    ['run_gui_qt5.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AI主播系统_调试版',
    debug=True,  # [CONFIG] 启用调试模式
    bootloader_ignore_signals=False,
    strip=False,  # [CONFIG] 保留调试符号
    upx=False,   # [CONFIG] 禁用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # [CONFIG] 显示控制台窗口，便于查看错误信息
    disable_windowed_traceback=False,  # [CONFIG] 启用错误回溯
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='简生活图标.ico'
)
'''
    
    with open('AI主播系统_调试版.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("[OK] 创建调试版规格文件成功: AI主播系统_调试版.spec")

def build_debug_executable():
    """构建调试版可执行文件"""
    print("[LOADING] 开始构建调试版可执行文件...")
    
    try:
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "AI主播系统_调试版.spec"]
        
        print(f"[LOADING] 执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("[OK] 调试版构建成功!")
            print("[FOLDER] 可执行文件位置: dist/AI主播系统_调试版.exe")
            return True
        else:
            print(f"[ERROR] 调试版构建失败:")
            print(f"错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"[ERROR] 构建过程出错: {e}")
        return False

def test_debug_exe():
    """测试调试版exe文件"""
    print("[LOADING] 测试调试版exe文件...")
    
    exe_path = Path("dist/AI主播系统_调试版.exe")
    if not exe_path.exists():
        print("[ERROR] 调试版exe文件不存在")
        return False
    
    try:
        print("[START] 启动调试版程序...")
        print("[TIP] 注意：调试版会显示控制台窗口，可以看到详细的错误信息")
        
        # 启动程序并等待一段时间
        process = subprocess.Popen([str(exe_path)], 
                                 cwd=exe_path.parent,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE,
                                 text=True)
        
        # 等待5秒看是否有错误
        try:
            stdout, stderr = process.communicate(timeout=5)
            if process.returncode != 0:
                print(f"[ERROR] 程序启动失败，返回码: {process.returncode}")
                if stderr:
                    print(f"错误信息: {stderr}")
                return False
        except subprocess.TimeoutExpired:
            # 程序还在运行，这是好事
            print("[OK] 程序启动成功，正在运行中...")
            process.terminate()
            return True
            
    except Exception as e:
        print(f"[ERROR] 测试exe文件失败: {e}")
        return False

def main():
    """主函数"""
    print("[CONFIG] AI主播系统调试打包工具")
    print("=" * 40)
    
    # 检查主程序文件
    if not os.path.exists("run_gui_qt5.py"):
        print("[ERROR] 未找到主程序文件 run_gui_qt5.py")
        return False
    
    # 清理之前的调试构建文件
    debug_files = ["AI主播系统_调试版.spec"]
    for file_name in debug_files:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"[LOADING] 删除旧调试文件: {file_name}")
    
    # 创建调试版规格文件
    create_debug_spec()
    
    # 构建调试版可执行文件
    if not build_debug_executable():
        return False
    
    # 测试调试版exe文件
    test_debug_exe()
    
    print("\n" + "=" * 40)
    print("[CONFIG] 调试版打包完成!")
    print("[FOLDER] 调试版文件: dist/AI主播系统_调试版.exe")
    print("\n[TIP] 调试提示:")
    print("- 调试版会显示控制台窗口")
    print("- 可以看到详细的错误信息和日志")
    print("- 如果程序无法启动，控制台会显示具体错误")
    print("- 调试版文件较大，仅用于问题诊断")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n按回车键退出...")
        else:
            input("\n调试打包失败，按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
    except Exception as e:
        print(f"\n[ERROR] 调试打包过程出现异常: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
