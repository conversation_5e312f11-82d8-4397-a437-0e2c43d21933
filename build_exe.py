#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI主播系统打包脚本
使用PyInstaller将程序打包成独立可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"[OK] PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("[ERROR] PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("[LOADING] 正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("[OK] PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] PyInstaller安装失败: {e}")
        return False

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 添加数据文件 - 只包含必要的资源文件，不包含源码和配置文件
added_files = [
    ('简生活图标.ico', '.'),  # [CONFIG] 图标文件作为资源内嵌到exe中
    # [SECURE] 不包含以下文件，避免源码泄露和配置冲突：
    # - src目录（源码）
    # - config.json（配置文件）
    # - *.db（数据库文件）
    # - *.log（日志文件）
    # - __pycache__（缓存目录）
    # - *.pyc（编译缓存）
]

# 隐藏导入的模块
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.QtMultimedia',
    'PyQt5.QtNetwork',
    'requests',
    'json',
    'sqlite3',
    'threading',
    'queue',
    'time',
    'datetime',
    'random',
    'os',
    'sys',
    'pathlib',
    'urllib3',
    'certifi',
    'charset_normalizer',
    'idna',
    'websocket',
    'websocket-client',
    'obswebsocket',
    'cv2',
    'numpy',
    'PIL',
    'Pillow',
]

a = Analysis(
    ['run_gui_qt5.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # [SECURE] 排除不需要的模块，减少文件大小
        'tkinter',
        'matplotlib',
        'scipy',
        'pandas',
        'jupyter',
        'IPython',
        'notebook',
        'test',
        'tests',
        'unittest',
        'doctest',
        'pydoc',
        'email',
        'xml.etree',
        'xml.dom',
        'xml.sax',
        'distutils',
        'setuptools',
        'pip',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AI主播系统',
    debug=False,  # [SECURE] 关闭调试模式，避免泄露信息
    bootloader_ignore_signals=False,
    strip=True,  # [SECURE] 启用strip，移除调试信息
    upx=True,  # [SECURE] 启用UPX压缩，进一步保护代码
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # [SECURE] 不显示控制台窗口，避免泄露调试信息
    disable_windowed_traceback=True,  # [SECURE] 禁用窗口化回溯，避免泄露代码信息
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='简生活图标.ico'  # 设置程序图标
)
'''
    
    with open('AI主播系统.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("[OK] 创建规格文件成功: AI主播系统.spec")

def build_executable():
    """构建可执行文件"""
    print("[LOADING] 开始构建可执行文件...")
    
    try:
        # 使用spec文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "AI主播系统.spec"]
        
        print(f"[LOADING] 执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("[OK] 构建成功!")
            print("[FOLDER] 可执行文件位置: dist/AI主播系统.exe")
            return True
        else:
            print(f"[ERROR] 构建失败:")
            print(f"错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"[ERROR] 构建过程出错: {e}")
        return False

def copy_resources():
    """复制必要的资源文件到dist目录 - 不包含源码文件"""
    print("[LOADING] 复制资源文件...")

    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("[ERROR] dist目录不存在")
        return False

    # [SECURE] 安全：只复制必要的非源码文件
    resources = [
        # 图标文件已内嵌到exe中，不需要作为外部文件复制
        # 注意：不复制src目录和.py文件，避免源码泄露
        # "src",  # 已注释掉
        # "run_gui_qt5.py",  # 已注释掉
    ]

    for resource in resources:
        src_path = Path(resource)
        if src_path.exists():
            dst_path = dist_dir / resource

            try:
                if src_path.is_file():
                    shutil.copy2(src_path, dst_path)
                    print(f"[OK] 复制文件: {resource}")
                elif src_path.is_dir():
                    if dst_path.exists():
                        shutil.rmtree(dst_path)
                    shutil.copytree(src_path, dst_path)
                    print(f"[OK] 复制目录: {resource}")
            except Exception as e:
                print(f"[WARNING] 复制 {resource} 失败: {e}")
        else:
            print(f"[WARNING] 资源文件不存在: {resource}")

    return True

def clean_source_files():
    """[SECURE] 安全清理：删除dist目录中可能泄露的源码文件和配置文件"""
    print("[SECURE] 清理源码文件和配置文件...")

    dist_dir = Path("dist")
    if not dist_dir.exists():
        return True

    # 需要删除的源码文件扩展名
    source_extensions = ['.py', '.pyc', '.pyo', '.pyw']

    # 需要删除的配置和缓存文件扩展名
    config_extensions = ['.db', '.log', '.json', '.ini', '.cfg', '.conf']

    # 需要删除的源码目录
    source_dirs = ['src', '__pycache__', 'build']

    # 需要删除的特定文件名（配置文件）
    config_files = [
        'config.json', 'settings.json', 'user_config.json',
        'database.db', 'app.db', 'user.db',
        'app.log', 'error.log', 'debug.log',
        'cache.db', 'temp.db'
    ]

    deleted_count = 0

    # 删除源码文件
    for file_path in dist_dir.rglob('*'):
        if file_path.is_file():
            # 删除源码文件
            if file_path.suffix.lower() in source_extensions:
                try:
                    file_path.unlink()
                    print(f"[DELETE] 删除源码文件: {file_path.relative_to(dist_dir)}")
                    deleted_count += 1
                except Exception as e:
                    print(f"[WARNING] 删除文件失败: {file_path} - {e}")

            # 删除配置文件（除了图标文件）
            elif (file_path.suffix.lower() in config_extensions and
                  file_path.name != '简生活图标.ico'):
                try:
                    file_path.unlink()
                    print(f"[DELETE] 删除配置文件: {file_path.relative_to(dist_dir)}")
                    deleted_count += 1
                except Exception as e:
                    print(f"[WARNING] 删除配置文件失败: {file_path} - {e}")

            # 删除特定的配置文件
            elif file_path.name.lower() in [f.lower() for f in config_files]:
                try:
                    file_path.unlink()
                    print(f"[DELETE] 删除配置文件: {file_path.relative_to(dist_dir)}")
                    deleted_count += 1
                except Exception as e:
                    print(f"[WARNING] 删除配置文件失败: {file_path} - {e}")

    # 删除源码目录
    for dir_name in source_dirs:
        dir_path = dist_dir / dir_name
        if dir_path.exists() and dir_path.is_dir():
            try:
                shutil.rmtree(dir_path)
                print(f"[DELETE] 删除源码目录: {dir_name}")
                deleted_count += 1
            except Exception as e:
                print(f"[WARNING] 删除目录失败: {dir_name} - {e}")

    if deleted_count > 0:
        print(f"[OK] 清理完成，删除了 {deleted_count} 个源码/配置文件/目录")
    else:
        print("[OK] 没有发现需要清理的源码/配置文件")

    return True

def create_readme():
    """创建使用说明文件"""
    readme_content = """# AI主播系统 - 独立运行版

## [INFO] 系统要求
- Windows 7/8/10/11 (64位)
- 至少2GB内存
- 至少500MB磁盘空间

## [START] 使用方法
1. 双击 `AI主播系统.exe` 启动程序
2. 首次运行会显示登录界面
3. 输入用户名和密码登录
4. 开始使用AI主播功能

## [FOLDER] 文件说明
- `AI主播系统.exe` - 主程序文件（包含所有功能和内嵌图标）
- `使用说明.txt` - 使用说明文档

## [STYLE] 图标说明
程序图标已完全内嵌到exe文件中：
- [OK] 无需额外的图标文件
- [OK] 程序图标自动显示在任务栏和窗口标题栏
- [OK] 支持高分辨率显示

## [WARNING] 注意事项
1. 首次运行可能需要较长时间加载
2. 请确保网络连接正常
3. 如遇到问题，请检查防火墙设置
4. 建议以管理员权限运行

## [CONFIG] 故障排除
- 如果程序无法启动，请尝试以管理员权限运行
- 如果出现网络错误，请检查网络连接和防火墙设置
- 如果界面显示异常，请检查系统显示设置

## [PHONE] 技术支持
如有问题请联系技术支持。

版本: 2.0.0
构建时间: {build_time}
"""
    
    from datetime import datetime
    build_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    with open("dist/使用说明.txt", "w", encoding="utf-8") as f:
        f.write(readme_content.format(build_time=build_time))
    
    print("[OK] 创建使用说明文件: dist/使用说明.txt")

def verify_no_source_leak():
    """[SECURE] 最终安全验证：确保dist目录中没有源码泄露"""
    print("[SECURE] 进行最终安全验证...")

    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("[OK] dist目录不存在，无需验证")
        return True

    # 检查是否有源码文件
    source_files = []
    for file_path in dist_dir.rglob('*'):
        if file_path.is_file():
            # 检查文件扩展名（排除图标文件）
            if (file_path.suffix.lower() in ['.py', '.pyc', '.pyo', '.pyw'] and
                file_path.name != '简生活图标.ico'):
                source_files.append(file_path)

    if source_files:
        print(f"[ERROR] 发现 {len(source_files)} 个源码文件:")
        for file_path in source_files:
            print(f"  - {file_path.relative_to(dist_dir)}")
        print("[SECURE] 正在删除这些文件...")

        for file_path in source_files:
            try:
                file_path.unlink()
                print(f"[DELETE] 已删除: {file_path.relative_to(dist_dir)}")
            except Exception as e:
                print(f"[ERROR] 删除失败: {file_path} - {e}")

        return False
    else:
        print("[OK] 安全验证通过，未发现源码泄露")
        return True

def main():
    """主函数"""
    print("[START] AI主播系统打包工具")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists("run_gui_qt5.py"):
        print("[ERROR] 未找到主程序文件 run_gui_qt5.py")
        print("请在项目根目录下运行此脚本")
        return False
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            return False
    
    # 清理之前的构建文件
    for dir_name in ["build", "dist", "__pycache__"]:
        if os.path.exists(dir_name):
            print(f"[LOADING] 清理目录: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 删除之前的spec文件
    spec_files = ["AI主播系统.spec"]
    for spec_file in spec_files:
        if os.path.exists(spec_file):
            os.remove(spec_file)
            print(f"[LOADING] 删除旧规格文件: {spec_file}")
    
    # 创建规格文件
    create_spec_file()
    
    # 构建可执行文件
    if not build_executable():
        return False
    
    # 复制资源文件
    copy_resources()

    # [SECURE] 安全清理：删除可能泄露的源码文件
    clean_source_files()

    # 创建使用说明
    create_readme()

    # [SECURE] 最终安全验证
    verify_no_source_leak()

    print("\n" + "=" * 50)
    print("[SUCCESS] 打包完成!")
    print("[FOLDER] 输出目录: dist/")
    print("[START] 可执行文件: dist/AI主播系统.exe")
    print("📖 使用说明: dist/使用说明.txt")
    print("\n[STYLE] 图标状态:")
    print("- [OK] 程序图标已内嵌到exe文件中")
    print("- [OK] 无需额外的图标文件")
    print("\n[TIP] 提示:")
    print("- 可以将整个dist目录复制到其他电脑上运行")
    print("- 首次运行可能需要较长时间加载")
    print("- 建议以管理员权限运行程序")
    print("- 程序图标会自动显示在任务栏和窗口中")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n按回车键退出...")
        else:
            input("\n打包失败，按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
    except Exception as e:
        print(f"\n[ERROR] 打包过程出现异常: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
