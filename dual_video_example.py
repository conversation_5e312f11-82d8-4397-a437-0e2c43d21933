"""
双主视频管理器使用示例
演示如何使用双主视频管理器实现无缝视频切换
"""

import time
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.playback_controller import PlaybackController
from src.services.logging_service import create_logger


def connect_obs(obs_controller, host="localhost", port=4455, password=""):
    """连接OBS WebSocket服务器"""
    try:
        print(f"  - 尝试连接到 {host}:{port}...")

        # 更新连接参数
        obs_controller.host = host
        obs_controller.port = port
        obs_controller.password = password
        obs_controller.websocket_url = f"ws://{host}:{port}"

        # 尝试连接
        success = obs_controller.connect()

        if success:
            print(f"  - 连接成功！")

            # 等待一下让连接稳定
            time.sleep(2)

            # 获取源列表
            sources = obs_controller.get_source_list()
            print(f"  - 找到 {len(sources)} 个媒体源")
            if sources:
                print("  - 可用的媒体源:")
                for i, source in enumerate(sources[:5]):  # 只显示前5个
                    print(f"    {i+1}. {source}")
                if len(sources) > 5:
                    print(f"    ... 还有 {len(sources) - 5} 个源")

            return True
        else:
            print(f"  - 连接失败")
            return False

    except Exception as e:
        print(f"  - 连接异常: {e}")
        return False


def get_user_video_sources():
    """获取用户指定的视频源名称"""
    print("\n🎯 配置视频源:")
    print("请输入您在OBS中的视频源名称（直接回车使用默认值）")

    source_a = input("视频源A名称 [默认: 主视频源A]: ").strip()
    if not source_a:
        source_a = "主视频源A"

    source_b = input("视频源B名称 [默认: 主视频源B]: ").strip()
    if not source_b:
        source_b = "主视频源B"

    print(f"✅ 将使用视频源: A='{source_a}', B='{source_b}'")
    return source_a, source_b


def check_obs_setup():
    """检查OBS设置"""
    print("\n🔍 OBS设置检查:")
    print("请确保以下设置正确:")
    print("1. OBS Studio 已启动")
    print("2. 工具 -> WebSocket服务器设置 -> 启用WebSocket服务器")
    print("3. 服务器端口: 4455 (默认)")
    print("4. 如果设置了密码，请记住密码")
    print("5. 在当前场景中添加了两个媒体源")

    continue_setup = input("\n是否继续连接? (y/n) [默认: y]: ").strip().lower()
    return continue_setup in ['', 'y', 'yes']


def main():
    """主函数"""
    logger = create_logger("dual_video_example")

    print("🎥 双主视频管理器使用示例")
    print("=" * 50)

    try:
        # 1. 创建播放控制器（包含双主视频管理器）
        print("\n📋 步骤1: 初始化播放控制器...")
        controller = PlaybackController()
        print("✅ 播放控制器初始化完成")

        # 1.5. 检查OBS设置
        if not check_obs_setup():
            print("❌ 用户取消操作")
            return

        # 1.6. 尝试连接OBS
        print("\n📋 步骤1.6: 连接OBS...")
        obs_connected = connect_obs(controller.obs_controller)
        if obs_connected:
            print("✅ OBS连接成功")
        else:
            print("⚠️ OBS连接失败，将在模拟模式下运行")
            print("   请确保OBS已启动并启用WebSocket服务器")

        # 2. 设置双主视频源
        print("\n📋 步骤2: 设置双主视频源...")
        if obs_connected:
            # 如果OBS连接成功，让用户选择实际的视频源
            source_a, source_b = get_user_video_sources()
        else:
            # 如果OBS未连接，使用默认名称
            source_a = "主视频源A"
            source_b = "主视频源B"
            print(f"使用默认视频源名称: A={source_a}, B={source_b}")
        
        success = controller.set_dual_video_sources(source_a, source_b)
        if success:
            print(f"✅ 双主视频源设置成功: A={source_a}, B={source_b}")
        else:
            print("❌ 双主视频源设置失败")
            return
        
        # 3. 配置切换参数
        print("\n📋 步骤3: 配置切换参数...")
        controller.set_video_switch_threshold(5.0)  # 提前5秒准备
        controller.set_video_speed_range(0.8, 1.5)  # 变速范围
        print("✅ 切换参数配置完成")
        
        # 4. 设置回调函数
        print("\n📋 步骤4: 设置回调函数...")
        
        def on_source_switched(current, previous):
            print(f"🔄 视频源切换: {previous} -> {current}")
        
        def on_source_prepared(source):
            print(f"⚡ 视频源已准备: {source}")
        
        def on_switch_error(error):
            print(f"❌ 切换错误: {error}")
        
        # 获取双主视频管理器并设置回调
        dual_manager = controller.dual_video_manager
        dual_manager.on_source_switched = on_source_switched
        dual_manager.on_source_prepared = on_source_prepared
        dual_manager.on_switch_error = on_switch_error
        print("✅ 回调函数设置完成")
        
        # 5. 开始监控
        print("\n📋 步骤5: 开始双主视频监控...")
        success = controller.start_dual_video_monitoring()
        if success:
            print("✅ 双主视频监控已开始")
        else:
            print("❌ 双主视频监控启动失败")
        
        # 6. 显示当前状态
        print("\n📋 步骤6: 显示当前状态...")
        display_status(controller, obs_connected)
        
        # 7. 演示单一视频源显示功能
        print("\n📋 步骤7: 演示单一视频源显示功能...")
        demonstrate_single_source_display(controller, obs_connected)

        # 8. 演示手动控制
        print("\n📋 步骤8: 演示手动控制...")
        demonstrate_manual_control(controller)
        
        # 9. 模拟监控运行
        print("\n📋 步骤9: 监控运行演示...")
        if obs_connected:
            simulate_monitoring(controller, duration=30, obs_connected=True)
        else:
            print("⚠️ OBS未连接，跳过实际监控演示")
            print("如果OBS已连接，这里会显示实时的视频播放状态和自动切换")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        logger.error(f"程序异常: {e}")
    finally:
        # 清理资源
        print("\n🧹 清理资源...")
        try:
            controller.stop_dual_video_monitoring()
            controller.close()
            print("✅ 资源清理完成")
        except:
            pass
        
        print("\n👋 程序结束")


def display_status(controller, obs_connected=False):
    """显示当前状态"""
    try:
        # 获取播放控制器状态
        status = controller.get_status()
        dual_video_status = status.get('dual_video_status', {})

        print("📊 当前状态:")
        print(f"  - OBS连接: {'✅ 已连接' if obs_connected else '❌ 未连接'}")
        print(f"  - 视频源A: {dual_video_status.get('video_source_a', 'N/A')}")
        print(f"  - 视频源B: {dual_video_status.get('video_source_b', 'N/A')}")
        print(f"  - 当前激活源: {dual_video_status.get('current_active_source', 'N/A')}")
        print(f"  - 下一个源: {dual_video_status.get('next_source', 'N/A')}")
        print(f"  - 监控状态: {'✅ 运行中' if dual_video_status.get('monitoring', False) else '❌ 未运行'}")
        print(f"  - 切换阈值: {dual_video_status.get('switch_threshold_seconds', 0)}秒")
        print(f"  - 变速范围: {dual_video_status.get('speed_range', (0, 0))}")

        # 获取当前视频状态
        if obs_connected:
            video_status = controller.get_current_video_status()
            if video_status:
                print(f"  - 播放进度: {video_status.get('progress_percent', 0):.1f}%")
                print(f"  - 媒体状态: {video_status.get('media_state', 'N/A')}")
                print(f"  - 当前时间: {video_status.get('current_time_formatted', 'N/A')}")
                print(f"  - 总时长: {video_status.get('total_time_formatted', 'N/A')}")
            else:
                print("  - 视频状态: 无法获取（可能视频源不存在）")
        else:
            print("  - 视频状态: OBS未连接，无法获取实际状态")

    except Exception as e:
        print(f"❌ 获取状态失败: {e}")


def demonstrate_single_source_display(controller, obs_connected=False):
    """演示单一视频源显示功能"""
    try:
        print("🎯 单一视频源显示演示:")

        if obs_connected:
            # 获取当前可见的视频源
            print("  - 检查当前可见的视频源...")
            visible_sources = controller.get_all_visible_video_sources()
            print(f"    当前可见视频源: {visible_sources}")
            print(f"    可见源数量: {len(visible_sources)}")

            # 确保只有一个视频源显示
            print("  - 确保只有一个视频源显示...")
            success = controller.ensure_single_video_source_display()
            print(f"    结果: {'成功' if success else '失败'}")

            # 再次检查
            visible_after = controller.get_all_visible_video_sources()
            print(f"    处理后可见视频源: {visible_after}")
            print(f"    处理后可见源数量: {len(visible_after)}")

            if len(visible_after) <= 1:
                print("    ✅ 验证通过：确保了单一视频源显示")
            else:
                print(f"    ❌ 验证失败：仍有 {len(visible_after)} 个视频源可见")
        else:
            print("  - OBS未连接，跳过实际检测")
            print("  - 在模拟模式下，此功能会确保只有一个媒体源显示")
            print("  - 连接OBS后可以看到真实的视频源管理效果")

    except Exception as e:
        print(f"❌ 单一视频源显示演示失败: {e}")


def demonstrate_manual_control(controller):
    """演示手动控制"""
    try:
        print("🎮 手动控制演示:")
        
        # 手动切换
        print("  - 执行手动切换...")
        success = controller.manual_switch_video()
        print(f"    结果: {'成功' if success else '失败'}")
        
        time.sleep(1)
        
        # 设置视频速度
        print("  - 设置视频源A速度为1.2x...")
        dual_manager = controller.dual_video_manager
        dual_manager.set_source_speed("主视频源A", 1.2)
        
        time.sleep(1)
        
        # 强制切换到指定源
        print("  - 强制切换到视频源B...")
        success = dual_manager.force_switch_to_source("主视频源B")
        print(f"    结果: {'成功' if success else '失败'}")
        
    except Exception as e:
        print(f"❌ 手动控制演示失败: {e}")


def simulate_monitoring(controller, duration=30, obs_connected=False):
    """监控运行演示"""
    try:
        if obs_connected:
            print(f"⏱️ 实时监控运行 {duration} 秒...")
            print("  (按 Ctrl+C 提前结束)")
            print("  监控将显示实际的视频播放状态和自动切换")
        else:
            print(f"⏱️ 模拟监控运行 {duration} 秒...")
            print("  (按 Ctrl+C 提前结束)")
            print("  这是模拟模式，实际连接OBS后会显示真实状态")

        start_time = time.time()
        last_status_time = 0
        switch_count = 0

        while time.time() - start_time < duration:
            current_time = time.time() - start_time

            # 每5秒显示一次状态
            if current_time - last_status_time >= 5:
                print(f"\n⏰ 运行时间: {current_time:.1f}秒")

                if obs_connected:
                    # 获取当前视频状态
                    video_status = controller.get_current_video_status()
                    if video_status:
                        active_source = video_status.get('active_source', 'N/A')
                        progress = video_status.get('progress_percent', 0)
                        state = video_status.get('media_state', 'N/A')
                        current_time_str = video_status.get('current_time_formatted', 'N/A')
                        total_time_str = video_status.get('total_time_formatted', 'N/A')

                        print(f"  📹 当前源: {active_source}")
                        print(f"  📊 进度: {progress:.1f}% ({current_time_str}/{total_time_str})")
                        print(f"  🎬 状态: {state}")

                        # 检查是否接近结束（模拟切换）
                        if progress > 90:
                            print(f"  ⚡ 视频即将结束，准备切换...")
                    else:
                        print("  ❌ 无法获取视频状态（可能视频源不存在或未播放）")
                        print("  💡 提示: 请确保视频源存在且正在播放媒体文件")
                else:
                    # 模拟模式
                    mock_progress = (current_time % 20) * 5  # 每20秒循环一次
                    mock_source = "主视频源A" if (int(current_time) // 20) % 2 == 0 else "主视频源B"
                    print(f"  📹 模拟源: {mock_source}")
                    print(f"  📊 模拟进度: {mock_progress:.1f}%")
                    print(f"  🎬 模拟状态: OBS_MEDIA_STATE_PLAYING")

                    if mock_progress > 90:
                        switch_count += 1
                        print(f"  🔄 模拟切换 #{switch_count}")

                last_status_time = current_time

            time.sleep(0.5)

        print(f"\n✅ 监控演示完成，总运行时间: {duration}秒")
        if not obs_connected:
            print("💡 连接OBS后可以看到真实的视频状态和自动切换效果")

    except KeyboardInterrupt:
        print(f"\n⏹️ 监控演示被用户中断")
    except Exception as e:
        print(f"\n❌ 监控演示异常: {e}")


def show_help():
    """显示帮助信息"""
    print("""
🎥 双主视频管理器使用示例

📋 使用前准备：
1. 启动 OBS Studio
2. 启用 WebSocket 服务器：
   - 工具 -> WebSocket服务器设置
   - 勾选"启用WebSocket服务器"
   - 服务器端口: 4455 (默认)
   - 可选择设置密码
3. 在当前场景中添加两个媒体源（视频文件）
4. 确保媒体源名称易于识别

🚀 运行示例：
python dual_video_example.py

📖 程序功能：
- 自动检测OBS连接状态
- 显示可用的媒体源列表
- 让用户选择实际的视频源名称
- 演示双主视频无缝切换功能
- 确保只有一个视频源处于显示状态
- 实时监控视频播放状态

💡 注意事项：
- 如果OBS未连接，程序会在模拟模式下运行
- 连接OBS后可以看到真实的视频状态和自动切换效果
- 程序支持自定义视频源名称
- 按 Ctrl+C 可以随时中断程序

🔧 故障排除：
- 确保OBS WebSocket服务器已启用
- 检查端口号是否为4455
- 确认视频源在当前场景中且正在播放
- 查看控制台日志获取详细错误信息
""")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help']:
        show_help()
    else:
        main()
