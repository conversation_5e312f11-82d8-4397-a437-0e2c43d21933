"""
AI Broadcaster v2 - 文件管理器
统一的文件操作管理
"""

import json
import shutil
import hashlib
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from datetime import datetime

from ..services.logging_service import create_logger
from ..services.error_handler import handle_exceptions


class FileManager:
    """文件管理器"""
    
    def __init__(self, base_dir: str = "data"):
        self.logger = create_logger("file_manager")
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        self.audio_dir = self.base_dir / "audio"
        self.temp_dir = self.base_dir / "temp"
        self.cache_dir = self.base_dir / "cache"
        self.backup_dir = self.base_dir / "backup"
        
        for dir_path in [self.audio_dir, self.temp_dir, self.cache_dir, self.backup_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    @handle_exceptions("FileManager")
    def save_json(self, data: Dict[str, Any], file_path: Union[str, Path], backup: bool = True) -> bool:
        """
        保存JSON文件
        
        Args:
            data: 要保存的数据
            file_path: 文件路径
            backup: 是否创建备份
            
        Returns:
            是否保存成功
        """
        file_path = Path(file_path)
        
        try:
            # 创建备份
            if backup and file_path.exists():
                self._create_backup(file_path)
            
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.log_success(f"JSON文件保存成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.log_failure(f"JSON文件保存失败 {file_path}: {e}")
            return False
    
    @handle_exceptions("FileManager")
    def load_json(self, file_path: Union[str, Path]) -> Optional[Dict[str, Any]]:
        """
        加载JSON文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            加载的数据
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            self.logger.log_warning(f"JSON文件不存在: {file_path}")
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.debug(f"JSON文件加载成功: {file_path}")
            return data
            
        except Exception as e:
            self.logger.log_failure(f"JSON文件加载失败 {file_path}: {e}")
            return None
    
    @handle_exceptions("FileManager")
    def save_text(self, text: str, file_path: Union[str, Path], encoding: str = 'utf-8') -> bool:
        """
        保存文本文件
        
        Args:
            text: 文本内容
            file_path: 文件路径
            encoding: 编码格式
            
        Returns:
            是否保存成功
        """
        file_path = Path(file_path)
        
        try:
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(text)
            
            self.logger.debug(f"文本文件保存成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.log_failure(f"文本文件保存失败 {file_path}: {e}")
            return False
    
    @handle_exceptions("FileManager")
    def load_text(self, file_path: Union[str, Path], encoding: str = 'utf-8') -> Optional[str]:
        """
        加载文本文件
        
        Args:
            file_path: 文件路径
            encoding: 编码格式
            
        Returns:
            文本内容
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            self.logger.log_warning(f"文本文件不存在: {file_path}")
            return None
        
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            
            self.logger.debug(f"文本文件加载成功: {file_path}")
            return content
            
        except Exception as e:
            self.logger.log_failure(f"文本文件加载失败 {file_path}: {e}")
            return None
    
    @handle_exceptions("FileManager")
    def copy_file(self, src: Union[str, Path], dst: Union[str, Path]) -> bool:
        """
        复制文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            
        Returns:
            是否复制成功
        """
        src_path = Path(src)
        dst_path = Path(dst)
        
        if not src_path.exists():
            self.logger.log_failure(f"源文件不存在: {src_path}")
            return False
        
        try:
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(src_path, dst_path)
            
            self.logger.log_success(f"文件复制成功: {src_path} -> {dst_path}")
            return True
            
        except Exception as e:
            self.logger.log_failure(f"文件复制失败: {e}")
            return False
    
    @handle_exceptions("FileManager")
    def move_file(self, src: Union[str, Path], dst: Union[str, Path]) -> bool:
        """
        移动文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            
        Returns:
            是否移动成功
        """
        src_path = Path(src)
        dst_path = Path(dst)
        
        if not src_path.exists():
            self.logger.log_failure(f"源文件不存在: {src_path}")
            return False
        
        try:
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(src_path), str(dst_path))
            
            self.logger.log_success(f"文件移动成功: {src_path} -> {dst_path}")
            return True
            
        except Exception as e:
            self.logger.log_failure(f"文件移动失败: {e}")
            return False
    
    @handle_exceptions("FileManager")
    def delete_file(self, file_path: Union[str, Path]) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否删除成功
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            self.logger.log_warning(f"文件不存在: {file_path}")
            return True
        
        try:
            file_path.unlink()
            self.logger.log_success(f"文件删除成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.log_failure(f"文件删除失败: {e}")
            return False
    
    def get_file_hash(self, file_path: Union[str, Path], algorithm: str = 'md5') -> Optional[str]:
        """
        获取文件哈希值
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法
            
        Returns:
            文件哈希值
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            return None
        
        try:
            hash_obj = hashlib.new(algorithm)
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
            
        except Exception as e:
            self.logger.log_failure(f"计算文件哈希失败: {e}")
            return None
    
    def _create_backup(self, file_path: Path):
        """创建文件备份"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
        backup_path = self.backup_dir / backup_name
        
        try:
            shutil.copy2(file_path, backup_path)
            self.logger.debug(f"备份创建成功: {backup_path}")
        except Exception as e:
            self.logger.log_warning(f"备份创建失败: {e}")
    
    def list_files(self, directory: Union[str, Path], pattern: str = "*") -> List[Path]:
        """
        列出目录中的文件
        
        Args:
            directory: 目录路径
            pattern: 文件模式
            
        Returns:
            文件路径列表
        """
        directory = Path(directory)
        
        if not directory.exists():
            return []
        
        try:
            return list(directory.glob(pattern))
        except Exception as e:
            self.logger.log_failure(f"列出文件失败: {e}")
            return []
    
    def get_file_info(self, file_path: Union[str, Path]) -> Optional[Dict[str, Any]]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            return None
        
        try:
            stat = file_path.stat()
            return {
                'name': file_path.name,
                'size': stat.st_size,
                'created': datetime.fromtimestamp(stat.st_ctime),
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'is_file': file_path.is_file(),
                'is_dir': file_path.is_dir(),
                'suffix': file_path.suffix,
                'parent': str(file_path.parent)
            }
        except Exception as e:
            self.logger.log_failure(f"获取文件信息失败: {e}")
            return None
