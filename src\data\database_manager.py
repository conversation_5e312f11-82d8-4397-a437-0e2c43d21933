"""
AI Broadcaster v2 - 数据库管理器
SQLite数据库的统一管理
"""

import sqlite3
import threading
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from contextlib import contextmanager

from ..services.logging_service import create_logger
from ..services.error_handler import handle_exceptions


class DatabaseManager:
    """数据库管理器"""

    def __init__(self, db_path: str = "data/ai_broadcaster.db"):
        self.logger = create_logger("database_manager")
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._local = threading.local()

        # 初始化数据库
        self._initialize_database()

    def _get_connection(self) -> sqlite3.Connection:
        """获取线程本地的数据库连接"""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(
                str(self.db_path),
                check_same_thread=False,
                timeout=30.0
            )
            self._local.connection.row_factory = sqlite3.Row
            # 启用外键约束
            self._local.connection.execute("PRAGMA foreign_keys = ON")
        return self._local.connection

    @contextmanager
    def get_cursor(self):
        """获取数据库游标的上下文管理器"""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            yield cursor
            conn.commit()
        except Exception as e:
            conn.rollback()
            self.logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            cursor.close()

    def _initialize_database(self):
        """初始化数据库表结构"""
        try:
            with self.get_cursor() as cursor:
                # 执行数据库迁移
                self._migrate_database(cursor)
                # 用户表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username TEXT UNIQUE NOT NULL,
                        password_hash TEXT NOT NULL DEFAULT '',
                        email TEXT,
                        phone TEXT,
                        nickname TEXT,
                        avatar TEXT,
                        machine_code TEXT,
                        user_type INTEGER DEFAULT 0,
                        status INTEGER DEFAULT 1,
                        balance DECIMAL(10,2) DEFAULT 0.00,
                        trial_days INTEGER DEFAULT 7,
                        expire_at DATETIME,
                        last_login DATETIME,
                        token TEXT,
                        token_expiry DATETIME,
                        expire_time DATETIME,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # AI主播表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS ai_speakers (
                        id INTEGER PRIMARY KEY,
                        name TEXT NOT NULL,
                        description TEXT,
                        voice_id INTEGER,
                        speed_min REAL DEFAULT 0.8,
                        speed_max REAL DEFAULT 1.2,
                        volume REAL DEFAULT 0.8,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 话术表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS scripts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT UNIQUE NOT NULL,
                        content TEXT,
                        category TEXT DEFAULT 'default',
                        tags TEXT,
                        user_id INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                """)

                # 时间段话术表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS time_segments (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        script_id INTEGER NOT NULL,
                        start_time TEXT NOT NULL,
                        end_time TEXT NOT NULL,
                        days_of_week TEXT DEFAULT '1,2,3,4,5,6,7',
                        priority INTEGER DEFAULT 1,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (script_id) REFERENCES scripts (id) ON DELETE CASCADE
                    )
                """)

                # AI对话表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS ai_dialogues (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT UNIQUE NOT NULL,
                        content TEXT,
                        trigger_keywords TEXT,
                        user_id INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                """)

                # 副视频表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS sub_videos (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        file_path TEXT NOT NULL,
                        duration REAL,
                        trigger_keywords TEXT,
                        user_id INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                """)

                # 系统设置表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS system_settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        game_name TEXT,
                        game_type TEXT,
                        prepare_voice_count INTEGER DEFAULT 5,
                        play_interval_min REAL DEFAULT 1.0,
                        play_interval_max REAL DEFAULT 3.0,
                        audio_device TEXT,
                        volume REAL DEFAULT 0.8,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                """)

                # OBS设置表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS obs_settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        host TEXT DEFAULT 'localhost',
                        port INTEGER DEFAULT 4455,
                        password TEXT,
                        video_source_a TEXT,
                        video_source_b TEXT,
                        speed_min REAL DEFAULT 0.8,
                        speed_max REAL DEFAULT 1.2,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                """)

                # 报时设置表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS timing_settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        enabled BOOLEAN DEFAULT FALSE,
                        interval_minutes INTEGER DEFAULT 30,
                        content TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                """)

                # 弹幕设置表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS danmaku_settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        websocket_url TEXT DEFAULT 'ws://127.0.0.1:9999',
                        enabled BOOLEAN DEFAULT FALSE,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                """)

                # 语音文件缓存表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS voice_cache (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        text_hash TEXT UNIQUE NOT NULL,
                        speaker_id INTEGER NOT NULL,
                        speed REAL NOT NULL,
                        file_path TEXT NOT NULL,
                        file_size INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 播放历史表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS play_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        voice_type TEXT NOT NULL,
                        content TEXT NOT NULL,
                        file_path TEXT,
                        played_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                """)

                # 提示词表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS prompts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        content TEXT NOT NULL,
                        category TEXT DEFAULT 'custom',
                        user_id INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                """)

                # 对话历史表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS conversations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_input TEXT NOT NULL,
                        ai_response TEXT NOT NULL,
                        context TEXT,
                        session_id TEXT,
                        user_id INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                """)

                # 刷新令牌表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS refresh_tokens (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        jti TEXT NOT NULL,
                        expires_at DATETIME NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id),
                        UNIQUE(user_id, jti)
                    )
                """)

                # API密钥表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS api_keys (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        api_key TEXT UNIQUE NOT NULL,
                        name TEXT NOT NULL,
                        permissions TEXT,
                        is_active BOOLEAN DEFAULT TRUE,
                        last_used DATETIME,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                """)

                # 支付订单表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS payment_orders (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        order_no TEXT UNIQUE NOT NULL,
                        user_id INTEGER NOT NULL,
                        package_id TEXT NOT NULL,
                        amount DECIMAL(10,2) NOT NULL,
                        payment_method TEXT NOT NULL,
                        status INTEGER DEFAULT 0,
                        package_info TEXT,
                        paid_at DATETIME,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                """)

                # 充值历史表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS recharge_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        order_no TEXT NOT NULL,
                        package_id TEXT NOT NULL,
                        amount DECIMAL(10,2) NOT NULL,
                        days INTEGER NOT NULL,
                        user_type_before INTEGER,
                        user_type_after INTEGER,
                        expire_before DATETIME,
                        expire_after DATETIME,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                """)

                # 敏感词表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS sensitive_words (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        word TEXT UNIQUE NOT NULL,
                        enabled BOOLEAN DEFAULT TRUE,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 弹幕规则表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS danmaku_rules (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        pattern TEXT NOT NULL,
                        action TEXT NOT NULL,
                        response TEXT,
                        priority INTEGER DEFAULT 1,
                        cooldown INTEGER DEFAULT 30,
                        enabled BOOLEAN DEFAULT TRUE,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # 创建索引
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_machine_code ON users(machine_code)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_scripts_name ON scripts(name)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_scripts_category ON scripts(category)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_time_segments_script ON time_segments(script_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_time_segments_time ON time_segments(start_time, end_time)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_voice_cache_hash ON voice_cache(text_hash)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_play_history_user ON play_history(user_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_prompts_category ON prompts(category)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_conversations_user ON conversations(user_id)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_conversations_session ON conversations(session_id)")

            self.logger.log_success("数据库初始化完成")

        except Exception as e:
            self.logger.log_failure(f"数据库初始化失败: {e}")
            raise

    def _migrate_database(self, cursor):
        """执行数据库迁移"""
        try:
            # 检查users表字段
            cursor.execute("PRAGMA table_info(users)")
            user_columns = [column[1] for column in cursor.fetchall()]

            if 'email' not in user_columns:
                cursor.execute("ALTER TABLE users ADD COLUMN email TEXT")
                self.logger.info("添加users.email字段")

            if 'nickname' not in user_columns:
                cursor.execute("ALTER TABLE users ADD COLUMN nickname TEXT")
                self.logger.info("添加users.nickname字段")

            if 'avatar' not in user_columns:
                cursor.execute("ALTER TABLE users ADD COLUMN avatar TEXT")
                self.logger.info("添加users.avatar字段")

            if 'user_type' not in user_columns:
                cursor.execute("ALTER TABLE users ADD COLUMN user_type INTEGER DEFAULT 0")
                self.logger.info("添加users.user_type字段")

            if 'status' not in user_columns:
                cursor.execute("ALTER TABLE users ADD COLUMN status INTEGER DEFAULT 1")
                self.logger.info("添加users.status字段")

            if 'balance' not in user_columns:
                cursor.execute("ALTER TABLE users ADD COLUMN balance DECIMAL(10,2) DEFAULT 0.00")
                self.logger.info("添加users.balance字段")

            if 'trial_days' not in user_columns:
                cursor.execute("ALTER TABLE users ADD COLUMN trial_days INTEGER DEFAULT 7")
                self.logger.info("添加users.trial_days字段")

            if 'expire_at' not in user_columns:
                cursor.execute("ALTER TABLE users ADD COLUMN expire_at DATETIME")
                self.logger.info("添加users.expire_at字段")

            if 'last_login' not in user_columns:
                cursor.execute("ALTER TABLE users ADD COLUMN last_login DATETIME")
                self.logger.info("添加users.last_login字段")

            if 'password_hash' not in user_columns:
                cursor.execute("ALTER TABLE users ADD COLUMN password_hash TEXT NOT NULL DEFAULT ''")
                self.logger.info("添加users.password_hash字段")

            # 检查scripts表是否有category字段
            cursor.execute("PRAGMA table_info(scripts)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'category' not in columns:
                # 添加category字段
                cursor.execute("ALTER TABLE scripts ADD COLUMN category TEXT DEFAULT 'default'")
                self.logger.info("添加scripts.category字段")

            if 'tags' not in columns:
                # 添加tags字段
                cursor.execute("ALTER TABLE scripts ADD COLUMN tags TEXT")
                self.logger.info("添加scripts.tags字段")

            # 检查time_segments表结构
            cursor.execute("PRAGMA table_info(time_segments)")
            time_columns = [column[1] for column in cursor.fetchall()]

            # 如果time_segments表结构不正确，重建它
            if 'days_of_week' not in time_columns or 'priority' not in time_columns:
                # 备份数据
                cursor.execute("CREATE TEMP TABLE time_segments_backup AS SELECT * FROM time_segments")

                # 删除旧表
                cursor.execute("DROP TABLE time_segments")

                # 创建新表
                cursor.execute("""
                    CREATE TABLE time_segments (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        script_id INTEGER NOT NULL,
                        start_time TEXT NOT NULL,
                        end_time TEXT NOT NULL,
                        days_of_week TEXT DEFAULT '1,2,3,4,5,6,7',
                        priority INTEGER DEFAULT 1,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (script_id) REFERENCES scripts (id) ON DELETE CASCADE
                    )
                """)

                self.logger.info("重建time_segments表结构")

        except Exception as e:
            self.logger.warning(f"数据库迁移失败: {e}")
            # 迁移失败不应该阻止数据库初始化

    @handle_exceptions("DatabaseManager")
    def execute_query(self, query: str, params: Tuple = ()) -> List[sqlite3.Row]:
        """执行查询语句"""
        with self.get_cursor() as cursor:
            cursor.execute(query, params)
            return cursor.fetchall()

    @handle_exceptions("DatabaseManager")
    def execute_update(self, query: str, params: Tuple = ()) -> int:
        """执行更新语句，返回影响的行数"""
        with self.get_cursor() as cursor:
            cursor.execute(query, params)
            return cursor.rowcount

    @handle_exceptions("DatabaseManager")
    def execute_insert(self, query: str, params: Tuple = ()) -> int:
        """执行插入语句，返回新插入记录的ID"""
        with self.get_cursor() as cursor:
            cursor.execute(query, params)
            return cursor.lastrowid

    @handle_exceptions("DatabaseManager")
    def execute_many(self, query: str, params_list: List[Tuple]) -> int:
        """批量执行语句"""
        with self.get_cursor() as cursor:
            cursor.executemany(query, params_list)
            return cursor.rowcount

    def close(self):
        """关闭数据库连接"""
        if hasattr(self._local, 'connection'):
            self._local.connection.close()
            delattr(self._local, 'connection')

    def backup_database(self, backup_path: str) -> bool:
        """备份数据库"""
        try:
            import shutil
            backup_file = Path(backup_path)
            backup_file.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(self.db_path, backup_file)
            self.logger.log_success(f"数据库备份完成: {backup_path}")
            return True
        except Exception as e:
            self.logger.log_failure(f"数据库备份失败: {e}")
            return False

    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        try:
            with self.get_cursor() as cursor:
                # 获取表信息
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]

                # 获取数据库大小
                db_size = self.db_path.stat().st_size if self.db_path.exists() else 0

                return {
                    'database_path': str(self.db_path),
                    'database_size': db_size,
                    'tables': tables,
                    'table_count': len(tables)
                }
        except Exception as e:
            self.logger.error(f"获取数据库信息失败: {e}")
            return {}
