#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新的语音文件名生成逻辑
"""

import hashlib
import time
from pathlib import Path

def test_voice_service_filename():
    """测试VoiceService中的新文件名格式"""
    print("🎤 测试VoiceService新文件名格式")
    print("=" * 50)

    # 测试数据
    test_cases = [
        {"text": "你好世界", "speaker_id": 0, "speed": 1.0},
        {"text": "测试语音文件", "speaker_id": 5, "speed": 110},
        {"text": "AI语音合成系统", "speaker_id": 3, "speed": 120},
    ]

    for i, case in enumerate(test_cases, 1):
        print(f"\n测试案例 {i}:")
        print(f"  文本: {case['text']}")
        print(f"  主播ID: {case['speaker_id']}")
        print(f"  语速: {case['speed']}")

        # 🔥 新逻辑：语音内容+时间戳的哈希值
        timestamp = int(time.time() * 1000)
        content_for_hash = f"{case['text']}_{timestamp}"
        text_hash = hashlib.md5(content_for_hash.encode()).hexdigest()[:16]
        filename = f"{text_hash}.wav"

        print(f"  哈希内容: {content_for_hash}")
        print(f"  生成的哈希: {text_hash}")
        print(f"  时间戳: {timestamp}")
        print(f"  最终文件名: {filename}")

        time.sleep(0.002)  # 确保时间戳不同

def test_run_gui_filename():
    """测试run_gui_qt5.py中的新文件名格式"""
    print("\n🖥️ 测试run_gui_qt5.py新文件名格式")
    print("=" * 50)

    # 测试数据
    text = "测试GUI语音文件"
    speaker_id = 5
    speed = 110

    print(f"文本: {text}")
    print(f"主播ID: {speaker_id}")
    print(f"语速: {speed}")

    # 🔥 新逻辑：语音内容+时间戳的哈希值
    timestamp = int(time.time() * 1000)
    content_for_hash = f"{text}_{timestamp}"
    text_hash = hashlib.md5(content_for_hash.encode('utf-8')).hexdigest()[:16]
    filename = f"{text_hash}.wav"

    print(f"哈希内容: {content_for_hash}")
    print(f"生成的哈希: {text_hash}")
    print(f"时间戳: {timestamp}")
    print(f"最终文件名: {filename}")

def test_voice_downloader_filename():
    """测试VoiceDownloader中的新文件名格式"""
    print("\n📥 测试VoiceDownloader新文件名格式")
    print("=" * 50)

    # 测试数据
    text = "测试下载器语音文件"
    speaker_id = 2
    speed = 1.5

    print(f"文本: {text}")
    print(f"主播ID: {speaker_id}")
    print(f"语速: {speed}")

    # 🔥 新逻辑：语音内容+时间戳的哈希值
    timestamp = int(time.time() * 1000)
    content = f"{text}_{timestamp}"
    text_hash = hashlib.md5(content.encode('utf-8')).hexdigest()[:16]
    filename = f"{text_hash}.wav"

    print(f"哈希内容: {content}")
    print(f"生成的哈希: {text_hash}")
    print(f"时间戳: {timestamp}")
    print(f"最终文件名: {filename}")

def compare_old_vs_new():
    """对比旧格式和新格式"""
    print("\n🔄 对比旧格式 vs 新格式")
    print("=" * 50)

    text = "这是一个测试语音文件"
    speaker_id = 5
    speed = 110

    # 旧格式
    old_text_hash = hashlib.md5(text.encode('utf-8')).hexdigest()[:8]
    old_filename = f"{old_text_hash}_{speaker_id}_{speed}.wav"

    # 🔥 新格式：语音内容+时间戳的哈希值
    timestamp = int(time.time() * 1000)
    content_for_hash = f"{text}_{timestamp}"
    new_text_hash = hashlib.md5(content_for_hash.encode('utf-8')).hexdigest()[:16]
    new_filename = f"{new_text_hash}.wav"

    print(f"测试文本: {text}")
    print(f"主播ID: {speaker_id}, 语速: {speed}")
    print()
    print(f"旧格式: {old_filename}")
    print(f"  - 哈希基于: 仅文本内容")
    print(f"  - 哈希长度: {len(old_text_hash)} 位")
    print(f"  - 文件名长度: {len(old_filename)} 字符")
    print(f"  - 包含参数: 是")
    print()
    print(f"新格式: {new_filename}")
    print(f"  - 哈希基于: 文本内容+时间戳")
    print(f"  - 哈希长度: {len(new_text_hash)} 位")
    print(f"  - 文件名长度: {len(new_filename)} 字符")
    print(f"  - 包含时间戳: 内嵌在哈希中")
    print(f"  - 时间戳: {timestamp}")

def test_uniqueness():
    """测试文件名唯一性"""
    print("\n🔒 测试文件名唯一性")
    print("=" * 50)

    text = "相同内容测试"
    speaker_id = 5
    speed = 110

    filenames = []
    for i in range(5):
        # 🔥 新逻辑：语音内容+时间戳的哈希值
        timestamp = int(time.time() * 1000)
        content_for_hash = f"{text}_{timestamp}"
        text_hash = hashlib.md5(content_for_hash.encode('utf-8')).hexdigest()[:16]
        filename = f"{text_hash}.wav"
        filenames.append(filename)
        print(f"  {i+1}. {filename}")
        time.sleep(0.001)  # 确保时间戳不同

    # 检查唯一性
    unique_filenames = set(filenames)
    print(f"\n生成了 {len(filenames)} 个文件名")
    print(f"唯一文件名: {len(unique_filenames)} 个")
    print(f"唯一性: {'✅ 通过' if len(filenames) == len(unique_filenames) else '❌ 失败'}")

if __name__ == "__main__":
    test_voice_service_filename()
    test_run_gui_filename()
    test_voice_downloader_filename()
    compare_old_vs_new()
    test_uniqueness()
    
    print("\n✅ 所有测试完成！")
    print("\n📋 新文件名格式总结:")
    print("  - VoiceService: {16位哈希}.wav")
    print("  - run_gui_qt5: {16位哈希}.wav")
    print("  - VoiceDownloader: {16位哈希}.wav")
    print("  - 哈希基于: 语音内容+时间戳")
    print("  - 时间戳: 毫秒级，内嵌在哈希中")
    print("  - 优势: 文件名更简洁，时间戳内嵌保证唯一性")
