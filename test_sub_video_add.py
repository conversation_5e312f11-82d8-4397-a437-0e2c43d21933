#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试副视频添加功能
验证修复后的OBS视频源获取是否正常
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_sub_video_add():
    """测试副视频添加功能"""
    print("🧪 测试副视频添加功能...")
    
    try:
        # 导入主程序
        from run_gui_qt5 import MainWindow
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建主窗口
        user_info = {
            'username': 'test_user',
            'user_id': 1,
            'expire_time': '2025-12-31'
        }
        window = MainWindow(user_info)
        
        def test_obs_connection():
            """测试OBS连接和视频源获取"""
            print("\n🔍 测试OBS连接状态...")
            
            # 检查OBS控制器
            obs_controllers = []
            
            if hasattr(window, 'obs_controller'):
                obs_controllers.append(('主OBS控制器', window.obs_controller))
            
            if hasattr(window, 'playback_controller') and window.playback_controller:
                obs_controllers.append(('播放控制器OBS', window.playback_controller.obs_controller))
            
            print(f"📋 发现 {len(obs_controllers)} 个OBS控制器")
            
            for name, obs_controller in obs_controllers:
                print(f"\n🔍 检查 {name}:")
                print(f"  - 控制器存在: {obs_controller is not None}")
                
                if obs_controller:
                    connected = getattr(obs_controller, 'connected', False)
                    print(f"  - 连接状态: {connected}")
                    
                    if connected:
                        try:
                            sources = obs_controller.get_source_list()
                            print(f"  - 视频源数量: {len(sources) if sources else 0}")
                            if sources:
                                print(f"  - 视频源列表: {sources[:5]}{'...' if len(sources) > 5 else ''}")
                        except Exception as e:
                            print(f"  - 获取视频源失败: {e}")
                    else:
                        print(f"  - 未连接，无法获取视频源")
            
            # 测试副视频添加对话框的视频源获取逻辑
            print(f"\n🎬 测试副视频添加对话框的视频源获取逻辑...")
            
            # 模拟副视频添加对话框中的逻辑
            obs_controller = None
            
            # 优先使用播放控制器中的OBS控制器
            if hasattr(window, 'playback_controller') and window.playback_controller:
                obs_controller = window.playback_controller.obs_controller
                print(f"  ✅ 使用播放控制器中的OBS控制器")
            elif hasattr(window, 'obs_controller'):
                obs_controller = window.obs_controller
                print(f"  ✅ 使用主OBS控制器")
            
            if obs_controller and hasattr(obs_controller, 'connected') and obs_controller.connected:
                sources = obs_controller.get_source_list()
                if sources:
                    print(f"  ✅ 成功获取到 {len(sources)} 个OBS视频源")
                    print(f"  📋 视频源列表: {sources}")
                    print(f"  🎯 副视频添加对话框应该显示这些视频源")
                else:
                    print(f"  ⚠️ OBS已连接但无可用视频源")
                    print(f"  🎯 副视频添加对话框应该显示'无可用视频源'")
            else:
                print(f"  ❌ OBS未连接，无法获取视频源列表")
                print(f"  🎯 副视频添加对话框应该显示'请先连接OBS'")
            
            # 提供测试建议
            print(f"\n💡 测试建议:")
            print(f"1. 确保OBS已启动并开启WebSocket服务器")
            print(f"2. 在主程序中连接OBS")
            print(f"3. 连接成功后，打开副视频设置")
            print(f"4. 点击'添加副视频'按钮")
            print(f"5. 检查视频源下拉框是否显示OBS中的视频源")
            
            # 退出测试
            QTimer.singleShot(2000, app.quit)
        
        # 等待界面初始化完成后开始测试
        QTimer.singleShot(3000, test_obs_connection)
        
        # 显示窗口
        window.show()
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sub_video_add()
