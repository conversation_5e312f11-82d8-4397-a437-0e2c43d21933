#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试AI回答中的变量替换功能
验证游戏名称和游戏类型是否正确替换
"""

import time
import random
import re

def process_random_text_selection(text):
    """处理随机文本选择【选项1|选项2|选项3】"""
    def replace_random_choice(match):
        choices = match.group(1).split('|')
        return random.choice(choices)
    
    return re.sub(r'【([^】]+)】', replace_random_choice, text)

def process_variables_and_random_text(text, game_name="上古", game_type="仙侠"):
    """
    模拟修复后的变量替换方法
    这是从run_gui_qt5.py第4937-4946行提取的逻辑
    """
    try:
        # 首先处理随机文本选择
        processed_text = process_random_text_selection(text)

        # 然后处理变量替换
        current_time = time.strftime("%H点%M分")
        current_date = time.strftime("%Y-%m-%d")

        # 🔥 修复：获取游戏信息，使用系统设置中的值
        # 这里模拟从界面控件获取的值
        game_name = game_name if game_name else "游戏"
        game_type = game_type if game_type else "游戏"

        # 替换变量
        processed_text = processed_text.replace('{time}', current_time)
        processed_text = processed_text.replace('{date}', current_date)
        processed_text = processed_text.replace('{gamename}', game_name)
        processed_text = processed_text.replace('{gametype}', game_type)  # 🔥 新增：游戏类型变量
        processed_text = processed_text.replace('{nick}', '观众')  # 弹幕用户昵称，这里简化处理

        return processed_text

    except Exception as e:
        print(f"❌ 处理变量和随机文本失败: {e}")
        return text

def test_ai_reply_variable_replacement():
    """测试AI回答中的变量替换"""
    print("🤖 测试AI回答变量替换功能")
    print("=" * 60)
    
    # 模拟AI回答模板（包含变量）
    test_cases = [
        {
            "template": "我是测试视频播放的，看下现在播放的视频是什么？{gamename}是一款{gametype}游戏，现在时间是{time}",
            "description": "包含游戏名称和游戏类型的AI回答"
        },
        {
            "template": "欢迎来到{gamename}的直播间！这是一款{gametype}类型的游戏",
            "description": "欢迎语中的变量替换"
        },
        {
            "template": "【感谢|谢谢】{nick}的提问！{gamename}确实是一款很棒的{gametype}游戏",
            "description": "随机选择+变量替换组合"
        },
        {
            "template": "现在是{time}，我们正在玩{gamename}，这是{gametype}类型的游戏",
            "description": "时间+游戏信息组合"
        },
        {
            "template": "{gamename}这款{gametype}游戏真的很好玩，推荐大家试试！",
            "description": "纯游戏信息变量"
        }
    ]
    
    print("📋 测试用例:")
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. {case['description']}")
        print(f"   模板: {case['template']}")
        
        # 修复前的效果（只有gamename，没有gametype）
        old_result = case['template'].replace('{gamename}', '游戏').replace('{gametype}', '{gametype}').replace('{time}', time.strftime("%H点%M分")).replace('{nick}', '观众')
        print(f"   修复前: {old_result}")
        
        # 修复后的效果
        new_result = process_variables_and_random_text(case['template'])
        print(f"   修复后: {new_result}")
        
        # 验证修复效果
        if '{gametype}' in old_result:
            print(f"   ❌ 修复前：游戏类型变量未替换")
        else:
            print(f"   ⚠️ 修复前：此模板不包含游戏类型变量")
            
        if '{gametype}' not in new_result and '仙侠' in new_result:
            print(f"   ✅ 修复后：游戏类型变量正确替换为'仙侠'")
        elif '{gametype}' in new_result:
            print(f"   ❌ 修复后：游戏类型变量仍未替换")
        else:
            print(f"   ⚠️ 修复后：此结果不包含游戏类型")
            
        if '上古' in new_result:
            print(f"   ✅ 修复后：游戏名称正确替换为'上古'")
        else:
            print(f"   ❌ 修复后：游戏名称未正确替换")

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 60)
    print("🔍 测试边界情况")
    
    edge_cases = [
        {
            "template": "{gametype}{gamename}",
            "description": "连续变量"
        },
        {
            "template": "游戏类型：{gametype}，游戏名称：{gamename}",
            "description": "明确标识的变量"
        },
        {
            "template": "【{gametype}|{gamename}】游戏推荐",
            "description": "随机选择中的变量"
        },
        {
            "template": "",
            "description": "空字符串"
        },
        {
            "template": "没有变量的普通文本",
            "description": "无变量文本"
        }
    ]
    
    for i, case in enumerate(edge_cases, 1):
        print(f"\n{i}. {case['description']}")
        print(f"   输入: '{case['template']}'")
        result = process_variables_and_random_text(case['template'])
        print(f"   输出: '{result}'")
        
        # 检查是否还有未替换的变量
        remaining_vars = []
        if '{gametype}' in result:
            remaining_vars.append('gametype')
        if '{gamename}' in result:
            remaining_vars.append('gamename')
        if '{time}' in result:
            remaining_vars.append('time')
        if '{nick}' in result:
            remaining_vars.append('nick')
            
        if remaining_vars:
            print(f"   ⚠️ 未替换的变量: {remaining_vars}")
        else:
            print(f"   ✅ 所有变量都已正确替换")

if __name__ == "__main__":
    print("🎮 AI回答变量替换测试")
    print("测试修复后的变量替换功能是否正确处理游戏名称和游戏类型")
    
    test_ai_reply_variable_replacement()
    test_edge_cases()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    
    print("\n💡 修复总结：")
    print("1. ✅ 新增了 {gametype} 变量的处理")
    print("2. ✅ 游戏名称 {gamename} 使用系统设置中的值")
    print("3. ✅ 游戏类型 {gametype} 使用系统设置中的值")
    print("4. ✅ 保持了其他变量的正常功能")
    print("5. ✅ 支持随机选择和变量替换的组合")
    
    print("\n🔧 在实际程序中：")
    print("- 游戏名称从 game_name_input.text() 获取，默认值为'游戏'")
    print("- 游戏类型从 game_type_input.text() 获取，默认值为'游戏'")
    print("- 系统设置恢复功能确保界面控件有正确的值")
    print("- AI回答中的变量会正确替换为用户设置的值")
