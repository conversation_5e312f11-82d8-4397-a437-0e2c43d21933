#!/usr/bin/env python3
"""
AI Broadcaster v2 - 新模块测试脚本
测试话术管理和AI对话模块
"""

import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.logging_service import setup_logging, create_logger
from src.data.database_manager import DatabaseManager
from src.core.script.script_manager import ScriptManager
from src.core.script.time_segment_manager import TimeSegmentManager
from src.core.script.script_scheduler import ScriptScheduler
from src.core.ai_chat.chat_manager import ChatManager
from src.core.ai_chat.prompt_manager import PromptManager
from src.core.ai_chat.conversation_history import ConversationHistory


def test_script_management():
    """测试话术管理模块"""
    print("📝 测试话术管理模块...")
    
    try:
        # 创建数据库管理器
        db = DatabaseManager("data/test_new_modules.db")
        
        # 测试话术管理器
        script_manager = ScriptManager(db)
        
        # 创建测试话术
        script_id = script_manager.create_script(
            name="测试话术",
            content="这是第一行话术\n这是第二行话术\n这是第三行话术",
            category="test"
        )
        
        if script_id:
            print(f"  ✅ 创建话术成功: ID {script_id}")
            
            # 获取话术
            script = script_manager.get_script(script_id)
            print(f"  ✅ 获取话术: {script['name']}")
            
            # 获取随机行
            random_line = script_manager.get_random_line(script_id)
            print(f"  ✅ 随机话术行: {random_line}")
            
            # 获取话术统计
            stats = script_manager.get_script_stats()
            print(f"  ✅ 话术统计: 总数 {stats['total_count']}")
            
        else:
            print("  ❌ 创建话术失败")
            return False
            
        return True, db, script_id
        
    except Exception as e:
        print(f"  ❌ 话术管理模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None


def test_time_segment_management(db, script_id):
    """测试时间段管理模块"""
    print("\n⏰ 测试时间段管理模块...")
    
    try:
        time_manager = TimeSegmentManager(db)
        
        # 创建时间段
        segment_id = time_manager.create_time_segment(
            script_id=script_id,
            start_time="09:00",
            end_time="12:00",
            days_of_week="1,2,3,4,5",  # 工作日
            priority=1
        )
        
        if segment_id:
            print(f"  ✅ 创建时间段成功: ID {segment_id}")
            
            # 获取话术的时间段
            segments = time_manager.get_time_segments_by_script(script_id)
            print(f"  ✅ 获取时间段: {len(segments)} 个")
            
            # 获取当前时间的话术
            current_scripts = time_manager.get_active_scripts_for_time()
            print(f"  ✅ 当前时间话术: {len(current_scripts)} 个")
            
            # 获取时间表概览
            schedule = time_manager.get_schedule_overview()
            print(f"  ✅ 时间表概览: {len(schedule)} 天")
            
        else:
            print("  ❌ 创建时间段失败")
            return False
            
        return True
        
    except Exception as e:
        print(f"  ❌ 时间段管理模块测试失败: {e}")
        return False


def test_script_scheduler(db):
    """测试话术调度器"""
    print("\n🎯 测试话术调度器...")
    
    try:
        scheduler = ScriptScheduler(db)
        
        # 获取当前话术
        current_script = scheduler.get_current_script()
        if current_script:
            print(f"  ✅ 当前话术: {current_script['name']}")
        else:
            print("  ℹ️  当前无计划话术")
            
        # 获取随机话术行
        random_line = scheduler.get_random_script_line()
        if random_line:
            print(f"  ✅ 随机话术行: {random_line}")
        else:
            print("  ℹ️  无可用话术")
            
        # 获取调度器统计
        stats = scheduler.get_scheduler_stats()
        print(f"  ✅ 调度器状态: 运行中={stats['is_running']}")
        
        # 获取时间表预览
        preview = scheduler.get_schedule_preview(hours=6)
        print(f"  ✅ 时间表预览: {len(preview)} 小时")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 话术调度器测试失败: {e}")
        return False


def test_ai_chat_management(db):
    """测试AI对话管理模块"""
    print("\n🤖 测试AI对话管理模块...")
    
    try:
        # 测试提示词管理器
        prompt_manager = PromptManager(db)
        
        # 获取系统提示词
        system_prompt = prompt_manager.get_system_prompt("friendly", "conversational")
        print(f"  ✅ 系统提示词: {len(system_prompt)} 字符")
        
        # 获取话题提示词
        topic_prompt = prompt_manager.get_topic_prompt("weather")
        print(f"  ✅ 话题提示词: {topic_prompt[:50]}...")
        
        # 创建自定义提示词
        custom_prompt_id = prompt_manager.create_custom_prompt(
            name="测试提示词",
            content="这是一个测试提示词",
            category="test"
        )
        
        if custom_prompt_id:
            print(f"  ✅ 创建自定义提示词: ID {custom_prompt_id}")
        
        # 测试对话历史管理器
        history_manager = ConversationHistory(db)
        
        # 添加对话记录
        conv_id = history_manager.add_conversation(
            user_input="你好",
            ai_response="您好！很高兴与您对话。",
            context={"test": True}
        )
        
        if conv_id:
            print(f"  ✅ 添加对话记录: ID {conv_id}")
            
            # 获取最近对话
            recent_convs = history_manager.get_recent_conversations(limit=5)
            print(f"  ✅ 最近对话: {len(recent_convs)} 条")
            
            # 获取对话统计
            stats = history_manager.get_conversation_stats()
            print(f"  ✅ 对话统计: 总数 {stats['total_conversations']}")
        
        # 测试AI对话管理器
        chat_manager = ChatManager(db)
        
        # 测试AI连接
        connection_test = chat_manager.test_ai_connection()
        print(f"  ✅ AI连接测试: {'成功' if connection_test['success'] else '失败'}")
        
        # 生成AI回复
        response = chat_manager.generate_response("今天天气怎么样？")
        if response:
            print(f"  ✅ AI回复生成: {response[:50]}...")
        else:
            print("  ⚠️  AI回复生成失败")
            
        # 获取对话统计
        chat_stats = chat_manager.get_chat_stats()
        print(f"  ✅ 对话统计: 请求数 {chat_stats['total_requests']}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ AI对话管理模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_integration(db):
    """集成测试"""
    print("\n🔗 集成测试...")
    
    try:
        # 创建管理器实例
        script_manager = ScriptManager(db)
        scheduler = ScriptScheduler(db)
        chat_manager = ChatManager(db)
        
        # 创建一些测试数据
        script_ids = []
        for i in range(3):
            script_id = script_manager.create_script(
                name=f"集成测试话术{i+1}",
                content=f"这是集成测试话术{i+1}的内容\n包含多行文本\n用于测试集成功能",
                category="integration_test"
            )
            if script_id:
                script_ids.append(script_id)
                
        print(f"  ✅ 创建测试话术: {len(script_ids)} 个")
        
        # 设置备用话术
        scheduler.set_fallback_scripts(script_ids)
        print("  ✅ 设置备用话术")
        
        # 获取当前话术并生成AI回复
        current_script = scheduler.get_current_script()
        if current_script:
            random_line = script_manager.get_random_line(current_script['id'])
            if random_line:
                ai_response = chat_manager.generate_response(
                    f"请对这句话术进行评价：{random_line}"
                )
                if ai_response:
                    print(f"  ✅ 集成AI回复: {ai_response[:50]}...")
                    
        print("  ✅ 集成测试完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 集成测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🧪 AI Broadcaster v2 新模块测试")
    print("=" * 60)
    
    # 初始化日志系统
    setup_logging()
    logger = create_logger("test")
    
    try:
        # 测试话术管理
        script_success, db, script_id = test_script_management()
        if not script_success:
            return 1
            
        # 测试时间段管理
        if script_id:
            time_success = test_time_segment_management(db, script_id)
            if not time_success:
                return 1
                
        # 测试话术调度器
        scheduler_success = test_script_scheduler(db)
        if not scheduler_success:
            return 1
            
        # 测试AI对话管理
        ai_success = test_ai_chat_management(db)
        if not ai_success:
            return 1
            
        # 集成测试
        integration_success = test_integration(db)
        if not integration_success:
            return 1
            
        print("\n🎉 所有新模块测试通过！")
        print("\n📝 测试总结:")
        print("  - 话术管理模块: ✅ 正常")
        print("  - 时间段管理模块: ✅ 正常")
        print("  - 话术调度器: ✅ 正常")
        print("  - AI对话管理: ✅ 正常")
        print("  - 提示词管理: ✅ 正常")
        print("  - 对话历史管理: ✅ 正常")
        print("  - 模块集成: ✅ 正常")
        
        # 清理测试数据
        if db:
            db.close()
            
        test_db_file = Path("data/test_new_modules.db")
        if test_db_file.exists():
            test_db_file.unlink()
            print("\n🧹 测试数据库文件已清理")
            
        print("\n🎯 新功能模块开发完成！现在可以继续开发其他模块。")
        return 0
        
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
