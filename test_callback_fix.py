#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试播放完成回调修复
"""

import os
import time
import threading
from pathlib import Path


def test_callback_fix():
    """测试播放完成回调修复"""
    print("🔍 测试播放完成回调修复...")
    
    # 查找测试文件
    voice_dir = Path("voices")
    if not voice_dir.exists():
        print("❌ voices目录不存在")
        return
        
    wav_files = list(voice_dir.glob("*.wav"))
    if not wav_files:
        print("❌ 没有找到wav文件")
        return
        
    test_files = wav_files[:3]  # 测试3个文件
    print(f"📁 测试文件: {[f.name for f in test_files]}")
    
    # 模拟主程序的播放器类
    class TestAudioPlayer:
        def __init__(self):
            self.is_playing = False
            self.current_file = None
            self.on_playback_end = None
            self.player_type = "pygame"
            
        def play(self, file_path):
            """播放音频文件"""
            if self.is_playing:
                print(f"⚠️ 正在播放其他音频，跳过: {Path(file_path).name}")
                return False
                
            self.current_file = file_path
            self.is_playing = True
            
            print(f"🎵 开始播放: {Path(file_path).name}")
            
            # 在新线程中播放
            play_thread = threading.Thread(target=self._play_worker, args=(file_path,))
            play_thread.daemon = True
            play_thread.start()
            
            return True
            
        def _play_worker(self, file_path):
            """播放工作线程"""
            try:
                self._play_with_pygame(file_path)
            except Exception as e:
                print(f"❌ 播放失败: {e}")
            finally:
                # 🔥 关键修复：播放完成后调用回调函数
                print(f"🎵 播放结束，准备调用回调: {Path(file_path).name}")
                
                # 重置播放状态
                self.is_playing = False
                
                # 调用播放完成回调
                if hasattr(self, 'on_playback_end') and self.on_playback_end:
                    print(f"🔄 触发播放完成回调: {Path(file_path).name}")
                    try:
                        self.on_playback_end(file_path)
                        print(f"✅ 播放完成回调已执行")
                    except Exception as callback_error:
                        print(f"❌ 播放完成回调异常: {callback_error}")
                        import traceback
                        traceback.print_exc()
                else:
                    print(f"⚠️ 播放器没有设置播放完成回调")
                    
        def _play_with_pygame(self, file_path):
            """pygame播放"""
            try:
                import pygame
                
                # 初始化
                if not pygame.mixer.get_init():
                    pygame.mixer.init()
                    
                # 停止之前的播放
                pygame.mixer.music.stop()
                
                # 加载并播放
                pygame.mixer.music.load(file_path)
                pygame.mixer.music.play()
                
                print(f"🎵 pygame播放开始: {Path(file_path).name}")
                
                # 等待播放完成
                start_time = time.time()
                max_duration = 30  # 最大30秒
                
                while self.is_playing and time.time() - start_time < max_duration:
                    if not pygame.mixer.music.get_busy():
                        elapsed = time.time() - start_time
                        print(f"🎵 pygame播放完成检测: {Path(file_path).name} (播放了{elapsed:.2f}秒)")
                        break
                    time.sleep(0.1)
                    
                # 如果超时，强制停止
                elapsed = time.time() - start_time
                if elapsed >= max_duration:
                    print(f"⚠️ 播放超时，强制停止: {Path(file_path).name} (播放了{elapsed:.2f}秒)")
                    pygame.mixer.music.stop()
                    
            except ImportError:
                print("❌ pygame不可用，使用模拟播放")
                self._simulate_play(file_path)
            except Exception as e:
                print(f"❌ pygame播放失败: {e}")
                self._simulate_play(file_path)
                
        def _simulate_play(self, file_path):
            """模拟播放"""
            duration = self._get_audio_duration(file_path)
            print(f"🎵 模拟播放: {Path(file_path).name} ({duration:.1f}秒)")
            
            start_time = time.time()
            while time.time() - start_time < duration and self.is_playing:
                time.sleep(0.1)
                
        def _get_audio_duration(self, file_path):
            """获取音频时长"""
            try:
                import wave
                with wave.open(file_path, 'rb') as wav_file:
                    frames = wav_file.getnframes()
                    sample_rate = wav_file.getframerate()
                    duration = frames / float(sample_rate)
                    return duration
            except:
                # 根据文件大小估算
                file_size = Path(file_path).stat().st_size
                return max(2, min(10, file_size / 16000))  # 2-10秒
    
    # 创建播放器
    player = TestAudioPlayer()
    
    # 播放完成计数器
    completed_count = 0
    
    def on_playback_finished(file_path):
        nonlocal completed_count
        completed_count += 1
        print(f"🔔 播放完成回调被触发 #{completed_count}: {Path(file_path).name}")
        
        # 播放下一个文件
        if completed_count < len(test_files):
            next_file = test_files[completed_count]
            print(f"🔄 准备播放下一个文件: {next_file.name}")
            time.sleep(1)  # 间隔1秒
            player.play(str(next_file))
        else:
            print("✅ 所有文件播放完成")
    
    # 设置回调函数
    player.on_playback_end = on_playback_finished
    
    # 开始播放第一个文件
    print(f"🎬 开始连续播放测试...")
    player.play(str(test_files[0]))
    
    # 等待所有播放完成
    max_wait_time = 60  # 最大等待60秒
    start_time = time.time()
    
    while completed_count < len(test_files) and time.time() - start_time < max_wait_time:
        time.sleep(0.5)
        
    # 检查结果
    if completed_count == len(test_files):
        print(f"✅ 连续播放测试成功！播放了 {completed_count}/{len(test_files)} 个文件")
        return True
    else:
        print(f"❌ 连续播放测试失败！只播放了 {completed_count}/{len(test_files)} 个文件")
        return False


if __name__ == "__main__":
    print("🧪 播放完成回调修复测试")
    print("=" * 50)
    
    success = test_callback_fix()
    
    if success:
        print("\n🎉 测试结果：播放完成回调修复成功！")
        print("现在主程序应该能够正常连续播放了。")
    else:
        print("\n❌ 测试结果：播放完成回调仍有问题")
        print("需要进一步调试。")
    
    print("\n✅ 测试完成")
