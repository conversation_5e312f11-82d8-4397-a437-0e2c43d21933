"""
AI Broadcaster v2 - 主入口点（将被移动到根目录）
应用程序启动和命令行接口
"""

import asyncio
import argparse
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.app.broadcaster import AIBroadcaster
from src.services.logging_service import setup_logging, create_logger


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="AI Broadcaster v2 - 智能AI广播系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python -m src.app.main                    # 启动广播器
  python -m src.app.main --auto-broadcast   # 启动并自动开始广播
  python -m src.app.main --config custom.yaml  # 使用自定义配置
  python -m src.app.main --debug            # 启用调试模式
        """
    )

    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config/app_config.yaml',
        help='配置文件路径 (默认: config/app_config.yaml)'
    )

    parser.add_argument(
        '--auto-broadcast', '-a',
        action='store_true',
        help='启动后自动开始广播'
    )

    parser.add_argument(
        '--debug', '-d',
        action='store_true',
        help='启用调试模式'
    )

    parser.add_argument(
        '--version', '-v',
        action='version',
        version='AI Broadcaster v2.0.0'
    )

    return parser.parse_args()


async def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 初始化日志系统
    setup_logging()
    logger = create_logger("main")

    logger.info("=" * 60)
    logger.info("🎙️  AI Broadcaster v2 启动中...")
    logger.info("=" * 60)

    try:
        # 创建广播器实例
        broadcaster = AIBroadcaster(config_path=args.config)

        # 初始化广播器
        if not await broadcaster.initialize():
            logger.error("❌ 广播器初始化失败")
            return 1

        # 如果指定了自动广播，则启动广播
        if args.auto_broadcast:
            logger.info("🚀 自动广播模式已启用")
            await broadcaster.start_broadcasting()

        # 启动广播器
        await broadcaster.start()

        return 0

    except KeyboardInterrupt:
        logger.info("👋 收到中断信号，正在关闭...")
        return 0
    except Exception as e:
        logger.error(f"❌ 应用程序异常: {e}")
        logger.exception("异常详情")
        return 1
    finally:
        logger.info("🛑 AI Broadcaster v2 已退出")


def run():
    """同步运行入口"""
    try:
        # 在Windows上设置事件循环策略
        if sys.platform == 'win32':
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

        # 运行主函数
        exit_code = asyncio.run(main())
        sys.exit(exit_code)

    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    run()
