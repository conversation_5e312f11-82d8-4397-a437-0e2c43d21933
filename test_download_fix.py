#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试下载修复功能
"""

import sys
import os
import json
from pathlib import Path

def test_download_fix():
    """测试下载修复功能"""
    print("🧪 测试下载修复功能...")
    
    # 检查播放列表文件
    playlist_file = Path("playlist_data.json")
    if not playlist_file.exists():
        print("❌ 播放列表文件不存在")
        return False
    
    # 读取播放列表
    try:
        with open(playlist_file, 'r', encoding='utf-8') as f:
            playlist_data = json.load(f)
        
        print(f"✅ 读取播放列表: {len(playlist_data)} 个项目")
        
        # 检查未下载项目
        voices_dir = Path("voices")
        voices_dir.mkdir(exist_ok=True)
        
        undownloaded_count = 0
        file_missing_count = 0
        
        for item in playlist_data:
            status = item.get('status', '')
            filename = item.get('filename', '')
            content = item.get('content', '')
            
            if status == '未下载':
                undownloaded_count += 1
                print(f"  - 状态为未下载: {content[:30]}...")
            elif filename and not (voices_dir / filename).exists():
                file_missing_count += 1
                print(f"  - 文件不存在: {filename}")
        
        print(f"📊 统计结果:")
        print(f"  - 状态为未下载: {undownloaded_count} 个")
        print(f"  - 文件不存在: {file_missing_count} 个")
        print(f"  - 需要下载: {undownloaded_count + file_missing_count} 个")
        
        # 检查voices目录中的文件
        voice_files = list(voices_dir.glob("*.wav"))
        print(f"  - voices目录中的文件: {len(voice_files)} 个")
        
        if voice_files:
            print("📁 voices目录中的文件:")
            for i, voice_file in enumerate(voice_files[:5]):  # 只显示前5个
                print(f"    {i+1}. {voice_file.name}")
            if len(voice_files) > 5:
                print(f"    ... 还有 {len(voice_files) - 5} 个文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取播放列表失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 下载修复功能测试")
    print("=" * 50)
    
    # 切换到正确的目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 运行测试
    success = test_download_fix()
    
    print("=" * 50)
    if success:
        print("✅ 测试完成")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    main()
