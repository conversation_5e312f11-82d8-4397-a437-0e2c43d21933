"""
AI主播系统 v2 - 语音服务
语音下载和主播管理
"""

import requests
import json
import os
import hashlib
from urllib.parse import quote
from typing import Dict, Any, List, Optional
from pathlib import Path

from .logging_service import create_logger
from .error_handler import handle_exceptions


class VoiceService:
    """语音服务管理器"""
    
    def __init__(self, voice_api_url: str = "http://ct.scjanelife.com/voice"):
        self.logger = create_logger("voice_service")
        self.voice_api_url = voice_api_url.rstrip('/')
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'AI-Broadcaster-Client/2.0.0'
        })

        # 禁用代理，直接连接
        self.session.proxies = {'http': None, 'https': None}

        # 语音文件保存目录
        self.voice_dir = Path("voices")
        self.voice_dir.mkdir(exist_ok=True)

        # 超时设置
        self.timeout = 10
        
    @handle_exceptions("VoiceService")
    def get_speakers(self) -> Dict[str, Any]:
        """
        获取主播列表
        
        Returns:
            主播列表结果
        """
        try:
            self.logger.info("获取主播列表...")
            
            # 发送请求
            response = self.session.get(
                f"{self.voice_api_url}/speakers",
                timeout=self.timeout
            )
            
            # 检查HTTP状态码
            if response.status_code != 200:
                return {
                    'success': False,
                    'message': f'服务器错误: HTTP {response.status_code}'
                }
                
            # 解析响应
            speakers = response.json()
            
            self.logger.info(f"获取到 {len(speakers)} 个主播")
            
            return {
                'success': True,
                'speakers': speakers
            }
            
        except requests.exceptions.ConnectionError:
            self.logger.error("无法连接到语音服务器")
            return {
                'success': False,
                'message': '无法连接到语音服务器，请检查网络连接'
            }
        except requests.exceptions.Timeout:
            self.logger.error("请求超时")
            return {
                'success': False,
                'message': '请求超时，请稍后重试'
            }
        except json.JSONDecodeError:
            self.logger.error("服务器响应格式错误")
            return {
                'success': False,
                'message': '服务器响应格式错误'
            }
        except Exception as e:
            self.logger.error(f"获取主播列表失败: {e}")
            return {
                'success': False,
                'message': f'获取主播列表失败: {str(e)}'
            }
            
    @handle_exceptions("VoiceService")
    def download_voice(self, text: str, speaker_id: int = 0, speed: float = 1.0) -> Dict[str, Any]:
        """
        下载语音文件

        Args:
            text: 要转换的文本
            speaker_id: 语音模型ID
            speed: 语速参数

        Returns:
            下载结果
        """
        try:
            import time
            # 🔥 修改：生成语音内容+时间戳的哈希值文件名
            timestamp = int(time.time() * 1000)  # 毫秒级时间戳
            content_for_hash = f"{text}_{timestamp}"
            text_hash = hashlib.md5(content_for_hash.encode()).hexdigest()[:16]
            filename = f"{text_hash}.wav"
            file_path = self.voice_dir / filename
            
            # 检查文件是否已存在
            if file_path.exists():
                self.logger.info(f"语音文件已存在: {filename}")
                return {
                    'success': True,
                    'file_path': str(file_path),
                    'cached': True
                }
            
            self.logger.info(f"下载语音: {text[:20]}...")
            
            # 编码文本
            encoded_text = quote(text)

            # 🔥 修复：构建请求URL，使用lang参数而不是speed
            url = f"{self.voice_api_url}/bert-vits2?id={speaker_id}&text={encoded_text}&lang={speed}"

            # 🔥 修复：发送请求时禁用代理
            response = self.session.get(url, timeout=self.timeout, proxies={'http': None, 'https': None})
            
            # 检查HTTP状态码
            if response.status_code != 200:
                return {
                    'success': False,
                    'message': f'语音生成失败: HTTP {response.status_code}'
                }
                
            # 保存音频文件
            with open(file_path, 'wb') as f:
                f.write(response.content)
                
            self.logger.info(f"语音文件保存成功: {filename}")
            
            return {
                'success': True,
                'file_path': str(file_path),
                'cached': False
            }
            
        except requests.exceptions.ConnectionError:
            self.logger.error("无法连接到语音服务器")
            return {
                'success': False,
                'message': '无法连接到语音服务器，请检查网络连接'
            }
        except requests.exceptions.Timeout:
            self.logger.error("语音下载超时")
            return {
                'success': False,
                'message': '语音下载超时，请稍后重试'
            }
        except Exception as e:
            self.logger.error(f"语音下载失败: {e}")
            return {
                'success': False,
                'message': f'语音下载失败: {str(e)}'
            }
            
    def get_voice_cache_info(self) -> Dict[str, Any]:
        """获取语音缓存信息"""
        try:
            voice_files = list(self.voice_dir.glob("*.wav"))
            total_size = sum(f.stat().st_size for f in voice_files)
            
            return {
                'file_count': len(voice_files),
                'total_size': total_size,
                'total_size_mb': round(total_size / 1024 / 1024, 2)
            }
        except Exception as e:
            self.logger.error(f"获取缓存信息失败: {e}")
            return {
                'file_count': 0,
                'total_size': 0,
                'total_size_mb': 0
            }
            
    def clear_voice_cache(self) -> bool:
        """清理语音缓存"""
        try:
            voice_files = list(self.voice_dir.glob("*.wav"))
            for file_path in voice_files:
                file_path.unlink()
            
            self.logger.info(f"清理了 {len(voice_files)} 个语音文件")
            return True
            
        except Exception as e:
            self.logger.error(f"清理缓存失败: {e}")
            return False
            
    def set_voice_api_url(self, url: str):
        """设置语音API地址"""
        self.voice_api_url = url.rstrip('/')
        self.logger.info(f"设置语音API地址: {self.voice_api_url}")
        
    def close(self):
        """关闭会话"""
        self.session.close()
