#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
副视频功能验证程序
简单验证副视频功能是否正常工作
"""

import json
from pathlib import Path

def verify_sub_video():
    """验证副视频功能"""
    print("🔍 副视频功能验证")
    print("=" * 50)
    
    # 检查播放列表
    print("\n📋 检查播放列表中的副视频...")
    playlist_file = Path("data/playlist.json")
    
    if playlist_file.exists():
        try:
            with open(playlist_file, 'r', encoding='utf-8') as f:
                playlist = json.load(f)
            
            print(f"✅ 播放列表包含 {len(playlist)} 个项目")
            
            sub_video_items = []
            for i, item in enumerate(playlist):
                sub_video = item.get('sub_video', '无')
                voice_type = item.get('voice_type', '未知')
                content = item.get('content', '')
                status = item.get('status', '未知')
                
                print(f"\n项目 {i+1}:")
                print(f"  类型: {voice_type}")
                print(f"  内容: {content[:50]}...")
                print(f"  状态: {status}")
                print(f"  副视频: {sub_video}")
                
                if sub_video and sub_video != '无':
                    sub_video_items.append({
                        'index': i+1,
                        'content': content,
                        'sub_video': sub_video,
                        'status': status
                    })
                    print(f"  ✅ 检测到副视频需求")
                else:
                    print(f"  ⚪ 无副视频需求")
            
            if sub_video_items:
                print(f"\n🎬 发现 {len(sub_video_items)} 个副视频项目:")
                for item in sub_video_items:
                    print(f"  • 项目{item['index']}: {item['content'][:30]}... → {item['sub_video']}")
                
                print(f"\n✅ 副视频功能正常工作！")
                print(f"当您在主程序中播放这些项目时，系统会自动切换到对应的副视频源。")
            else:
                print(f"\n⚠️ 播放列表中没有副视频项目")
                
        except Exception as e:
            print(f"❌ 读取播放列表失败: {e}")
    else:
        print("❌ 播放列表文件不存在")
    
    # 检查副视频配置
    print(f"\n📋 检查副视频配置...")
    sub_video_file = Path("data/sub_videos.json")
    
    if sub_video_file.exists():
        try:
            with open(sub_video_file, 'r', encoding='utf-8') as f:
                sub_videos = json.load(f)
            
            print(f"✅ 副视频配置包含 {len(sub_videos)} 个关键词")
            
            for keyword, config in sub_videos.items():
                video_source = config.get('video_source', '未知')
                scripts_count = len(config.get('scripts', []))
                print(f"  • {keyword} → {video_source} ({scripts_count}个话术)")
                
        except Exception as e:
            print(f"❌ 读取副视频配置失败: {e}")
    else:
        print("❌ 副视频配置文件不存在")
    
    # 提供测试建议
    print(f"\n💡 测试建议:")
    print(f"1. 启动主程序: python run_gui_qt5.py")
    print(f"2. 查看播放列表表格，副视频列应该显示副视频源名称")
    print(f"3. 点击播放按钮，观察控制台输出的副视频切换日志")
    print(f"4. 在弹幕设置中添加包含关键词的测试弹幕")
    print(f"5. 观察新添加的弹幕话术是否正确标识副视频")
    
    print(f"\n🎯 预期效果:")
    print(f"• 播放列表表格中副视频列显示源名称（不是'无'）")
    print(f"• 播放时控制台输出副视频切换日志")
    print(f"• OBS中的视频源会自动切换（如果OBS已连接）")
    
    print(f"\n🏁 副视频功能验证完成")

if __name__ == "__main__":
    verify_sub_video()
