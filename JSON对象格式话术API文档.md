# 📝 JSON对象格式话术API文档

## 🎯 概述

根据您的要求，我们已经将话术上传格式从JSON字符串改为JSON对象格式，并增强了话术解析功能，使其能够正确解析时间段和话术内容。

## 🔄 主要变更

### 1. 上传格式变更

#### 旧格式（JSON字符串）
```json
{
    "类型": "上传话术",
    "话术名": "xy-kaer",
    "上传数据": "{\n  \"0秒 - 10秒\": \"话术内容\",\n  \"10秒 - 20秒\": \"话术内容\"\n}"
}
```

#### 新格式（JSON对象）✨
```json
{
    "类型": "上传话术",
    "话术名": "xy-kaer",
    "上传数据": {
        "0秒 - 10秒": "1***哈喽，大家好，首先感谢【大哥|哥哥|宝子】们的捧场。\n2***稍作停留也是爱，所能接触的都是这四个大字。",
        "10秒 - 20秒": "6***自古套路得人心，但唯有真心得天下。\n7***新来的家人们，相遇就是缘分。",
        "20秒 - 30秒": "11***相信自己的眼睛，相信自己的耳朵。\n12***斩青丝斩难过斩断红尘不为过。"
    }
}
```

### 2. 解析功能增强

#### 支持多种输入格式
- **JSON对象**: 直接传递字典对象
- **JSON字符串**: 兼容旧版本的字符串格式
- **自动检测**: 系统自动识别输入格式并正确处理

#### 时间段数据解析
- **时间段提取**: 从"0秒 - 10秒"格式中提取开始和结束时间
- **话术内容解析**: 正确处理换行符和特殊字符
- **数据结构化**: 转换为内部标准格式

## 📋 详细API规范

### 1. 上传话术API

#### 请求格式
```json
{
    "类型": "上传话术",
    "话术名": "话术名称",
    "上传数据": {
        "时间段1": "话术内容1",
        "时间段2": "话术内容2",
        "时间段N": "话术内容N"
    }
}
```

#### 时间段格式
- **命名规则**: `"开始秒数秒 - 结束秒数秒"`
- **示例**: `"0秒 - 10秒"`, `"10秒 - 20秒"`, `"30秒 - 40秒"`
- **时间范围**: 0-3600秒（1小时）

#### 话术内容格式
- **换行符**: 使用 `\n` 表示换行
- **变量词**: 支持 `{nick}`, `{gametype}` 等变量
- **随机选择**: 支持 `【选项1|选项2|选项3】` 格式
- **编号标记**: 支持 `1***`, `2***` 等编号前缀

#### 响应格式
```json
{
    "状态": "成功",
    "信息": "话术上传成功"
}
```

### 2. 获取话术API

#### 请求格式
```json
{
    "类型": "获取话术",
    "话术名": "话术名称"
}
```

#### 响应格式（时间段话术）
```json
{
    "0秒 - 10秒": "1***哈喽，大家好，首先感谢【大哥|哥哥|宝子】们的捧场。\n2***稍作停留也是爱，所能接触的都是这四个大字。",
    "10秒 - 20秒": "6***自古套路得人心，但唯有真心得天下。\n7***新来的家人们，相遇就是缘分。",
    "20秒 - 30秒": "11***相信自己的眼睛，相信自己的耳朵。\n12***斩青丝斩难过斩断红尘不为过。"
}
```

#### 响应格式（普通话术）
```
欢迎来到直播间
请关注点赞
感谢大家的支持
```

## 🔧 代码实现

### 1. 客户端上传代码
```python
import requests
import json

def upload_time_segment_script(script_name, time_segments_data):
    """上传时间段话术（JSON对象格式）"""
    
    # 构造请求数据
    upload_request = {
        "类型": "上传话术",
        "话术名": script_name,
        "上传数据": time_segments_data  # 直接使用JSON对象
    }
    
    # 发送请求
    response = requests.post(
        "http://localhost:12456/",
        json=upload_request,
        timeout=10
    )
    
    return response.status_code == 200

# 使用示例
time_segments = {
    "0秒 - 10秒": "欢迎大家来到直播间！",
    "10秒 - 20秒": "请关注点赞支持！",
    "20秒 - 30秒": "感谢各位的观看！"
}

success = upload_time_segment_script("测试话术", time_segments)
```

### 2. 服务端解析代码
```python
def parse_time_segment_data(content):
    """解析时间段数据（支持多种格式）"""
    
    if isinstance(content, dict):
        # JSON对象格式，直接使用
        return content
    elif isinstance(content, str):
        # JSON字符串格式，解析后使用
        try:
            return json.loads(content)
        except json.JSONDecodeError:
            return None
    else:
        return None

def extract_time_info(segment_name):
    """从时间段名称提取时间信息"""
    import re
    
    pattern = r'(\d+)秒\s*-\s*(\d+)秒'
    match = re.search(pattern, segment_name)
    
    if match:
        start_time = int(match.group(1))
        end_time = int(match.group(2))
        return start_time, end_time
    
    return 0, 60  # 默认值
```

## ✨ 优势特点

### 1. 数据结构更清晰
- **直接对象**: 无需字符串转义和解析
- **类型安全**: 减少JSON解析错误
- **易于调试**: 数据结构一目了然

### 2. 兼容性更好
- **向后兼容**: 支持旧版本字符串格式
- **自动转换**: 系统自动处理格式差异
- **平滑升级**: 无需修改现有数据

### 3. 性能更优
- **减少序列化**: 避免不必要的JSON字符串转换
- **内存效率**: 直接操作对象，减少内存拷贝
- **处理速度**: 提高数据处理效率

## 🧪 测试验证

### 1. 功能测试
- ✅ JSON对象格式上传
- ✅ JSON字符串格式兼容
- ✅ 时间段数据解析
- ✅ 话术内容提取
- ✅ 错误处理机制

### 2. 兼容性测试
- ✅ 新旧格式混合使用
- ✅ 数据格式自动检测
- ✅ 错误格式容错处理

### 3. 性能测试
- ✅ 大量时间段数据处理
- ✅ 复杂话术内容解析
- ✅ 并发请求处理

## 📚 使用建议

1. **推荐使用JSON对象格式**：新开发的功能建议使用JSON对象格式
2. **保持向后兼容**：现有系统可以继续使用字符串格式
3. **逐步迁移**：可以逐步将旧格式迁移到新格式
4. **错误处理**：始终检查API响应状态和错误信息
5. **数据验证**：上传前验证时间段格式和话术内容

## 🔍 故障排除

### 常见问题
1. **JSON解析错误**: 检查数据格式是否正确
2. **时间段格式错误**: 确保使用"X秒 - Y秒"格式
3. **话术内容丢失**: 检查换行符和特殊字符处理
4. **服务器响应异常**: 检查网络连接和服务器状态

### 调试建议
1. 使用测试脚本验证API功能
2. 检查服务器日志获取详细错误信息
3. 对比新旧格式的数据差异
4. 使用浏览器开发者工具查看网络请求
