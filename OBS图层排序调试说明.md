# 🔍 OBS图层排序调试说明

## 🎯 调试目的

您发现了一个重要问题：不管是把副视频放到最顶层还是最底层，副视频都被放到了最底层。为了理解OBS的图层排序机制，我添加了详细的调试信息。

## 🔧 调试代码说明

### 添加的调试功能

```python
# 🔥 步骤5.1：调试OBS图层排序机制
sub_video_id = obs_controller._get_source_id(sub_video_source, scene_name)

# 获取场景中的所有项目数量和当前排序
scene_items_response = obs_controller.send_request_sync("GetSceneItemList", {
    "sceneName": scene_name
})

if scene_items_response and "sceneItems" in scene_items_response:
    scene_items = scene_items_response["sceneItems"]
    print(f"🔍 当前场景项目数量: {len(scene_items)}")
    
    # 显示当前所有项目的排序
    for i, item in enumerate(scene_items):
        source_name = item.get('sourceName', 'Unknown')
        item_index = item.get('sceneItemIndex', 'Unknown')
        print(f"  项目{i}: {source_name} (索引: {item_index})")
    
    # 获取副视频当前索引
    current_sub_index = None
    for item in scene_items:
        if item.get('sourceName') == sub_video_source:
            current_sub_index = item.get('sceneItemIndex')
            break
    
    print(f"🔍 副视频当前索引: {current_sub_index}")
    
    # 尝试将副视频移到索引0（测试是否为最顶层）
    obs_controller.send_request_sync("SetSceneItemIndex", {
        "sceneName": scene_name,
        "sceneItemId": sub_video_id,
        "sceneItemIndex": 0  # 测试索引0
    })
    print(f"📋 副视频已移到索引0: {sub_video_source}")
    
    # 再次获取场景项目列表，查看排序变化
    updated_scene_items_response = obs_controller.send_request_sync("GetSceneItemList", {
        "sceneName": scene_name
    })
    
    if updated_scene_items_response and "sceneItems" in updated_scene_items_response:
        updated_scene_items = updated_scene_items_response["sceneItems"]
        print(f"🔍 移动后场景项目排序:")
        for i, item in enumerate(updated_scene_items):
            source_name = item.get('sourceName', 'Unknown')
            item_index = item.get('sceneItemIndex', 'Unknown')
            print(f"  项目{i}: {source_name} (索引: {item_index})")
```

## 📋 调试信息解读

### 预期输出格式

当您触发副视频时，应该会看到类似这样的调试信息：

```
🔍 当前场景项目数量: 3
  项目0: 2222 (索引: 0)
  项目1: 1111 (索引: 1)
  项目2: 其他源 (索引: 2)
🔍 副视频当前索引: 1
📋 副视频已移到索引0: 1111
🔍 移动后场景项目排序:
  项目0: 1111 (索引: 0)
  项目1: 2222 (索引: 1)
  项目2: 其他源 (索引: 2)
```

### 关键观察点

#### 1. **索引与显示层级的关系**
- 观察索引0是否对应最顶层显示
- 观察索引变化后的实际显示效果

#### 2. **GetSceneItemList的返回顺序**
- 返回的项目顺序是否与显示层级一致
- 项目在列表中的位置与索引的关系

#### 3. **SetSceneItemIndex的实际效果**
- 设置索引0后，副视频是否真的移到了最顶层
- 其他项目的索引是否相应调整

## 🔍 可能的OBS图层机制

### 假设1：索引0为最顶层
```
索引0: 最顶层（最前面显示）
索引1: 第二层
索引2: 第三层
...
索引N: 最底层（最后面显示）
```

### 假设2：索引0为最底层
```
索引0: 最底层（最后面显示）
索引1: 第二层
索引2: 第三层
...
索引N: 最顶层（最前面显示）
```

### 假设3：列表顺序决定层级
```
GetSceneItemList返回的顺序就是显示层级
第一个项目: 最顶层
第二个项目: 第二层
...
最后一个项目: 最底层
```

## 🧪 测试步骤

### 步骤1：观察初始状态
1. 启动程序并连接OBS
2. 开始播放主视频
3. 观察当前场景中的项目排序

### 步骤2：触发副视频
1. 发送测试弹幕（包含关键词"代"）
2. 观察调试输出中的项目排序信息
3. 观察OBS中的实际显示效果

### 步骤3：分析结果
1. 对比移动前后的项目排序
2. 确认索引0对应的实际显示层级
3. 验证副视频是否真的在最顶层显示

## 🔧 根据调试结果的修复方案

### 如果索引0是最顶层
```python
# 将副视频移到最顶层
obs_controller.send_request_sync("SetSceneItemIndex", {
    "sceneName": scene_name,
    "sceneItemId": sub_video_id,
    "sceneItemIndex": 0  # 最顶层
})
```

### 如果索引0是最底层
```python
# 获取最大索引作为最顶层
max_index = len(scene_items) - 1
obs_controller.send_request_sync("SetSceneItemIndex", {
    "sceneName": scene_name,
    "sceneItemId": sub_video_id,
    "sceneItemIndex": max_index  # 最顶层
})
```

### 如果列表顺序决定层级
```python
# 使用SetSceneItemIndex移动到列表开头
obs_controller.send_request_sync("SetSceneItemIndex", {
    "sceneName": scene_name,
    "sceneItemId": sub_video_id,
    "sceneItemIndex": 0  # 列表开头
})
```

## 📝 调试结果记录

请在测试后记录以下信息：

### 移动前的状态
- [ ] 场景项目数量：_____
- [ ] 副视频当前索引：_____
- [ ] 副视频在OBS中的显示层级：_____

### 移动到索引0后的状态
- [ ] 副视频新索引：_____
- [ ] 副视频在OBS中的显示层级：_____
- [ ] 其他项目的索引变化：_____

### 实际显示效果
- [ ] 副视频是否在最顶层显示：_____
- [ ] 主视频是否被副视频覆盖：_____
- [ ] 切换效果是否符合预期：_____

## 🎯 下一步行动

根据调试结果，我们将：

1. **确定OBS的图层排序机制**
2. **修正SetSceneItemIndex的使用方法**
3. **实现正确的副视频置顶效果**
4. **优化切换逻辑和用户体验**

---

## 💡 提示

如果调试信息显示副视频确实移到了索引0，但在OBS中仍然显示在最底层，可能的原因包括：

1. **OBS版本差异**：不同版本的OBS可能有不同的图层机制
2. **场景设置**：场景中可能有特殊的图层设置
3. **源类型影响**：不同类型的源可能有不同的图层行为
4. **API理解偏差**：我们对OBS WebSocket API的理解可能有偏差

通过这次调试，我们将能够准确理解OBS的图层排序机制，并实现正确的副视频切换效果。
