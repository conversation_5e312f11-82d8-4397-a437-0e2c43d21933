#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试下载功能
"""

import hashlib
import json
from pathlib import Path

def test_filename_generation():
    """测试文件名生成"""
    print("🧪 测试文件名生成...")
    
    # 读取播放列表
    playlist_file = Path("data/playlist.json")
    with open(playlist_file, 'r', encoding='utf-8') as f:
        playlist_data = json.load(f)
    
    voices_dir = Path("voices")
    
    print(f"📋 播放列表中有 {len(playlist_data)} 个项目")
    
    undownloaded_items = [item for item in playlist_data if item['status'] == '未下载']
    print(f"📋 其中 {len(undownloaded_items)} 个状态为未下载")
    
    # 检查前5个未下载项目
    for i, item in enumerate(undownloaded_items[:5]):
        content = item['content']
        
        # 生成文件名（模拟download_voice函数的逻辑）
        text_hash = hashlib.md5(content.encode('utf-8')).hexdigest()[:8]
        filename_120 = f"{text_hash}_5_120.wav"  # 主播ID=5, 速度=120
        filename_100 = f"{text_hash}_0_100.wav"  # 主播ID=0, 速度=100
        
        filepath_120 = voices_dir / filename_120
        filepath_100 = voices_dir / filename_100
        
        print(f"\n{i+1}. 内容: {content[:50]}...")
        print(f"   哈希: {text_hash}")
        print(f"   文件名(5_120): {filename_120}")
        print(f"   文件名(0_100): {filename_100}")
        print(f"   文件存在(5_120): {filepath_120.exists()}")
        print(f"   文件存在(0_100): {filepath_100.exists()}")
        
        # 检查是否有任何匹配的文件
        matching_files = list(voices_dir.glob(f"{text_hash}_*.wav"))
        if matching_files:
            print(f"   匹配的文件: {[f.name for f in matching_files]}")
        else:
            print(f"   匹配的文件: 无")

def main():
    """主函数"""
    print("🔧 简单下载测试")
    print("=" * 50)
    
    test_filename_generation()
    
    print("=" * 50)
    print("✅ 测试完成")

if __name__ == "__main__":
    main()
