#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试更新API
"""

import requests
import json

def test_update_api():
    """测试更新API"""
    print("🧪 测试更新API")
    print("=" * 50)
    
    url = "http://**************:12456/admin/api/updates/current"
    print(f"请求URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            version = data.get('version', '')
            description = data.get('description', '')
            release_date = data.get('release_date', '')
            
            print(f"\n解析结果:")
            print(f"版本: {version}")
            print(f"日期: {release_date}")
            print(f"描述: {description}")
            
            return True
        else:
            print(f"请求失败: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"异常: {e}")
        return False

def test_config():
    """测试配置文件"""
    print("\n🧪 测试配置文件")
    print("=" * 50)
    
    try:
        with open("config/app_config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        version = config.get('app', {}).get('version', '未知')
        print(f"当前版本: {version}")
        return True
        
    except Exception as e:
        print(f"配置文件错误: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始简单测试")
    
    api_ok = test_update_api()
    config_ok = test_config()
    
    print(f"\n📊 测试结果:")
    print(f"API测试: {'✅ 通过' if api_ok else '❌ 失败'}")
    print(f"配置测试: {'✅ 通过' if config_ok else '❌ 失败'}")
    
    if api_ok and config_ok:
        print("\n🎉 基础功能正常，可以集成到主程序！")
    else:
        print("\n❌ 存在问题，需要修复")
