#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI直播系统功能测试程序
测试播放功能、生成新语音、播放顺序、时间段匹配、副视频AI对话等功能
"""

import sys
import time
import json
from pathlib import Path

def print_test_header(test_name):
    """打印测试标题"""
    print(f"\n{'='*60}")
    print(f"🧪 测试: {test_name}")
    print(f"{'='*60}")

def print_test_result(test_name, success, details=""):
    """打印测试结果"""
    status = "✅ 通过" if success else "❌ 失败"
    print(f"\n📊 测试结果: {test_name} - {status}")
    if details:
        print(f"📝 详情: {details}")

def test_playlist_initialization():
    """测试1: 播放列表初始化功能"""
    print_test_header("播放列表初始化功能")
    
    try:
        # 模拟话术时间段数据
        test_script_data = {
            "话术1": {
                "10秒 - 20秒": {
                    "start_time": 10,
                    "end_time": 20,
                    "content": "1***欢迎来到直播间！\n2***【大家好|各位好|hello】，正在玩{gamename}\n3***感谢{nick}的关注"
                },
                "40秒 - 50秒": {
                    "start_time": 40,
                    "end_time": 50,
                    "content": "1***感谢大家的观看\n2***谢谢大家的支持\n3***我们继续游戏"
                }
            }
        }
        
        print("📋 测试数据:")
        print(f"  - 话术数量: {len(test_script_data)}")
        print(f"  - 时间段数量: {sum(len(segments) for segments in test_script_data.values())}")
        
        # 模拟预备语音数量
        prepare_count = 3
        print(f"  - 预备语音数量: {prepare_count}")
        
        # 模拟生成播放列表
        playlist_items = []
        item_id = 1
        
        for script_name, time_segments in test_script_data.items():
            print(f"\n📝 处理话术: {script_name}")
            
            for segment_name, segment_data in time_segments.items():
                content = segment_data.get('content', '')
                
                # 解析话术内容
                scripts = []
                for line in content.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if '***' in line:
                            script_text = line.split('***', 1)[1].strip()
                            if script_text:
                                scripts.append(script_text)
                        else:
                            scripts.append(line)
                
                print(f"  📝 时间段 '{segment_name}': 找到 {len(scripts)} 条话术")
                
                # 生成预备语音
                import random
                selected_scripts = scripts[:prepare_count] if len(scripts) >= prepare_count else scripts * ((prepare_count // len(scripts)) + 1)
                selected_scripts = selected_scripts[:prepare_count]
                
                for i, script_content in enumerate(selected_scripts):
                    # 处理随机文本选择
                    processed_content = process_random_text(script_content)
                    
                    playlist_item = {
                        'id': item_id,
                        'voice_type': '主视频话术',
                        'content': processed_content,
                        'time_segment': segment_name,
                        'status': '模拟已下载',
                        'filename': f'test_{item_id}.wav',
                        'sub_video': '无'
                    }
                    playlist_items.append(playlist_item)
                    item_id += 1
                    
                    print(f"    - 生成语音 {i+1}/{len(selected_scripts)}: {processed_content[:30]}...")
        
        # 统计结果
        segment_counts = {}
        for item in playlist_items:
            segment = item['time_segment']
            segment_counts[segment] = segment_counts.get(segment, 0) + 1
        
        print(f"\n📊 各时间段语音数量统计:")
        all_sufficient = True
        for segment, count in segment_counts.items():
            status = "✅" if count >= prepare_count else "⚠️"
            print(f"  {status} {segment}: {count}/{prepare_count} 个语音")
            if count < prepare_count:
                all_sufficient = False
        
        print(f"\n✅ 播放列表初始化完成，共 {len(playlist_items)} 个项目")
        
        print_test_result("播放列表初始化功能", all_sufficient, 
                         f"生成了 {len(playlist_items)} 个播放项目，所有时间段都有足够语音" if all_sufficient else "部分时间段语音数量不足")
        
        return playlist_items, all_sufficient
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        print_test_result("播放列表初始化功能", False, f"异常: {e}")
        return [], False

def process_random_text(text):
    """处理随机文本选择"""
    import re
    import random
    
    def replace_random_choice(match):
        choices = match.group(1).split('|')
        return random.choice(choices)
    
    # 处理【选项1|选项2|选项3】格式
    result = re.sub(r'【([^】]+)】', replace_random_choice, text)
    
    # 处理变量替换
    replacements = {
        '{nick}': '测试用户',
        '{gamename}': '王者荣耀',
        '{time}': '14点30分',
        '{date}': '2024年12月',
        '{people}': '888'
    }
    
    for var, value in replacements.items():
        result = result.replace(var, value)
    
    return result

def test_time_segment_matching(playlist_items):
    """测试2: 时间段匹配功能"""
    print_test_header("时间段匹配功能")
    
    try:
        # 模拟不同的主视频播放位置
        test_positions = [
            {"position": 15, "expected_segment": "10秒 - 20秒"},
            {"position": 45, "expected_segment": "40秒 - 50秒"},
            {"position": 30, "expected_segment": "不在时间段内"},
            {"position": 5, "expected_segment": "不在时间段内"}
        ]
        
        # 时间段定义
        time_segments = {
            "10秒 - 20秒": {"start_time": 10, "end_time": 20},
            "40秒 - 50秒": {"start_time": 40, "end_time": 50}
        }
        
        all_matches_correct = True
        
        for test_case in test_positions:
            position = test_case["position"]
            expected = test_case["expected_segment"]
            
            print(f"\n🔍 测试位置: {position}秒")
            
            # 查找匹配的时间段
            matched_segment = "不在时间段内"
            for segment_name, segment_data in time_segments.items():
                start_time = segment_data["start_time"]
                end_time = segment_data["end_time"]
                
                print(f"  检查时间段 '{segment_name}': {start_time}-{end_time}秒")
                
                if start_time <= position <= end_time:
                    matched_segment = segment_name
                    print(f"  ✅ 匹配到时间段: '{segment_name}'")
                    break
            
            if matched_segment == "不在时间段内":
                print(f"  ⚠️ 位置{position}秒不在任何时间段内")
            
            # 查找对应的播放项目
            matching_items = [
                item for item in playlist_items
                if item['time_segment'] == matched_segment and item['voice_type'] == '主视频话术'
            ]
            
            print(f"  📋 找到 {len(matching_items)} 个匹配的播放项目")
            
            # 验证结果
            is_correct = matched_segment == expected
            if is_correct:
                print(f"  ✅ 时间段匹配正确: {matched_segment}")
                if matching_items:
                    selected_item = matching_items[0]
                    print(f"  🎯 选择播放: {selected_item['content'][:30]}...")
            else:
                print(f"  ❌ 时间段匹配错误: 期望 '{expected}', 实际 '{matched_segment}'")
                all_matches_correct = False
        
        print_test_result("时间段匹配功能", all_matches_correct, 
                         "所有位置的时间段匹配都正确" if all_matches_correct else "部分位置的时间段匹配有误")
        
        return all_matches_correct
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        print_test_result("时间段匹配功能", False, f"异常: {e}")
        return False

def test_playback_priority():
    """测试3: 播放优先级功能"""
    print_test_header("播放优先级功能")
    
    try:
        # 创建测试播放列表（包含不同类型的项目）
        test_playlist = [
            {
                'id': 1,
                'voice_type': '主视频话术',
                'content': '主视频话术内容1',
                'time_segment': '10秒 - 20秒',
                'status': '已下载',
                'filename': 'main1.wav'
            },
            {
                'id': 2,
                'voice_type': '弹幕话术',
                'content': '感谢测试用户的关注',
                'time_segment': '无',
                'status': '已下载',
                'filename': 'danmaku1.wav'
            },
            {
                'id': 3,
                'voice_type': '报时话术',
                'content': '现在时间是14:30',
                'time_segment': '无',
                'status': '已下载',
                'filename': 'time1.wav'
            },
            {
                'id': 4,
                'voice_type': '主视频话术',
                'content': '主视频话术内容2',
                'time_segment': '10秒 - 20秒',
                'status': '已下载',
                'filename': 'main2.wav'
            }
        ]
        
        print(f"📋 测试播放列表: {len(test_playlist)} 个项目")
        for item in test_playlist:
            print(f"  - {item['voice_type']}: {item['content'][:20]}...")
        
        # 模拟播放选择逻辑
        def get_next_playback_item(available_items, current_time_segment="10秒 - 20秒"):
            """模拟播放项目选择逻辑"""
            
            # 第一优先级：弹幕话术
            danmaku_items = [item for item in available_items if item['voice_type'] == '弹幕话术']
            if danmaku_items:
                return danmaku_items[0], "弹幕优先"
            
            # 第二优先级：报时话术
            time_announce_items = [item for item in available_items if item['voice_type'] == '报时话术']
            if time_announce_items:
                return time_announce_items[0], "报时优先"
            
            # 第三优先级：主视频话术（匹配时间段）
            if current_time_segment != "不在时间段内":
                matching_items = [
                    item for item in available_items
                    if item['voice_type'] == '主视频话术' and item['time_segment'] == current_time_segment
                ]
                if matching_items:
                    return matching_items[0], "时间段匹配"
            
            return None, "无匹配项目"
        
        # 测试不同场景
        test_scenarios = [
            {
                "name": "包含弹幕话术",
                "items": test_playlist,
                "expected_type": "弹幕话术",
                "expected_reason": "弹幕优先"
            },
            {
                "name": "无弹幕，有报时",
                "items": [item for item in test_playlist if item['voice_type'] != '弹幕话术'],
                "expected_type": "报时话术",
                "expected_reason": "报时优先"
            },
            {
                "name": "只有主视频话术",
                "items": [item for item in test_playlist if item['voice_type'] == '主视频话术'],
                "expected_type": "主视频话术",
                "expected_reason": "时间段匹配"
            }
        ]
        
        all_priorities_correct = True
        
        for scenario in test_scenarios:
            print(f"\n🎯 测试场景: {scenario['name']}")
            
            selected_item, reason = get_next_playback_item(scenario['items'])
            
            if selected_item:
                print(f"  🎵 选择播放: {selected_item['voice_type']} - {selected_item['content'][:20]}...")
                print(f"  📝 选择原因: {reason}")
                
                is_correct = (selected_item['voice_type'] == scenario['expected_type'] and 
                             reason == scenario['expected_reason'])
                
                if is_correct:
                    print(f"  ✅ 优先级正确")
                else:
                    print(f"  ❌ 优先级错误: 期望 {scenario['expected_type']}({scenario['expected_reason']})")
                    all_priorities_correct = False
            else:
                print(f"  ⚠️ 没有选择任何项目")
                if scenario['expected_type'] != "无":
                    all_priorities_correct = False
        
        print_test_result("播放优先级功能", all_priorities_correct, 
                         "所有优先级测试都正确" if all_priorities_correct else "部分优先级测试有误")
        
        return all_priorities_correct
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        print_test_result("播放优先级功能", False, f"异常: {e}")
        return False

def test_voice_generation():
    """测试4: 新语音生成功能"""
    print_test_header("新语音生成功能")
    
    try:
        # 模拟播放完成后的语音补充
        played_item = {
            'voice_type': '主视频话术',
            'content': '欢迎来到直播间！',
            'time_segment': '10秒 - 20秒'
        }
        
        # 模拟时间段话术数据
        segment_scripts = [
            "大家好，欢迎观看直播",
            "【感谢|谢谢】大家的支持",
            "正在玩{gamename}，很有趣",
            "现在时间是{time}"
        ]
        
        print(f"🎵 模拟播放完成: {played_item['content']}")
        print(f"📝 时间段: {played_item['time_segment']}")
        print(f"📋 可用话术: {len(segment_scripts)} 条")
        
        # 模拟生成新语音
        import random
        selected_script = random.choice(segment_scripts)
        processed_content = process_random_text(selected_script)
        
        new_item = {
            'id': 999,
            'voice_type': '主视频话术',
            'content': processed_content,
            'time_segment': played_item['time_segment'],
            'status': '未下载',
            'filename': '',
            'sub_video': '无'
        }
        
        print(f"\n🔄 补充新语音:")
        print(f"  - 原始话术: {selected_script}")
        print(f"  - 处理后: {processed_content}")
        print(f"  - 时间段: {new_item['time_segment']}")
        
        # 验证生成结果
        is_valid = (
            new_item['voice_type'] == '主视频话术' and
            new_item['time_segment'] == played_item['time_segment'] and
            new_item['content'] != selected_script  # 应该经过处理
        )
        
        print_test_result("新语音生成功能", is_valid, 
                         "成功生成新语音并正确处理变量" if is_valid else "语音生成有问题")
        
        return is_valid
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        print_test_result("新语音生成功能", False, f"异常: {e}")
        return False

def test_sub_video_ai_dialogue():
    """测试5: 带副视频的AI对话功能"""
    print_test_header("带副视频的AI对话功能")
    
    try:
        # 模拟AI对话数据
        ai_dialogue_data = {
            "游戏": "正在玩王者荣耀",
            "感谢": "谢谢大家的支持",
            "问候": "大家好，欢迎来到直播间"
        }
        
        # 模拟副视频设置
        sub_video_settings = [
            {
                "name": "游戏画面",
                "keywords": ["游戏", "王者", "英雄"],
                "source": "游戏画面源"
            },
            {
                "name": "感谢画面",
                "keywords": ["感谢", "谢谢", "支持"],
                "source": "感谢画面源"
            }
        ]
        
        print(f"📋 AI对话数据: {len(ai_dialogue_data)} 条")
        print(f"📋 副视频设置: {len(sub_video_settings)} 个")
        
        # 模拟弹幕触发AI对话
        test_danmaku_messages = [
            "主播在玩什么游戏？",
            "感谢主播的精彩直播",
            "大家好啊"
        ]
        
        all_dialogues_correct = True
        
        for message in test_danmaku_messages:
            print(f"\n💬 弹幕消息: {message}")
            
            # 查找匹配的AI对话
            matched_dialogue = None
            matched_keyword = None
            
            for keyword, reply in ai_dialogue_data.items():
                if keyword in message:
                    matched_dialogue = reply
                    matched_keyword = keyword
                    break
            
            if matched_dialogue:
                print(f"  🎯 匹配关键词: {matched_keyword}")
                print(f"  🤖 AI回复: {matched_dialogue}")
                
                # 查找对应的副视频
                matched_sub_video = None
                for sub_video in sub_video_settings:
                    if any(kw in matched_keyword for kw in sub_video["keywords"]):
                        matched_sub_video = sub_video
                        break
                
                if matched_sub_video:
                    print(f"  🎬 切换副视频: {matched_sub_video['name']} ({matched_sub_video['source']})")
                    
                    # 生成带副视频的播放项目
                    dialogue_item = {
                        'id': 888,
                        'voice_type': '弹幕话术',
                        'content': matched_dialogue,
                        'time_segment': '无',
                        'status': '模拟已下载',
                        'filename': f'dialogue_{matched_keyword}.wav',
                        'sub_video': matched_sub_video['source']
                    }
                    
                    print(f"  ✅ 生成AI对话项目: {dialogue_item['content']}")
                    print(f"  🎬 副视频源: {dialogue_item['sub_video']}")
                else:
                    print(f"  ⚠️ 未找到匹配的副视频")
                    all_dialogues_correct = False
            else:
                print(f"  ⚠️ 未找到匹配的AI对话")
        
        print_test_result("带副视频的AI对话功能", all_dialogues_correct, 
                         "所有弹幕都正确匹配AI对话和副视频" if all_dialogues_correct else "部分弹幕匹配有问题")
        
        return all_dialogues_correct
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        print_test_result("带副视频的AI对话功能", False, f"异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 AI直播系统功能测试开始")
    print("=" * 60)
    
    # 记录测试结果
    test_results = {}
    
    # 测试1: 播放列表初始化功能
    playlist_items, result1 = test_playlist_initialization()
    test_results["播放列表初始化"] = result1
    
    # 测试2: 时间段匹配功能
    result2 = test_time_segment_matching(playlist_items)
    test_results["时间段匹配"] = result2
    
    # 测试3: 播放优先级功能
    result3 = test_playback_priority()
    test_results["播放优先级"] = result3
    
    # 测试4: 新语音生成功能
    result4 = test_voice_generation()
    test_results["新语音生成"] = result4
    
    # 测试5: 带副视频的AI对话功能
    result5 = test_sub_video_ai_dialogue()
    test_results["副视频AI对话"] = result5
    
    # 汇总测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n🎯 总体结果: {passed_tests}/{total_tests} 个测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有功能测试通过！系统运行正常。")
    else:
        print("⚠️ 部分功能测试失败，需要检查相关功能。")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
