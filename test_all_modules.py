#!/usr/bin/env python3
"""
AI直播系统 v2 - 全模块测试脚本
测试所有已开发的功能模块
"""

import sys
import asyncio
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.logging_service import setup_logging, create_logger
from src.data.database_manager import DatabaseManager

# 导入所有模块
from src.core.script import ScriptManager, TimeSegmentManager, ScriptScheduler
from src.core.ai_chat import ChatManager, PromptManager, ConversationHistory
from src.core.obs import OBSController, SceneManager, SourceManager
from src.core.danmaku import DanmakuListener, DanmakuProcessor, DanmakuResponder
from src.core.timing import TimeAnnouncer, ScheduleManager


def test_obs_modules():
    """测试OBS模块"""
    print("🎥 测试OBS控制模块...")
    
    try:
        # 创建OBS控制器（不实际连接）
        obs_controller = OBSController("localhost", 4455, "")
        
        # 测试状态获取
        status = obs_controller.get_status()
        print(f"  ✅ OBS状态获取: 连接={status['connected']}")
        
        # 创建场景管理器
        scene_manager = SceneManager(obs_controller)
        
        # 添加场景配置
        scene_manager.add_scene_config("测试场景", {
            'description': '这是一个测试场景',
            'auto_switch_duration': 60,
            'priority': 1
        })
        
        # 添加场景调度
        scene_manager.add_scene_schedule({
            'scene_name': '测试场景',
            'start_time': '09:00',
            'end_time': '18:00',
            'days_of_week': '1,2,3,4,5',
            'priority': 1
        })
        
        # 获取统计
        scene_stats = scene_manager.get_scene_stats()
        print(f"  ✅ 场景管理: 配置数={scene_stats['total_scenes']}, 调度数={scene_stats['total_schedules']}")
        
        # 创建源管理器
        source_manager = SourceManager(obs_controller)
        
        # 创建源预设
        preset = source_manager.create_source_preset("测试预设", [
            {
                'source_name': '文本源',
                'visible': True,
                'settings': {'text': '测试文本'}
            }
        ])
        print(f"  ✅ 源管理: 预设创建成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ OBS模块测试失败: {e}")
        return False


def test_danmaku_modules():
    """测试弹幕模块"""
    print("\n💬 测试弹幕模块...")
    
    try:
        # 创建数据库管理器
        db = DatabaseManager("data/test_danmaku.db")
        
        # 创建弹幕监听器
        listener = DanmakuListener("ws://127.0.0.1:9999")
        
        # 测试连接（不实际连接）
        stats = listener.get_stats()
        print(f"  ✅ 弹幕监听器: 连接状态={stats['is_connected']}")
        
        # 创建弹幕处理器
        processor = DanmakuProcessor(db)
        
        # 测试弹幕处理
        test_danmaku = {
            'username': '测试用户',
            'content': '你好主播！',
            'user_id': 'test_user_123',
            'timestamp': datetime.now(),
            'platform': 'test'
        }
        
        result = processor.process_danmaku(test_danmaku)
        print(f"  ✅ 弹幕处理: 处理成功={result['processed']}, 关键词={result['keywords_matched']}")
        
        # 创建弹幕响应器
        responder = DanmakuResponder()
        
        # 测试响应生成
        response = responder.generate_response(test_danmaku, result)
        if response:
            print(f"  ✅ 弹幕响应: {response['text'][:30]}...")
        else:
            print(f"  ℹ️  弹幕响应: 无需响应")
            
        # 获取统计
        processor_stats = processor.get_processing_stats()
        print(f"  ✅ 处理统计: 总处理数={processor_stats['total_processed']}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 弹幕模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_timing_modules():
    """测试报时模块"""
    print("\n⏰ 测试报时模块...")
    
    try:
        # 创建时间播报器
        announcer = TimeAnnouncer()
        
        # 更新配置
        announcer.update_config({
            'enabled': True,
            'interval_minutes': 1,  # 测试用1分钟间隔
            'voice_enabled': False  # 禁用语音避免依赖
        })
        
        # 测试立即播报
        success = announcer.announce_now("这是一个测试播报")
        print(f"  ✅ 立即播报: 成功={success}")
        
        # 添加自定义消息
        announcer.add_custom_message("欢迎来到AI直播系统测试！")
        
        # 获取统计
        stats = announcer.get_stats()
        print(f"  ✅ 播报统计: 总播报数={stats['total_announcements']}")
        
        # 创建调度管理器
        scheduler = ScheduleManager()
        
        # 设置管理器引用
        scheduler.set_managers(time_announcer=announcer)
        
        # 创建测试任务
        task = scheduler.create_interval_task(
            task_id="test_announce",
            name="测试播报任务",
            action="announce",
            interval_seconds=300,  # 5分钟间隔
            params={'message': '这是定时播报测试'}
        )
        
        # 添加任务
        task_added = scheduler.add_task(task)
        print(f"  ✅ 任务调度: 任务添加={task_added}")
        
        # 获取统计
        scheduler_stats = scheduler.get_stats()
        print(f"  ✅ 调度统计: 总任务数={scheduler_stats['total_tasks']}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 报时模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_integration():
    """集成测试"""
    print("\n🔗 集成测试...")
    
    try:
        # 创建数据库管理器
        db = DatabaseManager("data/test_integration.db")
        
        # 创建各个管理器
        script_manager = ScriptManager(db)
        chat_manager = ChatManager(db)
        announcer = TimeAnnouncer()
        
        # 创建测试话术
        script_id = script_manager.create_script(
            name="集成测试话术",
            content="欢迎来到AI直播系统\n这是一个功能强大的直播助手\n感谢大家的支持",
            category="integration"
        )
        
        # 生成AI回复
        ai_response = chat_manager.generate_response("这个系统怎么样？")
        
        # 执行播报
        announce_success = announcer.announce_now("集成测试进行中...")
        
        print(f"  ✅ 话术创建: ID {script_id}")
        print(f"  ✅ AI回复: {ai_response[:30] if ai_response else '无回复'}...")
        print(f"  ✅ 播报执行: 成功={announce_success}")
        
        # 模拟弹幕处理流程
        processor = DanmakuProcessor(db)
        responder = DanmakuResponder(chat_manager)
        
        test_danmaku = {
            'username': '集成测试用户',
            'content': '这个直播系统真不错！',
            'user_id': 'integration_test_user',
            'timestamp': datetime.now()
        }
        
        # 处理弹幕
        process_result = processor.process_danmaku(test_danmaku)
        
        # 生成响应
        response_result = responder.generate_response(test_danmaku, process_result)
        
        print(f"  ✅ 弹幕处理: 关键词={process_result.get('keywords_matched', [])}")
        if response_result:
            print(f"  ✅ 自动回复: {response_result['text'][:30]}...")
        
        print("  ✅ 集成测试完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🎬 AI直播系统 v2 - 全模块测试")
    print("=" * 60)
    
    # 初始化日志系统
    setup_logging()
    logger = create_logger("test")
    
    test_results = []
    
    try:
        # 测试OBS模块
        obs_result = test_obs_modules()
        test_results.append(("OBS控制模块", obs_result))
        
        # 测试弹幕模块
        danmaku_result = test_danmaku_modules()
        test_results.append(("弹幕模块", danmaku_result))
        
        # 测试报时模块
        timing_result = test_timing_modules()
        test_results.append(("报时模块", timing_result))
        
        # 集成测试
        integration_result = test_integration()
        test_results.append(("集成测试", integration_result))
        
        # 输出测试结果
        print("\n🎉 测试结果总结:")
        print("=" * 60)
        
        all_passed = True
        for module_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {module_name}: {status}")
            if not result:
                all_passed = False
                
        if all_passed:
            print("\n🎊 所有模块测试通过！AI直播系统功能完整！")
            print("\n📋 已完成的功能模块:")
            print("  - 话术管理和调度")
            print("  - AI对话和回复")
            print("  - OBS场景控制")
            print("  - 弹幕监听和处理")
            print("  - 定时播报和调度")
            print("  - 模块间集成")
            
            print("\n🚀 系统已准备就绪，可以开始直播！")
        else:
            print("\n⚠️  部分模块测试失败，请检查相关功能。")
            
        # 清理测试数据
        test_files = [
            "data/test_danmaku.db",
            "data/test_integration.db"
        ]
        
        for file_path in test_files:
            test_file = Path(file_path)
            if test_file.exists():
                test_file.unlink()
                
        print("\n🧹 测试数据已清理")
        
        return 0 if all_passed else 1
        
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
