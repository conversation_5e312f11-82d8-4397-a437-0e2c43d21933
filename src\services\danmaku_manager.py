"""
AI Broadcaster v2 - 弹幕管理器
通过WebSocket连接弹幕服务器，处理弹幕消息
"""

import json
import asyncio
import websockets
import threading
import time
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime

from .logging_service import create_logger
from .error_handler import handle_exceptions


class DanmakuMessage:
    """🔥 修复：弹幕消息，支持多种类型"""

    def __init__(self, user: str, message: str, timestamp: float = None,
                 message_type: str = "ChatMessage", **extra_data):
        self.user = user
        self.message = message
        self.timestamp = timestamp or time.time()
        self.processed = False
        self.ai_triggered = False

        # 🔥 新增：支持不同类型的弹幕
        self.type = message_type
        self.extra_data = extra_data  # 存储额外数据，如礼物名称、在线人数等

        # 🔥 修复：将额外数据设置为对象的直接属性，方便访问
        for key, value in extra_data.items():
            setattr(self, key, value)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            'user': self.user,
            'message': self.message,
            'timestamp': self.timestamp,
            'time_str': datetime.fromtimestamp(self.timestamp).strftime('%H:%M:%S'),
            'processed': self.processed,
            'ai_triggered': self.ai_triggered,
            'type': self.type
        }

        # 添加额外数据
        result.update(self.extra_data)
        return result


class DanmakuManager:
    """弹幕管理器"""

    def __init__(self, server_url: str = "ws://127.0.0.1:9999"):
        self.logger = create_logger("danmaku_manager")
        self.server_url = server_url

        # 连接状态
        self.websocket = None
        self.connected = False
        self.connecting = False

        # 弹幕数据
        self.messages = []  # 弹幕消息列表
        self.max_messages = 1000  # 最大保存消息数

        # 回调函数
        self.on_connection_change: Optional[Callable] = None
        self.on_message_received: Optional[Callable] = None
        self.on_ai_triggered: Optional[Callable] = None  # AI对话触发回调

        # 事件循环
        self.loop = None
        self.loop_thread = None

        self.logger.info("弹幕管理器初始化完成")

    @handle_exceptions("DanmakuManager")
    def connect(self) -> bool:
        """连接弹幕服务器"""
        if self.connected or self.connecting:
            return self.connected

        try:
            self.connecting = True

            # 启动事件循环线程
            if not self.loop_thread or not self.loop_thread.is_alive():
                self.loop_thread = threading.Thread(target=self._run_event_loop, daemon=True)
                self.loop_thread.start()

                # 等待事件循环启动
                time.sleep(0.5)

            # 在事件循环中连接
            if self.loop:
                future = asyncio.run_coroutine_threadsafe(self._connect_async(), self.loop)
                result = future.result(timeout=10)
                return result

            return False

        except Exception as e:
            self.logger.error(f"连接弹幕服务器失败: {e}")
            self.connecting = False
            return False

    def _run_event_loop(self):
        """运行事件循环"""
        try:
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            self.loop.run_forever()
        except Exception as e:
            self.logger.error(f"事件循环异常: {e}")

    async def _connect_async(self) -> bool:
        """异步连接弹幕服务器"""
        try:
            self.websocket = await websockets.connect(self.server_url)

            # 启动消息监听
            asyncio.create_task(self._listen_messages())

            self.connected = True
            self.connecting = False

            self.logger.info(f"已连接到弹幕服务器: {self.server_url}")

            # 触发连接状态回调
            if self.on_connection_change:
                self.on_connection_change(True)

            return True

        except Exception as e:
            self.logger.error(f"异步连接弹幕服务器失败: {e}")
            self.connected = False
            self.connecting = False
            return False

    async def _listen_messages(self):
        """监听弹幕消息"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self._handle_message(data)
                except json.JSONDecodeError:
                    # 如果不是JSON，当作普通文本处理
                    await self._handle_text_message(message)

        except websockets.exceptions.ConnectionClosed:
            self.logger.info("弹幕连接已断开")
            self.connected = False
            if self.on_connection_change:
                self.on_connection_change(False)
        except Exception as e:
            self.logger.error(f"监听弹幕消息异常: {e}")

    async def _handle_message(self, data: Dict[str, Any]):
        """🔥 修复：处理弹幕消息，支持所有类型"""
        try:
            # 根据消息类型处理不同的弹幕数据格式
            message_type = data.get('type', '')

            if message_type == "ChatMessage":
                # 1. 发送消息的弹幕信息
                user = data.get('name', '匿名用户')
                message = data.get('content', '')

                if message:
                    danmaku_msg = DanmakuMessage(user, message, message_type="ChatMessage")
                    self._add_message(danmaku_msg)

                    self.logger.info(f"收到聊天弹幕: {user}: {message}")

                    # 触发消息接收回调
                    if self.on_message_received:
                        self.on_message_received(danmaku_msg)

            elif message_type == "GiftMessage":
                # 2. 送礼物类型的弹幕信息
                user = data.get('name', '匿名用户')
                gift_name = data.get('giftName', '礼物')
                message = f"送出了{gift_name}"

                danmaku_msg = DanmakuMessage(user, message, message_type="GiftMessage",
                                           giftName=gift_name)
                self._add_message(danmaku_msg)

                self.logger.info(f"收到礼物: {user} 送出 {gift_name}")

                # 触发消息接收回调
                if self.on_message_received:
                    self.on_message_received(danmaku_msg)

            elif message_type == "MemberMessage":
                # 3. 进入直播间的弹幕信息
                user = data.get('name', '匿名用户')
                message = "进入直播间"

                danmaku_msg = DanmakuMessage(user, message, message_type="MemberMessage")
                self._add_message(danmaku_msg)

                self.logger.info(f"用户进入: {user}")

                # 触发消息接收回调
                if self.on_message_received:
                    self.on_message_received(danmaku_msg)

            elif message_type == "live_like":
                # 4. 点赞直播间的弹幕信息
                user = data.get('name', '匿名用户')
                message = "点赞直播间"

                danmaku_msg = DanmakuMessage(user, message, message_type="live_like")
                self._add_message(danmaku_msg)

                self.logger.info(f"用户点赞: {user}")

                # 触发消息接收回调
                if self.on_message_received:
                    self.on_message_received(danmaku_msg)

            elif message_type == "SocialMessage":
                # 5. 关注主播的弹幕信息
                user = data.get('name', '匿名用户')
                message = "关注主播"

                danmaku_msg = DanmakuMessage(user, message, message_type="SocialMessage")
                self._add_message(danmaku_msg)

                self.logger.info(f"用户关注: {user}")

                # 触发消息接收回调
                if self.on_message_received:
                    self.on_message_received(danmaku_msg)

            elif message_type == "RoomUserSeqMessage":
                # 6. 直播间总人数的弹幕信息
                total_count = data.get('total', 0)
                user = "系统"
                message = f"当前在线人数: {total_count}"

                danmaku_msg = DanmakuMessage(user, message, message_type="RoomUserSeqMessage",
                                           total=total_count)
                self._add_message(danmaku_msg)

                self.logger.info(f"直播间人数更新: {total_count}")

                # 触发消息接收回调
                if self.on_message_received:
                    self.on_message_received(danmaku_msg)

            else:
                # 兼容旧格式：{"user": "用户名", "message": "消息"}
                user = data.get('user', '匿名用户')
                message = data.get('message', '')

                if message:
                    danmaku_msg = DanmakuMessage(user, message, message_type="ChatMessage")
                    self._add_message(danmaku_msg)

                    self.logger.info(f"收到弹幕: {user}: {message}")

                    # 触发消息接收回调
                    if self.on_message_received:
                        self.on_message_received(danmaku_msg)

        except Exception as e:
            self.logger.error(f"处理弹幕消息异常: {e}")

    async def _handle_text_message(self, message: str):
        """处理文本消息"""
        try:
            # 简单的文本消息，当作匿名弹幕处理
            danmaku_msg = DanmakuMessage('匿名用户', message.strip())
            self._add_message(danmaku_msg)

            self.logger.info(f"收到文本弹幕: {message}")

            # 触发消息接收回调
            if self.on_message_received:
                self.on_message_received(danmaku_msg)

        except Exception as e:
            self.logger.error(f"处理文本消息异常: {e}")

    def _add_message(self, message: DanmakuMessage):
        """添加弹幕消息"""
        self.messages.append(message)

        # 限制消息数量
        if len(self.messages) > self.max_messages:
            self.messages = self.messages[-self.max_messages:]

    @handle_exceptions("DanmakuManager")
    def disconnect(self):
        """断开弹幕连接"""
        try:
            if self.websocket and self.connected:
                if self.loop:
                    asyncio.run_coroutine_threadsafe(self.websocket.close(), self.loop)

                self.connected = False
                self.websocket = None

                self.logger.info("已断开弹幕连接")

                # 触发连接状态回调
                if self.on_connection_change:
                    self.on_connection_change(False)

        except Exception as e:
            self.logger.error(f"断开弹幕连接失败: {e}")

    @handle_exceptions("DanmakuManager")
    def send_message(self, user: str, message: str) -> bool:
        """发送模拟弹幕"""
        try:
            if not self.connected or not self.websocket:
                self.logger.error("弹幕服务器未连接")
                return False

            # 🔥 修复：使用与真实弹幕相同的格式
            data = {
                'type': 'ChatMessage',  # 使用真实弹幕的类型
                'name': user,           # 使用真实弹幕的用户名字段
                'content': message,     # 使用真实弹幕的内容字段
                'simulate': True        # 标记为模拟弹幕，避免重复处理
            }

            # 在事件循环中发送
            if self.loop:
                future = asyncio.run_coroutine_threadsafe(
                    self.websocket.send(json.dumps(data)), self.loop
                )
                future.result(timeout=5)

                self.logger.info(f"发送模拟弹幕: {user}: {message}")

                # 🔥 修复：只发送到服务器，不直接处理，让服务器回传后统一处理
                # 这样确保测试弹幕和真实弹幕走相同的处理流程

                return True

            return False

        except Exception as e:
            self.logger.error(f"发送模拟弹幕失败: {e}")
            return False

    def get_messages(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取弹幕消息列表"""
        messages = self.messages[-limit:] if limit > 0 else self.messages
        return [msg.to_dict() for msg in messages]

    def clear_messages(self):
        """清空弹幕消息"""
        self.messages.clear()
        self.logger.info("弹幕消息已清空")

    def mark_ai_triggered(self, message_index: int, keyword: str):
        """标记弹幕触发了AI对话"""
        try:
            if 0 <= message_index < len(self.messages):
                message = self.messages[message_index]
                message.ai_triggered = True
                message.processed = True

                self.logger.info(f"弹幕触发AI对话: {message.user}: {message.message} -> {keyword}")

                # 触发AI对话回调
                if self.on_ai_triggered:
                    self.on_ai_triggered(message, keyword)

        except Exception as e:
            self.logger.error(f"标记AI触发失败: {e}")

    def check_ai_keywords(self, keywords: List[str]) -> List[Dict[str, Any]]:
        """检查弹幕中的AI关键词"""
        triggered_messages = []

        for i, message in enumerate(self.messages):
            if message.processed:
                continue

            for keyword in keywords:
                if keyword.lower() in message.message.lower():
                    triggered_messages.append({
                        'message_index': i,
                        'message': message.to_dict(),
                        'keyword': keyword
                    })
                    break

        return triggered_messages

    def get_connection_status(self) -> Dict[str, Any]:
        """获取连接状态"""
        return {
            'connected': self.connected,
            'connecting': self.connecting,
            'server_url': self.server_url,
            'message_count': len(self.messages),
            'max_messages': self.max_messages
        }

    def get_statistics(self) -> Dict[str, Any]:
        """获取弹幕统计信息"""
        total_messages = len(self.messages)
        ai_triggered = sum(1 for msg in self.messages if msg.ai_triggered)
        processed = sum(1 for msg in self.messages if msg.processed)

        return {
            'total_messages': total_messages,
            'ai_triggered_count': ai_triggered,
            'processed_count': processed,
            'unprocessed_count': total_messages - processed,
            'connected': self.connected
        }


def main():
    """测试函数"""
    manager = DanmakuManager()

    # 设置回调
    def on_connection_change(connected):
        print(f"连接状态变化: {'已连接' if connected else '已断开'}")

    def on_message_received(message):
        print(f"收到弹幕: {message.user}: {message.message}")

    def on_ai_triggered(message, keyword):
        print(f"AI触发: {message.user}: {message.message} -> {keyword}")

    manager.on_connection_change = on_connection_change
    manager.on_message_received = on_message_received
    manager.on_ai_triggered = on_ai_triggered

    # 测试连接
    if manager.connect():
        print("✅ 弹幕服务器连接成功")

        # 发送测试弹幕
        manager.send_message("测试用户", "你好，这是测试弹幕")
        manager.send_message("测试用户2", "游戏很好玩")

        # 等待一段时间
        time.sleep(2)

        # 获取消息
        messages = manager.get_messages(10)
        print(f"弹幕消息: {messages}")

        # 测试AI关键词检查
        keywords = ["游戏", "你好", "测试"]
        triggered = manager.check_ai_keywords(keywords)
        print(f"触发的关键词: {triggered}")

        # 断开连接
        manager.disconnect()
    else:
        print("❌ 弹幕服务器连接失败")


if __name__ == "__main__":
    main()
