#!/usr/bin/env python3
"""
测试新的话术格式功能
"""

import json

def test_new_script_format():
    """测试新的话术格式"""
    print("=== 测试新话术格式 ===")
    
    # 新格式示例
    new_format_data = {
        "0秒 - 10秒": "1***哈喽，大家好，首先感谢【大哥|哥哥|宝子】们的捧场。\n2***稍作停留也是爱，所能接触的都是这四个大字，随便那个都是这四个大字。\n3***没有花里胡哨，没有弯弯绕绕。\n4***有啥就问，新来的家人们，不要藏着掖着。\n5***云想衣裳花想容，春风拂槛露华浓，仙侠界的白莲花，花容月貌顶呱呱。",
        "10秒 - 20秒": "6***自古套路得人心，但唯有真心得天下，咱们心不要慌，手不要抖，跟着感觉走。\n7***新来的家人们，相遇就是缘分，不要犹豫不要徘徊。\n8***你们的到来蓬荜生辉，老妹我深感荣幸\n9***随便什么数字都是这四个大字，目光所及都是这四个大字。\n10***俗话说的好，大哥们，人生不主动，快乐少一半",
        "20秒 - 30秒": "11***相信自己的眼睛，相信自己的耳朵\n12***斩青丝斩难过斩断红尘不为过，建模美 姿势帅，呼风唤雨惹人爱\n13***不穿摸，不贴片，仙侠界的彭于晏，（千人捧万人追）手游界的刘亦菲\n14***播播间人很多，主播一个人一张嘴，忙不过来，理解下，大哥们\n15***万水千山总是情，今天主播最热情",
        "30秒 - 40秒": "16***今天只为做人气，玩归玩，闹归闹，不拿真诚开玩笑\n17***我一说，你一笑，【大哥|哥哥|宝子】们开心比啥都重要。\n18***落地人上人，只管开心，只管快乐。\n19***千里姻缘一线牵，欢迎大哥们来到播播间\n20***山外青山楼外楼，播播间里无忧愁。",
        "40秒 - 50秒": "21***只管开心只管爽，都是这四个大字都是这四个大字\n22***走过南呀闯过北，主播认识大家不后悔。\n23***有啥就说有啥就问，相遇就是缘分。人生何处不相逢啊。\n24***给个面子捧个场，大哥们，不要到处乱跑了\n25***真真实实，实实在在，简简又单单。",
        "50秒 - 60秒": "26***没有花里胡哨，没有弯弯绕绕，啥啥都是这四个大字。\n27***相信自己的眼睛，相信自己的耳朵\n28***老弟正儿八经，诚意满满，爱意满满（今天呢，老弟也是带着诚意来的）\n29***新来的大哥们，心别慌手别抖，不要着急离开\n30***万水千山总是情，今天主播最热情"
    }
    
    # 转换为JSON字符串
    json_content = json.dumps(new_format_data, ensure_ascii=False, indent=2)
    
    print("新格式JSON内容:")
    print(json_content[:500] + "..." if len(json_content) > 500 else json_content)
    
    # 验证JSON格式
    try:
        parsed_data = json.loads(json_content)
        print(f"\n✓ JSON格式验证成功")
        print(f"✓ 时间段数量: {len(parsed_data)}")
        
        # 验证数据结构
        for segment_name, segment_content in parsed_data.items():
            if isinstance(segment_content, str):
                lines = segment_content.split('\n')
                print(f"✓ {segment_name}: {len(lines)} 行话术")
            else:
                print(f"✗ {segment_name}: 数据类型错误 ({type(segment_content)})")
                
        return True
        
    except json.JSONDecodeError as e:
        print(f"✗ JSON格式验证失败: {e}")
        return False

def test_format_conversion():
    """测试格式转换功能"""
    print("\n=== 测试格式转换功能 ===")
    
    # 旧格式示例
    old_format_data = {
        "0秒 - 10秒": {
            "start": 0,
            "end": 10,
            "content": "1***哈喽，大家好\n2***感谢大家的捧场"
        },
        "10秒 - 20秒": {
            "start": 10,
            "end": 20,
            "content": "3***自古套路得人心\n4***新来的家人们"
        }
    }
    
    print("旧格式数据:")
    print(json.dumps(old_format_data, ensure_ascii=False, indent=2))
    
    # 转换为新格式
    def convert_old_to_new(old_data):
        """将旧格式转换为新格式"""
        new_data = {}
        for segment_name, segment_data in old_data.items():
            if isinstance(segment_data, dict) and 'content' in segment_data:
                new_data[segment_name] = segment_data['content']
            else:
                new_data[segment_name] = str(segment_data)
        return new_data
    
    new_format_data = convert_old_to_new(old_format_data)
    
    print("\n转换后的新格式:")
    print(json.dumps(new_format_data, ensure_ascii=False, indent=2))
    
    # 验证转换结果
    if len(new_format_data) == len(old_format_data):
        print("✓ 转换成功，时间段数量一致")
        
        for segment_name in old_format_data.keys():
            if segment_name in new_format_data:
                old_content = old_format_data[segment_name].get('content', '')
                new_content = new_format_data[segment_name]
                if old_content == new_content:
                    print(f"✓ {segment_name}: 内容转换正确")
                else:
                    print(f"✗ {segment_name}: 内容转换错误")
            else:
                print(f"✗ {segment_name}: 时间段丢失")
        
        return True
    else:
        print("✗ 转换失败，时间段数量不一致")
        return False

def test_script_parsing():
    """测试话术解析功能"""
    print("\n=== 测试话术解析功能 ===")
    
    # 模拟解析新格式的函数
    def parse_new_format(content):
        """解析新格式话术"""
        try:
            data = json.loads(content)
            
            # 检查是否为新格式
            if isinstance(data, dict) and all(isinstance(v, str) for v in data.values()):
                print("✓ 检测到新格式话术")
                
                # 解析每个时间段
                parsed_segments = {}
                for segment_name, segment_content in data.items():
                    # 提取时间信息
                    import re
                    pattern = r'(\d+)秒\s*-\s*(\d+)秒'
                    match = re.search(pattern, segment_name)
                    
                    if match:
                        start_time = int(match.group(1))
                        end_time = int(match.group(2))
                    else:
                        start_time = 0
                        end_time = 60
                    
                    # 解析话术行
                    lines = segment_content.split('\n')
                    script_lines = [line.strip() for line in lines if line.strip()]
                    
                    parsed_segments[segment_name] = {
                        'start': start_time,
                        'end': end_time,
                        'content': segment_content,
                        'script_count': len(script_lines)
                    }
                    
                    print(f"  ✓ {segment_name}: {start_time}-{end_time}秒, {len(script_lines)}行话术")
                
                return parsed_segments
            else:
                print("✗ 不是新格式话术")
                return None
                
        except json.JSONDecodeError:
            print("✗ JSON解析失败")
            return None
    
    # 测试数据
    test_content = """{
  "0秒 - 10秒": "1***哈喽，大家好\\n2***感谢大家的捧场",
  "10秒 - 20秒": "3***自古套路得人心\\n4***新来的家人们"
}"""
    
    print("测试内容:")
    print(test_content)
    
    result = parse_new_format(test_content)
    
    if result:
        print(f"\n✓ 解析成功，共 {len(result)} 个时间段")
        return True
    else:
        print("\n✗ 解析失败")
        return False

def test_ui_width_calculation():
    """测试UI宽度计算"""
    print("\n=== 测试UI宽度设置 ===")
    
    # 模拟选择框宽度设置
    combo_width = 180  # 固定宽度
    button_width = 80   # 保存按钮宽度
    label_width = 80    # 标签宽度
    
    total_width = label_width + combo_width + 20  # 20px间距
    
    print(f"选择框设置:")
    print(f"  标签宽度: {label_width}px")
    print(f"  选择框宽度: {combo_width}px")
    print(f"  保存按钮宽度: {button_width}px")
    print(f"  总宽度: {total_width}px")
    
    # 验证宽度合理性
    if combo_width > 100 and combo_width < 300:
        print("✓ 选择框宽度设置合理")
        return True
    else:
        print("✗ 选择框宽度设置不合理")
        return False

def main():
    """主测试函数"""
    print("开始测试新话术格式功能...")
    
    try:
        # 运行所有测试
        test1 = test_new_script_format()
        test2 = test_format_conversion()
        test3 = test_script_parsing()
        test4 = test_ui_width_calculation()
        
        print("\n=== 测试总结 ===")
        if test1 and test2 and test3 and test4:
            print("✅ 所有测试通过")
            print("新话术格式功能实现正确")
        else:
            print("❌ 部分测试失败")
            print("需要进一步检查")
            
        print("\n功能说明:")
        print("1. 选择框宽度已缩短至180px，与保存按钮右边齐平")
        print("2. 话术格式已更新为新的JSON格式")
        print("3. 上传和获取话术都支持新格式")
        print("4. 自动兼容旧格式，可以无缝转换")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")

if __name__ == "__main__":
    main()
