#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的更新对话框
避免复杂的导入依赖，直接使用PyQt5基础组件
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
    QTextEdit, QPushButton, QFrame, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from typing import Dict, Any

class SimpleUpdateDialog(QDialog):
    """简化的更新对话框类"""
    
    # 定义信号
    update_confirmed = pyqtSignal()  # 用户确认已更新
    update_skipped = pyqtSignal()    # 用户选择不更新
    
    def __init__(self, update_info: Dict[str, Any], current_version: str, parent=None):
        super().__init__(parent)
        self.update_info = update_info
        self.current_version = current_version
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("发现新版本")
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题区域
        self.create_title_section(main_layout)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(line)
        
        # 版本信息区域
        self.create_version_section(main_layout)
        
        # 更新内容区域
        self.create_content_section(main_layout)
        
        # 按钮区域
        self.create_button_section(main_layout)
        
    def create_title_section(self, parent_layout):
        """创建标题区域"""
        title_layout = QHBoxLayout()
        
        # 更新图标
        icon_label = QLabel("🆕")
        icon_label.setStyleSheet("font-size: 32px;")
        title_layout.addWidget(icon_label)
        
        # 标题文本
        title_label = QLabel("发现新版本")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        parent_layout.addLayout(title_layout)
        
    def create_version_section(self, parent_layout):
        """创建版本信息区域"""
        version_layout = QVBoxLayout()
        
        # 当前版本
        current_label = QLabel(f"当前版本: {self.current_version}")
        current_label.setStyleSheet("color: #666; font-size: 12px;")
        version_layout.addWidget(current_label)
        
        # 最新版本
        latest_version = self.update_info.get('version', '未知')
        latest_label = QLabel(f"最新版本: {latest_version}")
        latest_font = QFont()
        latest_font.setPointSize(14)
        latest_font.setBold(True)
        latest_label.setFont(latest_font)
        latest_label.setStyleSheet("color: #2196F3;")
        version_layout.addWidget(latest_label)
        
        # 发布日期
        release_date = self.update_info.get('release_date', '未知日期')
        date_label = QLabel(f"发布日期: {release_date}")
        date_label.setStyleSheet("color: #666; font-size: 12px;")
        version_layout.addWidget(date_label)
        
        parent_layout.addLayout(version_layout)
        
    def create_content_section(self, parent_layout):
        """创建更新内容区域"""
        content_label = QLabel("更新内容:")
        content_font = QFont()
        content_font.setBold(True)
        content_label.setFont(content_font)
        parent_layout.addWidget(content_label)
        
        # 更新内容文本框
        self.content_text = QTextEdit()
        self.content_text.setReadOnly(True)
        self.content_text.setMaximumHeight(150)
        
        # 设置更新内容
        description = self.update_info.get('description', '暂无更新说明')
        self.content_text.setPlainText(description)
        
        # 设置样式
        self.content_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px;
                background-color: #f9f9f9;
                font-family: 'Microsoft YaHei', sans-serif;
                font-size: 12px;
            }
        """)
        
        parent_layout.addWidget(self.content_text)
        
        # 说明文本
        info_label = QLabel("请选择是否已完成更新：")
        info_label.setStyleSheet("color: #666; font-size: 12px; margin-top: 10px;")
        parent_layout.addWidget(info_label)
        
    def create_button_section(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 不更新按钮
        self.skip_button = QPushButton("不更新")
        self.skip_button.setFixedSize(100, 35)
        self.skip_button.setStyleSheet("""
            QPushButton {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e9e9e9;
                border-color: #ccc;
            }
            QPushButton:pressed {
                background-color: #ddd;
            }
        """)
        self.skip_button.clicked.connect(self.on_skip_update)
        button_layout.addWidget(self.skip_button)
        
        # 间距
        button_layout.addSpacing(10)
        
        # 已更新按钮
        self.confirm_button = QPushButton("已更新")
        self.confirm_button.setFixedSize(100, 35)
        self.confirm_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                border: none;
                border-radius: 4px;
                color: white;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)
        self.confirm_button.clicked.connect(self.on_confirm_update)
        button_layout.addWidget(self.confirm_button)
        
        parent_layout.addLayout(button_layout)
        
    def on_confirm_update(self):
        """用户确认已更新"""
        reply = QMessageBox.question(
            self,
            "确认更新",
            "确认您已手动更新到新版本？\n\n点击'是'将更新本地版本记录。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.update_confirmed.emit()
            self.accept()
    
    def on_skip_update(self):
        """用户选择不更新"""
        self.update_skipped.emit()
        self.reject()
    
    def closeEvent(self, event):
        """处理窗口关闭事件"""
        # 关闭窗口等同于选择不更新
        self.update_skipped.emit()
        event.accept()
    
    def get_update_info(self) -> Dict[str, Any]:
        """获取更新信息"""
        return self.update_info
    
    def get_current_version(self) -> str:
        """获取当前版本"""
        return self.current_version
