#!/usr/bin/env python3
"""
AI主播系统 v2 - GUI API功能测试
测试主界面中的API功能是否正常
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.logging_service import setup_logging, create_logger
from src.services.api_manager import APIManager


def test_gui_api_functions():
    """测试GUI中使用的API功能"""
    print("🚀 测试GUI API功能")
    print("="*50)

    # 初始化API管理器
    config = {
        'voice_api_url': 'http://ct.scjanelife.com/voice',  # 外部语音服务器
        'server_url': 'http://localhost:12456'  # 本地服务器
    }
    api_manager = APIManager(config)

    try:
        # 1. 测试主播列表功能
        print("\n🎤 测试主播列表功能")
        print("-" * 30)
        speakers_result = api_manager.get_speakers()
        if speakers_result['success']:
            speakers = speakers_result['speakers']
            print(f"✅ 获取到 {len(speakers)} 个主播:")
            for i, speaker in enumerate(speakers):
                if i >= 3:  # 只显示前3个
                    break
                if isinstance(speaker, dict):
                    display_text = f"ID:{speaker.get('id')} - {speaker.get('name', '未知主播')}"
                else:
                    display_text = str(speaker)
                print(f"   {display_text}")
        else:
            print(f"❌ 获取主播列表失败: {speakers_result['message']}")

        # 2. 测试话术列表功能
        print("\n📝 测试话术列表功能")
        print("-" * 30)
        scripts_result = api_manager.get_script_list()
        if scripts_result['success']:
            print(f"✅ 获取到 {len(scripts_result['scripts'])} 个话术:")
            for script in scripts_result['scripts'][:3]:
                script_name = script.get('name', '未知话术')
                print(f"   {script_name}")

            # 测试获取第一个话术内容
            if scripts_result['scripts']:
                first_script = scripts_result['scripts'][0]['name']
                print(f"\n📄 测试获取话术内容: {first_script}")
                content_result = api_manager.get_script_content(first_script)
                if content_result['success']:
                    content = content_result['content'][:100] + "..." if len(content_result['content']) > 100 else content_result['content']
                    print(f"✅ 内容预览: {content}")
                else:
                    print(f"❌ 获取话术内容失败: {content_result['message']}")
        else:
            print(f"❌ 获取话术列表失败: {scripts_result['message']}")

        # 3. 测试AI对话列表功能
        print("\n💬 测试AI对话列表功能")
        print("-" * 30)
        dialogues_result = api_manager.get_dialogue_list()
        if dialogues_result['success']:
            print(f"✅ 获取到 {len(dialogues_result['dialogues'])} 个AI对话:")
            for dialogue in dialogues_result['dialogues'][:3]:
                dialogue_name = dialogue.get('name', '未知对话')
                print(f"   {dialogue_name}")

            # 测试获取第一个对话内容
            if dialogues_result['dialogues']:
                first_dialogue = dialogues_result['dialogues'][0]['name']
                print(f"\n📄 测试获取对话内容: {first_dialogue}")
                content_result = api_manager.get_dialogue_content(first_dialogue)
                if content_result['success']:
                    content = content_result['content'][:200] + "..." if len(content_result['content']) > 200 else content_result['content']
                    print(f"✅ 内容预览: {content}")

                    # 解析对话内容
                    print("\n🔍 解析对话关键词:")
                    try:
                        import json
                        data = json.loads(content_result['content'])

                        if 'data' in data and isinstance(data['data'], list):
                            keywords = []
                            for item in data['data']:
                                if isinstance(item, dict) and 'gjc' in item:
                                    keywords.append(item['gjc'])

                            if keywords:
                                print(f"✅ 找到 {len(keywords)} 个关键词: {', '.join(keywords[:5])}")
                            else:
                                print("⚠️ 未找到关键词")
                        else:
                            print("⚠️ 对话数据格式不正确")
                    except json.JSONDecodeError:
                        print("⚠️ 对话内容不是有效的JSON格式")
                else:
                    print(f"❌ 获取对话内容失败: {content_result['message']}")
        else:
            print(f"❌ 获取AI对话列表失败: {dialogues_result['message']}")

        # 4. 测试服务状态
        print("\n📊 测试服务状态")
        print("-" * 30)
        status = api_manager.get_service_status()
        for service_name, service_status in status.items():
            available = "✅" if service_status['available'] else "❌"
            print(f"{service_name}: {available}")

            if service_name == 'voice_service' and service_status['available']:
                cache_info = service_status['cache_info']
                print(f"   缓存文件: {cache_info['file_count']} 个")
                print(f"   缓存大小: {cache_info['total_size_mb']} MB")

        print("\n" + "="*50)
        print("🎉 GUI API功能测试完成！")
        print("\n💡 测试结果总结:")
        print("   - 主播列表: 可用于下拉选择框")
        print("   - 话术管理: 支持列表获取和内容编辑")
        print("   - AI对话管理: 支持对话选择和关键词解析")
        print("   - 所有功能都已集成到主界面中")

    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        api_manager.close()


def main():
    """主函数"""
    # 初始化日志
    setup_logging()
    logger = create_logger("gui_api_test")

    try:
        test_gui_api_functions()
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        logger.error(f"测试失败: {e}")
        print(f"\n❌ 测试失败: {e}")


if __name__ == "__main__":
    main()
