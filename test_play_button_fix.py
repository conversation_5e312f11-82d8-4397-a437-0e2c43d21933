#!/usr/bin/env python3
"""
测试播放按钮修复 - 验证点击播放后是否生成预备语音
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QFont


class PlayButtonTestWindow(QMainWindow):
    """播放按钮测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.init_test_data()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("播放按钮功能测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 标题
        title_label = QLabel("🎮 播放按钮功能测试")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #333; margin: 20px; text-align: center;")
        layout.addWidget(title_label)
        
        # 状态标签
        self.status_label = QLabel("准备测试播放功能")
        self.status_label.setFont(QFont("Arial", 12))
        self.status_label.setStyleSheet("color: #666; margin: 10px; text-align: center;")
        layout.addWidget(self.status_label)
        
        # 按钮容器
        button_container = QWidget()
        button_layout = QHBoxLayout()
        button_container.setLayout(button_layout)
        
        # 测试播放按钮
        self.test_play_button = QPushButton("🎵 测试播放功能")
        self.test_play_button.setFixedSize(150, 50)
        self.test_play_button.clicked.connect(self.test_play_function)
        self.test_play_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                border: 2px solid #4CAF50;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5CBF60, stop:1 #4CAF50);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
            }
        """)
        button_layout.addWidget(self.test_play_button)
        
        # 清空测试数据按钮
        self.clear_button = QPushButton("🗑 清空测试数据")
        self.clear_button.setFixedSize(150, 50)
        self.clear_button.clicked.connect(self.clear_test_data)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #9E9E9E, stop:1 #757575);
                border: 2px solid #9E9E9E;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #BDBDBD, stop:1 #9E9E9E);
            }
        """)
        button_layout.addWidget(self.clear_button)
        
        layout.addWidget(button_container)
        
        # 日志显示区域
        log_label = QLabel("📋 测试日志:")
        log_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(log_label)
        
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setFont(QFont("Consolas", 10))
        self.log_display.setStyleSheet("""
            QTextEdit {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        layout.addWidget(self.log_display)
        
        # 说明文字
        info_label = QLabel(
            "测试说明：\n"
            "1. 点击'测试播放功能'按钮模拟播放操作\n"
            "2. 系统会检查是否生成预备语音\n"
            "3. 如果没有话术，会自动创建默认话术和时间段\n"
            "4. 查看日志了解详细的执行过程"
        )
        info_label.setFont(QFont("Arial", 10))
        info_label.setStyleSheet("color: #888; margin: 10px; line-height: 1.5;")
        layout.addWidget(info_label)
        
    def init_test_data(self):
        """初始化测试数据"""
        self.playlist_items = []
        self.script_time_segments = {}
        self.voice_count_value = 5
        self.current_script = ""
        
        self.log("🚀 测试环境初始化完成")
        
    def log(self, message):
        """添加日志"""
        self.log_display.append(f"[{self.get_timestamp()}] {message}")
        print(message)
        
    def get_timestamp(self):
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
        
    def test_play_function(self):
        """测试播放功能"""
        try:
            self.log("🎵 开始测试播放功能...")
            self.status_label.setText("🎵 测试播放中...")
            self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
            
            # 模拟播放按钮的逻辑
            self.test_play_button.setText("🎵 测试中...")
            self.test_play_button.setEnabled(False)
            
            # 1. 检查当前话术
            self.log("📋 检查当前话术...")
            if not self.current_script:
                self.log("⚠️ 未选择话术，创建默认话术")
                self.create_default_script_and_segments()
                self.current_script = "默认话术"
                self.log(f"✅ 已创建默认话术: {self.current_script}")
            
            # 2. 检查时间段
            self.log("📋 检查时间段设置...")
            time_segments = self.script_time_segments.get(self.current_script, {})
            if not time_segments:
                self.log("⚠️ 话术没有时间段设置，创建默认时间段")
                self.create_default_time_segments(self.current_script)
                time_segments = self.script_time_segments.get(self.current_script, {})
                self.log(f"✅ 已创建 {len(time_segments)} 个默认时间段")
            
            # 3. 检查预备语音数量
            self.log("📋 检查预备语音数量...")
            self.check_and_generate_voices()
            
            # 4. 显示结果
            self.show_test_results()
            
            # 延迟恢复按钮
            QTimer.singleShot(3000, self.restore_test_button)
            
        except Exception as e:
            self.log(f"❌ 测试播放功能失败: {e}")
            self.restore_test_button()
            
    def create_default_script_and_segments(self):
        """创建默认话术和时间段"""
        try:
            self.log("🔧 创建默认话术和时间段...")
            default_script_name = "默认话术"
            self.create_default_time_segments(default_script_name)
            self.log(f"✅ 默认话术创建完成: {default_script_name}")
            
        except Exception as e:
            self.log(f"❌ 创建默认话术失败: {e}")
            
    def create_default_time_segments(self, script_name):
        """创建默认时间段"""
        try:
            self.log(f"🔧 为话术 '{script_name}' 创建默认时间段...")
            
            default_segments = [
                {
                    "name": "0秒 - 60秒",
                    "start": 0,
                    "end": 60,
                    "content": "欢迎大家来到直播间！\n【大家好|各位好|hello大家好】，我是主播！\n感谢大家的关注和支持！"
                },
                {
                    "name": "60秒 - 120秒", 
                    "start": 60,
                    "end": 120,
                    "content": "现在是互动时间！\n【感谢大家的支持|谢谢各位观众|感谢收看】\n【有什么想了解的|大家有什么问题|想知道什么】可以发弹幕"
                },
                {
                    "name": "120秒 - 180秒",
                    "start": 120, 
                    "end": 180,
                    "content": "接下来给大家展示今天的主要内容\n【这个很有趣|大家看这里|注意看这个】\n【希望大家喜欢|觉得不错的话|如果感兴趣的话】记得点赞收藏"
                }
            ]
            
            self.script_time_segments[script_name] = {}
            for segment in default_segments:
                self.script_time_segments[script_name][segment["name"]] = {
                    'start': segment["start"],
                    'end': segment["end"], 
                    'content': segment["content"]
                }
            
            self.log(f"✅ 创建了 {len(default_segments)} 个默认时间段")
            
        except Exception as e:
            self.log(f"❌ 创建默认时间段失败: {e}")
            
    def check_and_generate_voices(self):
        """检查并生成预备语音"""
        try:
            self.log("🔍 检查各时间段的预备语音数量...")
            
            time_segments = self.script_time_segments.get(self.current_script, {})
            prepare_count = self.voice_count_value
            
            total_generated = 0
            
            for segment_name, segment_data in time_segments.items():
                # 统计现有语音
                existing_count = len([
                    item for item in self.playlist_items
                    if item.get('voice_type') == '主视频话术' and item.get('time_segment') == segment_name
                ])
                
                need_count = prepare_count - existing_count
                
                if need_count > 0:
                    self.log(f"📝 时间段 '{segment_name}' 需要补充 {need_count} 个语音")
                    
                    # 生成语音
                    content = segment_data.get('content', '')
                    scripts = [line.strip() for line in content.split('\n') if line.strip()]
                    
                    for i in range(need_count):
                        import random
                        selected_script = random.choice(scripts) if scripts else f"默认语音内容 {i+1}"
                        
                        voice_item = {
                            'id': len(self.playlist_items) + 1,
                            'voice_type': '主视频话术',
                            'content': selected_script,
                            'time_segment': segment_name,
                            'status': '未下载',
                            'filename': '',
                            'sub_video': '无'
                        }
                        
                        self.playlist_items.append(voice_item)
                        total_generated += 1
                        
                        self.log(f"  + 生成语音 {i+1}/{need_count}: {selected_script[:30]}...")
                else:
                    self.log(f"✅ 时间段 '{segment_name}' 语音数量充足 ({existing_count}/{prepare_count})")
            
            if total_generated > 0:
                self.log(f"🎉 总共生成了 {total_generated} 个预备语音")
            else:
                self.log("✅ 所有时间段的语音数量都已充足")
                
        except Exception as e:
            self.log(f"❌ 检查生成预备语音失败: {e}")
            
    def show_test_results(self):
        """显示测试结果"""
        try:
            self.log("📊 测试结果统计:")
            self.log(f"  - 当前话术: {self.current_script}")
            self.log(f"  - 时间段数量: {len(self.script_time_segments.get(self.current_script, {}))}")
            self.log(f"  - 预备语音总数: {len(self.playlist_items)}")
            
            # 按时间段统计
            segment_stats = {}
            for item in self.playlist_items:
                segment = item.get('time_segment', '未知')
                if segment not in segment_stats:
                    segment_stats[segment] = 0
                segment_stats[segment] += 1
            
            for segment, count in segment_stats.items():
                self.log(f"  - {segment}: {count} 个语音")
                
            self.log("🎉 播放功能测试完成！预备语音已生成")
            
        except Exception as e:
            self.log(f"❌ 显示测试结果失败: {e}")
            
    def clear_test_data(self):
        """清空测试数据"""
        try:
            self.log("🗑 清空测试数据...")
            self.playlist_items = []
            self.script_time_segments = {}
            self.current_script = ""
            
            self.status_label.setText("测试数据已清空")
            self.status_label.setStyleSheet("color: #666; font-weight: normal;")
            
            self.log("✅ 测试数据清空完成")
            
        except Exception as e:
            self.log(f"❌ 清空测试数据失败: {e}")
            
    def restore_test_button(self):
        """恢复测试按钮"""
        self.test_play_button.setText("🎵 测试播放功能")
        self.test_play_button.setEnabled(True)
        self.status_label.setText("测试完成，可以重新测试")
        self.status_label.setStyleSheet("color: #666; font-weight: normal;")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f5f5f5;
        }
        QWidget {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
    """)
    
    window = PlayButtonTestWindow()
    window.show()
    
    print("🎮 播放按钮功能测试程序启动")
    print("点击'测试播放功能'按钮验证预备语音生成")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
