"""
AI直播系统 v2 - 弹幕响应器
自动回复弹幕和生成响应
"""

import random
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class DanmakuResponder:
    """弹幕响应器"""
    
    def __init__(self, chat_manager=None, voice_manager=None):
        self.logger = create_logger("danmaku_responder")
        self.chat_manager = chat_manager
        self.voice_manager = voice_manager
        
        # 响应模板
        self.response_templates = {
            'greetings': [
                '你好！欢迎来到直播间！',
                '大家好！感谢关注！',
                '欢迎新朋友！',
                '你好呀！很高兴见到你！'
            ],
            'questions': [
                '这是个好问题，让我想想...',
                '嗯，关于这个问题...',
                '你问得很好！',
                '这个问题很有意思！'
            ],
            'praise': [
                '谢谢夸奖！',
                '你太客气了！',
                '感谢支持！',
                '谢谢！你也很棒！'
            ],
            'requests': [
                '好的，我考虑一下！',
                '这个建议不错！',
                '让我准备一下！',
                '稍等，马上来！'
            ],
            'gifts': [
                '谢谢礼物！太感谢了！',
                '感谢支持！',
                '谢谢老板！',
                '太客气了！谢谢！'
            ],
            'interactive': [
                '我们来聊聊吧！',
                '有什么想说的吗？',
                '大家一起互动起来！',
                '欢迎大家聊天！'
            ],
            'default': [
                '收到！',
                '好的！',
                '明白了！',
                '谢谢！'
            ]
        }
        
        # 个性化回复
        self.personalized_responses = {}
        
        # 响应配置
        self.response_config = {
            'auto_response_enabled': True,
            'response_probability': 0.3,  # 30%概率回复
            'ai_response_enabled': False,
            'voice_response_enabled': False,
            'response_delay_min': 1,  # 最小延迟（秒）
            'response_delay_max': 3,  # 最大延迟（秒）
            'max_responses_per_minute': 10
        }
        
        # 回调函数
        self.on_response_generated: Optional[Callable] = None
        self.on_voice_response_generated: Optional[Callable] = None
        
        # 统计信息
        self.stats = {
            'total_responses': 0,
            'template_responses': 0,
            'ai_responses': 0,
            'voice_responses': 0,
            'response_rate': 0.0,
            'last_response_time': None
        }
        
        # 响应限制
        self.response_history = []
        
    @handle_exceptions("DanmakuResponder")
    def generate_response(self, danmaku_info: Dict[str, Any], 
                         processing_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        生成弹幕响应
        
        Args:
            danmaku_info: 弹幕信息
            processing_result: 处理结果
            
        Returns:
            响应信息
        """
        try:
            # 检查是否需要响应
            if not self._should_respond(danmaku_info, processing_result):
                return None
                
            # 检查响应频率限制
            if not self._check_response_rate_limit():
                return None
                
            username = danmaku_info.get('username', '朋友')
            content = danmaku_info.get('content', '')
            keywords_matched = processing_result.get('keywords_matched', [])
            
            response_text = None
            response_type = 'template'
            
            # 1. 检查是否有建议的响应
            if processing_result.get('suggested_response'):
                response_text = processing_result['suggested_response']
                response_type = 'rule'
                
            # 2. 尝试AI响应
            elif self.response_config['ai_response_enabled'] and self.chat_manager:
                ai_response = self._generate_ai_response(danmaku_info, processing_result)
                if ai_response:
                    response_text = ai_response
                    response_type = 'ai'
                    
            # 3. 使用模板响应
            if not response_text:
                response_text = self._generate_template_response(keywords_matched, username)
                response_type = 'template'
                
            if not response_text:
                return None
                
            # 创建响应信息
            response_info = {
                'text': response_text,
                'type': response_type,
                'target_user': username,
                'original_message': content,
                'keywords': keywords_matched,
                'timestamp': datetime.now(),
                'delay': random.uniform(
                    self.response_config['response_delay_min'],
                    self.response_config['response_delay_max']
                )
            }
            
            # 更新统计
            self.stats['total_responses'] += 1
            if response_type == 'template':
                self.stats['template_responses'] += 1
            elif response_type == 'ai':
                self.stats['ai_responses'] += 1
                
            self.stats['last_response_time'] = datetime.now()
            
            # 添加到响应历史
            self._add_to_response_history()
            
            # 生成语音响应
            if self.response_config['voice_response_enabled']:
                voice_info = self._generate_voice_response(response_text)
                if voice_info:
                    response_info['voice'] = voice_info
                    self.stats['voice_responses'] += 1
                    
            # 触发回调
            if self.on_response_generated:
                self.on_response_generated(response_info)
                
            self.logger.info(f"生成响应: {username} -> {response_text[:30]}...")
            
            return response_info
            
        except Exception as e:
            self.logger.error(f"生成弹幕响应失败: {e}")
            return None
            
    def _should_respond(self, danmaku_info: Dict[str, Any], 
                       processing_result: Dict[str, Any]) -> bool:
        """判断是否应该响应"""
        # 检查自动响应是否启用
        if not self.response_config['auto_response_enabled']:
            return False
            
        # 检查是否被阻止
        if processing_result.get('blocked', False):
            return False
            
        # 检查是否在冷却时间
        if processing_result.get('action') == 'cooldown':
            return False
            
        # 检查是否明确需要响应
        if processing_result.get('response_needed', False):
            return True
            
        # 检查关键词匹配
        keywords_matched = processing_result.get('keywords_matched', [])
        if keywords_matched:
            # 根据概率决定是否响应
            return random.random() < self.response_config['response_probability']
            
        return False
        
    def _check_response_rate_limit(self) -> bool:
        """检查响应频率限制"""
        now = datetime.now()
        one_minute_ago = now - timedelta(minutes=1)
        
        # 清理过期的响应记录
        self.response_history = [
            timestamp for timestamp in self.response_history
            if timestamp > one_minute_ago
        ]
        
        # 检查是否超过限制
        max_responses = self.response_config['max_responses_per_minute']
        return len(self.response_history) < max_responses
        
    def _add_to_response_history(self):
        """添加响应到历史记录"""
        self.response_history.append(datetime.now())
        
    def _generate_template_response(self, keywords_matched: List[str], 
                                  username: str) -> Optional[str]:
        """生成模板响应"""
        try:
            # 根据关键词选择响应模板
            template_category = 'default'
            
            if keywords_matched:
                # 使用第一个匹配的关键词类别
                template_category = keywords_matched[0]
                
            # 获取响应模板
            templates = self.response_templates.get(template_category, 
                                                   self.response_templates['default'])
            
            if not templates:
                return None
                
            # 随机选择一个模板
            template = random.choice(templates)
            
            # 个性化处理
            if '{username}' in template:
                template = template.replace('{username}', username)
            elif template_category == 'greetings':
                # 对问候语添加用户名
                template = f"{template.rstrip('！!')} {username}！"
                
            return template
            
        except Exception as e:
            self.logger.error(f"生成模板响应失败: {e}")
            return None
            
    def _generate_ai_response(self, danmaku_info: Dict[str, Any], 
                             processing_result: Dict[str, Any]) -> Optional[str]:
        """生成AI响应"""
        try:
            if not self.chat_manager:
                return None
                
            username = danmaku_info.get('username', '朋友')
            content = danmaku_info.get('content', '')
            
            # 构建上下文
            context = {
                'username': username,
                'user_message': content,
                'keywords': processing_result.get('keywords_matched', []),
                'is_live_stream': True,
                'response_type': 'danmaku_reply'
            }
            
            # 生成AI回复
            ai_response = self.chat_manager.generate_response(
                f"用户 {username} 在直播间说：{content}",
                context
            )
            
            return ai_response
            
        except Exception as e:
            self.logger.error(f"生成AI响应失败: {e}")
            return None
            
    def _generate_voice_response(self, text: str) -> Optional[Dict[str, Any]]:
        """生成语音响应"""
        try:
            if not self.voice_manager:
                return None
                
            # 生成语音文件
            voice_file = self.voice_manager.generate_voice(text)
            
            if voice_file:
                voice_info = {
                    'file_path': voice_file,
                    'text': text,
                    'duration': self.voice_manager.get_audio_duration(voice_file)
                }
                
                if self.on_voice_response_generated:
                    self.on_voice_response_generated(voice_info)
                    
                return voice_info
                
            return None
            
        except Exception as e:
            self.logger.error(f"生成语音响应失败: {e}")
            return None
            
    @handle_exceptions("DanmakuResponder")
    def add_response_template(self, category: str, template: str):
        """添加响应模板"""
        if category not in self.response_templates:
            self.response_templates[category] = []
            
        if template not in self.response_templates[category]:
            self.response_templates[category].append(template)
            self.logger.info(f"添加响应模板: {category} -> {template}")
            
    @handle_exceptions("DanmakuResponder")
    def remove_response_template(self, category: str, template: str):
        """移除响应模板"""
        if category in self.response_templates and template in self.response_templates[category]:
            self.response_templates[category].remove(template)
            self.logger.info(f"移除响应模板: {category} -> {template}")
            
    @handle_exceptions("DanmakuResponder")
    def set_personalized_response(self, username: str, response: str):
        """设置个性化响应"""
        self.personalized_responses[username] = response
        self.logger.info(f"设置个性化响应: {username} -> {response}")
        
    @handle_exceptions("DanmakuResponder")
    def update_config(self, config: Dict[str, Any]):
        """更新响应配置"""
        self.response_config.update(config)
        self.logger.info(f"响应配置已更新: {list(config.keys())}")
        
    @handle_exceptions("DanmakuResponder")
    def get_config(self) -> Dict[str, Any]:
        """获取响应配置"""
        return self.response_config.copy()
        
    @handle_exceptions("DanmakuResponder")
    def get_stats(self) -> Dict[str, Any]:
        """获取响应统计"""
        stats = self.stats.copy()
        
        # 计算响应率
        if self.stats['total_responses'] > 0:
            # 这里需要总的弹幕数量来计算准确的响应率
            # 暂时使用已有数据
            stats['response_rate'] = self.stats['total_responses'] / max(self.stats['total_responses'], 1)
        else:
            stats['response_rate'] = 0.0
            
        stats['templates_count'] = sum(len(templates) for templates in self.response_templates.values())
        stats['personalized_responses_count'] = len(self.personalized_responses)
        stats['current_response_rate'] = len(self.response_history)  # 当前分钟内的响应数
        
        return stats
        
    @handle_exceptions("DanmakuResponder")
    def get_response_templates(self) -> Dict[str, List[str]]:
        """获取响应模板"""
        return {k: v.copy() for k, v in self.response_templates.items()}
        
    @handle_exceptions("DanmakuResponder")
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_responses': 0,
            'template_responses': 0,
            'ai_responses': 0,
            'voice_responses': 0,
            'response_rate': 0.0,
            'last_response_time': None
        }
        self.response_history.clear()
        self.logger.info("响应统计已重置")
