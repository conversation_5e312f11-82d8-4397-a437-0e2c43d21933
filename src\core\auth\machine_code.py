"""
AI Broadcaster v2 - 机器码生成器
硬件指纹生成和验证
"""

import hashlib
import platform
import psutil
from typing import Optional

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class MachineCodeGenerator:
    """机器码生成器"""
    
    def __init__(self):
        self.logger = create_logger("machine_code")
    
    @handle_exceptions("MachineCodeGenerator")
    def get_cpu_info(self) -> str:
        """获取CPU信息"""
        try:
            # 获取CPU型号
            cpu_info = platform.processor()
            if not cpu_info:
                # 备用方法
                import cpuinfo
                cpu_info = cpuinfo.get_cpu_info().get('brand_raw', 'Unknown')
            return cpu_info
        except:
            return platform.machine()
    
    @handle_exceptions("MachineCodeGenerator")
    def get_disk_serial(self) -> str:
        """获取硬盘序列号"""
        try:
            import subprocess
            import sys
            
            if sys.platform == "win32":
                # Windows系统
                result = subprocess.run(
                    ['wmic', 'diskdrive', 'get', 'serialnumber'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:  # 跳过标题行
                        serial = line.strip()
                        if serial and serial != "SerialNumber":
                            return serial
            else:
                # Linux/Mac系统
                result = subprocess.run(
                    ['lsblk', '-o', 'SERIAL', '-n'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    serials = result.stdout.strip().split('\n')
                    for serial in serials:
                        if serial.strip():
                            return serial.strip()
        except:
            pass
        
        # 备用方法：使用磁盘分区信息
        try:
            partitions = psutil.disk_partitions()
            if partitions:
                return partitions[0].device
        except:
            pass
        
        return "UNKNOWN_DISK"
    
    @handle_exceptions("MachineCodeGenerator")
    def get_mac_address(self) -> str:
        """获取MAC地址"""
        try:
            import uuid
            mac = uuid.getnode()
            return ':'.join(('%012X' % mac)[i:i+2] for i in range(0, 12, 2))
        except:
            return "UNKNOWN_MAC"
    
    @handle_exceptions("MachineCodeGenerator")
    def get_motherboard_serial(self) -> str:
        """获取主板序列号"""
        try:
            import subprocess
            import sys
            
            if sys.platform == "win32":
                result = subprocess.run(
                    ['wmic', 'baseboard', 'get', 'serialnumber'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:
                        serial = line.strip()
                        if serial and serial != "SerialNumber":
                            return serial
            else:
                # Linux系统
                try:
                    with open('/sys/class/dmi/id/board_serial', 'r') as f:
                        return f.read().strip()
                except:
                    pass
        except:
            pass
        
        return "UNKNOWN_BOARD"
    
    def generate_machine_code(self) -> str:
        """
        生成机器码
        
        Returns:
            机器码字符串
        """
        try:
            # 收集硬件信息
            system_info = platform.system()
            cpu_info = self.get_cpu_info()
            disk_serial = self.get_disk_serial()
            mac_address = self.get_mac_address()
            motherboard_serial = self.get_motherboard_serial()
            
            # 组合硬件信息
            hardware_info = f"{system_info}|{cpu_info}|{disk_serial}|{mac_address}|{motherboard_serial}"
            
            # 生成MD5哈希
            machine_code = hashlib.md5(hardware_info.encode('utf-8')).hexdigest().upper()
            
            self.logger.debug(f"生成机器码: {machine_code}")
            return machine_code
            
        except Exception as e:
            self.logger.error(f"生成机器码失败: {e}")
            # 备用机器码
            fallback_info = f"{platform.system()}|{platform.machine()}|{platform.node()}"
            return hashlib.md5(fallback_info.encode('utf-8')).hexdigest().upper()
    
    def verify_machine_code(self, stored_code: str) -> bool:
        """
        验证机器码
        
        Args:
            stored_code: 存储的机器码
            
        Returns:
            是否匹配
        """
        current_code = self.generate_machine_code()
        return current_code == stored_code
    
    def get_hardware_info(self) -> dict:
        """
        获取详细硬件信息
        
        Returns:
            硬件信息字典
        """
        try:
            return {
                'system': platform.system(),
                'machine': platform.machine(),
                'processor': self.get_cpu_info(),
                'disk_serial': self.get_disk_serial(),
                'mac_address': self.get_mac_address(),
                'motherboard_serial': self.get_motherboard_serial(),
                'python_version': platform.python_version(),
                'platform': platform.platform(),
                'machine_code': self.generate_machine_code()
            }
        except Exception as e:
            self.logger.error(f"获取硬件信息失败: {e}")
            return {
                'machine_code': self.generate_machine_code(),
                'error': str(e)
            }
