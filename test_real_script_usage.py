#!/usr/bin/env python3
"""
测试真实话术使用 - 验证播放时只使用话术管理中的真实时间段话术
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel, QTextEdit, QComboBox, QSpinBox
from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QFont


class RealScriptTestWindow(QMainWindow):
    """真实话术测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.init_test_data()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("真实话术使用测试")
        self.setGeometry(100, 100, 900, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 标题
        title_label = QLabel("🎯 真实话术使用测试")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #333; margin: 20px; text-align: center;")
        layout.addWidget(title_label)
        
        # 状态标签
        self.status_label = QLabel("准备测试真实话术使用")
        self.status_label.setFont(QFont("Arial", 12))
        self.status_label.setStyleSheet("color: #666; margin: 10px; text-align: center;")
        layout.addWidget(self.status_label)
        
        # 控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)
        
        # 测试按钮
        button_container = QWidget()
        button_layout = QHBoxLayout()
        button_container.setLayout(button_layout)
        
        # 测试无话术情况
        self.test_no_script_button = QPushButton("🚫 测试无话术播放")
        self.test_no_script_button.clicked.connect(self.test_no_script_play)
        self.test_no_script_button.setStyleSheet(self.get_button_style("warning"))
        button_layout.addWidget(self.test_no_script_button)
        
        # 测试有话术无时间段情况
        self.test_no_segments_button = QPushButton("⚠️ 测试无时间段播放")
        self.test_no_segments_button.clicked.connect(self.test_no_segments_play)
        self.test_no_segments_button.setStyleSheet(self.get_button_style("warning"))
        button_layout.addWidget(self.test_no_segments_button)
        
        # 测试正常情况
        self.test_normal_button = QPushButton("✅ 测试正常播放")
        self.test_normal_button.clicked.connect(self.test_normal_play)
        self.test_normal_button.setStyleSheet(self.get_button_style("success"))
        button_layout.addWidget(self.test_normal_button)
        
        layout.addWidget(button_container)
        
        # 日志显示区域
        log_label = QLabel("📋 测试日志:")
        log_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(log_label)
        
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setFont(QFont("Consolas", 10))
        self.log_display.setStyleSheet("""
            QTextEdit {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        layout.addWidget(self.log_display)
        
        # 说明文字
        info_label = QLabel(
            "测试说明：\n"
            "1. 「测试无话术播放」- 模拟用户未选择任何话术的情况\n"
            "2. 「测试无时间段播放」- 模拟用户选择了话术但未设置时间段的情况\n"
            "3. 「测试正常播放」- 模拟用户正确设置了话术和时间段的情况\n\n"
            "修复后的系统应该：\n"
            "• 在前两种情况下提醒用户去话术管理中设置\n"
            "• 在第三种情况下正常生成预备语音"
        )
        info_label.setFont(QFont("Arial", 10))
        info_label.setStyleSheet("color: #888; margin: 10px; line-height: 1.5;")
        layout.addWidget(info_label)
        
    def create_control_panel(self):
        """创建控制面板"""
        panel = QWidget()
        layout = QHBoxLayout()
        panel.setLayout(layout)
        
        # 话术选择
        layout.addWidget(QLabel("当前话术:"))
        self.script_combo = QComboBox()
        self.script_combo.addItems(["", "测试话术1", "测试话术2", "完整话术"])
        layout.addWidget(self.script_combo)
        
        # 预备语音数量
        layout.addWidget(QLabel("预备语音数量:"))
        self.voice_count = QSpinBox()
        self.voice_count.setRange(1, 20)
        self.voice_count.setValue(5)
        layout.addWidget(self.voice_count)
        
        return panel
        
    def get_button_style(self, button_type):
        """获取按钮样式"""
        base_style = """
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                border: 2px solid;
                border-radius: 6px;
                padding: 8px 16px;
                min-width: 120px;
                min-height: 35px;
            }
            QPushButton:hover {
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                transform: translateY(1px);
            }
        """
        
        if button_type == "success":
            return base_style + """
                QPushButton {
                    background: #28a745;
                    border-color: #28a745;
                    color: white;
                }
                QPushButton:hover {
                    background: #218838;
                }
            """
        elif button_type == "warning":
            return base_style + """
                QPushButton {
                    background: #ffc107;
                    border-color: #ffc107;
                    color: #212529;
                }
                QPushButton:hover {
                    background: #e0a800;
                }
            """
        return base_style
        
    def init_test_data(self):
        """初始化测试数据"""
        self.playlist_items = []
        self.script_time_segments = {
            "完整话术": {
                "0秒 - 60秒": {
                    "start": 0,
                    "end": 60,
                    "content": """# 开场时间段话术 (0-60秒)

欢迎大家来到直播间！
【大家好|各位好|hello大家好】，我是主播！
感谢大家的关注和支持！
今天给大家带来精彩的内容
【请大家点点关注|麻烦点个关注|关注一下主播】
有什么问题可以在弹幕里提问哦"""
                },
                "60秒 - 120秒": {
                    "start": 60,
                    "end": 120,
                    "content": """# 互动时间段话术 (60-120秒)

现在是互动时间！
【感谢大家的支持|谢谢各位观众|感谢收看】
【有什么想了解的|大家有什么问题|想知道什么】可以发弹幕
我会认真回答每一个问题
【点赞支持一下|给个小心心|双击666】
让我们一起愉快地聊天吧"""
                }
            }
        }
        
        self.log("🚀 测试环境初始化完成")
        self.log("📝 已创建完整话术示例，包含2个时间段")
        
    def log(self, message):
        """添加日志"""
        self.log_display.append(f"[{self.get_timestamp()}] {message}")
        print(message)
        
    def get_timestamp(self):
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
        
    def test_no_script_play(self):
        """测试无话术播放"""
        try:
            self.log("🚫 开始测试无话术播放...")
            self.status_label.setText("🚫 测试无话术播放中...")
            self.status_label.setStyleSheet("color: #dc3545; font-weight: bold;")
            
            # 清空话术选择
            self.script_combo.setCurrentText("")
            current_script = ""
            
            self.log("📋 当前状态:")
            self.log(f"  - 选择的话术: '{current_script}'")
            self.log(f"  - 话术是否为空: {not current_script}")
            
            # 模拟播放逻辑
            if not current_script:
                self.log("⚠️ 检测到未选择话术")
                self.show_script_management_reminder()
                self.log("✅ 已显示话术管理提醒，不会生成默认话术")
                return
            
            self.log("❌ 这行代码不应该执行到")
            
        except Exception as e:
            self.log(f"❌ 测试无话术播放失败: {e}")
            
    def test_no_segments_play(self):
        """测试无时间段播放"""
        try:
            self.log("⚠️ 开始测试无时间段播放...")
            self.status_label.setText("⚠️ 测试无时间段播放中...")
            self.status_label.setStyleSheet("color: #ffc107; font-weight: bold;")
            
            # 选择一个没有时间段的话术
            self.script_combo.setCurrentText("测试话术1")
            current_script = "测试话术1"
            
            self.log("📋 当前状态:")
            self.log(f"  - 选择的话术: '{current_script}'")
            self.log(f"  - 话术在script_time_segments中: {current_script in self.script_time_segments}")
            
            # 模拟播放逻辑
            if not current_script:
                self.log("⚠️ 检测到未选择话术")
                self.show_script_management_reminder()
                return
            
            # 获取当前话术的时间段
            time_segments = self.script_time_segments.get(current_script, {})
            if not time_segments:
                self.log(f"⚠️ 检测到话术 '{current_script}' 没有时间段设置")
                self.show_time_segment_setup_reminder(current_script)
                self.log("✅ 已显示时间段设置提醒，不会生成默认时间段")
                return
            
            self.log("❌ 这行代码不应该执行到")
            
        except Exception as e:
            self.log(f"❌ 测试无时间段播放失败: {e}")
            
    def test_normal_play(self):
        """测试正常播放"""
        try:
            self.log("✅ 开始测试正常播放...")
            self.status_label.setText("✅ 测试正常播放中...")
            self.status_label.setStyleSheet("color: #28a745; font-weight: bold;")
            
            # 选择完整话术
            self.script_combo.setCurrentText("完整话术")
            current_script = "完整话术"
            
            self.log("📋 当前状态:")
            self.log(f"  - 选择的话术: '{current_script}'")
            self.log(f"  - 话术在script_time_segments中: {current_script in self.script_time_segments}")
            
            # 模拟播放逻辑
            if not current_script:
                self.log("⚠️ 检测到未选择话术")
                self.show_script_management_reminder()
                return
            
            # 获取当前话术的时间段
            time_segments = self.script_time_segments.get(current_script, {})
            if not time_segments:
                self.log(f"⚠️ 检测到话术 '{current_script}' 没有时间段设置")
                self.show_time_segment_setup_reminder(current_script)
                return
            
            self.log(f"✅ 话术 '{current_script}' 有 {len(time_segments)} 个时间段")
            
            # 生成预备语音
            self.generate_voices_from_real_scripts(current_script, time_segments)
            
        except Exception as e:
            self.log(f"❌ 测试正常播放失败: {e}")
            
    def generate_voices_from_real_scripts(self, script_name, time_segments):
        """从真实话术生成预备语音"""
        try:
            self.log(f"🎵 开始从真实话术生成预备语音: {script_name}")
            
            prepare_count = self.voice_count.value()
            total_generated = 0
            
            for segment_name, segment_data in time_segments.items():
                self.log(f"📝 处理时间段: {segment_name}")
                
                # 获取时间段话术内容
                content = segment_data.get('content', '')
                if not content.strip():
                    self.log(f"  ⚠️ 时间段 '{segment_name}' 内容为空")
                    continue
                
                # 解析话术内容
                scripts = []
                for line in content.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        scripts.append(line)
                
                self.log(f"  📊 解析出 {len(scripts)} 条话术")
                
                # 生成预备语音
                import random
                for i in range(min(prepare_count, len(scripts))):
                    selected_script = random.choice(scripts)
                    
                    voice_item = {
                        'id': len(self.playlist_items) + 1,
                        'voice_type': '主视频话术',
                        'content': selected_script,
                        'time_segment': segment_name,
                        'status': '未下载',
                        'filename': '',
                        'sub_video': '无'
                    }
                    
                    self.playlist_items.append(voice_item)
                    total_generated += 1
                    
                    self.log(f"    + 生成语音 {i+1}: {selected_script[:40]}...")
            
            self.log(f"🎉 预备语音生成完成！")
            self.log(f"📊 统计结果:")
            self.log(f"  - 处理话术: {script_name}")
            self.log(f"  - 时间段数量: {len(time_segments)}")
            self.log(f"  - 生成语音总数: {total_generated}")
            self.log(f"  - 语音来源: 话术管理中的真实时间段话术")
            
            self.status_label.setText("✅ 正常播放测试完成")
            self.status_label.setStyleSheet("color: #28a745; font-weight: bold;")
            
        except Exception as e:
            self.log(f"❌ 生成预备语音失败: {e}")
            
    def show_script_management_reminder(self):
        """显示话术管理提醒"""
        self.log("💡 显示话术管理提醒对话框")
        self.log("📝 提醒内容: 请先在话术管理中选择或创建话术")
        
    def show_time_segment_setup_reminder(self, script_name):
        """显示时间段设置提醒"""
        self.log(f"💡 显示时间段设置提醒对话框: {script_name}")
        self.log("📝 提醒内容: 请先为该话术设置时间段话术")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f5f5f5;
        }
        QWidget {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
    """)
    
    window = RealScriptTestWindow()
    window.show()
    
    print("🎯 真实话术使用测试程序启动")
    print("测试播放系统是否正确使用话术管理中的真实时间段话术")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
