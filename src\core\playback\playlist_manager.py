"""
AI Broadcaster v2 - 播放列表管理器
管理待播放的音频文件队列
"""

import random
import threading
import time
from typing import List, Optional, Dict, Any, Callable
from pathlib import Path
from collections import deque

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class PlaylistItem:
    """播放列表项"""
    
    def __init__(self, file_path: str, title: str = "", metadata: Optional[Dict[str, Any]] = None):
        self.file_path = file_path
        self.title = title or Path(file_path).stem
        self.metadata = metadata or {}
        self.created_at = None
        self.played_at = None
        self.play_count = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'file_path': self.file_path,
            'title': self.title,
            'metadata': self.metadata,
            'created_at': self.created_at,
            'played_at': self.played_at,
            'play_count': self.play_count
        }


class PlaylistManager:
    """播放列表管理器"""
    
    def __init__(self, max_size: int = 1000):
        self.logger = create_logger("playlist_manager")
        self.max_size = max_size
        
        # 播放队列
        self._playlist = deque(maxlen=max_size)
        self._current_index = -1
        self._lock = threading.Lock()
        
        # 播放模式
        self.shuffle_mode = False
        self.repeat_mode = False  # False: 不重复, True: 重复播放列表
        
        # 回调函数
        self.on_playlist_change: Optional[Callable] = None
        self.on_current_change: Optional[Callable] = None
        
        # 历史记录
        self._play_history = deque(maxlen=100)
    
    @handle_exceptions("PlaylistManager")
    def add_item(self, file_path: str, title: str = "", metadata: Optional[Dict[str, Any]] = None) -> bool:
        """添加播放项"""
        try:
            if not Path(file_path).exists():
                self.logger.error(f"文件不存在: {file_path}")
                return False
            
            with self._lock:
                item = PlaylistItem(file_path, title, metadata)
                self._playlist.append(item)
                
                self.logger.debug(f"添加播放项: {item.title}")
                
                # 触发回调
                if self.on_playlist_change:
                    self.on_playlist_change()
                
                return True
                
        except Exception as e:
            self.logger.error(f"添加播放项失败: {e}")
            return False
    
    @handle_exceptions("PlaylistManager")
    def add_items(self, items: List[Dict[str, Any]]) -> int:
        """批量添加播放项"""
        success_count = 0
        
        for item_data in items:
            file_path = item_data.get('file_path', '')
            title = item_data.get('title', '')
            metadata = item_data.get('metadata', {})
            
            if self.add_item(file_path, title, metadata):
                success_count += 1
        
        self.logger.info(f"批量添加播放项完成: {success_count}/{len(items)} 成功")
        return success_count
    
    def remove_item(self, index: int) -> bool:
        """移除指定索引的播放项"""
        try:
            with self._lock:
                if 0 <= index < len(self._playlist):
                    removed_item = self._playlist[index]
                    del self._playlist[index]
                    
                    # 调整当前索引
                    if index <= self._current_index:
                        self._current_index -= 1
                    
                    self.logger.debug(f"移除播放项: {removed_item.title}")
                    
                    # 触发回调
                    if self.on_playlist_change:
                        self.on_playlist_change()
                    
                    return True
                else:
                    self.logger.error(f"无效的索引: {index}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"移除播放项失败: {e}")
            return False
    
    def clear_playlist(self):
        """清空播放列表"""
        with self._lock:
            self._playlist.clear()
            self._current_index = -1
            
            self.logger.info("播放列表已清空")
            
            # 触发回调
            if self.on_playlist_change:
                self.on_playlist_change()
    
    def get_current_item(self) -> Optional[PlaylistItem]:
        """获取当前播放项"""
        with self._lock:
            if 0 <= self._current_index < len(self._playlist):
                return self._playlist[self._current_index]
            return None
    
    def get_next_item(self) -> Optional[PlaylistItem]:
        """获取下一个播放项"""
        with self._lock:
            if len(self._playlist) == 0:
                return None
            
            if self.shuffle_mode:
                # 随机模式
                next_index = random.randint(0, len(self._playlist) - 1)
            else:
                # 顺序模式
                next_index = self._current_index + 1
                
                # 检查是否到达列表末尾
                if next_index >= len(self._playlist):
                    if self.repeat_mode:
                        next_index = 0  # 重复播放
                    else:
                        return None  # 播放完毕
            
            self._current_index = next_index
            current_item = self._playlist[self._current_index]
            
            # 更新播放统计
            current_item.play_count += 1
            current_item.played_at = None  # 这里应该设置当前时间
            
            # 添加到历史记录
            self._play_history.append(current_item.to_dict())
            
            self.logger.debug(f"下一个播放项: {current_item.title}")
            
            # 触发回调
            if self.on_current_change:
                self.on_current_change(current_item)
            
            return current_item
    
    def get_previous_item(self) -> Optional[PlaylistItem]:
        """获取上一个播放项"""
        with self._lock:
            if len(self._playlist) == 0:
                return None
            
            if self.shuffle_mode:
                # 随机模式下从历史记录获取
                if len(self._play_history) >= 2:
                    # 获取倒数第二个播放项
                    prev_item_data = self._play_history[-2]
                    file_path = prev_item_data['file_path']
                    
                    # 在播放列表中查找
                    for i, item in enumerate(self._playlist):
                        if item.file_path == file_path:
                            self._current_index = i
                            return item
                
                return None
            else:
                # 顺序模式
                prev_index = self._current_index - 1
                
                if prev_index < 0:
                    if self.repeat_mode:
                        prev_index = len(self._playlist) - 1
                    else:
                        return None
                
                self._current_index = prev_index
                current_item = self._playlist[self._current_index]
                
                self.logger.debug(f"上一个播放项: {current_item.title}")
                
                # 触发回调
                if self.on_current_change:
                    self.on_current_change(current_item)
                
                return current_item
    
    def set_current_index(self, index: int) -> bool:
        """设置当前播放索引"""
        with self._lock:
            if 0 <= index < len(self._playlist):
                self._current_index = index
                current_item = self._playlist[self._current_index]
                
                self.logger.debug(f"设置当前播放项: {current_item.title}")
                
                # 触发回调
                if self.on_current_change:
                    self.on_current_change(current_item)
                
                return True
            else:
                self.logger.error(f"无效的索引: {index}")
                return False
    
    def set_shuffle_mode(self, enabled: bool):
        """设置随机播放模式"""
        self.shuffle_mode = enabled
        self.logger.info(f"随机播放模式: {'开启' if enabled else '关闭'}")
    
    def set_repeat_mode(self, enabled: bool):
        """设置重复播放模式"""
        self.repeat_mode = enabled
        self.logger.info(f"重复播放模式: {'开启' if enabled else '关闭'}")
    
    def get_playlist_items(self) -> List[Dict[str, Any]]:
        """获取播放列表所有项"""
        with self._lock:
            return [item.to_dict() for item in self._playlist]
    
    def get_playlist_info(self) -> Dict[str, Any]:
        """获取播放列表信息"""
        with self._lock:
            current_item = self.get_current_item()
            
            return {
                'total_items': len(self._playlist),
                'current_index': self._current_index,
                'current_item': current_item.to_dict() if current_item else None,
                'shuffle_mode': self.shuffle_mode,
                'repeat_mode': self.repeat_mode,
                'max_size': self.max_size
            }
    
    def get_play_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取播放历史"""
        with self._lock:
            history_list = list(self._play_history)
            return history_list[-limit:] if limit > 0 else history_list
    
    def shuffle_playlist(self):
        """打乱播放列表"""
        with self._lock:
            if len(self._playlist) > 1:
                # 保存当前播放项
                current_item = self.get_current_item()
                
                # 转换为列表并打乱
                playlist_list = list(self._playlist)
                random.shuffle(playlist_list)
                
                # 重新创建deque
                self._playlist = deque(playlist_list, maxlen=self.max_size)
                
                # 重新定位当前播放项
                if current_item:
                    for i, item in enumerate(self._playlist):
                        if item.file_path == current_item.file_path:
                            self._current_index = i
                            break
                    else:
                        self._current_index = 0
                
                self.logger.info("播放列表已打乱")
                
                # 触发回调
                if self.on_playlist_change:
                    self.on_playlist_change()
    
    def sort_playlist(self, key: str = "title", reverse: bool = False):
        """排序播放列表"""
        with self._lock:
            if len(self._playlist) > 1:
                # 保存当前播放项
                current_item = self.get_current_item()
                
                # 排序
                if key == "title":
                    sorted_list = sorted(self._playlist, key=lambda x: x.title, reverse=reverse)
                elif key == "file_path":
                    sorted_list = sorted(self._playlist, key=lambda x: x.file_path, reverse=reverse)
                elif key == "play_count":
                    sorted_list = sorted(self._playlist, key=lambda x: x.play_count, reverse=reverse)
                else:
                    return False
                
                # 重新创建deque
                self._playlist = deque(sorted_list, maxlen=self.max_size)
                
                # 重新定位当前播放项
                if current_item:
                    for i, item in enumerate(self._playlist):
                        if item.file_path == current_item.file_path:
                            self._current_index = i
                            break
                    else:
                        self._current_index = 0
                
                self.logger.info(f"播放列表已按 {key} 排序")
                
                # 触发回调
                if self.on_playlist_change:
                    self.on_playlist_change()
                
                return True
            
            return False
    
    def has_next(self) -> bool:
        """检查是否有下一个播放项"""
        with self._lock:
            if len(self._playlist) == 0:
                return False
            
            if self.shuffle_mode or self.repeat_mode:
                return True
            
            return self._current_index + 1 < len(self._playlist)
    
    def has_previous(self) -> bool:
        """检查是否有上一个播放项"""
        with self._lock:
            if len(self._playlist) == 0:
                return False
            
            if self.shuffle_mode:
                return len(self._play_history) >= 2
            
            if self.repeat_mode:
                return True
            
            return self._current_index > 0
