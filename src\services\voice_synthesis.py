"""
AI Broadcaster v2 - 语音合成服务
从API下载语音文件
"""

import os
import requests
import hashlib
import time
from pathlib import Path
from typing import Dict, Any, Optional
from urllib.parse import urljoin

from .logging_service import create_logger
from .error_handler import handle_exceptions


class VoiceSynthesis:
    """语音合成服务"""
    
    def __init__(self, voice_server: str = "http://ct.scjanelife.com"):
        self.logger = create_logger("voice_synthesis")
        self.voice_server = voice_server.rstrip('/')
        
        # 语音缓存目录
        self.cache_dir = Path("data/voice_cache")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 请求会话
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'AI-Broadcaster-Client/2.0.0'
        })

        # 禁用代理，直接连接
        self.session.proxies = {'http': None, 'https': None}

        # 超时设置
        self.timeout = 30
        
        # 默认参数
        self.default_speaker_id = 0
        self.default_speed = 1.0
        
        self.logger.info("语音合成服务初始化完成")
    
    @handle_exceptions("VoiceSynthesis")
    def get_speakers(self) -> Dict[str, Any]:
        """获取可用的语音主播列表"""
        try:
            self.logger.info("获取语音主播列表...")
            
            url = f"{self.voice_server}/voice/speakers"
            response = self.session.get(url, timeout=self.timeout)
            
            if response.status_code != 200:
                return {
                    'success': False,
                    'message': f'服务器错误: HTTP {response.status_code}'
                }
            
            # 解析响应
            data = response.json()
            
            # 提取主播信息
            speakers = []
            if isinstance(data, dict):
                for category, speaker_list in data.items():
                    if isinstance(speaker_list, list):
                        for speaker in speaker_list:
                            if isinstance(speaker, dict) and 'id' in speaker and 'name' in speaker:
                                speakers.append({
                                    'id': speaker['id'],
                                    'name': speaker['name'],
                                    'category': category
                                })
            
            self.logger.info(f"获取到 {len(speakers)} 个语音主播")
            
            return {
                'success': True,
                'speakers': speakers
            }
            
        except requests.exceptions.ConnectionError:
            self.logger.error("无法连接到语音服务器")
            return {
                'success': False,
                'message': '无法连接到语音服务器，请检查网络连接'
            }
        except requests.exceptions.Timeout:
            self.logger.error("请求超时")
            return {
                'success': False,
                'message': '请求超时，请稍后重试'
            }
        except Exception as e:
            self.logger.error(f"获取语音主播列表失败: {e}")
            return {
                'success': False,
                'message': f'获取语音主播列表失败: {str(e)}'
            }
    
    @handle_exceptions("VoiceSynthesis")
    def synthesize_voice(self, text: str, speaker_id: int = None, speed: float = None) -> Dict[str, Any]:
        """合成语音"""
        try:
            if not text.strip():
                return {
                    'success': False,
                    'message': '文本内容不能为空'
                }
            
            # 使用默认参数
            speaker_id = speaker_id if speaker_id is not None else self.default_speaker_id
            speed = speed if speed is not None else self.default_speed
            
            # 生成缓存文件名
            cache_key = self._generate_cache_key(text, speaker_id, speed)
            cache_file = self.cache_dir / f"{cache_key}.wav"
            
            # 检查缓存
            if cache_file.exists():
                self.logger.info(f"使用缓存语音文件: {cache_file.name}")
                return {
                    'success': True,
                    'file_path': str(cache_file),
                    'cached': True
                }
            
            self.logger.info(f"合成语音: {text[:50]}...")
            
            # 准备请求参数
            url = f"{self.voice_server}/voice/bert-vits2"
            params = {
                'text': text,
                'speaker_id': speaker_id,
                'speed': speed
            }
            
            # 发送请求
            response = self.session.get(url, params=params, timeout=self.timeout)
            
            if response.status_code != 200:
                return {
                    'success': False,
                    'message': f'语音合成失败: HTTP {response.status_code}'
                }
            
            # 检查响应内容类型
            content_type = response.headers.get('content-type', '')
            if 'audio' not in content_type and 'application/octet-stream' not in content_type:
                return {
                    'success': False,
                    'message': '服务器返回的不是音频文件'
                }
            
            # 保存音频文件
            with open(cache_file, 'wb') as f:
                f.write(response.content)
            
            self.logger.info(f"语音合成成功: {cache_file.name}")
            
            return {
                'success': True,
                'file_path': str(cache_file),
                'cached': False
            }
            
        except requests.exceptions.ConnectionError:
            self.logger.error("无法连接到语音服务器")
            return {
                'success': False,
                'message': '无法连接到语音服务器，请检查网络连接'
            }
        except requests.exceptions.Timeout:
            self.logger.error("语音合成请求超时")
            return {
                'success': False,
                'message': '语音合成超时，请稍后重试'
            }
        except Exception as e:
            self.logger.error(f"语音合成失败: {e}")
            return {
                'success': False,
                'message': f'语音合成失败: {str(e)}'
            }
    
    def _generate_cache_key(self, text: str, speaker_id: int, speed: float) -> str:
        """生成缓存键"""
        # 使用文本、主播ID和速度生成唯一键
        content = f"{text}_{speaker_id}_{speed}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    @handle_exceptions("VoiceSynthesis")
    def batch_synthesize(self, texts: list, speaker_id: int = None, speed: float = None) -> Dict[str, Any]:
        """批量合成语音"""
        try:
            if not texts:
                return {
                    'success': False,
                    'message': '文本列表不能为空'
                }
            
            results = []
            success_count = 0
            
            for i, text in enumerate(texts):
                self.logger.info(f"批量合成进度: {i+1}/{len(texts)}")
                
                result = self.synthesize_voice(text, speaker_id, speed)
                results.append({
                    'text': text,
                    'result': result
                })
                
                if result['success']:
                    success_count += 1
                
                # 避免请求过于频繁
                if i < len(texts) - 1:
                    time.sleep(0.1)
            
            self.logger.info(f"批量合成完成: {success_count}/{len(texts)} 成功")
            
            return {
                'success': True,
                'results': results,
                'success_count': success_count,
                'total_count': len(texts)
            }
            
        except Exception as e:
            self.logger.error(f"批量语音合成失败: {e}")
            return {
                'success': False,
                'message': f'批量语音合成失败: {str(e)}'
            }
    
    def set_default_params(self, speaker_id: int, speed: float):
        """设置默认参数"""
        self.default_speaker_id = speaker_id
        self.default_speed = max(0.5, min(2.0, speed))
        self.logger.info(f"设置默认参数: speaker_id={speaker_id}, speed={self.default_speed}")
    
    def clear_cache(self, older_than_days: int = 7) -> int:
        """清理缓存文件"""
        try:
            cleared_count = 0
            cutoff_time = time.time() - (older_than_days * 24 * 3600)
            
            for cache_file in self.cache_dir.glob("*.wav"):
                if cache_file.stat().st_mtime < cutoff_time:
                    cache_file.unlink()
                    cleared_count += 1
            
            self.logger.info(f"清理了 {cleared_count} 个缓存文件")
            return cleared_count
            
        except Exception as e:
            self.logger.error(f"清理缓存失败: {e}")
            return 0
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        try:
            cache_files = list(self.cache_dir.glob("*.wav"))
            total_size = sum(f.stat().st_size for f in cache_files)
            
            return {
                'cache_dir': str(self.cache_dir),
                'file_count': len(cache_files),
                'total_size_mb': total_size / (1024 * 1024),
                'files': [f.name for f in cache_files[:10]]  # 只返回前10个文件名
            }
            
        except Exception as e:
            self.logger.error(f"获取缓存信息失败: {e}")
            return {
                'cache_dir': str(self.cache_dir),
                'file_count': 0,
                'total_size_mb': 0,
                'files': []
            }
    
    def close(self):
        """关闭语音合成服务"""
        self.session.close()
        self.logger.info("语音合成服务已关闭")


def main():
    """测试函数"""
    voice_service = VoiceSynthesis()
    
    # 测试获取主播列表
    print("获取语音主播列表...")
    speakers_result = voice_service.get_speakers()
    if speakers_result['success']:
        speakers = speakers_result['speakers']
        print(f"✅ 获取到 {len(speakers)} 个主播")
        for speaker in speakers[:5]:  # 只显示前5个
            print(f"  - {speaker['name']} (ID: {speaker['id']}, 类别: {speaker['category']})")
    else:
        print(f"❌ 获取主播列表失败: {speakers_result['message']}")
    
    # 测试语音合成
    print("\n测试语音合成...")
    test_text = "欢迎来到AI主播系统，这是一个测试语音。"
    result = voice_service.synthesize_voice(test_text, speaker_id=0, speed=1.0)
    
    if result['success']:
        print(f"✅ 语音合成成功: {result['file_path']}")
        print(f"   缓存状态: {'使用缓存' if result['cached'] else '新生成'}")
    else:
        print(f"❌ 语音合成失败: {result['message']}")
    
    # 测试批量合成
    print("\n测试批量语音合成...")
    test_texts = [
        "大家好，欢迎观看直播！",
        "感谢大家的支持！",
        "请多多关注我们的频道！"
    ]
    
    batch_result = voice_service.batch_synthesize(test_texts)
    if batch_result['success']:
        print(f"✅ 批量合成完成: {batch_result['success_count']}/{batch_result['total_count']}")
    else:
        print(f"❌ 批量合成失败: {batch_result['message']}")
    
    # 获取缓存信息
    cache_info = voice_service.get_cache_info()
    print(f"\n缓存信息:")
    print(f"  文件数量: {cache_info['file_count']}")
    print(f"  总大小: {cache_info['total_size_mb']:.2f} MB")
    
    # 关闭服务
    voice_service.close()


if __name__ == "__main__":
    main()
