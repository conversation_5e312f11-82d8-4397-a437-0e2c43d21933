#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试程序 - 用于验证打包是否正常
"""

import sys
import os
from pathlib import Path

def main():
    print("=" * 50)
    print("AI主播系统 - 简单测试版本")
    print("=" * 50)
    
    try:
        # 测试基本模块导入
        print("[TEST] 测试基本模块导入...")
        import json
        import sqlite3
        import threading
        import queue
        import time
        import datetime
        import random
        print("[OK] 基本模块导入成功")
        
        # 测试PyQt5导入
        print("[TEST] 测试PyQt5导入...")
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        print("[OK] PyQt5导入成功")
        
        # 测试网络模块导入
        print("[TEST] 测试网络模块导入...")
        import requests
        print("[OK] 网络模块导入成功")
        
        # 创建简单的GUI测试
        print("[TEST] 创建GUI应用...")
        app = QApplication(sys.argv)
        
        # 显示消息框
        msg = QMessageBox()
        msg.setWindowTitle("AI主播系统测试")
        msg.setText("打包测试成功！\n\n所有核心模块都能正常导入。")
        msg.setIcon(QMessageBox.Information)
        msg.setStandardButtons(QMessageBox.Ok)
        
        print("[OK] GUI应用创建成功")
        print("[INFO] 显示测试对话框...")
        
        result = msg.exec_()
        print(f"[OK] 用户点击了按钮，返回值: {result}")
        
        print("[SUCCESS] 所有测试通过！")
        return 0
        
    except ImportError as e:
        print(f"[ERROR] 模块导入失败: {e}")
        return 1
    except Exception as e:
        print(f"[ERROR] 程序运行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    input("\n按回车键退出...")
    sys.exit(exit_code)
