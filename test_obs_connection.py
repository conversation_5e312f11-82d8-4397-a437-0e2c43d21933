#!/usr/bin/env python3
"""
OBS WebSocket连接测试程序
用于测试OBS连接和获取源列表功能
"""

import asyncio
import websockets
import json
import hashlib
import base64
from typing import Optional, Dict, Any

class OBSWebSocketTest:
    """OBS WebSocket测试类"""
    
    def __init__(self, host: str = "localhost", port: int = 4455, password: str = ""):
        self.host = host
        self.port = port
        self.password = password
        self.websocket = None
        self.is_connected = False
        self.is_authenticated = False
        self.request_id = 0
        
    async def connect(self) -> bool:
        """连接到OBS WebSocket"""
        try:
            uri = f"ws://{self.host}:{self.port}"
            print(f"🔄 正在连接到OBS: {uri}")
            
            self.websocket = await websockets.connect(uri)
            print("✅ WebSocket连接成功")
            
            # 等待Hello消息
            hello_message = await self.websocket.recv()
            hello_data = json.loads(hello_message)
            print(f"📨 收到Hello消息: {hello_data}")
            
            if hello_data.get("op") == 0:  # Hello
                # 发送Identify消息
                await self.send_identify(hello_data.get("d", {}))
                
                # 等待Identified消息
                identified_message = await self.websocket.recv()
                identified_data = json.loads(identified_message)
                print(f"📨 收到Identified消息: {identified_data}")
                
                if identified_data.get("op") == 2:  # Identified
                    self.is_connected = True
                    self.is_authenticated = True
                    print("✅ OBS身份验证成功")
                    return True
                else:
                    print(f"❌ 身份验证失败: {identified_data}")
                    return False
            else:
                print(f"❌ 未收到Hello消息: {hello_data}")
                return False
                
        except Exception as e:
            print(f"❌ 连接OBS失败: {e}")
            return False
    
    async def send_identify(self, hello_data: Dict[str, Any]):
        """发送身份验证消息"""
        identify_data = {
            "op": 1,  # Identify
            "d": {
                "rpcVersion": 1
            }
        }
        
        # 如果需要密码验证
        if "authentication" in hello_data and self.password:
            auth_data = hello_data["authentication"]
            challenge = auth_data.get("challenge", "")
            salt = auth_data.get("salt", "")
            
            # 生成认证字符串
            secret = base64.b64encode(
                hashlib.sha256((self.password + salt).encode()).digest()
            ).decode()
            
            auth_response = base64.b64encode(
                hashlib.sha256((secret + challenge).encode()).digest()
            ).decode()
            
            identify_data["d"]["authentication"] = auth_response
            print(f"🔐 使用密码验证")
        else:
            print(f"🔓 无密码验证")
        
        await self.websocket.send(json.dumps(identify_data))
        print(f"📤 发送Identify消息: {identify_data}")
    
    async def send_request(self, request_type: str, request_data: Optional[Dict] = None) -> Optional[Dict]:
        """发送请求并等待响应"""
        if not self.is_authenticated:
            print("❌ 未通过身份验证")
            return None
        
        self.request_id += 1
        request = {
            "op": 6,  # Request
            "d": {
                "requestType": request_type,
                "requestId": str(self.request_id),
                "requestData": request_data or {}
            }
        }
        
        try:
            await self.websocket.send(json.dumps(request))
            print(f"📤 发送请求: {request_type}")
            
            # 等待响应
            response_message = await self.websocket.recv()
            response_data = json.loads(response_message)
            print(f"📨 收到响应: {response_data}")
            
            if response_data.get("op") == 7:  # RequestResponse
                return response_data.get("d", {})
            else:
                print(f"❌ 响应格式错误: {response_data}")
                return None
                
        except Exception as e:
            print(f"❌ 发送请求失败: {e}")
            return None
    
    async def get_version(self) -> Optional[Dict]:
        """获取OBS版本信息"""
        return await self.send_request("GetVersion")
    
    async def get_input_list(self) -> Optional[Dict]:
        """获取输入源列表"""
        return await self.send_request("GetInputList")
    
    async def get_scene_list(self) -> Optional[Dict]:
        """获取场景列表"""
        return await self.send_request("GetSceneList")
    
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            self.is_connected = False
            self.is_authenticated = False
            print("✅ 已断开OBS连接")

async def test_obs_connection():
    """测试OBS连接"""
    print("🚀 开始测试OBS WebSocket连接")
    print("=" * 50)
    
    # 创建测试实例
    obs = OBSWebSocketTest(host="localhost", port=4455, password="")
    
    try:
        # 测试连接
        if await obs.connect():
            print("\n🎯 测试OBS功能:")
            print("-" * 30)
            
            # 获取版本信息
            version = await obs.get_version()
            if version:
                print(f"✅ OBS版本: {version.get('obsVersion', 'Unknown')}")
                print(f"✅ WebSocket版本: {version.get('obsWebSocketVersion', 'Unknown')}")
            
            # 获取输入源列表
            print("\n📹 获取输入源列表:")
            inputs = await obs.get_input_list()
            if inputs and "inputs" in inputs:
                input_list = inputs["inputs"]
                print(f"✅ 找到 {len(input_list)} 个输入源:")
                for i, input_item in enumerate(input_list, 1):
                    input_name = input_item.get("inputName", "Unknown")
                    input_kind = input_item.get("inputKind", "Unknown")
                    print(f"  {i}. {input_name} ({input_kind})")
            else:
                print("❌ 未获取到输入源列表")
            
            # 获取场景列表
            print("\n🎬 获取场景列表:")
            scenes = await obs.get_scene_list()
            if scenes and "scenes" in scenes:
                scene_list = scenes["scenes"]
                print(f"✅ 找到 {len(scene_list)} 个场景:")
                for i, scene in enumerate(scene_list, 1):
                    scene_name = scene.get("sceneName", "Unknown")
                    print(f"  {i}. {scene_name}")
            else:
                print("❌ 未获取到场景列表")
        
        else:
            print("❌ 连接OBS失败")
    
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
    
    finally:
        await obs.disconnect()
    
    print("\n" + "=" * 50)
    print("🏁 OBS连接测试完成")

def main():
    """主函数"""
    print("OBS WebSocket连接测试工具")
    print("请确保OBS Studio已启动并启用了WebSocket服务器")
    print("OBS Studio -> 工具 -> WebSocket服务器设置")
    print()
    
    # 运行异步测试
    asyncio.run(test_obs_connection())

if __name__ == "__main__":
    main()
