#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试播放完成回调
"""

import os
import time
import threading
from pathlib import Path


def test_playback_callback():
    """测试播放完成回调"""
    print("🔍 测试播放完成回调...")
    
    # 查找测试文件
    voice_dir = Path("voices")
    if not voice_dir.exists():
        print("❌ voices目录不存在")
        return
        
    wav_files = list(voice_dir.glob("*.wav"))
    if not wav_files:
        print("❌ 没有找到wav文件")
        return
        
    test_file = wav_files[0]
    print(f"📁 测试文件: {test_file}")
    
    # 测试pygame播放完成回调
    try:
        import pygame
        
        # 初始化
        pygame.mixer.init()
        
        # 播放完成标志
        playback_finished = False
        
        def on_playback_finished():
            nonlocal playback_finished
            playback_finished = True
            print("🔔 播放完成回调被触发")
        
        # 播放音频
        pygame.mixer.music.load(str(test_file))
        pygame.mixer.music.play()
        
        print("🎵 开始播放...")
        start_time = time.time()
        
        # 监控播放状态
        while pygame.mixer.music.get_busy():
            time.sleep(0.1)
            
        # 播放结束
        elapsed = time.time() - start_time
        print(f"✅ 播放结束，耗时: {elapsed:.1f}秒")
        
        # 手动触发回调
        on_playback_finished()
        
        # 检查回调是否被触发
        if playback_finished:
            print("✅ 播放完成回调测试成功")
        else:
            print("❌ 播放完成回调未被触发")
            
    except ImportError:
        print("❌ pygame不可用")
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def test_main_program_callback():
    """测试主程序中的播放完成回调机制"""
    print("\n🔍 分析主程序播放完成回调机制...")
    
    # 检查主程序文件
    main_file = Path("run_gui_qt5.py")
    if not main_file.exists():
        print("❌ 主程序文件不存在")
        return
        
    # 读取主程序内容
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
        
    # 查找播放完成回调相关代码
    callback_patterns = [
        "on_internal_audio_finished",
        "on_simple_audio_finished", 
        "on_audio_playback_finished",
        "playback_finished.connect",
        "on_playback_end"
    ]
    
    print("📋 播放完成回调相关代码:")
    for pattern in callback_patterns:
        if pattern in content:
            print(f"  ✅ 找到: {pattern}")
            
            # 统计出现次数
            count = content.count(pattern)
            print(f"     出现次数: {count}")
        else:
            print(f"  ❌ 未找到: {pattern}")
            
    # 查找播放状态重置相关代码
    reset_patterns = [
        "audio_playing = False",
        "is_playing = False",
        "current_playing_item = None"
    ]
    
    print("\n📋 播放状态重置相关代码:")
    for pattern in reset_patterns:
        if pattern in content:
            count = content.count(pattern)
            print(f"  ✅ 找到: {pattern} (出现{count}次)")
        else:
            print(f"  ❌ 未找到: {pattern}")


def test_audio_player_status():
    """测试音频播放器状态"""
    print("\n🔍 测试音频播放器状态...")
    
    # 查找测试文件
    voice_dir = Path("voices")
    if not voice_dir.exists():
        print("❌ voices目录不存在")
        return
        
    wav_files = list(voice_dir.glob("*.wav"))
    if not wav_files:
        print("❌ 没有找到wav文件")
        return
        
    test_file = wav_files[0]
    
    try:
        import pygame
        
        # 初始化
        pygame.mixer.init()
        
        print("🎵 播放前状态:")
        print(f"  - get_busy(): {pygame.mixer.music.get_busy()}")
        
        # 播放音频
        pygame.mixer.music.load(str(test_file))
        pygame.mixer.music.play()
        
        print("🎵 播放中状态:")
        print(f"  - get_busy(): {pygame.mixer.music.get_busy()}")
        
        # 等待播放完成
        while pygame.mixer.music.get_busy():
            time.sleep(0.1)
            
        print("🎵 播放后状态:")
        print(f"  - get_busy(): {pygame.mixer.music.get_busy()}")
        
        print("✅ 音频播放器状态测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    print("🧪 播放完成回调测试")
    print("=" * 50)
    
    test_playback_callback()
    test_main_program_callback()
    test_audio_player_status()
    
    print("\n✅ 测试完成")
