#!/usr/bin/env python3
"""
独立测试话术解析功能
"""

import json
import re

def test_real_json_parsing():
    """测试真实的JSON解析"""
    print("=== 测试真实JSON解析 ===")
    
    # 从图片中看到的真实JSON数据（简化版本）
    real_json = """{
  "0秒 - 10秒": "1***哈喽，大家好，首先感谢【大哥|哥哥|宝子】们的捧场。\\n2***稍作停留也是爱，所能接触的都是这四个大字，随便哪个都是这四个大字。\\n3***没有花里胡哨，没有弯弯绕绕。\\n4***有啥就问，新来的家人们，不要藏着掖着。\\n5***云想衣裳花想容，春风拂槛露华浓，仙侠界的白莲花，花容月貌顶呱呱。",
  "10秒 - 20秒": "6***自古套路得人心，但唯有真心得天下，咱们心不要慌，手不要抖，跟着感觉走。\\n7***新来的家人们，相遇就是缘分，不要犹豫不要徘徊。\\n8***你们的到来蓬荜生辉，老妹我深感荣幸\\n9***随便什么数字都是这四个大字，目光所及都是这四个大字。\\n10***俗话说的好，大哥们，人生不主动，快乐少一半",
  "20秒 - 30秒": "11***相信自己的眼睛，相信自己的耳朵\\n12***斩青丝斩难过斩断红尘不为过，建模美 姿势帅，呼风唤雨惹人爱\\n13***不穿摸，不贴片，仙侠界的彭于晏，（千人捧万人追）手游界的刘亦菲\\n14***播播间人很多，主播一个人一张嘴，忙不过来，理解下，大哥们\\n15***万水千山总是情，今天主播最热情"
}"""
    
    print("原始JSON内容:")
    print(real_json)
    print("\n" + "="*50)
    
    # 解析JSON
    try:
        data = json.loads(real_json)
        print(f"✅ JSON解析成功，包含 {len(data)} 个时间段")
        
        # 检查格式
        is_new_format = all(isinstance(v, str) for v in data.values())
        print(f"✅ 格式检测: {'新格式' if is_new_format else '旧格式'}")
        
        if is_new_format:
            print("\n📋 解析结果:")
            for segment_name, segment_content in data.items():
                print(f"\n🕐 时间段: {segment_name}")
                
                # 提取时间信息
                pattern = r'(\d+)秒\s*-\s*(\d+)秒'
                match = re.search(pattern, segment_name)
                if match:
                    start_time = int(match.group(1))
                    end_time = int(match.group(2))
                    print(f"   ⏰ 时间: {start_time}秒 - {end_time}秒")
                
                # 解析话术内容
                lines = segment_content.split('\\n')
                print(f"   📝 话术内容 ({len(lines)}行):")
                for i, line in enumerate(lines, 1):
                    if line.strip():
                        print(f"      {i}. {line}")
                
                print(f"   📊 统计: {len(lines)}行话术")
            
            return True, data
        else:
            print("❌ 不是新格式")
            return False, {}
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        return False, {}

def test_display_generation(parsed_data):
    """测试显示文本生成"""
    print("\n=== 测试显示文本生成 ===")
    
    script_name = "kaer"
    
    # 模拟解析后的数据结构
    script_time_segments = {script_name: {}}
    
    # 转换解析数据为内部格式
    for segment_name, segment_content in parsed_data.items():
        # 提取时间信息
        pattern = r'(\d+)秒\s*-\s*(\d+)秒'
        match = re.search(pattern, segment_name)
        if match:
            start_time = int(match.group(1))
            end_time = int(match.group(2))
        else:
            start_time = 0
            end_time = 60
        
        script_time_segments[script_name][segment_name] = {
            'start': start_time,
            'end': end_time,
            'content': segment_content
        }
    
    # 生成显示文本
    if script_name in script_time_segments and script_time_segments[script_name]:
        time_segments_count = len(script_time_segments[script_name])
        display_text = f"# 时间段话术：{script_name}\n"
        display_text += f"# 共有 {time_segments_count} 个时间段\n\n"
        display_text += "# 时间段列表：\n"
        
        for segment_name, segment_data in script_time_segments[script_name].items():
            start_time = segment_data.get('start', 0)
            end_time = segment_data.get('end', 0)
            content_lines = len(segment_data.get('content', '').split('\\n'))
            display_text += f"# - {segment_name} ({start_time}秒-{end_time}秒) - {content_lines}行话术\n"
        
        display_text += "\n# 请在左侧时间段列表中选择具体时间段进行编辑"
        display_text += "\n# 注意：此话术已解析为时间段格式，不显示原始JSON内容"
        
        print("生成的显示文本:")
        print(display_text)
        
        return display_text, script_time_segments
    else:
        return "# 该话术暂无内容", {}

def test_complete_workflow():
    """测试完整的工作流程"""
    print("\n=== 测试完整工作流程 ===")
    
    # 模拟完整的解析函数
    def parse_time_segment_json_format(script_name, content):
        """完整的解析函数"""
        try:
            print(f"[SEARCH] 开始解析时间段JSON格式，话术: {script_name}")
            print(f"[INFO] JSON内容长度: {len(content)} 字符")
            
            # 解析JSON
            time_segments_data = json.loads(content)
            
            if not isinstance(time_segments_data, dict):
                print(f"[WARNING] JSON数据不是字典格式")
                return False, {}
            
            # 检查是否为新格式
            is_new_format = all(isinstance(v, str) for v in time_segments_data.values())
            print(f"[INFO] 检测格式类型: {'新格式' if is_new_format else '旧格式'}")
            
            if not is_new_format:
                print(f"[WARNING] 不是新的时间段格式")
                return False, {}
            
            # 初始化数据结构
            script_time_segments = {script_name: {}}
            
            # 解析每个时间段
            parsed_count = 0
            for time_key, time_data in time_segments_data.items():
                print(f"[SEARCH] 处理时间段: '{time_key}'")
                
                if isinstance(time_data, str):
                    segment_content = time_data
                    
                    # 提取时间信息
                    pattern = r'(\d+)秒\s*-\s*(\d+)秒'
                    match = re.search(pattern, time_key)
                    
                    if match:
                        start_time = int(match.group(1))
                        end_time = int(match.group(2))
                    else:
                        start_time = parsed_count * 10
                        end_time = (parsed_count + 1) * 10
                    
                    print(f"   [DATA] 时间信息: {start_time}-{end_time}秒")
                    
                    # 统计话术行数
                    content_lines = segment_content.split('\\n')
                    script_lines = [line for line in content_lines if line.strip() and '***' in line]
                    print(f"   [INFO] 话术行数: {len(script_lines)}/{len(content_lines)}")
                    
                    # 存储数据
                    script_time_segments[script_name][time_key] = {
                        'start': start_time,
                        'end': end_time,
                        'content': segment_content
                    }
                    
                    print(f"[OK] 解析时间段: '{time_key}'")
                    parsed_count += 1
            
            print(f"[SUCCESS] 成功解析 {parsed_count} 个时间段")
            return True, script_time_segments
            
        except json.JSONDecodeError as e:
            print(f"[ERROR] JSON解析失败: {e}")
            return False, {}
        except Exception as e:
            print(f"[ERROR] 解析异常: {e}")
            return False, {}
    
    # 测试数据
    test_json = """{
  "0秒 - 10秒": "1***哈喽，大家好，首先感谢【大哥|哥哥|宝子】们的捧场。\\n2***稍作停留也是爱，所能接触的都是这四个大字。",
  "10秒 - 20秒": "3***自古套路得人心，但唯有真心得天下。\\n4***新来的家人们，相遇就是缘分。",
  "20秒 - 30秒": "5***相信自己的眼睛，相信自己的耳朵。\\n6***斩青丝斩难过斩断红尘不为过。"
}"""
    
    # 执行解析
    success, parsed_data = parse_time_segment_json_format("测试话术", test_json)
    
    if success:
        print(f"\n✅ 解析成功")
        
        # 生成显示文本
        script_name = "测试话术"
        if script_name in parsed_data and parsed_data[script_name]:
            time_segments_count = len(parsed_data[script_name])
            display_text = f"# 时间段话术：{script_name}\n"
            display_text += f"# 共有 {time_segments_count} 个时间段\n\n"
            display_text += "# 时间段列表：\n"
            
            for segment_name, segment_data in parsed_data[script_name].items():
                start_time = segment_data.get('start', 0)
                end_time = segment_data.get('end', 0)
                content_lines = len(segment_data.get('content', '').split('\\n'))
                display_text += f"# - {segment_name} ({start_time}秒-{end_time}秒) - {content_lines}行话术\n"
            
            display_text += "\n# 请在左侧时间段列表中选择具体时间段进行编辑"
            display_text += "\n# 注意：此话术已解析为时间段格式，不显示原始JSON内容"
            
            print("\n📄 最终显示文本:")
            print(display_text)
            
            return True
    else:
        print(f"\n❌ 解析失败")
        return False

def main():
    """主测试函数"""
    print("开始独立测试话术解析功能...")
    
    try:
        # 测试1：真实JSON解析
        success1, parsed_data = test_real_json_parsing()
        
        # 测试2：显示文本生成
        if success1:
            display_text, script_time_segments = test_display_generation(parsed_data)
        
        # 测试3：完整工作流程
        success3 = test_complete_workflow()
        
        print("\n=== 测试总结 ===")
        if success1 and success3:
            print("✅ 所有测试通过")
            print("解析功能工作正常")
            
            print("\n🔧 关键发现:")
            print("1. JSON格式检测正确")
            print("2. 时间段解析成功")
            print("3. 话术内容提取正确")
            print("4. 显示文本生成正确")
            
            print("\n📋 下一步:")
            print("1. 将测试通过的解析逻辑同步到主程序")
            print("2. 确保主程序中的数据结构初始化")
            print("3. 验证缓存逻辑不会干扰解析")
            
        else:
            print("❌ 部分测试失败")
            print("需要进一步调试")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    main()
