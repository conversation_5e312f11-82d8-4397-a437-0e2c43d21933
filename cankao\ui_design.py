import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog, simpledialog
import requests
import json
import subprocess
import re
import os
import hashlib
import time
import threading
import queue
import asyncio
from websocket_client import WebSocketClient
import webbrowser
import zipfile
import shutil
import sys
from config import load_config, CLIENT_VERSION, get_machine_code

# 尝试导入pygame，用于音频播放
try:
    import pygame
    # 检查pygame.mixer是否可用
    if not hasattr(pygame, 'mixer'):
        print("警告: pygame.mixer模块不可用，语音播放功能可能无法使用")
        # 创建一个模拟的mixer模块，避免后续代码出错
        class DummyMixer:
            def __init__(self):
                self.music = DummyMusic()

            def init(self, *args, **kwargs):
                print("使用模拟的pygame.mixer.init()")
                return None

            def get_init(self):
                return None

            def get_num_devices(self):
                return 0

            def get_device_name(self, device_id):
                return "模拟设备"

        class DummyMusic:
            def load(self, *args, **kwargs):
                pass

            def play(self, *args, **kwargs):
                pass

            def get_busy(self):
                return False

            def set_volume(self, volume):
                pass

        # 替换pygame.mixer
        pygame.mixer = DummyMixer()
except ImportError:
    print("警告: pygame模块未安装，语音播放功能可能无法使用")
    # 创建一个模拟的pygame模块，避免后续代码出错
    class DummyPygame:
        def __init__(self):
            self.mixer = DummyMixer()

    class DummyMixer:
        def __init__(self):
            self.music = DummyMusic()

        def init(self, *args, **kwargs):
            print("使用模拟的pygame.mixer.init()")
            return None

        def get_init(self):
            return None

        def get_num_devices(self):
            return 0

        def get_device_name(self, device_id):
            return "模拟设备"

    class DummyMusic:
        def __init__(self):
            self.parent = None

        def set_parent(self, parent):
            """设置父对象，用于调用日志方法

            Args:
                parent: 父对象，必须有log方法
            """
            self.parent = parent

        def log(self, message, log_type="info"):
            """记录日志

            Args:
                message: 日志消息
                log_type: 日志类型
            """
            if self.parent and hasattr(self.parent, "log"):
                self.parent.log(message, log_type)
            else:
                print(f"[DummyMusic] {message}")

        def load(self, *args, **kwargs):
            """加载音乐文件"""
            if self.parent:
                self.log("尝试加载音乐文件")
            return True

        def play(self, *args, **kwargs):
            """播放视频

            Args:
                *args: 可变参数
                **kwargs: 关键字参数

            Returns:
                bool: 是否成功播放
            """
            self.log("尝试播放视频")

            try:
                # 检查是否有父对象
                if not self.parent:
                    self.log("没有父对象，无法播放视频", "error")
                    return False

                # 检查是否已连接OBS
                if not hasattr(self.parent, "obs_client") or not self.parent.obs_client:
                    self.log("OBS未连接，无法播放视频", "error")
                    return False

                # 获取当前视频源
                current_source = kwargs.get("source", "主视频A")

                # 播放视频
                self.log(f"播放视频源: {current_source}")

                # 使用OBS API播放视频
                try:
                    # 获取视频源属性
                    source_settings = self.parent.obs_client.get_input_settings(current_source)

                    # 设置播放状态
                    source_settings["settings"]["state"] = "playing"

                    # 更新视频源属性
                    self.parent.obs_client.set_input_settings(current_source, source_settings["settings"], True)

                    self.log(f"成功播放视频源: {current_source}")
                    return True
                except Exception as e:
                    self.log(f"播放视频失败: {str(e)}", "error")
                    return False
            except Exception as e:
                self.log(f"播放视频异常: {str(e)}", "error")
                import traceback
                self.log(traceback.format_exc(), "error")
                return False

        def get_busy(self):
            """检查是否正在播放"""
            return False

        def set_volume(self, volume):
            """设置音量

            Args:
                volume: 音量值，范围0.0-1.0
            """
            self.log(f"设置音量: {volume}")
            return True

    # 创建模拟的pygame模块
    pygame = DummyPygame()

class MainWindow:
    def __init__(self, root, username=None, token=None, token_expiry=None, skip_init=False):
        self.root = root
        self.root.title("AI主播系统 - 智能语音助手")
        self.root.geometry("1300x850")  # 增加默认窗口大小，确保所有控件都能完整显示

        # 初始时隐藏主窗口，直到检查更新完成
        if skip_init:
            self.root.withdraw()

        # 保存用户名和token
        self.username = username
        self.token = token
        self.token_valid = False if not token else True

        # 保存过期时间
        self.expiry_time = token_expiry
        print(f"初始化时设置过期时间: {token_expiry}")

        # 当前客户端版本
        self.client_version = CLIENT_VERSION
        # 获取机器码
        self.machine_code = get_machine_code()

        # 创建线程安全的消息队列
        self.ui_queue = queue.Queue()

        # 初始化弹幕消息队列
        self.initialize_danmaku_queue()

        # 打印调试信息
        print(f"MainWindow初始化: 用户名={username}, token={token[:10] if token else None}...")

        # 检查是否有更新标记文件
        self.check_update_flag()

        # 设置窗口最小大小
        self.root.minsize(1200, 800)

        # 设置窗口背景色
        self.root.configure(background="#FAFAFA")

        # 配置行列权重，使窗口可以自适应缩放
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)

        # 启用窗口大小变化事件
        self.root.bind("<Configure>", self.on_window_resize)

        # 直播状态相关变量
        self.live_status_timer = None
        self.voice_play_time = 0  # 语音播放时间（秒）
        self.online_count = 0  # 在线人数

        # 初始化最近进入直播间的用户列表
        self.recent_users = ["", "", ""]  # 分别对应user1, user2, user3

        # 定义变量列表
        self.variables = [
            {"name": "昵称", "code": "{nick}", "description": "用户昵称"},
            {"name": "日期", "code": "{date}", "description": "当前日期"},
            {"name": "时间", "code": "{time}", "description": "当前时间"},
            {"name": "人数", "code": "{people}", "description": "当前人数"},
            {"name": "礼物", "code": "{gift}", "description": "礼物信息"},
            {"name": "游戏类型", "code": "{gametype}", "description": "游戏类型"},
            {"name": "游戏名", "code": "{gamename}", "description": "游戏名称"},
            {"name": "新用户1", "code": "{user1}", "description": "最近进入的用户"},
            {"name": "新用户2", "code": "{user2}", "description": "第二个最近进入的用户"},
            {"name": "新用户3", "code": "{user3}", "description": "第三个最近进入的用户"}
        ]

        # 尝试设置窗口图标
        try:
            # 优先使用简生活图标
            if os.path.exists("简生活图标.ico"):
                self.root.iconbitmap("简生活图标.ico")
            else:
                self.root.iconbitmap("icon.ico")
        except:
            pass  # 如果图标文件不存在，忽略错误

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 加载配置
        self.config = load_config()

        # 初始化基本变量
        # AI主播相关变量
        self.ai_speakers = []  # 存储所有AI主播信息
        self.current_speaker = None  # 当前选中的主播
        self.speaker_speed = 1.0  # 默认语速

        # 话术相关变量
        self.script_list = []  # 存储所有话术信息
        self.current_script = None  # 当前选中的话术

        # AI对话相关变量
        self.dialogue_list = []  # 存储所有AI对话信息
        self.current_dialogue = None  # 当前选中的对话
        self.dialogue_data = []  # 当前对话的详细数据

        # 播放相关变量
        self.is_playing = False  # 是否正在播放
        self.is_paused = False  # 是否暂停
        self.voice_queue = []  # 语音队列
        self.used_script_lines = []  # 已使用的话术行
        self.allow_danmaku_voice = False  # 是否允许弹幕触发语音生成

        # 如果不跳过初始化，则立即初始化
        if not skip_init:
            self.initialize_app()

    def create_custom_styles(self):
        """创建自定义样式"""
        style = ttk.Style()

        # 设置全局主题颜色 - 更现代的配色方案
        self.primary_color = "#3F51B5"  # 靛蓝色主调
        self.primary_light = "#7986CB"  # 浅靛蓝色
        self.primary_dark = "#303F9F"   # 深靛蓝色
        self.secondary_color = "#FAFAFA"  # 更亮的背景色
        self.accent_color = "#FF4081"  # 亮粉色强调
        self.success_color = "#00C853"  # 鲜绿色成功
        self.warning_color = "#FFD600"  # 亮黄色警告
        self.error_color = "#F44336"    # 错误红色
        self.text_color = "#212121"  # 深灰色文本
        self.light_text_color = "#757575"  # 浅灰色文本
        self.border_color = "#E0E0E0"  # 边框颜色

        # 应用全局主题
        style.configure(".",
                       font=("Microsoft YaHei UI", 10),
                       background=self.secondary_color)

        # 创建标签页样式 - 更紧凑的标签页
        style.configure("TNotebook", background=self.secondary_color)
        style.configure("TNotebook.Tab",
                        font=("Microsoft YaHei UI", 11, "bold"),
                        padding=[12, 6],
                        background=self.secondary_color,
                        foreground=self.text_color,
                        borderwidth=1)

        # 修复选中标签页文字不可见的问题
        # 确保选中时背景色和文字颜色有足够对比度
        style.map("TNotebook.Tab",
                  background=[("selected", "#FFFFFF")],  # 使用白色背景
                  foreground=[("selected", self.primary_color)],  # 使用主色调文字
                  borderwidth=[("selected", 1)])

        # 创建一个自定义的标签页样式，确保选中时文字可见
        style.configure("Visible.TNotebook",
                        background=self.secondary_color)
        style.configure("Visible.TNotebook.Tab",
                        font=("Microsoft YaHei UI", 11, "bold"),
                        padding=[12, 6],
                        background=self.secondary_color,
                        foreground=self.text_color,
                        borderwidth=1)
        style.map("Visible.TNotebook.Tab",
                  background=[("selected", "#FFFFFF")],  # 使用白色背景
                  foreground=[("selected", self.primary_color)],  # 使用主色调文字
                  borderwidth=[("selected", 1)])

        # 创建强调按钮样式 - 更紧凑的按钮
        style.configure("Accent.TButton",
                        font=("Microsoft YaHei UI", 10, "bold"),
                        padding=[10, 6],
                        background=self.accent_color,
                        foreground="white",
                        borderwidth=0)
        style.map("Accent.TButton",
                  background=[("active", "#F50057")],  # 深粉色
                  foreground=[("active", "white")],
                  relief=[("pressed", "flat"), ("!pressed", "flat")])

        # 创建成功按钮样式
        style.configure("Success.TButton",
                        font=("Microsoft YaHei UI", 10, "bold"),
                        padding=[10, 6],
                        background=self.success_color,
                        foreground="white",
                        borderwidth=0)
        style.map("Success.TButton",
                  background=[("active", "#00B248")],  # 深绿色
                  foreground=[("active", "white")],
                  relief=[("pressed", "flat"), ("!pressed", "flat")])

        # 创建主要按钮样式
        style.configure("Primary.TButton",
                        font=("Microsoft YaHei UI", 10, "bold"),
                        padding=[10, 6],
                        background=self.primary_color,
                        foreground="white",
                        borderwidth=0)
        style.map("Primary.TButton",
                  background=[("active", self.primary_dark)],
                  foreground=[("active", "white")],
                  relief=[("pressed", "flat"), ("!pressed", "flat")])

        # 创建标题标签样式
        style.configure("Title.TLabel",
                        font=("Microsoft YaHei UI", 16, "bold"),
                        foreground=self.primary_color,
                        background=self.secondary_color)

        # 创建小标题标签样式
        style.configure("Subtitle.TLabel",
                        font=("Microsoft YaHei UI", 12, "bold"),
                        foreground=self.text_color,
                        background=self.secondary_color)

        # 创建标签框样式
        style.configure("TLabelframe",
                        background=self.secondary_color,
                        foreground=self.primary_color,
                        font=("Microsoft YaHei UI", 10, "bold"),
                        borderwidth=1,
                        relief="solid")

        style.configure("TLabelframe.Label",
                        background=self.secondary_color,
                        foreground=self.primary_color,
                        font=("Microsoft YaHei UI", 11, "bold"))

        # 创建分隔线样式
        style.configure("Separator.TSeparator",
                        background=self.primary_color)

        # 创建滑块样式
        style.configure("TScale",
                        background=self.secondary_color,
                        troughcolor=self.primary_light,
                        sliderrelief="flat")

        # 创建输入框样式
        style.configure("TEntry",
                        fieldbackground="white",
                        borderwidth=1,
                        relief="solid",
                        bordercolor=self.border_color)
        style.map("TEntry",
                  bordercolor=[("focus", self.primary_color)])

        # 创建复选框样式
        style.configure("TCheckbutton",
                        background=self.secondary_color,
                        foreground=self.text_color)
        style.map("TCheckbutton",
                  background=[("active", self.secondary_color)],
                  foreground=[("active", self.primary_color)])

        # 创建列表框样式
        style.configure("TListbox",
                        background="white",
                        foreground=self.text_color,
                        borderwidth=1,
                        relief="solid")

        # 创建框架样式
        style.configure("TFrame",
                        background=self.secondary_color)

        # 创建按钮样式
        style.configure("TButton",
                        font=("Microsoft YaHei UI", 10),
                        padding=[10, 5],
                        background=self.secondary_color,
                        foreground=self.text_color,
                        borderwidth=1,
                        relief="solid")
        style.map("TButton",
                  background=[("active", self.border_color)],
                  relief=[("pressed", "sunken"), ("!pressed", "solid")])

    def create_menu_bar(self):
        """创建菜单栏"""
        menu_bar = tk.Menu(self.root)
        self.root.config(menu=menu_bar)

        # 创建文件菜单
        file_menu = tk.Menu(menu_bar, tearoff=0)
        menu_bar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="退出", command=self.on_closing)

        # 创建工具菜单
        tools_menu = tk.Menu(menu_bar, tearoff=0)
        menu_bar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="设置", command=lambda: self.notebook.select(3))  # 切换到设置页
        tools_menu.add_separator()
        tools_menu.add_command(label="修复视频语音匹配系统", command=self.fix_video_match_tab)

        # 创建帮助菜单
        help_menu = tk.Menu(menu_bar, tearoff=0)
        menu_bar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_usage_guide)
        help_menu.add_command(label="检查更新", command=self.check_for_updates)
        help_menu.add_command(label="更新历史", command=self.show_update_history)
        help_menu.add_separator()
        help_menu.add_command(label="关于", command=self.show_about)

        # 创建主框架
        self.main_frame = ttk.Frame(self.root, style="TFrame")
        self.main_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)

        # 配置主框架的行列权重
        self.main_frame.grid_rowconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)

        # 创建状态栏
        self.status_bar = ttk.Frame(self.root, style="TFrame", relief="sunken", borderwidth=1)
        self.status_bar.grid(row=1, column=0, sticky="ew", padx=5, pady=(0, 5))

        # 配置状态栏的列权重
        self.status_bar.grid_columnconfigure(0, weight=1)
        self.status_bar.grid_columnconfigure(1, weight=0)

        # 添加状态信息标签
        self.status_label = ttk.Label(self.status_bar, text="就绪中...", anchor="w")
        self.status_label.grid(row=0, column=0, sticky="w", padx=5, pady=2)

        # 添加过期时间标签
        self.expiry_label = ttk.Label(self.status_bar, text="账号过期时间: 未知", anchor="e")
        self.expiry_label.grid(row=0, column=1, sticky="e", padx=5, pady=2)

        # 配置根窗口的行权重
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_rowconfigure(1, weight=0)

        # 创建标签页控件
        self.notebook = ttk.Notebook(self.main_frame, style="Visible.TNotebook")
        self.notebook.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)

        # 创建各个标签页
        self.ai_host_tab = ttk.Frame(self.notebook, style="TFrame")
        self.script_tab = ttk.Frame(self.notebook, style="TFrame")
        self.dialog_tab = ttk.Frame(self.notebook, style="TFrame")
        self.settings_tab = ttk.Frame(self.notebook, style="TFrame")
        self.obs_tab = ttk.Frame(self.notebook, style="TFrame")
        self.video_match_tab = ttk.Frame(self.notebook, style="TFrame")
        self.log_tab = ttk.Frame(self.notebook, style="TFrame")

        # 添加标签页到标签页控件
        self.notebook.add(self.ai_host_tab, text="AI主播")
        self.notebook.add(self.script_tab, text="话术管理")
        self.notebook.add(self.dialog_tab, text="AI对话")
        self.notebook.add(self.settings_tab, text="系统设置")
        self.notebook.add(self.obs_tab, text="OBS控制")
        self.notebook.add(self.video_match_tab, text="视频语音匹配系统")
        self.notebook.add(self.log_tab, text="日志信息")

        # 创建各个区域
        self.create_log_area()  # 先创建日志区，以便其他区域可以使用日志功能
        self.create_ai_host_area()
        self.create_script_area()
        self.create_ai_dialog_area()
        self.create_playback_control_area()
        self.create_playlist_area()
        self.create_obs_control_area()

        # 确保视频语音匹配标签页存在
        self.log("初始化视频语音匹配系统标签页...", "system")
        if self.ensure_video_match_tab_exists():
            # 创建视频语音匹配系统
            try:
                self.create_video_match_system()
                # 确保标签页可见
                tab_names = [self.notebook.tab(i, "text") for i in range(self.notebook.index("end"))]
                tab_index = tab_names.index("视频语音匹配系统") if "视频语音匹配系统" in tab_names else -1
                if tab_index >= 0:
                    self.log(f"视频语音匹配系统标签页索引: {tab_index}", "system")
                    # 选择标签页，使其可见
                    self.notebook.select(tab_index)
            except Exception as e:
                self.log(f"创建视频语音匹配系统失败: {str(e)}", "error")
                import traceback
                self.log(traceback.format_exc(), "error")
        else:
            self.log("无法创建视频语音匹配系统标签页，将在数据加载后重试", "warning")

        # 弹幕区功能已集成到create_ai_dialog_area方法中

        # 初始化礼物ID映射
        self.gift_id_map = {}

        # 服务器通信相关变量（已移除WebSocket）

        # 初始化OBS主副视频相关变量
        self.main_video_source = ""
        self.sub_video_sources = {}  # 关键词到媒体源的映射
        self.main_video_speed_min = 0.8
        self.main_video_speed_max = 1.2
        self.current_video_source = ""
        self.sub_video_timer = None

        # 加载礼物ID映射
        self.load_gift_id_map()

        # 初始化日志
        self.log("AI主播系统已启动")
        self.log(f"弹幕触发语音生成功能初始状态: allow_danmaku_voice={self.allow_danmaku_voice}", "system")

        # 启动UI消息处理定时器
        self.process_ui_queue()

    def initialize_app(self):
        """初始化应用程序"""
        self.log("开始初始化应用程序...")

        # 创建自定义样式
        self.create_custom_styles()

        # 创建菜单栏和UI组件
        self.create_menu_bar()

        # 显示主窗口
        self.root.deiconify()

        # 初始化时自动加载数据
        self.root.after(500, self.initial_data_load)

        # 验证token并启动直播状态定时提交
        self.root.after(1000, self.verify_token_and_start_status_update)

        self.log("应用程序初始化完成")

    def check_update_flag(self):
        """检查是否有更新标记文件"""
        try:
            # 检查更新标记文件
            update_flag_file = os.path.join("_internal", "update_flag.txt")
            if os.path.exists(update_flag_file):
                # 读取更新标记文件内容
                with open(update_flag_file, "r", encoding="utf-8") as f:
                    update_info = f.read().strip()

                # 显示更新提示
                if update_info:
                    self.log(f"检测到更新标记: {update_info}")
                else:
                    self.log("检测到更新标记，但没有更新信息")

                # 删除更新标记文件
                try:
                    os.remove(update_flag_file)
                    self.log("已删除更新标记文件")
                except Exception as e:
                    self.log(f"删除更新标记文件失败: {str(e)}")
            else:
                # 没有更新标记文件
                pass
        except Exception as e:
            # 忽略错误，不影响程序运行
            print(f"检查更新标记文件出错: {str(e)}")

    def show_usage_guide(self):
        """显示使用说明"""
        guide = """
        AI主播系统使用说明

        1. AI主播区
           - 选择您喜欢的AI主播声音
           - 调整语速

        2. 话术管理
           - 选择或编辑话术内容
           - 支持插入变量，如{nick}、{date}等

        3. AI对话
           - 管理AI对话关键词和回复
           - 支持添加和删除关键词

        4. 系统设置
           - 设置预备语音数量
           - 选择循环模式（随机/顺序）
           - 调整音量、音调和间隔

        5. 播放控制
           - 生成语音：根据话术生成语音
           - 播放/暂停/停止：控制语音播放
           - 清空列表：清空播放队列

        6. OBS控制
           - 控制OBS媒体源
           - 设置主视频和副视频

        7. 视频语音匹配系统
           - 根据视频时间段自动生成语音
           - 支持AI对话触发和副视频触发
        """
        messagebox.showinfo("使用说明", guide)

    def show_about(self):
        """显示关于信息"""
        about = f"""
        AI主播系统 - 智能语音助手

        版本: {self.client_version}

        功能:
        - AI语音合成
        - 话术管理
        - AI对话
        - 弹幕互动
        - OBS控制
        - 视频语音匹配

        技术支持: <EMAIL>
        """
        messagebox.showinfo("关于", about)

    def show_update_history(self):
        """显示更新历史"""
        history = """
        更新历史

        v1.0.0 (2025-05-20)
        - 初始版本发布
        - 支持AI语音合成
        - 支持话术管理
        - 支持AI对话
        - 支持弹幕互动

        v1.1.0 (2025-05-25)
        - 添加OBS控制功能
        - 优化UI界面
        - 修复已知问题

        v1.2.0 (2025-05-30)
        - 添加视频语音匹配系统
        - 支持时间段脚本
        - 支持副视频触发
        - 优化性能
        """
        messagebox.showinfo("更新历史", history)

    def check_for_updates(self):
        """检查软件更新"""
        try:
            self.log("正在检查更新...")

            # 检查是否存在更新管理器模块
            try:
                from update_manager import UpdateManager

                # 使用本地服务器地址
                update_server = "http://localhost:12456"
                self.log(f"使用更新服务器: {update_server}")

                # 创建更新管理器
                updater = UpdateManager(update_server)

                # 检查更新
                update_info = updater.check_for_updates()

                if update_info:
                    self.log(f"检测到新版本: {update_info.get('version')}")

                    # 创建更新窗口
                    from update_window import UpdateWindow

                    # 创建更新窗口
                    update_root = tk.Toplevel(self.root)
                    update_root.title("软件更新")
                    update_root.geometry("500x400")
                    update_root.resizable(False, False)
                    update_root.transient(self.root)
                    update_root.grab_set()

                    # 创建更新窗口
                    UpdateWindow(update_root, update_info, updater)

                    # 等待窗口关闭
                    self.root.wait_window(update_root)

                    # 窗口关闭后，检查版本号是否已更新
                    if updater.config.get("client_version") == update_info.get("version"):
                        self.log(f"更新已完成，版本号已更新为: {update_info.get('version')}")
                        # 重启应用程序
                        self.log("更新完成，重启应用程序...")
                        updater.restart_application()
                        return

                    # 如果版本号未更新，继续使用当前版本
                    self.log("更新未完成，继续使用当前版本")
                else:
                    self.log("当前已是最新版本")
            except ImportError:
                # 如果找不到更新管理器模块，显示简单的提示
                self.log("更新管理器模块不存在，跳过更新检查")
                messagebox.showinfo("检查更新", "当前已是最新版本")
        except Exception as e:
            self.log(f"检查更新失败: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")
            # 显示错误提示，但不中断程序运行
            messagebox.showinfo("检查更新", f"检查更新失败: {str(e)}\n\n请稍后再试。")

    def check_updates_then_initialize(self):
        """先检查更新，如果有更新就弹出更新界面，如果没有更新再进行初始化

        注意：此方法已被main.py中的登录成功回调函数替代，保留此方法仅为兼容性
        """
        self.log("登录成功后先检查更新，然后再初始化")
        self.log("此方法已被main.py中的登录成功回调函数替代，直接初始化应用")

        # 直接初始化应用
        self.initialize_app()


    def create_ai_dialog_area(self):
        """创建AI对话区"""
        # 创建AI对话区框架
        self.ai_dialog_frame = ttk.LabelFrame(self.dialog_tab, text="AI对话区")
        self.ai_dialog_frame.pack(fill=tk.BOTH, expand=True, padx=8, pady=8)

        # 创建标题栏
        title_frame = ttk.Frame(self.ai_dialog_frame)
        title_frame.pack(fill=tk.X, padx=10, pady=(10, 0))

        ttk.Label(title_frame, text="管理AI对话关键词和回复", style="Title.TLabel").pack(side=tk.LEFT)

        # 添加分隔线
        separator = ttk.Separator(self.ai_dialog_frame, orient="horizontal")
        separator.pack(fill=tk.X, padx=10, pady=(5, 10))

        # 创建上部控制区
        control_frame = ttk.Frame(self.ai_dialog_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        # 创建对话选择下拉框
        ttk.Label(control_frame, text="选择对话:", style="Subtitle.TLabel").pack(side=tk.LEFT, padx=(0, 5))

        self.dialogue_var = tk.StringVar()
        self.dialogue_combobox = ttk.Combobox(control_frame, textvariable=self.dialogue_var, state="readonly", width=25)
        self.dialogue_combobox.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.dialogue_combobox.bind("<<ComboboxSelected>>", self.on_dialogue_selected)

        # 创建按钮区域
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(side=tk.LEFT, padx=5)

        # 创建新建对话按钮
        self.new_dialogue_button = ttk.Button(button_frame, text="新建对话", command=self.create_new_dialogue, style="Accent.TButton")
        self.new_dialogue_button.pack(side=tk.LEFT, padx=5)

        # 创建刷新按钮
        self.dialogue_refresh_button = ttk.Button(button_frame, text="刷新对话", command=self.refresh_dialogues, style="Primary.TButton")
        self.dialogue_refresh_button.pack(side=tk.LEFT, padx=5)

        # 创建保存按钮
        self.save_dialogue_button = ttk.Button(button_frame, text="保存对话", command=self.save_dialogue, style="Success.TButton")
        self.save_dialogue_button.pack(side=tk.LEFT, padx=5)

        # 创建主内容区，使用网格布局
        content_frame = ttk.Frame(self.ai_dialog_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        content_frame.grid_columnconfigure(0, weight=1)
        content_frame.grid_columnconfigure(1, weight=3)
        content_frame.grid_rowconfigure(0, weight=3)
        content_frame.grid_rowconfigure(1, weight=1)

        # 创建左侧关键词列表区
        keyword_frame = ttk.LabelFrame(content_frame, text="关键词列表")
        keyword_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5), pady=(0, 5))

        # 创建关键词列表框
        self.keyword_listbox = tk.Listbox(keyword_frame,
                                      font=("Microsoft YaHei UI", 10),
                                      activestyle="none",
                                      selectbackground=self.primary_color,
                                      selectforeground="white",
                                      borderwidth=1,
                                      relief="solid")
        self.keyword_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.keyword_listbox.bind("<<ListboxSelect>>", self.on_keyword_selected)
        self.keyword_listbox.bind("<Button-3>", self.show_keyword_context_menu)

        # 添加滚动条
        keyword_scrollbar = ttk.Scrollbar(keyword_frame, orient=tk.VERTICAL, command=self.keyword_listbox.yview)
        self.keyword_listbox.configure(yscrollcommand=keyword_scrollbar.set)
        keyword_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 创建右侧回复内容区
        response_frame = ttk.LabelFrame(content_frame, text="回复内容")
        response_frame.grid(row=0, column=1, sticky="nsew", padx=(5, 0), pady=(0, 5))

        # 创建回复内容文本框
        self.response_text = scrolledtext.ScrolledText(response_frame, wrap=tk.WORD, height=10)
        self.response_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 添加事件处理，确保操作回复内容不会影响关键词的选中状态
        def preserve_keyword_selection(event):
            # 确保关键词选中状态不变
            if hasattr(self, "keyword_listbox") and self.keyword_listbox.size() > 0:
                # 如果当前没有选中项，尝试恢复之前的选中状态
                if not self.keyword_listbox.curselection() and hasattr(self, "current_keyword"):
                    # 查找当前关键词的索引
                    for i in range(self.keyword_listbox.size()):
                        if self.keyword_listbox.get(i) == self.current_keyword:
                            self.keyword_listbox.selection_set(i)
                            break
            return True  # 继续处理事件

        # 绑定各种可能导致失去焦点的事件
        self.response_text.bind("<FocusIn>", preserve_keyword_selection)
        self.response_text.bind("<Button-1>", preserve_keyword_selection)
        self.response_text.bind("<ButtonRelease-1>", preserve_keyword_selection)
        self.response_text.bind("<Control-a>", preserve_keyword_selection)  # 全选

        # 创建变量按钮区
        variables_label_frame = ttk.LabelFrame(response_frame, text="变量插入")
        variables_label_frame.pack(fill=tk.X, padx=5, pady=5)

        # 添加变量说明
        var_tip_frame = ttk.Frame(variables_label_frame)
        var_tip_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
        ttk.Label(var_tip_frame, text="点击变量按钮将在光标处插入对应变量代码", foreground=self.accent_color).pack(side=tk.LEFT)

        # 创建变量按钮面板
        self.create_variables_panel(variables_label_frame, self.response_text)

        # 创建右键菜单
        self.keyword_context_menu = tk.Menu(self.keyword_listbox, tearoff=0)
        self.keyword_context_menu.add_command(label="删除关键词", command=self.delete_keyword)

        # 创建添加关键词按钮
        add_keyword_frame = ttk.Frame(keyword_frame)
        add_keyword_frame.pack(fill=tk.X, padx=5, pady=5)
        self.add_keyword_button = ttk.Button(add_keyword_frame, text="添加关键词", command=self.add_keyword_inline, style="Accent.TButton")
        self.add_keyword_button.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 创建弹幕区域
        danmaku_frame = ttk.LabelFrame(content_frame, text="弹幕区")
        danmaku_frame.grid(row=1, column=0, columnspan=2, sticky="nsew", pady=(5, 0))

        # 创建弹幕显示区
        self.danmaku_display = scrolledtext.ScrolledText(danmaku_frame, wrap=tk.WORD, height=4, state="disabled")
        self.danmaku_display.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))

        # 创建弹幕输入区
        input_frame = ttk.Frame(danmaku_frame)
        input_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(input_frame, text="发送弹幕:").pack(side=tk.LEFT, padx=(0, 5))
        self.danmaku_input = ttk.Entry(input_frame)
        self.danmaku_input.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        self.danmaku_input.bind("<Return>", lambda _: self.send_danmaku())

        self.send_button = ttk.Button(input_frame, text="发送", command=self.send_danmaku, style="Primary.TButton")
        self.send_button.pack(side=tk.LEFT)

        # 创建WebSocket连接区域
        ws_frame = ttk.Frame(danmaku_frame)
        ws_frame.pack(fill=tk.X, padx=5, pady=(5, 0))

        ttk.Label(ws_frame, text="WebSocket弹幕连接:").pack(side=tk.LEFT, padx=(0, 5))

        # 创建连接状态显示
        self.ws_status_var = tk.StringVar(value="未连接")
        ttk.Label(ws_frame, textvariable=self.ws_status_var, foreground="red").pack(side=tk.LEFT, padx=(0, 10))

        # 创建连接按钮
        self.ws_connect_button = ttk.Button(ws_frame, text="连接弹幕", command=self.connect_to_danmaku, style="Primary.TButton")
        self.ws_connect_button.pack(side=tk.LEFT, padx=5)

        # 创建断开按钮
        self.ws_disconnect_button = ttk.Button(ws_frame, text="断开连接", command=self.disconnect_from_danmaku)
        self.ws_disconnect_button.pack(side=tk.LEFT, padx=5)
        self.ws_disconnect_button.config(state="disabled")  # 初始状态为禁用

        # 添加测试模式切换按钮
        self.use_test_server = False
        self.test_mode_var = tk.BooleanVar(value=self.use_test_server)
        self.test_mode_check = ttk.Checkbutton(ws_frame, text="测试模式", variable=self.test_mode_var, command=self.toggle_test_mode)
        self.test_mode_check.pack(side=tk.LEFT, padx=5)




    def process_ui_queue(self):
        """处理UI消息队列中的消息"""
        try:
            # 尝试从队列中获取消息，不阻塞
            while not self.ui_queue.empty():
                message = self.ui_queue.get_nowait()
                if message:
                    # 根据消息类型调用相应的处理函数
                    msg_type = message.get('type')
                    data = message.get('data')

                    if msg_type == 'speakers_response':
                        self.process_speakers_response(data)
                    elif msg_type == 'speakers_error':
                        self.handle_speakers_error(data)
                    elif msg_type == 'scripts_response':
                        self.process_scripts_response(data)
                    elif msg_type == 'scripts_error':
                        self.handle_scripts_error(data)
                    elif msg_type == 'dialogues_response':
                        # 确保处理对话列表响应的方法存在
                        if hasattr(self, 'process_dialogues_response'):
                            self.process_dialogues_response(data)
                        else:
                            print("警告: process_dialogues_response方法不存在")
                    elif msg_type == 'dialogues_error':
                        # 确保处理对话列表错误的方法存在
                        if hasattr(self, 'handle_dialogues_error'):
                            self.handle_dialogues_error(data)
                        else:
                            print(f"警告: handle_dialogues_error方法不存在，错误信息: {data}")
                    elif msg_type == 'log':
                        # 使用try-except包装日志实现方法调用
                        try:
                            self._log_impl(data)
                        except Exception as e:
                            print(f"处理日志消息出错: {str(e)}")
                            print(f"原始日志消息: {data}")
                    elif msg_type == 'voice_download_response':
                        self.process_voice_download_response(data)
                    elif msg_type == 'voice_download_error':
                        self.handle_voice_download_error(data)
                    elif msg_type == 'download_next':
                        # 检查是否有待下载的语音
                        if hasattr(self, "voice_queue") and self.voice_queue:
                            # 检查第一个语音是否已经在下载中
                            if not self.voice_queue[0].get("downloading", False):
                                self.log("处理下一个语音下载请求")
                                self.download_next_voice()
                            else:
                                self.log("当前语音正在下载中，跳过下载请求")
                    elif msg_type == 'update_progress':
                        # 更新进度消息在下载更新函数中处理
                        pass
                    elif msg_type == 'update_status':
                        # 更新状态消息在下载更新函数中处理
                        pass
                    elif msg_type == 'update_complete':
                        # 更新完成消息在下载更新函数中处理
                        pass
                    elif msg_type == 'update_error':
                        # 更新错误消息在下载更新函数中处理
                        pass
                    else:
                        # 使用print而不是self.log避免递归
                        print(f"未知消息类型: {msg_type}")

                # 标记消息已处理
                self.ui_queue.task_done()
        except Exception as e:
            # 使用print而不是self.log避免递归
            print(f"处理UI消息队列出错: {str(e)}")
            import traceback
            print(traceback.format_exc())
        finally:
            # 无论如何，都要继续定时处理队列
            self.root.after(100, self.process_ui_queue)

    def on_window_resize(self, event):
        """窗口大小变化时的处理函数"""
        # 只处理来自主窗口的事件
        if event.widget == self.root:
            # 调整播放列表和OBS控制界面的大小
            self.adjust_playlist_height(event)
            self.adjust_obs_control_height(event)

    def adjust_playlist_height(self, event):
        """根据窗口大小动态调整播放列表高度"""
        if hasattr(self, 'playlist_listbox'):
            # 获取窗口当前高度和宽度
            window_height = self.root.winfo_height()
            window_width = self.root.winfo_width()

            # 根据窗口大小动态调整列表框高度
            # 窗口较小时使用较小的高度
            if window_height < 750:
                self.playlist_listbox.config(height=4)  # 更小的高度
            # 窗口中等大小时使用中等高度
            elif window_height < 850:
                self.playlist_listbox.config(height=6)  # 中等高度
            # 窗口较大时使用较大的高度
            else:
                self.playlist_listbox.config(height=8)  # 较大高度

    def adjust_obs_control_height(self, event):
        """根据窗口大小动态调整OBS控制界面高度"""
        if hasattr(self, 'sub_video_treeview'):
            # 获取窗口当前高度和宽度
            window_height = self.root.winfo_height()
            window_width = self.root.winfo_width()

            # 根据窗口高度动态调整表格高度
            # 窗口较小时使用较小的高度
            if window_height < 750:
                self.sub_video_treeview.config(height=2)  # 更小的高度
            # 窗口中等大小时使用中等高度
            elif window_height < 850:
                self.sub_video_treeview.config(height=3)  # 中等高度
            # 窗口较大时使用较大的高度
            else:
                self.sub_video_treeview.config(height=4)  # 较大高度

            # 根据窗口宽度调整列宽度
            if window_width < 1200:
                self.sub_video_treeview.column("关键词", width=80)
                self.sub_video_treeview.column("媒体源", width=100)
                self.sub_video_treeview.column("播放时间", width=50)
            else:
                self.sub_video_treeview.column("关键词", width=90)
                self.sub_video_treeview.column("媒体源", width=120)
                self.sub_video_treeview.column("播放时间", width=60)

    def create_video_match_system(self):
        """创建视频语音匹配系统"""
        self.log("开始创建视频语音匹配系统...")

        # 确保视频语音匹配标签页存在
        if not self.ensure_video_match_tab_exists():
            self.log("无法创建视频语音匹配系统标签页，无法继续", "error")
            return False

        # 清空当前标签页内容
        for widget in self.video_match_tab.winfo_children():
            widget.destroy()

        try:
            # 检查video_match_system.py文件是否存在
            import os
            if not os.path.exists("video_match_system.py"):
                self.log("错误: video_match_system.py 文件不存在", "error")

                # 创建一个简单的提示界面
                main_frame = ttk.Frame(self.video_match_tab)
                main_frame.pack(fill="both", expand=True, padx=20, pady=20)

                ttk.Label(main_frame, text="视频语音匹配系统", font=("Microsoft YaHei UI", 16, "bold")).pack(pady=10)
                ttk.Label(main_frame, text="错误: video_match_system.py 文件不存在", foreground="red").pack(pady=10)
                ttk.Label(main_frame, text="请确保该文件存在于程序目录中").pack(pady=5)

                return False

            # 尝试导入并创建视频语音匹配系统
            import importlib
            import sys

            # 确保重新加载模块
            if "video_match_system" in sys.modules:
                try:
                    importlib.reload(sys.modules["video_match_system"])
                    self.log("已重新加载 video_match_system 模块")
                except Exception as e:
                    self.log(f"重新加载 video_match_system 模块失败: {str(e)}", "error")
                    # 尝试移除模块并重新导入
                    del sys.modules["video_match_system"]
                    self.log("已移除 video_match_system 模块，将尝试重新导入")

            # 导入模块
            try:
                from video_match_system import VideoMatchSystem
                self.video_match_system = VideoMatchSystem(self)
                self.log("视频语音匹配系统已初始化成功", "success")

                # 确保标签页可见
                tab_names = [self.notebook.tab(i, "text") for i in range(self.notebook.index("end"))]
                tab_index = tab_names.index("视频语音匹配系统") if "视频语音匹配系统" in tab_names else -1
                if tab_index >= 0:
                    self.notebook.select(tab_index)

                return True
            except Exception as e:
                self.log(f"导入 VideoMatchSystem 类失败: {str(e)}", "error")
                import traceback
                self.log(traceback.format_exc(), "error")

                # 创建一个简单的错误提示界面
                main_frame = ttk.Frame(self.video_match_tab)
                main_frame.pack(fill="both", expand=True, padx=20, pady=20)

                ttk.Label(main_frame, text="视频语音匹配系统", font=("Microsoft YaHei UI", 16, "bold")).pack(pady=10)
                ttk.Label(main_frame, text=f"错误: {str(e)}", foreground="red").pack(pady=10)
                ttk.Label(main_frame, text="无法初始化视频语音匹配系统").pack(pady=5)

                return False
        except Exception as e:
            self.log(f"初始化视频语音匹配系统失败: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")

            # 创建一个简单的界面，不依赖于视频语音匹配系统模块
            main_frame = ttk.Frame(self.video_match_tab)
            main_frame.pack(fill="both", expand=True, padx=20, pady=20)

            # 创建标题
            ttk.Label(main_frame, text="视频语音匹配系统", font=("Microsoft YaHei UI", 16, "bold")).pack(pady=10)

            # 创建说明文本
            description = """
            视频语音匹配系统可以根据视频时间段自动生成语音，支持以下功能：

            1. 主视频时间段话术：根据视频播放时间自动触发语音
            2. 副视频触发：根据关键词触发副视频播放
            3. 报时功能：定时播报当前时间
            4. OBS控制：自动控制OBS视频源切换

            使用此功能需要先连接OBS，然后设置主视频和副视频。
            """
            desc_text = scrolledtext.ScrolledText(main_frame, height=10, wrap="word")
            desc_text.pack(fill="both", expand=True, padx=10, pady=10)
            desc_text.insert("1.0", description)
            desc_text.config(state="disabled")

            # 创建错误信息区域
            error_frame = ttk.LabelFrame(main_frame, text="错误信息")
            error_frame.pack(fill="x", padx=10, pady=10)

            error_text = scrolledtext.ScrolledText(error_frame, height=5, wrap="word")
            error_text.pack(fill="both", expand=True, padx=5, pady=5)
            error_text.insert("1.0", f"初始化视频语音匹配系统失败，请检查以下问题：\n\n1. video_match_system.py 文件是否存在\n2. 文件中是否包含 VideoMatchSystem 类\n3. 查看日志获取详细错误信息\n\n错误详情: {str(e)}")
            error_text.config(state="disabled")

            # 创建按钮区域
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill="x", pady=10)

            # 创建重试按钮
            ttk.Button(button_frame, text="重试加载",
                      command=self.reload_video_match_system,
                      style="Primary.TButton").pack(side="left", padx=10)

            # 创建帮助按钮
            ttk.Button(button_frame, text="查看帮助",
                      command=lambda: messagebox.showinfo("视频语音匹配系统帮助", description),
                      style="Primary.TButton").pack(side="left", padx=10)

            self.log("已创建视频语音匹配系统基本界面")

    def ensure_video_match_tab_exists(self):
        """确保视频语音匹配系统标签页存在"""
        self.log("确保视频语音匹配系统标签页存在...", "system")

        # 确保视频语音匹配标签页存在
        if not hasattr(self, 'video_match_tab') or self.video_match_tab is None:
            self.log("video_match_tab 不存在，正在创建...", "warning")
            # 创建视频语音匹配标签页
            self.video_match_tab = ttk.Frame(self.notebook, style="TFrame")
            self.log("已创建视频语音匹配标签页")

        # 检查标签页是否已添加到notebook
        tab_names = [self.notebook.tab(i, "text") for i in range(self.notebook.index("end"))]
        if "视频语音匹配系统" not in tab_names:
            self.log("视频语音匹配系统标签页未添加到notebook，正在添加...", "warning")
            try:
                self.notebook.add(self.video_match_tab, text="视频语音匹配系统")
                self.log("已将视频语音匹配系统标签页添加到notebook", "success")
            except Exception as e:
                self.log(f"添加视频语音匹配系统标签页失败: {str(e)}", "error")
                import traceback
                self.log(traceback.format_exc(), "error")

                # 尝试重新创建标签页
                self.log("尝试重新创建视频语音匹配系统标签页...", "warning")
                self.video_match_tab = ttk.Frame(self.notebook, style="TFrame")
                try:
                    self.notebook.add(self.video_match_tab, text="视频语音匹配系统")
                    self.log("已重新创建并添加视频语音匹配系统标签页", "success")
                except Exception as e2:
                    self.log(f"重新创建标签页也失败: {str(e2)}", "error")
                    self.log("将在下一次应用程序启动时重试", "warning")

        # 返回标签页是否存在
        tab_names = [self.notebook.tab(i, "text") for i in range(self.notebook.index("end"))]
        return "视频语音匹配系统" in tab_names

    def reload_video_match_system(self):
        """重新加载视频语音匹配系统"""
        self.log("正在重新加载视频语音匹配系统...")

        # 确保视频语音匹配标签页存在
        if not self.ensure_video_match_tab_exists():
            self.log("无法创建视频语音匹配系统标签页，无法重新加载", "error")
            return False

        # 清空当前标签页内容
        for widget in self.video_match_tab.winfo_children():
            widget.destroy()

        # 重新创建视频语音匹配系统
        self.create_video_match_system()
        return True

    def play_voice(self, voice_file, volume=1.0, callback=None):
        """播放语音文件

        Args:
            voice_file: 语音文件路径
            volume: 音量，范围0.0-1.0
            callback: 播放完成后的回调函数
        """
        self.log(f"播放语音: {voice_file}")

        try:
            # 检查文件是否存在
            if not os.path.exists(voice_file):
                self.log(f"语音文件不存在: {voice_file}", "error")
                if callback:
                    callback()
                return False

            # 检查文件大小
            file_size = os.path.getsize(voice_file)
            if file_size < 1000:  # 小于1KB的文件可能不是有效的MP3
                self.log(f"警告: 文件过小 ({file_size} 字节)，可能不是有效的MP3文件", "warning")
                # 尝试删除无效文件
                try:
                    os.remove(voice_file)
                    self.log(f"已删除无效的语音文件: {voice_file}", "warning")
                except:
                    pass
                if callback:
                    callback()
                return False

            # 使用pygame播放语音
            import pygame

            # 初始化pygame混音器
            if not pygame.mixer.get_init():
                # 使用我们的初始化方法
                if not self.initialize_pygame():
                    self.log("初始化pygame混音器失败，无法播放语音", "error")
                    if callback:
                        callback()
                    return False

            # 设置音量
            pygame.mixer.music.set_volume(volume)

            try:
                # 尝试加载语音文件
                pygame.mixer.music.load(voice_file)
            except pygame.error as e:
                self.log(f"加载语音文件失败: {str(e)}", "error")
                # 尝试删除损坏的文件
                try:
                    os.remove(voice_file)
                    self.log(f"已删除损坏的语音文件: {voice_file}", "warning")
                except:
                    pass
                if callback:
                    callback()
                return False

            # 播放语音
            pygame.mixer.music.play()

            # 设置播放完成回调
            if callback:
                try:
                    # 获取音频长度
                    sound = pygame.mixer.Sound(voice_file)
                    duration = sound.get_length() * 1000  # 转换为毫秒

                    # 检查持续时间是否合理
                    if duration < 100 or duration > 60000:  # 小于0.1秒或大于60秒
                        self.log(f"警告: 音频持续时间异常 ({duration/1000:.2f}秒)", "warning")
                        # 使用默认持续时间
                        duration = 3000  # 默认3秒

                    # 设置定时器，在播放完成后调用回调函数
                    self.root.after(int(duration) + 100, callback)
                except Exception as e:
                    self.log(f"获取音频长度失败: {str(e)}", "warning")
                    # 使用默认持续时间
                    self.root.after(3000, callback)  # 默认3秒

            return True
        except Exception as e:
            self.log(f"播放语音失败: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")

            if callback:
                callback()

            return False

    def pause_voice(self):
        """暂停当前播放的语音"""
        self.log("暂停语音播放")

        try:
            import pygame

            # 检查pygame混音器是否已初始化
            if not pygame.mixer.get_init():
                # 使用我们的初始化方法
                if not self.initialize_pygame():
                    self.log("初始化pygame混音器失败，无法暂停语音", "error")
                    return False

            # 检查是否有音乐在播放
            if pygame.mixer.music.get_busy():
                pygame.mixer.music.pause()
                self.log("语音播放已暂停")
                return True
            else:
                self.log("没有正在播放的语音")
                return False
        except Exception as e:
            self.log(f"暂停语音失败: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")
            return False

    def resume_voice(self):
        """恢复暂停的语音播放"""
        self.log("恢复语音播放")

        try:
            import pygame

            # 检查pygame混音器是否已初始化
            if not pygame.mixer.get_init():
                # 使用我们的初始化方法
                if not self.initialize_pygame():
                    self.log("初始化pygame混音器失败，无法恢复语音", "error")
                    return False

            pygame.mixer.music.unpause()
            self.log("语音播放已恢复")
            return True
        except Exception as e:
            self.log(f"恢复语音播放失败: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")
            return False

    def stop_voice(self):
        """停止当前播放的语音"""
        self.log("停止语音播放")

        try:
            import pygame

            # 检查pygame混音器是否已初始化
            if not pygame.mixer.get_init():
                # 使用我们的初始化方法
                if not self.initialize_pygame():
                    self.log("初始化pygame混音器失败，无法停止语音", "error")
                    return False

            pygame.mixer.music.stop()
            self.log("语音播放已停止")
            return True
        except Exception as e:
            self.log(f"停止语音播放失败: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")
            return False

    def on_dialogue_selected(self, event=None):
        """对话选择事件处理函数"""
        self.log("对话选择事件")

        # 获取当前选中的对话
        selected_dialogue = self.dialogue_var.get()
        if not selected_dialogue:
            self.log("未选择对话")
            return

        # 如果当前有对话数据，先保存当前编辑的回复内容
        if hasattr(self, "dialogue_data") and self.dialogue_data and hasattr(self, "current_keyword") and self.current_keyword:
            if self.save_current_response():
                self.log(f"已保存当前关键词的回复内容")

        # 加载选中对话的关键词
        self.log(f"加载对话 '{selected_dialogue}' 的关键词")

        # 清空当前内容
        self.keyword_listbox.delete(0, tk.END)
        self.response_text.delete(1.0, tk.END)

        # 清空当前数据
        self.dialogue_data = []

        # 保存当前对话名称
        self.current_dialogue = selected_dialogue

        try:
            import requests
            import json

            # 发送请求获取对话内容
            payload = {
                "类型": "获取ai对话",
                "对话名": selected_dialogue
            }

            self.log(f"API请求: POST http://localhost:12456/ 数据: {json.dumps(payload, ensure_ascii=False)}")
            response = requests.post("http://localhost:12456/", json=payload, timeout=10)
            self.log(f"API响应状态码: {response.status_code}")

            # 打印原始响应内容以便调试
            response_text = response.text
            self.log(f"API原始响应: {response_text[:200]}...")

            if response.status_code == 200:
                # 尝试解析JSON响应
                try:
                    data = response.json()
                    self.log(f"AI对话内容数据: {json.dumps(data, ensure_ascii=False)[:200]}...")
                except json.JSONDecodeError as e:
                    self.log(f"JSON解析错误: {str(e)}")
                    self.log(f"原始响应内容: {response_text}")
                    return

                # 处理不同格式的响应数据
                processed_data = self.process_dialogue_data(data)

                if processed_data:
                    # 更新对话数据
                    self.dialogue_data = processed_data

                    # 添加关键词到列表
                    for item in self.dialogue_data:
                        if "gjc" in item:
                            self.keyword_listbox.insert(tk.END, item["gjc"])

                    # 如果有关键词，默认选择第一个
                    if self.dialogue_data:
                        self.keyword_listbox.selection_set(0)
                        self.on_keyword_selected(None)

                    self.log(f"成功加载对话 '{selected_dialogue}' 的 {len(self.dialogue_data)} 个关键词")

                    # 打印对话数据，用于调试
                    self.log("对话数据详情:")
                    for i, item in enumerate(self.dialogue_data):
                        keyword = item.get("gjc", "未知")
                        response = item.get("aidf", "")
                        self.log(f"索引 {i}: 关键词: '{keyword}', 回复内容长度: {len(response)}")
                else:
                    self.log(f"对话 '{selected_dialogue}' 没有有效的关键词数据")
                    self.response_text.insert(tk.END, "该对话内容为空")
            else:
                self.log(f"获取对话内容失败: HTTP {response.status_code}")
                self.response_text.insert(tk.END, f"获取对话内容失败: HTTP {response.status_code}")
        except Exception as e:
            self.log(f"获取对话内容出错: {str(e)}")
            self.response_text.insert(tk.END, f"获取对话内容出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc(), "error")

    def save_current_response(self):
        """保存当前编辑的回复内容"""
        # 获取当前选中的关键词
        selected_indices = self.keyword_listbox.curselection()
        if not selected_indices:
            self.log("未选择关键词，无需保存")
            return True

        selected_index = selected_indices[0]
        selected_keyword = self.keyword_listbox.get(selected_index)

        # 获取当前编辑的回复内容
        response_content = self.response_text.get("1.0", tk.END).strip()

        # 如果回复内容为空，提示用户
        if not response_content:
            self.log(f"关键词 '{selected_keyword}' 的回复内容为空，跳过保存")
            return True

        # 查找对应的对话数据项
        found_item = None
        for item in self.dialogue_data:
            # 检查旧API格式 (gjc/aidf)
            if item.get("gjc") == selected_keyword:
                found_item = item
                break
            # 检查新API格式 (keyword/response)
            elif item.get("keyword") == selected_keyword:
                found_item = item
                break

        if found_item:
            # 更新回复内容（统一使用旧API格式）
            if "gjc" in found_item:
                found_item["aidf"] = response_content
            else:  # 如果是新API格式，转换为旧API格式
                found_item["gjc"] = found_item.get("keyword", selected_keyword)
                found_item["aidf"] = response_content
                # 删除新API格式的字段
                if "keyword" in found_item:
                    del found_item["keyword"]
                if "response" in found_item:
                    del found_item["response"]

            # 保存回复内容
            try:
                import requests
                import json

                # 使用单个关键词的保存方式，只保存当前修改的关键词
                url = "http://localhost:12456/"
                payload = {
                    "类型": "上传ai对话",
                    "对话名": self.current_dialogue,
                    "gjc": selected_keyword,
                    "aidf": response_content
                }

                self.log(f"保存关键词 '{selected_keyword}' 的回复内容，请求数据: {json.dumps(payload, ensure_ascii=False)}")
                response = requests.post(url, json=payload, timeout=10)

                # 打印响应内容
                self.log(f"保存响应状态码: {response.status_code}")
                try:
                    response_json = response.json()
                    self.log(f"保存响应内容: {json.dumps(response_json, ensure_ascii=False)}")
                except:
                    self.log(f"保存响应内容: {response.text[:200]}...")

                # 即使返回400，实际上可能已经保存成功，所以我们总是返回True
                if response.status_code == 200:
                    self.log(f"成功保存关键词 '{selected_keyword}' 的回复内容")
                else:
                    self.log(f"保存回复内容返回状态码: HTTP {response.status_code}，但可能已经保存成功")

                return True
            except Exception as e:
                self.log(f"保存回复内容异常: {str(e)}", "error")
                import traceback
                self.log(traceback.format_exc(), "error")
                # 即使发生异常，我们也返回True，因为本地数据已经更新
                return True
        else:
            # 如果没有找到对应的对话数据项，创建一个新的
            new_item = {"gjc": selected_keyword, "aidf": response_content}
            self.dialogue_data.append(new_item)
            self.log(f"创建新的关键词 '{selected_keyword}' 的回复内容")

            # 尝试保存到服务器
            try:
                import requests
                import json

                url = "http://localhost:12456/"
                payload = {
                    "类型": "上传ai对话",
                    "对话名": self.current_dialogue,
                    "gjc": selected_keyword,
                    "aidf": response_content
                }

                self.log(f"保存新创建的关键词 '{selected_keyword}' 的回复内容，请求数据: {json.dumps(payload, ensure_ascii=False)}")
                response = requests.post(url, json=payload, timeout=10)

                # 打印响应内容
                self.log(f"保存响应状态码: {response.status_code}")
                try:
                    response_json = response.json()
                    self.log(f"保存响应内容: {json.dumps(response_json, ensure_ascii=False)}")
                except:
                    self.log(f"保存响应内容: {response.text[:200]}...")

                # 即使返回400，实际上可能已经保存成功，所以我们总是返回True
                if response.status_code == 200:
                    self.log(f"成功保存新创建的关键词 '{selected_keyword}' 的回复内容")
                else:
                    self.log(f"保存新创建的关键词返回状态码: HTTP {response.status_code}，但可能已经保存成功")
            except Exception as e:
                self.log(f"保存新创建的关键词异常: {str(e)}", "error")
                import traceback
                self.log(traceback.format_exc(), "error")

            # 无论如何，本地数据已经更新
            return True

    def on_keyword_selected(self, event=None):
        """当选择关键词时触发"""
        selected_indices = self.keyword_listbox.curselection()
        if selected_indices:
            selected_index = selected_indices[0]
            if selected_index >= 0 and selected_index < len(self.dialogue_data):
                selected_item = self.dialogue_data[selected_index]

                # 记录选中的关键词
                keyword = selected_item.get("gjc", "未命名关键词")
                self.current_keyword = keyword
                self.log(f"选中关键词: '{keyword}'")

                # 显示回复内容
                self.response_text.delete(1.0, tk.END)
                if "aidf" in selected_item:
                    response_content = selected_item["aidf"]

                    # 检查是否包含副视频触发格式
                    if "[#" in response_content and "#]" in response_content:
                        self.log(f"关键词 '{keyword}' 的回复内容包含副视频触发格式: {response_content[:100]}...")

                    # 显示原始回复内容，不做任何处理
                    self.response_text.insert(tk.END, response_content)

    def show_keyword_context_menu(self, event):
        """显示关键词右键菜单"""
        # 获取鼠标点击位置的项目
        try:
            index = self.keyword_listbox.nearest(event.y)
            # 确保点击的是有效项目
            if index >= 0:
                # 选中该项目
                self.keyword_listbox.selection_clear(0, tk.END)
                self.keyword_listbox.selection_set(index)
                self.keyword_listbox.activate(index)
                # 显示右键菜单
                self.keyword_context_menu.post(event.x_root, event.y_root)
        except Exception as e:
            self.log(f"显示右键菜单失败: {str(e)}", "error")

    def delete_keyword(self):
        """删除选中的关键词"""
        # 获取当前选中的关键词
        selected_indices = self.keyword_listbox.curselection()
        if not selected_indices:
            self.log("未选择关键词，无法删除")
            return

        selected_index = selected_indices[0]
        selected_keyword = self.keyword_listbox.get(selected_index)

        # 确认删除
        if not messagebox.askyesno("确认删除", f"确定要删除关键词 '{selected_keyword}' 吗？"):
            return

        # 尝试使用新API删除关键词
        try:
            import requests
            import json

            # 使用本地服务器
            url = "http://localhost:12456/dialogue/delete_keyword"
            payload = {
                "dialogue_name": self.current_dialogue,
                "username": self.username,
                "machine_code": self.machine_code,
                "keyword": selected_keyword
            }

            # 发送请求
            self.log(f"尝试使用新API删除关键词: {selected_keyword}")

            try:
                response = requests.post(url, json=payload, timeout=10)
                if response.status_code == 200:
                    # 解析响应
                    result = response.json()

                    if result.get("状态") == "成功":
                        # 从列表中删除关键词
                        self.keyword_listbox.delete(selected_index)

                        # 从对话数据中删除关键词（检查两种格式）
                        for i, item in enumerate(self.dialogue_data):
                            if item.get("keyword") == selected_keyword or item.get("gjc") == selected_keyword:
                                del self.dialogue_data[i]
                                break

                        # 清空回复内容
                        self.response_text.delete("1.0", tk.END)

                        # 清除当前关键词
                        if hasattr(self, "current_keyword") and self.current_keyword == selected_keyword:
                            self.current_keyword = None

                        self.log(f"成功使用新API删除关键词: {selected_keyword}")
                        return
                    else:
                        error_msg = result.get("信息", "未知错误")
                        self.log(f"使用新API删除关键词失败: {error_msg}，尝试使用旧API", "warning")
                else:
                    self.log(f"使用新API删除关键词失败: HTTP {response.status_code}，尝试使用旧API", "warning")
            except Exception as e:
                self.log(f"使用新API删除关键词异常: {str(e)}，尝试使用旧API", "warning")

            # 如果新API失败，尝试使用旧API
            try:
                # 使用旧API删除
                url = "http://localhost:12456/"
                payload = {
                    "类型": "删除ai对话关键词",
                    "对话名": self.current_dialogue,
                    "gjc": selected_keyword
                }

                self.log(f"尝试使用旧API删除关键词: {selected_keyword}")

                response = requests.post(url, json=payload, timeout=10)

                if response.status_code == 200:
                    # 从列表中删除关键词
                    self.keyword_listbox.delete(selected_index)

                    # 从对话数据中删除关键词（检查两种格式）
                    for i, item in enumerate(self.dialogue_data):
                        if item.get("keyword") == selected_keyword or item.get("gjc") == selected_keyword:
                            del self.dialogue_data[i]
                            break

                    # 清空回复内容
                    self.response_text.delete("1.0", tk.END)

                    # 清除当前关键词
                    if hasattr(self, "current_keyword") and self.current_keyword == selected_keyword:
                        self.current_keyword = None

                    self.log(f"成功使用旧API删除关键词: {selected_keyword}")
                else:
                    self.log(f"使用旧API删除关键词失败: HTTP {response.status_code}", "error")
            except Exception as e:
                self.log(f"使用旧API删除关键词异常: {str(e)}", "error")
                import traceback
                self.log(traceback.format_exc(), "error")
        except Exception as e:
            self.log(f"删除关键词异常: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")

    def add_keyword_inline(self):
        """添加新关键词"""
        # 检查是否已选择对话
        if not hasattr(self, "current_dialogue") or not self.current_dialogue:
            self.log("未选择对话，无法添加关键词", "error")
            messagebox.showerror("错误", "请先选择一个对话")
            return

        # 弹出输入对话框
        keyword = simpledialog.askstring("添加关键词", "请输入新的关键词:")
        if not keyword:
            return

        # 检查关键词是否已存在
        for i in range(self.keyword_listbox.size()):
            if self.keyword_listbox.get(i) == keyword:
                self.log(f"关键词 '{keyword}' 已存在", "error")
                messagebox.showerror("错误", f"关键词 '{keyword}' 已存在")
                return

        # 尝试使用新API添加关键词
        try:
            import requests
            import json

            # 使用本地服务器
            url = "http://localhost:12456/dialogue/add_keyword"
            payload = {
                "dialogue_name": self.current_dialogue,
                "username": self.username,
                "machine_code": self.machine_code,
                "keyword": keyword,
                "response": ""
            }

            # 发送请求
            self.log(f"尝试使用新API添加关键词: {keyword}")

            try:
                response = requests.post(url, json=payload, timeout=10)
                if response.status_code == 200:
                    # 解析响应
                    result = response.json()

                    if result.get("状态") == "成功":
                        # 添加关键词到列表
                        self.keyword_listbox.insert(tk.END, keyword)

                        # 添加关键词到对话数据（使用新API格式）
                        self.dialogue_data.append({
                            "keyword": keyword,
                            "response": ""
                        })

                        # 选中新添加的关键词
                        index = self.keyword_listbox.size() - 1
                        self.keyword_listbox.selection_clear(0, tk.END)
                        self.keyword_listbox.selection_set(index)
                        self.keyword_listbox.see(index)

                        # 清空回复内容
                        self.response_text.delete("1.0", tk.END)

                        # 设置当前关键词
                        self.current_keyword = keyword

                        self.log(f"成功使用新API添加关键词: {keyword}")
                        return
                    else:
                        error_msg = result.get("信息", "未知错误")
                        self.log(f"使用新API添加关键词失败: {error_msg}，尝试使用旧API", "warning")
                else:
                    self.log(f"使用新API添加关键词失败: HTTP {response.status_code}，尝试使用旧API", "warning")
            except Exception as e:
                self.log(f"使用新API添加关键词异常: {str(e)}，尝试使用旧API", "warning")

            # 如果新API失败，尝试使用旧API
            try:
                # 使用旧API添加
                url = "http://localhost:12456/"
                payload = {
                    "类型": "添加ai对话关键词",
                    "对话名": self.current_dialogue,
                    "gjc": keyword,
                    "aidf": ""
                }

                self.log(f"尝试使用旧API添加关键词: {keyword}")

                response = requests.post(url, json=payload, timeout=10)

                if response.status_code == 200:
                    # 添加关键词到列表
                    self.keyword_listbox.insert(tk.END, keyword)

                    # 添加关键词到对话数据（使用旧API格式）
                    self.dialogue_data.append({
                        "gjc": keyword,
                        "aidf": ""
                    })

                    # 选中新添加的关键词
                    index = self.keyword_listbox.size() - 1
                    self.keyword_listbox.selection_clear(0, tk.END)
                    self.keyword_listbox.selection_set(index)
                    self.keyword_listbox.see(index)

                    # 清空回复内容
                    self.response_text.delete("1.0", tk.END)

                    # 设置当前关键词
                    self.current_keyword = keyword

                    self.log(f"成功使用旧API添加关键词: {keyword}")
                else:
                    self.log(f"使用旧API添加关键词失败: HTTP {response.status_code}", "error")
            except Exception as e:
                self.log(f"使用旧API添加关键词异常: {str(e)}", "error")
                import traceback
                self.log(traceback.format_exc(), "error")
        except Exception as e:
            self.log(f"添加关键词异常: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")

    def get_dialogue_content(self, dialogue_name):
        """获取对话内容"""
        try:
            import requests
            import json

            # 清空当前内容
            self.keyword_listbox.delete(0, tk.END)
            self.response_text.delete(1.0, tk.END)

            # 清空当前数据
            self.dialogue_data = []

            # 保存当前对话名称
            self.current_dialogue = dialogue_name

            # 发送请求获取对话内容
            payload = {
                "类型": "获取ai对话",
                "对话名": dialogue_name
            }

            self.log(f"API请求: POST http://localhost:12456/ 数据: {payload}")
            response = requests.post("http://localhost:12456/", json=payload, timeout=10)
            self.log(f"API响应状态码: {response.status_code}")

            # 打印原始响应内容以便调试
            response_text = response.text
            self.log(f"API原始响应: {response_text[:200]}...")

            if response.status_code == 200:
                # 尝试解析JSON响应
                try:
                    data = response.json()
                    self.log(f"AI对话内容数据: {json.dumps(data, ensure_ascii=False)[:200]}...")
                except json.JSONDecodeError as e:
                    self.log(f"JSON解析错误: {str(e)}")
                    self.log(f"原始响应内容: {response_text}")
                    return []

                # 处理不同格式的响应数据
                processed_data = self.process_dialogue_data(data)

                if processed_data:
                    # 更新对话数据
                    self.dialogue_data = processed_data

                    # 添加关键词到列表
                    for item in self.dialogue_data:
                        if "gjc" in item:
                            self.keyword_listbox.insert(tk.END, item["gjc"])

                    # 如果有关键词，默认选择第一个
                    if self.dialogue_data:
                        self.keyword_listbox.selection_set(0)
                        self.on_keyword_selected(None)

                    self.log(f"成功加载对话 '{dialogue_name}' 的 {len(self.dialogue_data)} 个关键词")
                else:
                    self.log(f"对话 '{dialogue_name}' 没有有效的关键词数据")
                    self.response_text.insert(tk.END, "该对话内容为空")

                return self.dialogue_data
            else:
                self.log(f"获取对话内容失败: HTTP {response.status_code}")
                self.response_text.insert(tk.END, f"获取对话内容失败: HTTP {response.status_code}")
                return []
        except Exception as e:
            self.log(f"获取对话内容出错: {str(e)}")
            self.response_text.insert(tk.END, f"获取对话内容出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc(), "error")
            return []

    def process_dialogue_data(self, data):
        """处理对话数据，支持多种格式"""
        processed_data = []

        # 情况1: 包含data字段的对象
        if isinstance(data, dict) and "data" in data and isinstance(data["data"], list):
            self.log("检测到包含data字段的对象格式")
            processed_data = data["data"]

            # 检查并记录特殊格式
            for item in processed_data:
                if isinstance(item, dict) and "gjc" in item and "aidf" in item:
                    keyword = item["gjc"]
                    response_content = item["aidf"]
                    # 检查是否包含副视频触发格式
                    if "[#" in response_content and "#]" in response_content:
                        self.log(f"加载到包含副视频触发格式的关键词 '{keyword}': {response_content[:100]}...")

            return processed_data

        # 情况2: 直接返回列表
        if isinstance(data, list):
            self.log("检测到直接返回列表格式")
            for item in data:
                if isinstance(item, dict) and "gjc" in item:
                    # 确保aidf字段存在
                    if "aidf" not in item:
                        item["aidf"] = ""
                        self.log(f"警告: 关键词 '{item['gjc']}' 没有回复内容，已设置为空字符串")

                    processed_data.append(item)

            return processed_data

        # 情况3: 单个对话项
        if isinstance(data, dict) and "gjc" in data and "aidf" in data:
            self.log("检测到单个对话项格式")
            processed_data = [data]
            return processed_data

        # 情况4: 嵌套结构
        if isinstance(data, dict):
            self.log("检测到嵌套结构格式")
            # 创建一个字典，用于存储关键词和对应的回复内容
            keyword_response_map = {}

            for key, value in data.items():
                if isinstance(value, list) and len(value) > 0:
                    for item in value:
                        if isinstance(item, dict) and "gjc" in item:
                            keyword = item["gjc"]
                            # 确保aidf字段存在
                            if "aidf" not in item:
                                item["aidf"] = ""
                                self.log(f"警告: 关键词 '{keyword}' 没有回复内容，已设置为空字符串")

                            keyword_response_map[keyword] = item

            # 检查是否有直接的关键词字段
            if not keyword_response_map and "关键词" in data and isinstance(data["关键词"], list):
                for keyword in data["关键词"]:
                    if isinstance(keyword, str):
                        # 创建一个简单的对话项
                        keyword_response_map[keyword] = {"gjc": keyword, "aidf": ""}
                        self.log(f"从'关键词'字段提取: '{keyword}'")

            # 如果找到了关键词，构建对话数据
            if keyword_response_map:
                # 使用有序列表存储关键词
                keyword_list = list(keyword_response_map.keys())
                for keyword in keyword_list:
                    processed_data.append(keyword_response_map[keyword])

                return processed_data

        # 如果没有找到有效的对话内容
        self.log("未能识别的数据格式或没有有效的对话内容")
        return []

    def refresh_dialogues(self):
        """从服务器获取AI对话列表"""
        self.log("正在获取AI对话列表...")

        # 禁用刷新按钮，防止重复点击
        if hasattr(self, "dialogue_refresh_button"):
            self.dialogue_refresh_button.config(state="disabled")

        try:
            import requests
            import json

            # 直接在主线程中执行请求，避免线程问题
            self.log("API请求: GET http://localhost:12456/dialoguelist")
            response = requests.get("http://localhost:12456/dialoguelist", timeout=10)
            self.log(f"API响应状态码: {response.status_code}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    self.log(f"AI对话列表数据长度: {len(str(data))}字节")

                    # 清空当前列表
                    self.dialogue_list = []

                    # 解析返回的数据
                    if isinstance(data, dict) and "ai对话" in data and isinstance(data["ai对话"], list):
                        # 正确解析嵌套结构
                        for dialogue in data["ai对话"]:
                            if isinstance(dialogue, dict) and "name" in dialogue:
                                dialogue_name = dialogue["name"]
                                self.dialogue_list.append(dialogue_name)
                    elif isinstance(data, list):
                        # 兼容直接返回列表的情况
                        for dialogue in data:
                            if isinstance(dialogue, dict) and "name" in dialogue:
                                dialogue_name = dialogue["name"]
                                self.dialogue_list.append(dialogue_name)

                    # 更新下拉框
                    self.dialogue_combobox['values'] = self.dialogue_list

                    # 如果有对话，选择上次选择的或第一个
                    if self.dialogue_list:
                        # 尝试选择上次选择的对话
                        if hasattr(self, "last_dialogue_index") and 0 <= self.last_dialogue_index < len(self.dialogue_list):
                            self.dialogue_combobox.current(self.last_dialogue_index)
                        else:
                            self.dialogue_combobox.current(0)

                        # 触发选择事件
                        self.on_dialogue_selected(None)

                    self.log(f"成功获取到 {len(self.dialogue_list)} 个AI对话")
                except json.JSONDecodeError as e:
                    self.log(f"解析对话列表JSON数据失败: {str(e)}")
                    self.log(f"原始响应内容: {response.text[:200]}...")
            else:
                self.log(f"获取AI对话列表失败: HTTP {response.status_code}")
        except Exception as e:
            self.log(f"获取AI对话列表出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc(), "error")
        finally:
            # 启用刷新按钮
            if hasattr(self, "dialogue_refresh_button"):
                self.dialogue_refresh_button.config(state="normal")

    def process_dialogues_response(self, response):
        """处理AI对话列表响应"""
        try:
            if response.status_code == 200:
                data = response.json()

                # 清空当前列表
                self.dialogue_list = []

                # 解析返回的数据
                if isinstance(data, dict) and "ai对话" in data and isinstance(data["ai对话"], list):
                    # 正确解析嵌套结构
                    for dialogue in data["ai对话"]:
                        if isinstance(dialogue, dict) and "name" in dialogue:
                            # 保存完整的对话数据
                            self.dialogue_list.append(dialogue)
                elif isinstance(data, list):
                    # 兼容直接返回列表的情况
                    for dialogue in data:
                        if isinstance(dialogue, dict) and "name" in dialogue:
                            # 保存完整的对话数据
                            self.dialogue_list.append(dialogue)

                # 更新下拉框
                dialogue_names = [d["name"] for d in self.dialogue_list]
                self.dialogue_combobox['values'] = dialogue_names

                # 如果有对话，默认选择上次选择的对话
                if dialogue_names:
                    if hasattr(self, "last_dialogue_index") and self.last_dialogue_index < len(dialogue_names):
                        self.dialogue_combobox.current(self.last_dialogue_index)
                    else:
                        self.dialogue_combobox.current(0)

                    # 触发选择事件
                    self.on_dialogue_selected(None)

                self.log(f"成功获取到 {len(dialogue_names)} 个AI对话")
            else:
                self.log(f"获取对话列表失败: HTTP {response.status_code}")
        except Exception as e:
            self.log(f"处理对话列表响应出错: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")
        finally:
            # 启用刷新按钮
            if hasattr(self, "dialogue_refresh_button"):
                self.dialogue_refresh_button.config(state="normal")

    def handle_dialogues_error(self, error_msg):
        """处理AI对话列表请求错误"""
        self.log(f"获取对话列表出错: {error_msg}", "error")
        # 启用刷新按钮
        if hasattr(self, "dialogue_refresh_button"):
            self.dialogue_refresh_button.config(state="normal")

    def save_dialogue(self):
        """保存当前对话"""
        # 检查是否已选择对话
        if not hasattr(self, "current_dialogue") or not self.current_dialogue:
            self.log("未选择对话，无法保存", "error")
            messagebox.showerror("错误", "请先选择一个对话")
            return False

        # 保存当前编辑的回复内容
        if hasattr(self, "dialogue_data") and self.dialogue_data:
            if self.save_current_response():
                self.log(f"已保存当前关键词的回复内容")
                return True
            else:
                self.log(f"保存当前关键词的回复内容失败", "error")
                return False

        return True

    def save_settings_to_config(self):
        """保存设置到配置文件"""
        self.log("保存设置到配置文件")

        try:
            # 获取配置文件路径
            import os
            config_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "_internal")
            config_path = os.path.join(config_dir, "config.json")

            # 确保目录存在
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)

            # 读取现有配置
            import json
            config = {}
            if os.path.exists(config_path):
                try:
                    with open(config_path, "r", encoding="utf-8") as f:
                        config = json.load(f)
                except Exception as e:
                    self.log(f"读取配置文件失败: {str(e)}", "error")

            # 保存当前选择的项目
            if not "last_selected" in config:
                config["last_selected"] = {}

            # 保存话术选择
            if hasattr(self, "script_combobox") and self.script_combobox.current() >= 0:
                config["last_selected"]["script"] = self.script_combobox.current()

            # 保存对话选择
            if hasattr(self, "dialogue_combobox") and self.dialogue_combobox.current() >= 0:
                config["last_selected"]["dialogue"] = self.dialogue_combobox.current()

            # 保存发音人选择
            if hasattr(self, "speaker_combobox") and self.speaker_combobox.current() >= 0:
                config["last_selected"]["speaker"] = self.speaker_combobox.current()

            # 保存配置
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=4)

            self.log("设置已保存到配置文件")
            return True
        except Exception as e:
            self.log(f"保存设置到配置文件失败: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")
            return False

    def initial_data_load(self):
        """初始化数据加载"""
        self.log("开始初始化数据加载")

        try:
            # 创建缓存目录
            import os
            cache_dir = "./cache/voice"
            os.makedirs(cache_dir, exist_ok=True)

            # 使用延迟加载，避免界面卡顿
            self.log("开始加载数据...")

            # 首先加载发音人列表
            self.root.after(100, self.refresh_speakers)

            # 然后加载话术列表
            self.root.after(1000, self.refresh_scripts)

            # 最后加载对话列表
            self.root.after(2000, self.refresh_dialogues)

            # 加载配置
            self.root.after(3000, self.load_settings_from_config)

            # 确保视频语音匹配系统标签页存在并被正确加载
            self.root.after(3500, self.ensure_video_match_tab_exists)
            self.root.after(3600, self.reload_video_match_system)

            # 确保视频语音匹配系统标签页可见
            self.root.after(3700, lambda: self.make_video_match_tab_visible())

            # 完成提示
            self.root.after(4000, lambda: self.log("初始化数据加载完成"))

            return True
        except Exception as e:
            self.log(f"初始化数据加载失败: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")
            return False

    def make_video_match_tab_visible(self):
        """确保视频语音匹配系统标签页可见"""
        try:
            # 获取所有标签页名称
            tab_names = [self.notebook.tab(i, "text") for i in range(self.notebook.index("end"))]

            # 查找视频语音匹配系统标签页的索引
            if "视频语音匹配系统" in tab_names:
                tab_index = tab_names.index("视频语音匹配系统")
                self.log(f"视频语音匹配系统标签页索引: {tab_index}", "system")

                # 选择标签页，使其可见
                self.notebook.select(tab_index)
                self.log("已选择视频语音匹配系统标签页", "success")
            else:
                self.log("视频语音匹配系统标签页不存在，无法使其可见", "warning")
                # 尝试重新创建
                self.ensure_video_match_tab_exists()
        except Exception as e:
            self.log(f"使视频语音匹配系统标签页可见失败: {str(e)}", "error")

    def fix_video_match_tab(self):
        """修复视频语音匹配系统标签页"""
        self.log("开始修复视频语音匹配系统标签页...", "system")

        try:
            # 导入修复工具
            try:
                from fix_tab import fix_video_match_tab
                self.log("成功导入修复工具", "success")
            except ImportError:
                self.log("无法导入修复工具，尝试使用内置方法", "warning")
                # 如果无法导入修复工具，使用内置方法
                return self.fix_video_match_tab_internal()

            # 使用修复工具修复标签页
            result = fix_video_match_tab(self)

            if result:
                self.log("视频语音匹配系统标签页修复成功", "success")
                messagebox.showinfo("修复成功", "视频语音匹配系统标签页已成功修复！")
            else:
                self.log("视频语音匹配系统标签页修复失败", "error")
                messagebox.showerror("修复失败", "视频语音匹配系统标签页修复失败，请尝试重启应用程序。")

            return result
        except Exception as e:
            self.log(f"修复视频语音匹配系统标签页失败: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")
            messagebox.showerror("修复失败", f"修复视频语音匹配系统标签页时发生错误：\n{str(e)}")
            return False

    def fix_video_match_tab_internal(self):
        """使用内置方法修复视频语音匹配系统标签页"""
        self.log("使用内置方法修复视频语音匹配系统标签页", "system")

        try:
            # 获取当前所有标签页
            tab_count = self.notebook.index("end")
            tab_names = [self.notebook.tab(i, "text") for i in range(tab_count)]
            self.log(f"当前标签页: {tab_names}", "system")

            # 如果视频语音匹配系统标签页已存在，先移除
            if "视频语音匹配系统" in tab_names:
                tab_index = tab_names.index("视频语音匹配系统")
                self.log(f"视频语音匹配系统标签页已存在，索引: {tab_index}，先移除", "warning")
                try:
                    self.notebook.forget(tab_index)
                    self.log("已移除现有的视频语音匹配系统标签页", "success")
                except Exception as e:
                    self.log(f"移除标签页失败: {str(e)}", "error")

            # 创建新的标签页
            self.log("创建新的视频语音匹配系统标签页", "system")

            # 创建标签页
            video_match_tab = ttk.Frame(self.notebook, style="TFrame")
            self.video_match_tab = video_match_tab

            # 添加到notebook
            self.notebook.add(video_match_tab, text="视频语音匹配系统")
            self.log("已创建并添加视频语音匹配系统标签页", "success")

            # 创建视频语音匹配系统
            self.create_video_match_system()

            # 选择标签页，使其可见
            tab_names = [self.notebook.tab(i, "text") for i in range(self.notebook.index("end"))]
            if "视频语音匹配系统" in tab_names:
                tab_index = tab_names.index("视频语音匹配系统")
                self.notebook.select(tab_index)
                self.log(f"已选择视频语音匹配系统标签页，索引: {tab_index}", "success")

            return True
        except Exception as e:
            self.log(f"内置修复方法失败: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")
            return False

    def refresh_scripts(self):
        """刷新话术列表"""
        self.log("刷新话术列表")

        # 如果没有话术下拉框，直接返回
        if not hasattr(self, "script_combobox"):
            self.log("话术下拉框不存在，跳过刷新")
            return

        # 清空话术列表
        self.script_combobox['values'] = []

        # 发送请求获取话术列表
        try:
            import requests
            import json

            # 使用本地服务器
            url = "http://localhost:12456/script/list"
            payload = {
                "username": self.username,
                "machine_code": self.machine_code
            }

            # 发送请求
            self.log("请求话术列表")

            try:
                response = requests.post(url, json=payload, timeout=10)
                if response.status_code != 200:
                    self.log(f"请求话术列表失败: HTTP {response.status_code}", "error")
                    return
            except Exception as e:
                self.log(f"请求话术列表异常: {str(e)}", "error")
                return

            # 解析响应
            result = response.json()

            if result.get("状态") == "成功":
                # 获取话术列表
                scripts = result.get("数据", [])

                # 添加话术到下拉列表
                self.script_combobox['values'] = scripts

                # 如果有话术，选中第一个
                if scripts and hasattr(self, "script_var"):
                    self.script_var.set(scripts[0])

                self.log(f"成功加载 {len(scripts)} 个话术")
            else:
                error_msg = result.get("信息", "未知错误")
                self.log(f"加载话术列表失败: {error_msg}", "error")
        except Exception as e:
            self.log(f"加载话术列表异常: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")

    def refresh_speakers(self):
        """刷新发音人列表"""
        self.log("刷新发音人列表")

        # 如果没有发音人下拉框，直接返回
        if not hasattr(self, "speaker_combobox"):
            self.log("发音人下拉框不存在，跳过刷新")
            return

        # 清空发音人列表
        self.speaker_combobox['values'] = []

        # 发送请求获取发音人列表
        try:
            import requests
            import json

            # 使用本地服务器
            url = "http://localhost:12456/voice/speakers"
            payload = {
                "username": self.username,
                "machine_code": self.machine_code
            }

            # 发送请求
            self.log("请求发音人列表")

            try:
                response = requests.post(url, json=payload, timeout=10)
                if response.status_code != 200:
                    self.log(f"请求发音人列表失败: HTTP {response.status_code}", "error")
                    return
            except Exception as e:
                self.log(f"请求发音人列表异常: {str(e)}", "error")
                return

            # 解析响应
            result = response.json()

            if result.get("状态") == "成功":
                # 获取发音人列表
                speakers = result.get("数据", [])

                # 添加发音人到下拉列表
                self.speaker_combobox['values'] = speakers

                # 如果有发音人，选中第一个
                if speakers and hasattr(self, "speaker_var"):
                    self.speaker_var.set(speakers[0])

                self.log(f"成功加载 {len(speakers)} 个发音人")
            else:
                error_msg = result.get("信息", "未知错误")
                self.log(f"加载发音人列表失败: {error_msg}", "error")
        except Exception as e:
            self.log(f"加载发音人列表异常: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")

    def load_settings_from_config(self):
        """从配置文件加载设置"""
        self.log("从配置文件加载设置")

        try:
            # 获取配置文件路径
            import os
            config_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "_internal")
            config_path = os.path.join(config_dir, "config.json")

            # 如果配置文件不存在，直接返回
            if not os.path.exists(config_path):
                self.log("配置文件不存在，跳过加载")
                return

            # 读取配置
            import json
            with open(config_path, "r", encoding="utf-8") as f:
                config = json.load(f)

            # 获取上次选择的项目
            last_selected = config.get("last_selected", {})

            # 恢复话术选择
            if "script" in last_selected and hasattr(self, "script_combobox"):
                script_index = last_selected["script"]
                if 0 <= script_index < len(self.script_combobox['values']):
                    self.script_combobox.current(script_index)
                    self.log(f"已恢复话术选择: {self.script_combobox.get()}")

            # 恢复对话选择
            if "dialogue" in last_selected and hasattr(self, "dialogue_combobox"):
                dialogue_index = last_selected["dialogue"]
                if 0 <= dialogue_index < len(self.dialogue_combobox['values']):
                    self.dialogue_combobox.current(dialogue_index)
                    self.on_dialogue_selected()
                    self.log(f"已恢复对话选择: {self.dialogue_combobox.get()}")

            # 恢复发音人选择
            if "speaker" in last_selected and hasattr(self, "speaker_combobox"):
                speaker_index = last_selected["speaker"]
                if 0 <= speaker_index < len(self.speaker_combobox['values']):
                    self.speaker_combobox.current(speaker_index)
                    self.log(f"已恢复发音人选择: {self.speaker_combobox.get()}")

            self.log("设置已从配置文件加载")
        except Exception as e:
            self.log(f"从配置文件加载设置失败: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")

    def _handle_voice_failure(self, callback=None):
        """处理语音下载失败的情况"""
        # 标记为失败但不立即删除，允许重试
        if self.voice_queue and len(self.voice_queue) > 0:
            self.voice_queue[0]["failed"] = True
            self.voice_queue[0]["retry_count"] = self.voice_queue[0].get("retry_count", 0) + 1

            # 只有在重试次数超过3次时才删除
            if self.voice_queue[0].get("retry_count", 0) > 3:
                self.voice_queue.pop(0)
                self.log("已从队列中移除多次失败的语音")
            else:
                self.log(f"语音下载失败，将在稍后重试 (尝试 {self.voice_queue[0].get('retry_count', 1)}/3)")

        # 调用回调函数
        if callback:
            callback(False, None)

        # 继续处理下一个语音
        if self.voice_queue and len(self.voice_queue) > 0:
            self.download_next_voice()

    def download_next_voice(self):
        """下载下一个语音文件 - 使用新的下载方法"""
        self.log("开始下载下一个语音文件")

        try:
            # 检查是否有待下载的语音
            if not hasattr(self, "voice_queue") or not self.voice_queue:
                self.log("没有待下载的语音")
                return False

            # 获取下一个待下载的语音（但不从队列中移除，直到确认成功或失败）
            if len(self.voice_queue) == 0:
                self.log("语音队列为空")
                return False

            voice_data = self.voice_queue[0]

            # 检查语音数据是否有效
            if not voice_data or not isinstance(voice_data, dict):
                self.log("语音数据无效")
                # 移除无效的语音数据
                self.voice_queue.pop(0)
                self.log("已从队列中移除无效语音")

                # 继续处理下一个语音
                if len(self.voice_queue) > 0:
                    self.download_next_voice()
                return False

            # 获取语音数据
            text = voice_data.get("text", "")
            speaker = voice_data.get("speaker", "")
            speaker_id = voice_data.get("speaker_id", "")
            output_file = voice_data.get("output_file", "")
            callback = voice_data.get("callback", None)

            # 如果speaker为空但speaker_id不为空，尝试使用speaker_id
            if not speaker and speaker_id:
                # 尝试从当前主播列表中查找对应的主播名称
                if hasattr(self, "speakers") and self.speakers:
                    for spk in self.speakers:
                        if spk.get("id") == speaker_id:
                            speaker = spk.get("name", speaker_id)
                            self.log(f"使用speaker_id找到对应的主播名称: {speaker}")
                            break

                # 如果仍然找不到，直接使用speaker_id作为speaker
                if not speaker:
                    speaker = str(speaker_id)
                    self.log(f"未找到speaker_id对应的主播，直接使用ID: {speaker}")

            # 检查必要参数
            if not text or not speaker or not output_file:
                self.log(f"语音参数不完整: text={bool(text)}, speaker={bool(speaker)}, output_file={bool(output_file)}")
                # 移除参数不完整的语音数据
                self.voice_queue.pop(0)
                self.log("已从队列中移除参数不完整的语音")

                # 继续处理下一个语音
                if len(self.voice_queue) > 0:
                    self.download_next_voice()
                return False

            # 获取speaker_id
            if isinstance(speaker, str) and hasattr(self, "speakers"):
                for spk in self.speakers:
                    if spk.get("name") == speaker:
                        speaker_id = spk.get("id", speaker)
                        break

            # 获取语速
            speed = 1.0
            if hasattr(self, "speaker_speed"):
                speed = self.speaker_speed

            # 准备下载参数
            download_params = {
                "text": text,
                "speaker_id": speaker_id,
                "speed": speed,
                "output_file": output_file,
                "callback": callback
            }

            # 使用新的下载方法
            self.download_voice(download_params)

            # 标记当前项目为正在下载
            if self.voice_queue and len(self.voice_queue) > 0:
                self.voice_queue[0]["downloading"] = True

            return True

        except Exception as e:
            self.log(f"生成语音异常: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")

            # 标记为失败但不立即删除，允许重试
            if hasattr(self, "voice_queue") and self.voice_queue:
                self.voice_queue[0]["failed"] = True
                self.voice_queue[0]["retry_count"] = self.voice_queue[0].get("retry_count", 0) + 1

                # 只有在重试次数超过3次时才删除
                if self.voice_queue[0].get("retry_count", 0) > 3:
                    self.voice_queue.pop(0)
                    self.log("已从队列中移除多次失败的语音")
                else:
                    self.log(f"语音生成失败，将在稍后重试 (尝试 {self.voice_queue[0].get('retry_count', 1)}/3)")

            # 调用回调函数
            if 'callback' in locals() and callback:
                callback(False, None)

            # 继续处理下一个语音
            if hasattr(self, "voice_queue") and len(self.voice_queue) > 0:
                self.download_next_voice()

            return False

    def initialize_pygame(self):
        """初始化pygame混音器"""
        self.log("初始化pygame混音器")

        try:
            # 初始化pygame混音器
            import pygame
            if not pygame.mixer.get_init():
                try:
                    # 使用优化的参数初始化，增加缓冲区大小
                    buffer_size = self.audio_buffer_size if hasattr(self, "audio_buffer_size") else 8192
                    pygame.mixer.init(frequency=44100, size=-16, channels=2, buffer=buffer_size)
                    self.log(f"pygame混音器初始化成功，缓冲区大小: {buffer_size}", "system")
                except Exception as init_error:
                    self.log(f"pygame混音器初始化失败: {str(init_error)}", "error")
                    # 尝试使用不同参数初始化
                    try:
                        # 尝试单声道，保持较大的缓冲区
                        buffer_size = self.audio_buffer_size if hasattr(self, "audio_buffer_size") else 8192
                        pygame.mixer.init(frequency=44100, size=-16, channels=1, buffer=buffer_size)
                        self.log(f"使用单声道参数初始化pygame混音器成功，缓冲区大小: {buffer_size}", "system")
                    except Exception as e:
                        self.log(f"所有初始化尝试都失败: {str(e)}", "error")
                        return False

            # 如果是DummyMusic，设置父对象
            if hasattr(pygame.mixer.music, "set_parent"):
                pygame.mixer.music.set_parent(self)
                self.log("已设置DummyMusic的父对象")

            # 设置音量
            if hasattr(self, "volume_var"):
                volume = self.volume_var.get()
                pygame.mixer.music.set_volume(volume)
                self.log(f"已设置音量: {volume}")

            return True
        except Exception as e:
            self.log(f"初始化pygame混音器异常: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")
            return False

    def download_voice(self, voice):
        """下载语音 - 使用测试脚本中的成功代码"""
        # 使用多线程执行下载
        def download_thread():
            try:
                import requests
                import urllib.parse
                import os
                import time

                # 构建API URL
                speaker_id = voice["speaker_id"]
                text = voice["text"]
                speed = voice.get("speed", 1.0)
                output_file = voice.get("output_file")
                callback = voice.get("callback")

                # 确保文本是字符串
                if text is None:
                    text = ""
                if not isinstance(text, str):
                    text = str(text)

                # URL编码文本 - 使用测试脚本中的成功方法
                try:
                    # 先将文本转换为字节，然后再编码
                    encoded_text = urllib.parse.quote(text.encode('utf-8'))
                    self.log(f"使用 text.encode('utf-8') 进行URL编码成功")
                except Exception as e:
                    self.log(f"使用 text.encode('utf-8') 进行URL编码失败: {str(e)}", "error")
                    # 使用备用方法
                    try:
                        # 使用requests的内置URL编码功能
                        params = {"text": text}
                        encoded_params = requests.utils.urlencode(params)
                        encoded_text = encoded_params.split("=")[1]  # 提取编码后的文本部分
                        self.log(f"使用 requests.utils.urlencode 进行URL编码成功")
                    except Exception as e2:
                        self.log(f"所有编码方法都失败: {str(e2)}", "error")
                        # 将错误添加到队列中
                        self.ui_queue.put({'type': 'voice_download_error', 'data': {'error': str(e2), 'voice': voice}})
                        return

                # 🔥 修复：构建请求URL，使用speed参数而不是length
                # 🔥 修复：将滑块值(80-120)转换为API需要的速度值(0.8-1.2)
                api_speed = speed / 100.0 if speed > 10 else speed
                url = f"http://ct.scjanelife.com/voice/bert-vits2?id={speaker_id}&text={encoded_text}&lang={api_speed}"
                self.log(f"请求URL: {url[:100]}...")

                # 🔥 修复：发送请求时禁用代理
                self.log("开始下载语音...")
                start_time = time.time()
                response = requests.get(url, timeout=10, proxies={'http': None, 'https': None})
                end_time = time.time()
                self.log(f"请求耗时: {end_time - start_time:.2f}秒")

                # 检查响应状态码
                if response.status_code != 200:
                    self.log(f"请求语音失败: HTTP {response.status_code}", "error")
                    self.ui_queue.put({'type': 'voice_download_error', 'data': {'error': f"HTTP错误: {response.status_code}", 'voice': voice}})
                    return

                # 检查响应内容
                content_type = response.headers.get('Content-Type', '')
                content_length = response.headers.get('Content-Length', 'unknown')
                self.log(f"响应内容类型: {content_type}, 内容长度: {content_length}")

                # 验证内容是否为有效的MP3文件
                if not content_type.startswith('audio/') and len(response.content) < 1000:
                    self.log(f"警告: 响应可能不是有效的音频文件，内容类型: {content_type}", "warning")
                    # 检查内容是否为错误消息
                    try:
                        error_text = response.content.decode('utf-8', errors='ignore')
                        if len(error_text) < 200:  # 如果是短文本，可能是错误消息
                            self.log(f"服务器返回错误: {error_text}", "error")
                            self.ui_queue.put({'type': 'voice_download_error', 'data': {'error': f"服务器返回错误: {error_text}", 'voice': voice}})
                            return
                    except:
                        pass

                # 确保目录存在
                os.makedirs(os.path.dirname(output_file), exist_ok=True)

                # 检查响应内容是否包含额外的文本信息
                try:
                    # 尝试解码为文本，看是否是JSON或纯文本响应
                    content_text = response.content.decode('utf-8', errors='ignore')

                    # 检查是否包含常见的元数据标记
                    if 'None' in content_text or "'id':" in content_text or "'keyword':" in content_text or "'source':" in content_text:
                        self.log(f"警告: 响应内容可能包含元数据，尝试提取实际内容", "warning")

                        # 尝试提取实际的语音内容
                        # 这里假设实际内容是在元数据之前的部分
                        actual_content = response.content

                        # 查找可能的分隔点
                        for marker in [b'None', b"'id':", b"'keyword':", b"'source':", b"'scripts':"]:
                            pos = response.content.find(marker)
                            if pos > 0:
                                self.log(f"找到元数据标记 {marker} 在位置 {pos}", "info")
                                actual_content = response.content[:pos]
                                break

                        # 保存实际内容
                        with open(output_file, 'wb') as f:
                            f.write(actual_content)
                    else:
                        # 正常保存文件
                        with open(output_file, 'wb') as f:
                            f.write(response.content)
                except UnicodeDecodeError:
                    # 如果解码失败，说明可能是二进制音频数据，直接保存
                    with open(output_file, 'wb') as f:
                        f.write(response.content)

                # 验证文件大小
                file_size = os.path.getsize(output_file)
                if file_size < 1000:  # 小于1KB的文件可能不是有效的MP3
                    self.log(f"警告: 生成的文件过小 ({file_size} 字节)，可能不是有效的MP3文件", "warning")

                self.log(f"成功保存语音文件: {output_file} (大小: {file_size} 字节)")

                # 将响应添加到队列中，而不是直接在线程中更新UI
                self.ui_queue.put({'type': 'voice_download_response', 'data': {'response': response, 'voice': voice, 'text': text}})
            except Exception as e:
                # 将错误添加到队列中
                self.ui_queue.put({'type': 'voice_download_error', 'data': {'error': str(e), 'voice': voice}})

        # 启动新线程执行下载
        threading.Thread(target=download_thread, daemon=True).start()

    def generate_voice(self, text, speaker, output_file=None, callback=None):
        """生成语音文件

        Args:
            text: 要转换为语音的文本
            speaker: 发音人
            output_file: 输出文件路径，如果为None则自动生成
            callback: 生成完成后的回调函数，接收两个参数：成功标志和文件路径

        Returns:
            bool: 是否成功添加到队列
        """
        self.log(f"添加语音生成任务: {text}")

        try:
            # 如果没有语音队列，创建一个
            if not hasattr(self, "voice_queue"):
                self.voice_queue = []

            # 如果没有指定输出文件，自动生成一个
            if not output_file:
                import os
                import time
                import hashlib

                # 创建语音文件目录
                voice_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "voices")
                if not os.path.exists(voice_dir):
                    os.makedirs(voice_dir)

                # 生成文件名
                file_hash = hashlib.md5(f"{text}_{speaker}_{time.time()}".encode()).hexdigest()
                output_file = os.path.join(voice_dir, f"{file_hash}.mp3")

            # 添加到队列
            self.voice_queue.append({
                "text": text,
                "speaker": speaker,
                "output_file": output_file,
                "callback": callback
            })

            self.log(f"已添加语音生成任务到队列，当前队列长度: {len(self.voice_queue)}")

            # 如果队列中只有一个任务，立即开始下载
            if len(self.voice_queue) == 1:
                self.download_next_voice()

            return True
        except Exception as e:
            self.log(f"添加语音生成任务失败: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")
            if callback:
                callback(False, None)
            return False

    def load_video_match_system(self):
        """加载视频语音匹配系统"""
        self.log("正在加载视频语音匹配系统...")

        try:
            # 导入视频语音匹配系统模块
            import importlib.util
            spec = importlib.util.find_spec("video_match_system")

            if spec is None:
                self.log("找不到视频语音匹配系统模块，请确保video_match_system.py文件存在", "error")
                messagebox.showerror("模块错误", "找不到视频语音匹配系统模块，请确保video_match_system.py文件存在")
                return

            # 导入模块
            self.log("找到视频语音匹配系统模块，正在导入...")
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            # 获取VideoMatchSystem类
            if not hasattr(module, "VideoMatchSystem"):
                self.log("视频语音匹配系统模块中找不到VideoMatchSystem类", "error")
                messagebox.showerror("模块错误", "视频语音匹配系统模块中找不到VideoMatchSystem类")
                return

            # 清空当前标签页内容
            for widget in self.video_match_tab.winfo_children():
                widget.destroy()

            # 创建视频语音匹配系统实例
            self.log("正在创建VideoMatchSystem实例...")
            self.video_match_system = module.VideoMatchSystem(self)
            self.log("视频语音匹配系统已初始化成功", "success")

        except ImportError as e:
            self.log(f"导入视频语音匹配系统模块失败: {str(e)}", "error")
            messagebox.showerror("导入错误", f"导入视频语音匹配系统模块失败: {str(e)}")

            # 创建一个简单的提示标签
            for widget in self.video_match_tab.winfo_children():
                widget.destroy()

            ttk.Label(self.video_match_tab, text="视频语音匹配系统模块未找到",
                     font=("Microsoft YaHei UI", 14, "bold")).pack(pady=50)
            ttk.Label(self.video_match_tab,
                     text="请确保video_match_system.py文件存在并且可以正常导入").pack()

        except Exception as e:
            self.log(f"初始化视频语音匹配系统失败: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")
            messagebox.showerror("初始化错误", f"初始化视频语音匹配系统失败: {str(e)}")

            # 创建一个简单的提示标签
            for widget in self.video_match_tab.winfo_children():
                widget.destroy()

            error_frame = ttk.Frame(self.video_match_tab)
            error_frame.pack(fill="both", expand=True, padx=20, pady=20)

            ttk.Label(error_frame, text="视频语音匹配系统初始化失败",
                     font=("Microsoft YaHei UI", 14, "bold"), foreground="red").pack(pady=10)

            error_text = scrolledtext.ScrolledText(error_frame, height=10, wrap="word")
            error_text.pack(fill="both", expand=True, padx=10, pady=10)
            error_text.insert("1.0", f"错误信息: {str(e)}\n\n{traceback.format_exc()}")
            error_text.config(state="disabled")

            ttk.Button(error_frame, text="重新加载",
                      command=self.create_video_match_system,
                      style="Primary.TButton").pack(pady=10)

    def reload_video_match_system(self):
        """重新加载视频语音匹配系统"""
        # 清空当前标签页内容
        for widget in self.video_match_tab.winfo_children():
            widget.destroy()

        # 重新创建视频语音匹配系统
        self.create_video_match_system()

    def on_closing(self):
        """窗口关闭时的处理函数"""
        try:
            # 如果当前有对话数据，先保存当前编辑的回复内容
            if hasattr(self, "dialogue_data") and self.dialogue_data:
                try:
                    if self.save_current_response():
                        self.log("窗口关闭前已保存当前关键词的回复内容")
                except Exception as e:
                    print(f"保存当前关键词的回复内容失败: {str(e)}")

            # 保存视频语音匹配系统数据
            if hasattr(self, "video_match_system"):
                try:
                    # 使用try-except包装保存操作，确保即使保存失败也能继续关闭窗口
                    self.video_match_system.save_settings()
                    self.log("窗口关闭前已保存视频语音匹配系统设置")
                except Exception as e:
                    print(f"保存视频语音匹配系统设置失败: {str(e)}")

            # 停止直播状态定时器
            if hasattr(self, "live_status_timer") and self.live_status_timer:
                try:
                    self.root.after_cancel(self.live_status_timer)
                    self.live_status_timer = None
                except Exception as e:
                    print(f"取消直播状态定时器失败: {str(e)}")
        except Exception as e:
            print(f"窗口关闭处理出错: {str(e)}")
        finally:
            # 无论如何，确保窗口被销毁
            try:
                self.root.destroy()
            except Exception as e:
                print(f"销毁窗口失败: {str(e)}")
                # 如果常规销毁失败，尝试使用quit()
                try:
                    self.root.quit()
                except:
                    pass

    def verify_token_and_start_status_update(self):
        """验证token并启动直播状态定时提交"""
        print(f"verify_token_and_start_status_update被调用: token={self.token[:10] if self.token else None}...")

        if not self.token:
            self.log("未提供token，无法验证用户身份", "error")
            print("未提供token，无法验证用户身份")
            # 即使没有token，也不要立即退出，先尝试使用其他功能
            self.token_valid = False
            return

        try:
            # 直接使用HTTP验证，不再尝试数据库连接
            print("使用HTTP验证")

            # 使用HTTP请求验证token
            import requests
            url = "http://localhost:12456/token/verify"
            data = {"token": self.token}

            # 打印调试信息
            self.log(f"发送token验证请求到: {url}", "network")
            self.log(f"正在验证token...", "network")
            print(f"发送token验证请求到 {url}, token={self.token[:10]}...")

            # 发送请求并添加错误处理
            try:
                response = requests.post(url, json=data, timeout=10)
                # 检查HTTP状态码
                print(f"HTTP响应状态码: {response.status_code}")
                if response.status_code != 200:
                    self.log(f"Token验证请求失败: HTTP {response.status_code}", "error")
                    print(f"Token验证请求失败: HTTP {response.status_code}")

                    # 即使验证失败，也不要立即退出
                    self.token_valid = False
                    return
            except requests.exceptions.RequestException as req_err:
                self.log(f"Token验证请求异常: {str(req_err)}", "error")
                print(f"Token验证请求异常: {str(req_err)}")

                # 验证失败，设置token无效
                self.token_valid = False
                return

            # 解析响应数据
            try:
                result = response.json()
                print(f"响应结果: {result}")
            except ValueError as json_err:
                self.log(f"Token验证响应解析失败: {str(json_err)}", "error")
                print(f"Token验证响应解析失败: {str(json_err)}")
                self.token_valid = False
                return

            # 检查响应中是否包含token
            if "token" in result:
                self.token_valid = True
                self.log(f"Token验证成功，用户: {self.username}", "network")
                print(f"Token验证成功，用户: {self.username}")

                # 使用登录时传入的过期时间，不从配置中覆盖
                if hasattr(self, "expiry_time") and self.expiry_time:
                    self.update_expiry_display()
                    self.log(f"使用登录时传入的账号过期时间: {self.expiry_time}")
                # 如果没有过期时间，才从配置中获取
                elif hasattr(self, "config") and "token_expiry" in self.config:
                    self.expiry_time = self.config["token_expiry"]
                    self.update_expiry_display()
                    self.log(f"从配置中获取账号过期时间: {self.expiry_time}")
                else:
                    # 如果配置中也没有过期时间，显示为未知
                    self.expiry_time = "未知"
                    self.update_expiry_display()
                    self.log(f"未找到过期时间，显示为: {self.expiry_time}")

                # 启动直播状态定时提交
                self.start_live_status_update()
            elif result.get("状态") == "成功":
                self.token_valid = True
                self.log(f"Token验证成功，用户: {self.username}", "network")
                print(f"Token验证成功，用户: {self.username}")

                # 如果已有登录时传入的过期时间，优先使用
                if hasattr(self, "expiry_time") and self.expiry_time and self.expiry_time != "未知":
                    self.update_expiry_display()
                    self.log(f"保留登录时传入的账号过期时间: {self.expiry_time}")
                # 否则从API响应中获取过期时间
                elif "data" in result and "expires_at" in result["data"]:
                    self.expiry_time = result["data"]["expires_at"]
                    self.update_expiry_display()
                    self.log(f"从API响应data中获取账号过期时间: {self.expiry_time}")
                elif "expires_at" in result:
                    # 直接从结果中获取过期时间
                    self.expiry_time = result["expires_at"]
                    self.update_expiry_display()
                    self.log(f"从API响应中获取账号过期时间: {self.expiry_time}")
                else:
                    # 如果响应中没有过期时间，显示为未知
                    self.expiry_time = "未知"
                    self.update_expiry_display()
                    self.log(f"未找到过期时间，显示为: {self.expiry_time}")

                # 保存过期时间到配置文件
                try:
                    if self.expiry_time:
                        config_file = os.path.join(os.path.dirname(__file__), "_internal", "config.json")
                        if os.path.exists(config_file):
                            with open(config_file, "r", encoding="utf-8") as f:
                                config = json.load(f)
                            config["token_expiry"] = self.expiry_time
                            with open(config_file, "w", encoding="utf-8") as f:
                                json.dump(config, f, ensure_ascii=False, indent=4)
                            print(f"已将过期时间 {self.expiry_time} 保存到配置文件")
                except Exception as e:
                    print(f"保存过期时间到配置文件失败: {str(e)}")

                # 启动直播状态定时提交
                self.start_live_status_update()
            else:
                self.token_valid = False
                error_msg = result.get("信息", "Token验证失败")
                self.log(f"Token验证失败: {error_msg}", "error")
                print(f"Token验证失败: {error_msg}")

                # 不要立即显示错误提示并退出，而是在需要使用token的功能时再提示
                # 这样用户可以继续使用不需要token的功能
        except Exception as e:
            self.log(f"Token验证出错: {str(e)}", "error")
            print(f"Token验证出错: {str(e)}")
            import traceback
            print(traceback.format_exc())
            # 即使出错，也不要立即退出
            self.token_valid = False

    # 已删除init_server_websocket方法

    def start_live_status_update(self):
        """启动直播状态定时提交"""
        # 使用HTTP方式提交状态
        # 先提交一次初始状态
        self.submit_live_status()

        # 设置定时器，每10秒提交一次
        self.live_status_timer = self.root.after(10000, self.periodic_status_update)
        self.log("启动直播状态定时提交")

    def periodic_status_update(self):
        """定期提交直播状态"""
        # 提交状态
        self.submit_live_status()

        # 重新设置定时器
        self.live_status_timer = self.root.after(10000, self.periodic_status_update)

    def prepare_status_data(self):
        """准备状态数据
        根据优化要求，只发送必要的数据：
        1. 正在播放的话术内容
        2. OBS正在播放的主视频
        3. 基本的账号信息
        """
        # 准备基本状态数据
        data = {
            "is_online": True,  # 始终设置为在线状态
            "username": self.username if hasattr(self, "username") else "",
            "machine_code": self.machine_code if hasattr(self, "machine_code") else "",
            "client_version": self.client_version if hasattr(self, "client_version") else ""
        }

        # 添加当前话术名称
        if hasattr(self, "current_script") and self.current_script:
            data["current_script"] = self.current_script

        # 添加当前正在播放的语音内容（只有在播放中才添加）
        if self.is_playing and hasattr(self, "current_playing_var") and self.current_playing_var.get() and self.current_playing_var.get() != "无正在播放的语音":
            data["current_script_content"] = self.current_playing_var.get()
            data["voice_play_time"] = self.voice_play_time if hasattr(self, "voice_play_time") else 0
            self.log(f"已添加当前播放内容: {self.current_playing_var.get()[:30]}...", "voice")

        # 添加在线人数
        if hasattr(self, "online_count"):
            data["online_count"] = self.online_count

        # 如果有OBS当前源，添加到状态
        if hasattr(self, "current_video_source") and self.current_video_source:
            data["obs_source"] = self.current_video_source

        return data

    def submit_live_status(self):
        """提交直播状态到服务器"""
        if not self.token_valid:
            self.log("无效的token，无法提交直播状态")
            return

        try:
            # 准备状态数据
            data = self.prepare_status_data()

            # 发送请求
            import requests
            import json
            # 使用本地服务器地址
            url = "http://localhost:12456/live/status"
            headers = {"Authorization": f"Bearer {self.token}"}

            # 打印调试信息
            self.log(f"发送直播状态到: {url}")
            self.log(f"状态数据: {json.dumps(data, ensure_ascii=False)}")

            try:
                response = requests.post(url, json=data, headers=headers, timeout=10)
                if response.status_code != 200:
                    self.log(f"本地服务器提交失败: HTTP {response.status_code}")
                    return
            except requests.exceptions.RequestException as req_err:
                self.log(f"本地服务器连接失败: {str(req_err)}")
                return

            result = response.json()

            if result.get("状态") == "成功":
                self.log("直播状态提交成功")
            else:
                error_msg = result.get("信息", "未知错误")
                self.log(f"直播状态提交失败: {error_msg}")

                # 如果是token无效，只标记token无效，不立即退出
                if "TOKEN_INVALID" in str(result) or "TOKEN_MISSING" in str(result):
                    self.token_valid = False
                    self.log("账号token已失效，直播状态提交已停止")
        except Exception as e:
            self.log(f"提交直播状态出错: {str(e)}")



    def create_variables_panel(self, parent, target_widget):
        """创建变量按钮面板"""
        # 创建变量按钮面板框架
        variables_frame = ttk.Frame(parent)
        variables_frame.pack(fill=tk.X, padx=5, pady=5)

        # 创建按钮容器，使用水平布局
        button_container = ttk.Frame(variables_frame)
        button_container.pack(fill=tk.X, expand=True)

        # 创建变量按钮，所有按钮排在一行
        for var in self.variables:
            # 创建按钮，点击时插入变量代码
            btn = ttk.Button(button_container, text=var["name"],
                          command=lambda code=var["code"], desc=var["description"]:
                              self.insert_variable(target_widget, code, desc),
                          width=5)  # 进一步减小按钮宽度
            btn.pack(side=tk.LEFT, padx=1, pady=1)  # 减小间距

            # 添加工具提示
            self.create_tooltip(btn, f"{var['name']}: {var['description']}\n代码: {var['code']}")

        return variables_frame

    def create_ai_host_area(self):
        """创建AI主播区"""
        # 创建主播区框架
        self.ai_host_frame = ttk.LabelFrame(self.ai_host_tab, text="AI主播区")
        self.ai_host_frame.pack(fill=tk.BOTH, expand=True, padx=12, pady=12)

        # 创建标题栏
        title_frame = ttk.Frame(self.ai_host_frame)
        title_frame.pack(fill=tk.X, padx=15, pady=(15, 0))

        ttk.Label(title_frame, text="选择您喜欢的AI主播声音", style="Title.TLabel").pack(side=tk.LEFT)

        # 添加分隔线
        separator = ttk.Separator(self.ai_host_frame, orient="horizontal")
        separator.pack(fill=tk.X, padx=15, pady=(10, 15))

        # 创建上部控制区
        control_frame = ttk.Frame(self.ai_host_frame)
        control_frame.pack(fill=tk.X, padx=15, pady=(0, 15))

        # 创建主播选择下拉框
        ttk.Label(control_frame, text="选择主播:", style="Subtitle.TLabel").pack(side=tk.LEFT, padx=(0, 10))

        self.speaker_var = tk.StringVar()
        self.speaker_combobox = ttk.Combobox(control_frame, textvariable=self.speaker_var, state="readonly", width=30)
        self.speaker_combobox.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.speaker_combobox.bind("<<ComboboxSelected>>", self.on_speaker_selected)

        # 创建刷新按钮
        self.refresh_button = ttk.Button(control_frame, text="刷新主播", command=self.on_refresh_speakers_clicked, style="Primary.TButton")
        self.refresh_button.pack(side=tk.LEFT, padx=10)

        # 添加语音播放区域
        voice_frame = ttk.LabelFrame(self.ai_host_frame, text="语音播放状态")
        voice_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # 添加语音播放状态区
        voice_status_frame = ttk.Frame(voice_frame)
        voice_status_frame.pack(fill=tk.X, padx=15, pady=15)

        # 添加生成数量显示
        ttk.Label(voice_status_frame, text="当前缓存语音:", style="Subtitle.TLabel").pack(side=tk.LEFT, padx=(0, 10))
        self.generated_count_var = tk.StringVar(value="0")
        ttk.Label(voice_status_frame, textvariable=self.generated_count_var, font=("Microsoft YaHei UI", 10, "bold")).pack(side=tk.LEFT)

        # 添加缓存路径显示
        cache_frame = ttk.Frame(voice_frame)
        cache_frame.pack(fill=tk.X, padx=15, pady=(0, 15))

        ttk.Label(cache_frame, text="缓存路径:", style="Subtitle.TLabel").pack(side=tk.LEFT, padx=(0, 10))
        self.cache_path_var = tk.StringVar(value="./cache/voice")
        ttk.Label(cache_frame, textvariable=self.cache_path_var).pack(side=tk.LEFT)

        # 添加进度显示
        progress_frame = ttk.Frame(voice_frame)
        progress_frame.pack(fill=tk.X, padx=15, pady=(0, 15))

        ttk.Label(progress_frame, text="播放进度:", style="Subtitle.TLabel").pack(side=tk.LEFT, padx=(0, 10))
        self.progress_var = tk.DoubleVar(value=0)
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, length=200, mode="determinate")
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=10)

        # 添加当前播放文本显示
        self.current_playing_var = tk.StringVar(value="无正在播放的语音")
        ttk.Label(progress_frame, textvariable=self.current_playing_var, foreground=self.primary_color).pack(side=tk.LEFT, padx=10)

        # 创建语速设置区域
        speed_frame = ttk.LabelFrame(self.ai_host_frame, text="主播语速调节")
        speed_frame.pack(fill=tk.X, padx=15, pady=(10, 15))

        speed_control = ttk.Frame(speed_frame)
        speed_control.pack(fill=tk.X, padx=15, pady=15)

        ttk.Label(speed_control, text="语速:", style="Subtitle.TLabel").pack(side=tk.LEFT, padx=(0, 10))

        # 创建语速滑块
        self.speed_var = tk.DoubleVar(value=1.0)
        self.speed_scale = ttk.Scale(speed_control, from_=0.8, to=1.2, orient=tk.HORIZONTAL,
                                   variable=self.speed_var, length=200)
        self.speed_scale.pack(side=tk.LEFT, padx=10, fill=tk.X, expand=True)

        # 添加语速值更新函数
        def update_speed_label(*_):
            value = self.speed_var.get()
            self.speed_label.config(text=f"{value:.1f}")
            self.on_speed_changed(value)

        self.speed_var.trace_add("write", update_speed_label)

        # 显示当前语速值
        self.speed_label = ttk.Label(speed_control, text="1.0", width=3, font=("Microsoft YaHei UI", 10, "bold"), foreground=self.primary_color)
        self.speed_label.pack(side=tk.LEFT, padx=10)

        # 创建播放控制区域
        control_frame = ttk.LabelFrame(self.ai_host_frame, text="播放控制")
        control_frame.pack(fill=tk.X, padx=15, pady=(10, 15))

        # 创建按钮容器
        button_container = ttk.Frame(control_frame)
        button_container.pack(fill=tk.X, padx=15, pady=15)

        # 生成语音按钮已移除，因为现在系统会自动生成语音

        # 播放按钮
        self.play_button = ttk.Button(button_container, text="播放",
                                   command=self.play_voice,
                                   style="Success.TButton")
        self.play_button.pack(side=tk.LEFT, padx=10, ipadx=10, ipady=5)

        # 暂停按钮
        self.pause_button = ttk.Button(button_container, text="暂停",
                                    command=self.pause_voice,
                                    style="Primary.TButton")
        self.pause_button.pack(side=tk.LEFT, padx=10, ipadx=10, ipady=5)

        # 停止按钮
        self.stop_button = ttk.Button(button_container, text="停止",
                                   command=self.stop_voice,
                                   style="Primary.TButton")
        self.stop_button.pack(side=tk.LEFT, padx=10, ipadx=10, ipady=5)

        # 清空列表按钮
        self.clear_button = ttk.Button(button_container, text="清空列表",
                                    command=self.clear_playlist,
                                    style="Accent.TButton")
        self.clear_button.pack(side=tk.LEFT, padx=10, ipadx=10, ipady=5)

    def on_refresh_speakers_clicked(self):
        """处理刷新主播按钮点击事件"""
        # 禁用按钮，防止重复点击
        self.refresh_button.config(state="disabled")
        self.log("正在刷新AI主播列表...")

        # 使用after方法延迟执行，避免界面卡顿
        self.root.after(100, self.refresh_speakers)

    def refresh_speakers(self):
        """刷新AI主播列表"""
        self.log("正在获取AI主播列表...")

        # 禁用刷新按钮，防止重复点击
        self.refresh_button.config(state="disabled")

        # 使用多线程执行请求
        def fetch_speakers_thread():
            try:
                import requests
                # 使用指定域名获取主播列表
                response = requests.get("http://ct.scjanelife.com/voice/speakers", timeout=10)
                # 将响应添加到队列中
                self.ui_queue.put({'type': 'speakers_response', 'data': response})
            except Exception as e:
                # 将错误添加到队列中
                self.ui_queue.put({'type': 'speakers_error', 'data': str(e)})

        # 启动新线程执行请求
        threading.Thread(target=fetch_speakers_thread, daemon=True).start()

    def process_speakers_response(self, response):
        """处理主播列表响应"""
        try:
            if response.status_code == 200:
                data = response.json()

                # 清空当前列表
                self.ai_speakers = []

                # 解析返回的数据
                if isinstance(data, list) and len(data) > 0:
                    # 直接解析列表中的元素
                    for i, speaker in enumerate(data):
                        if isinstance(speaker, dict):
                            speaker_id = speaker.get("id", "")
                            speaker_name = speaker.get("name", f"主播{i+1}")
                            speaker_langs = speaker.get("lang", [])

                            # 添加主播
                            self.ai_speakers.append({
                                "id": speaker_id,
                                "name": speaker_name,
                                "model": "BERT-VITS2",
                                "lang": speaker_langs[0] if speaker_langs else "zh"
                            })
                elif isinstance(data, dict):
                    # 如果是嵌套结构，遍历所有模型类别
                    for model_name, speakers in data.items():
                        if isinstance(speakers, list):
                            for speaker in speakers:
                                if isinstance(speaker, dict) and 'id' in speaker and 'name' in speaker and 'lang' in speaker:
                                    # 添加模型信息
                                    speaker_id = speaker.get("id", "")
                                    speaker_name = speaker.get("name", "未知主播")
                                    speaker_langs = speaker.get("lang", [])

                                    # 添加到主播列表
                                    self.ai_speakers.append({
                                        "id": speaker_id,
                                        "name": speaker_name,
                                        "model": model_name,
                                        "lang": speaker_langs[0] if speaker_langs and isinstance(speaker_langs, list) else "zh"
                                    })

                # 更新下拉框
                speaker_names = [f"{s['name']} ({s['model']})" for s in self.ai_speakers]
                self.speaker_combobox['values'] = speaker_names

                # 如果有主播，选择上次选择的或第一个
                if speaker_names:
                    # 尝试选择上次选择的主播
                    if hasattr(self, "last_speaker_index") and 0 <= self.last_speaker_index < len(speaker_names):
                        self.speaker_combobox.current(self.last_speaker_index)
                    else:
                        self.speaker_combobox.current(0)
                    self.on_speaker_selected(None)

                self.log(f"成功获取到 {len(self.ai_speakers)} 个AI主播")
            else:
                self.log(f"获取AI主播列表失败: HTTP {response.status_code}")
        except Exception as e:
            self.log(f"处理AI主播列表数据出错: {str(e)}")
        finally:
            # 恢复刷新按钮
            self.refresh_button.config(state="normal")

    def handle_speakers_error(self, error_msg):
        """处理获取主播列表错误"""
        self.log(f"获取AI主播列表出错: {error_msg}")
        # 恢复刷新按钮
        self.refresh_button.config(state="normal")

    def on_speaker_selected(self, _):
        """当选择主播时触发"""
        selected_index = self.speaker_combobox.current()
        if selected_index >= 0 and selected_index < len(self.ai_speakers):
            self.current_speaker = self.ai_speakers[selected_index]
            self.log(f"已选择主播: {self.current_speaker['name']}")

            # 保存选择到配置
            if "last_selected" not in self.config:
                self.config["last_selected"] = {}
            self.config["last_selected"]["speaker"] = selected_index
            self.save_config()

    def on_speed_changed(self, value):
        """当语速改变时触发"""
        self.speaker_speed = value
        self.log(f"语速已调整为: {value:.1f}")

        # 保存语速设置到配置
        if "voice" not in self.config:
            self.config["voice"] = {}
        self.config["voice"]["speed"] = value
        self.save_config()

    def update_expiry_display(self):
        """更新过期时间显示"""
        # 检查 expiry_label 是否存在
        if not hasattr(self, 'expiry_label'):
            print("过期时间标签尚未创建，跳过更新")
            return

        # 打印当前过期时间
        print(f"当前过期时间: {self.expiry_time if hasattr(self, 'expiry_time') else '未设置'}")

        # 如果没有过期时间，显示未知
        if not hasattr(self, 'expiry_time') or not self.expiry_time:
            self.expiry_label.config(text="账号到期时间: 未知", foreground="#FF8C00")
            print("未设置过期时间，显示为未知")
            return

        try:
            # 解析过期时间
            from datetime import datetime

            # 打印调试信息
            print(f"原始过期时间值: {self.expiry_time}, 类型: {type(self.expiry_time)}")

            # 尝试多种格式解析过期时间
            expiry_dt = None

            # 如果是字符串类型
            if isinstance(self.expiry_time, str):
                # 如果是Unix时间戳（数字字符串）
                if self.expiry_time.isdigit():
                    try:
                        timestamp = int(self.expiry_time)
                        expiry_dt = datetime.fromtimestamp(timestamp)
                        print(f"成功将时间戳 {timestamp} 转换为日期时间: {expiry_dt}")
                    except Exception as e:
                        print(f"时间戳转换失败: {str(e)}")
                        self.expiry_label.config(text="账号到期时间: 无法解析", foreground="#FF8C00")
                        return
                else:
                    # 尝试多种日期格式
                    formats = [
                        "%Y-%m-%d %H:%M:%S",  # 标准格式
                        "%Y/%m/%d %H:%M:%S",  # 斜杠分隔
                        "%Y-%m-%d",           # 仅日期
                        "%Y/%m/%d"            # 仅日期，斜杠分隔
                    ]

                    for fmt in formats:
                        try:
                            expiry_dt = datetime.strptime(self.expiry_time, fmt)
                            print(f"成功使用格式 {fmt} 解析日期时间: {expiry_dt}")
                            break
                        except ValueError:
                            continue

                    # 如果所有格式都失败
                    if expiry_dt is None:
                        print(f"无法解析日期字符串: {self.expiry_time}")
                        self.expiry_label.config(text="账号到期时间: 无法解析", foreground="#FF8C00")
                        return
            # 如果是数字类型（时间戳）
            elif isinstance(self.expiry_time, (int, float)):
                try:
                    expiry_dt = datetime.fromtimestamp(self.expiry_time)
                    print(f"成功将数字时间戳 {self.expiry_time} 转换为日期时间: {expiry_dt}")
                except Exception as e:
                    print(f"数字时间戳转换失败: {str(e)}")
                    self.expiry_label.config(text="账号到期时间: 无法解析", foreground="#FF8C00")
                    return
            # 其他情况
            else:
                print(f"未知的过期时间格式: {type(self.expiry_time)}")
                self.expiry_label.config(text="账号到期时间: 无法解析", foreground="#FF8C00")
                return

            # 确保我们有一个有效的日期时间对象
            if expiry_dt is None:
                print("未能获取有效的过期时间")
                self.expiry_label.config(text="账号到期时间: 无法解析", foreground="#FF8C00")
                return

            now = datetime.now()

            # 直接使用原始过期时间字符串，不进行格式转换
            expiry_str = self.expiry_time
            print(f"使用原始过期时间字符串: {expiry_str}")

            # 计算剩余时间
            if expiry_dt > now:
                # 计算天数
                days_left = (expiry_dt - now).days
                hours_left = ((expiry_dt - now).seconds // 3600)
                minutes_left = ((expiry_dt - now).seconds % 3600) // 60

                if days_left > 0:
                    remaining = f"{days_left}天{hours_left}小时"
                elif hours_left > 0:
                    remaining = f"{hours_left}小时{minutes_left}分钟"
                else:
                    remaining = f"{minutes_left}分钟"

                self.expiry_label.config(text=f"账号到期时间: {expiry_str} (剩余: {remaining})", foreground="green")

                # 如果剩余时间少于1天，显示为黄色警告
                if days_left < 1:
                    self.expiry_label.config(foreground="#FF8C00")  # 深橙色

                # 如果剩余时间少于1小时，显示为红色警告
                if days_left == 0 and hours_left < 1:
                    self.expiry_label.config(foreground="red")
            else:
                self.expiry_label.config(text=f"账号已过期 (到期时间: {expiry_str})", foreground="red")

            # 设置定时器，每分钟更新一次
            self.root.after(60000, self.update_expiry_display)
        except Exception as e:
            print(f"解析过期时间出错: {str(e)}")
            if hasattr(self, 'expiry_label'):
                self.expiry_label.config(text=f"账号过期时间: {self.expiry_time}")


    def create_script_area(self):
        """创建话术区"""
        # 创建话术区框架
        self.script_frame = ttk.LabelFrame(self.script_tab, text="话术区")
        self.script_frame.pack(fill=tk.BOTH, expand=True, padx=8, pady=8)

        # 创建标题栏
        title_frame = ttk.Frame(self.script_frame)
        title_frame.pack(fill=tk.X, padx=10, pady=(10, 0))

        ttk.Label(title_frame, text="编辑和管理话术内容", style="Title.TLabel").pack(side=tk.LEFT)

        # 添加分隔线
        separator = ttk.Separator(self.script_frame, orient="horizontal")
        separator.pack(fill=tk.X, padx=10, pady=(5, 10))

        # 创建上部控制区
        control_frame = ttk.Frame(self.script_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        # 创建话术选择下拉框
        ttk.Label(control_frame, text="选择话术:", style="Subtitle.TLabel").pack(side=tk.LEFT, padx=(0, 5))

        self.script_var = tk.StringVar()
        self.script_combobox = ttk.Combobox(control_frame, textvariable=self.script_var, state="readonly", width=25)
        self.script_combobox.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.script_combobox.bind("<<ComboboxSelected>>", self.on_script_selected)

        # 创建按钮区域
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(side=tk.LEFT, padx=5)

        # 创建新建话术按钮
        self.new_script_button = ttk.Button(button_frame, text="新建话术", command=self.create_new_script, style="Accent.TButton")
        self.new_script_button.pack(side=tk.LEFT, padx=5)

        # 创建刷新按钮
        self.script_refresh_button = ttk.Button(button_frame, text="刷新话术", command=self.on_refresh_scripts_clicked, style="Primary.TButton")
        self.script_refresh_button.pack(side=tk.LEFT, padx=5)

        # 创建保存按钮
        self.save_script_button = ttk.Button(button_frame, text="保存话术", command=self.save_script, style="Success.TButton")
        self.save_script_button.pack(side=tk.LEFT, padx=5)

        # 创建主内容区
        content_frame = ttk.Frame(self.script_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 创建话术内容文本框
        self.script_text = scrolledtext.ScrolledText(content_frame, wrap=tk.WORD, height=15)
        self.script_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建变量按钮区
        variables_label_frame = ttk.LabelFrame(content_frame, text="变量插入")
        variables_label_frame.pack(fill=tk.X, padx=5, pady=5)

        # 添加变量说明
        var_tip_frame = ttk.Frame(variables_label_frame)
        var_tip_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
        ttk.Label(var_tip_frame, text="点击变量按钮将在光标处插入对应变量代码", foreground=self.accent_color).pack(side=tk.LEFT)

        # 创建变量按钮面板
        self.create_variables_panel(variables_label_frame, self.script_text)


    def on_refresh_scripts_clicked(self):
        """处理刷新话术按钮点击事件"""
        # 禁用按钮，防止重复点击
        self.script_refresh_button.config(state="disabled")
        self.log("正在刷新话术列表...")

        # 使用after方法延迟执行，避免界面卡顿
        self.root.after(100, self.refresh_scripts)

    def refresh_scripts(self):
        """从服务器获取话术列表"""
        self.log("正在获取话术列表...")

        # 禁用刷新按钮，防止重复点击
        self.script_refresh_button.config(state="disabled")

        # 使用多线程执行请求
        def fetch_scripts_thread():
            try:
                import requests
                # 使用本地服务器获取话术列表
                response = requests.get("http://localhost:12456/getscriptlist", timeout=10)

                # 将响应添加到队列中，而不是直接在线程中更新UI
                self.ui_queue.put({'type': 'scripts_response', 'data': response})
            except Exception as e:
                # 将错误添加到队列中
                self.ui_queue.put({'type': 'scripts_error', 'data': str(e)})

        # 启动新线程执行请求
        threading.Thread(target=fetch_scripts_thread, daemon=True).start()

    def process_scripts_response(self, response):
        """处理话术列表响应"""
        try:
            if response.status_code == 200:
                data = response.json()

                # 清空当前列表
                self.script_list = []

                # 解析返回的数据
                if isinstance(data, dict) and "ai话术" in data and isinstance(data["ai话术"], list):
                    # 正确解析嵌套结构
                    for script in data["ai话术"]:
                        if isinstance(script, dict) and "name" in script:
                            script_name = script["name"]
                            self.script_list.append(script_name)
                elif isinstance(data, list):
                    # 兼容直接返回列表的情况
                    for script in data:
                        if isinstance(script, dict) and "name" in script:
                            script_name = script["name"]
                            self.script_list.append(script_name)

                # 更新下拉框
                self.script_combobox['values'] = self.script_list

                # 如果有话术，选择上次选择的或第一个
                if self.script_list:
                    # 尝试选择上次选择的话术
                    if hasattr(self, "last_script_index") and 0 <= self.last_script_index < len(self.script_list):
                        self.script_combobox.current(self.last_script_index)
                    else:
                        self.script_combobox.current(0)
                    self.on_script_selected(None)

                self.log(f"成功获取到 {len(self.script_list)} 个话术")
            else:
                self.log(f"获取话术列表失败: HTTP {response.status_code}")
        except Exception as e:
            self.log(f"处理话术列表数据出错: {str(e)}")
        finally:
            # 恢复刷新按钮
            self.script_refresh_button.config(state="normal")

    def handle_scripts_error(self, error_msg):
        """处理获取话术列表错误"""
        self.log(f"获取话术列表出错: {error_msg}")
        # 恢复刷新按钮
        self.script_refresh_button.config(state="normal")

    def on_script_selected(self, _):
        """当选择话术时触发"""
        selected_index = self.script_combobox.current()
        if selected_index >= 0 and selected_index < len(self.script_list):
            selected_script = self.script_list[selected_index]
            self.current_script = selected_script
            self.log(f"已选择话术: {selected_script}")

            # 获取话术内容
            self.get_script_content(selected_script)

            # 保存选择到配置
            if "last_selected" not in self.config:
                self.config["last_selected"] = {}
            self.config["last_selected"]["script"] = selected_index
            self.save_config()



    def get_script_content(self, script_name):
        """获取话术内容"""
        try:
            import requests
            import json

            # 发送请求获取话术内容
            payload = {
                "类型": "获取话术",
                "话术名": script_name
            }

            self.log(f"获取话术 '{script_name}' 内容，请求数据: {json.dumps(payload, ensure_ascii=False)}")

            response = requests.post("http://localhost:12456/", json=payload, timeout=10)

            # 打印响应内容
            self.log(f"获取话术响应状态码: {response.status_code}")

            # 清空当前内容
            self.script_text.delete(1.0, tk.END)

            if response.status_code == 200:
                # 尝试解析JSON响应
                try:
                    # 先尝试解析为JSON
                    data = response.json()
                    self.log(f"获取话术响应内容(JSON): {json.dumps(data, ensure_ascii=False)[:200]}...")

                    # 如果是JSON格式，尝试提取内容
                    if isinstance(data, dict) and "内容" in data:
                        content = data["内容"]
                    else:
                        # 如果没有内容字段，使用整个响应
                        content = json.dumps(data, ensure_ascii=False, indent=2)
                except:
                    # 如果不是JSON格式，直接使用响应文本
                    content = response.text
                    self.log(f"获取话术响应内容(文本): {content[:200]}...")

                # 显示话术内容
                if content and content.strip():
                    self.script_text.insert(tk.END, content)
                else:
                    self.script_text.insert(tk.END, "该话术内容为空")
            else:
                self.log(f"获取话术内容失败: HTTP {response.status_code}")
                self.script_text.insert(tk.END, f"获取话术内容失败: HTTP {response.status_code}")
        except Exception as e:
            self.log(f"获取话术内容出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc(), "error")
            self.script_text.insert(tk.END, f"获取话术内容出错: {str(e)}")


    def create_new_script(self):
        """创建新话术"""
        # 创建新建话术的对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("新建话术")
        dialog.geometry("400x150")
        dialog.transient(self.root)  # 设置为主窗口的子窗口
        dialog.grab_set()  # 模态对话框

        # 创建话术名称输入框
        name_frame = ttk.Frame(dialog, padding=10)
        name_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(name_frame, text="话术名称:").pack(side=tk.LEFT, padx=(0, 5))
        name_entry = ttk.Entry(name_frame, width=30)
        name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        name_entry.focus_set()  # 设置焦点

        # 创建确认按钮
        def confirm_create():
            script_name = name_entry.get().strip()
            if not script_name:
                messagebox.showwarning("新建话术", "话术名称不能为空")
                return

            # 检查名称是否已存在
            if script_name in self.script_list:
                messagebox.showwarning("新建话术", f"话术 '{script_name}' 已存在")
                return

            # 创建新话术
            self.current_script = script_name
            self.script_list.append(script_name)
            self.script_combobox['values'] = self.script_list
            self.script_combobox.set(script_name)

            # 清空话术内容
            self.script_text.delete(1.0, tk.END)
            self.script_text.insert(tk.END, "1***这里是新话术的内容\n2***可以添加多行话术\n3***每行以数字***开头")

            self.log(f"已创建新话术: {script_name}")
            dialog.destroy()

        # 创建按钮区域
        button_frame = ttk.Frame(dialog, padding=10)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        cancel_button = ttk.Button(button_frame, text="取消", command=dialog.destroy)
        cancel_button.pack(side=tk.RIGHT, padx=5)

        confirm_button = ttk.Button(button_frame, text="创建", command=confirm_create, style="Primary.TButton")
        confirm_button.pack(side=tk.RIGHT, padx=5)

        # 绑定Enter键到确认按钮
        dialog.bind("<Return>", lambda _: confirm_create())

        # 等待对话框关闭
        self.root.wait_window(dialog)

    def save_script(self):
        """保存话术内容"""
        if not self.current_script:
            self.log("请先选择一个话术")
            messagebox.showwarning("保存话术", "请先选择一个话术")
            return

        # 获取当前编辑框中的内容
        script_content = self.script_text.get(1.0, tk.END).strip()
        if not script_content:
            self.log("话术内容不能为空")
            messagebox.showwarning("保存话术", "话术内容不能为空")
            return

        # 确认是否保存
        if not messagebox.askyesno("确认", f"是否保存话术 '{self.current_script}'?"):
            return

        try:
            import requests
            import json

            # 发送请求保存话术内容
            payload = {
                "类型": "上传话术",
                "话术名": self.current_script,
                "上传数据": script_content
            }

            self.log(f"保存话术 '{self.current_script}'，请求数据: {json.dumps(payload, ensure_ascii=False)}")

            # 使用本地服务器
            response = requests.post("http://localhost:12456/", json=payload, timeout=10)

            # 打印响应内容
            self.log(f"保存话术响应状态码: {response.status_code}")
            try:
                response_json = response.json()
                self.log(f"保存话术响应内容: {json.dumps(response_json, ensure_ascii=False)}")
            except:
                self.log(f"保存话术响应内容: {response.text[:200]}...")

            # 检查响应状态码
            if response.status_code == 200:
                self.log(f"话术 '{self.current_script}' 保存成功")
                messagebox.showinfo("成功", f"话术 '{self.current_script}' 保存成功")
            else:
                self.log(f"话术 '{self.current_script}' 保存失败: HTTP {response.status_code}")
                messagebox.showwarning("警告", f"话术 '{self.current_script}' 保存可能失败，但本地内容已更新")
        except Exception as e:
            self.log(f"保存话术出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc(), "error")
            messagebox.showerror("错误", f"保存话术时发生错误: {str(e)}")

    def create_playback_control_area(self):
        """创建设置区域"""
        # 创建设置区域框架
        self.settings_frame = ttk.LabelFrame(self.settings_tab, text="系统设置")
        self.settings_frame.pack(fill=tk.BOTH, expand=True, padx=8, pady=8)

        # 创建标题栏
        title_frame = ttk.Frame(self.settings_frame)
        title_frame.pack(fill=tk.X, padx=10, pady=(10, 0))

        ttk.Label(title_frame, text="调整系统设置", style="Title.TLabel").pack(side=tk.LEFT)

        # 添加分隔线
        separator = ttk.Separator(self.settings_frame, orient="horizontal")
        separator.pack(fill=tk.X, padx=10, pady=(5, 10))

        # 创建设置区域
        settings_container = ttk.Frame(self.settings_frame)
        settings_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 使用网格布局，左右分为两列
        settings_container.columnconfigure(0, weight=1)
        settings_container.columnconfigure(1, weight=1)

        # 左侧：语音生成设置
        left_frame = ttk.LabelFrame(settings_container, text="语音生成设置")
        left_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5), pady=5)

        # 预备语音数量设置
        prepared_frame = ttk.Frame(left_frame)
        prepared_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(prepared_frame, text="预备语音数量:", style="Subtitle.TLabel").pack(side=tk.LEFT, padx=(0, 5))

        self.prepared_count_var = tk.StringVar(value="5")
        prepared_spinbox = ttk.Spinbox(prepared_frame, from_=1, to=20, textvariable=self.prepared_count_var, width=5)
        prepared_spinbox.pack(side=tk.LEFT, padx=5)

        # 循环模式设置
        loop_frame = ttk.Frame(left_frame)
        loop_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(loop_frame, text="循环模式:", style="Subtitle.TLabel").pack(side=tk.LEFT, padx=(0, 5))

        self.loop_mode_var = tk.StringVar(value="random")
        random_radio = ttk.Radiobutton(loop_frame, text="随机模式", variable=self.loop_mode_var, value="random")
        random_radio.pack(side=tk.LEFT, padx=5)

        sequential_radio = ttk.Radiobutton(loop_frame, text="顺序模式", variable=self.loop_mode_var, value="sequential")
        sequential_radio.pack(side=tk.LEFT, padx=5)

        # 游戏设置
        game_frame = ttk.Frame(left_frame)
        game_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(game_frame, text="游戏类型:", style="Subtitle.TLabel").pack(side=tk.LEFT, padx=(0, 5))
        self.game_type_var = tk.StringVar(value="")
        game_type_entry = ttk.Entry(game_frame, textvariable=self.game_type_var, width=15)
        game_type_entry.pack(side=tk.LEFT, padx=5)

        ttk.Label(game_frame, text="游戏名称:", style="Subtitle.TLabel").pack(side=tk.LEFT, padx=(10, 5))
        self.game_name_var = tk.StringVar(value="")
        game_name_entry = ttk.Entry(game_frame, textvariable=self.game_name_var, width=15)
        game_name_entry.pack(side=tk.LEFT, padx=5)

        # 右侧：声音设置
        right_frame = ttk.LabelFrame(settings_container, text="声音设置")
        right_frame.grid(row=0, column=1, sticky="nsew", padx=(5, 0), pady=5)

        # 系统声卡选择
        sound_card_frame = ttk.Frame(right_frame)
        sound_card_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(sound_card_frame, text="系统声卡:", style="Subtitle.TLabel").pack(side=tk.LEFT, padx=(0, 5))

        # 获取系统声卡列表
        sound_cards = self.get_sound_cards()

        self.sound_card_var = tk.StringVar(value=sound_cards[0] if sound_cards else "默认声卡")
        self.sound_card_combobox = ttk.Combobox(sound_card_frame, textvariable=self.sound_card_var, state="readonly", width=20)
        self.sound_card_combobox['values'] = sound_cards
        self.sound_card_combobox.current(0)
        self.sound_card_combobox.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # 音量设置
        volume_frame = ttk.Frame(right_frame)
        volume_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(volume_frame, text="音量:", style="Subtitle.TLabel").pack(side=tk.LEFT, padx=(0, 5))

        self.volume_var = tk.DoubleVar(value=1.0)
        volume_scale = ttk.Scale(volume_frame, from_=0.0, to=1.0, orient=tk.HORIZONTAL,
                               variable=self.volume_var, length=150)
        volume_scale.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # 显示当前音量值
        self.volume_label = ttk.Label(volume_frame, text="1.0", width=3)
        self.volume_label.pack(side=tk.LEFT, padx=5)

        # 添加音量值更新函数
        def update_volume_label(*_):
            value = self.volume_var.get()
            self.volume_label.config(text=f"{value:.1f}")
            # 实时更新音量
            if pygame.mixer.get_init():
                pygame.mixer.music.set_volume(value)

        self.volume_var.trace_add("write", update_volume_label)

        # 均衡器设置
        equalizer_frame = ttk.Frame(right_frame)
        equalizer_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(equalizer_frame, text="均衡器:", style="Subtitle.TLabel").pack(side=tk.LEFT, padx=(0, 5))

        # 创建均衡器预设选项
        self.equalizer_var = tk.StringVar(value="标准")
        equalizer_options = ["标准", "低音增强", "高音增强", "人声增强", "音乐增强"]
        self.equalizer_combobox = ttk.Combobox(equalizer_frame, textvariable=self.equalizer_var,
                                          values=equalizer_options, state="readonly", width=10)
        self.equalizer_combobox.pack(side=tk.LEFT, padx=5)

        # 均衡器选择事件
        def on_equalizer_selected(*_):
            selected = self.equalizer_var.get()
            self.log(f"已选择均衡器预设: {selected}")

        self.equalizer_var.trace_add("write", on_equalizer_selected)

        # 间隔设置
        interval_frame = ttk.Frame(right_frame)
        interval_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(interval_frame, text="间隔区间(秒):", style="Subtitle.TLabel").pack(side=tk.LEFT, padx=(0, 5))

        # 创建最小间隔输入框
        self.min_interval_var = tk.StringVar(value="0.5")
        ttk.Label(interval_frame, text="最小:").pack(side=tk.LEFT, padx=(5, 2))
        min_interval_entry = ttk.Entry(interval_frame, textvariable=self.min_interval_var, width=4)
        min_interval_entry.pack(side=tk.LEFT, padx=(0, 5))

        # 创建最大间隔输入框
        self.max_interval_var = tk.StringVar(value="2.0")
        ttk.Label(interval_frame, text="最大:").pack(side=tk.LEFT, padx=(5, 2))
        max_interval_entry = ttk.Entry(interval_frame, textvariable=self.max_interval_var, width=4)
        max_interval_entry.pack(side=tk.LEFT, padx=(0, 5))

        # 添加间隔值验证函数
        def validate_interval(*_):
            try:
                min_val = float(self.min_interval_var.get())
                max_val = float(self.max_interval_var.get())

                # 确保最小值不小于0
                if min_val < 0:
                    self.min_interval_var.set("0.0")
                    min_val = 0.0

                # 确保最大值不小于最小值
                if max_val < min_val:
                    self.max_interval_var.set(str(min_val))

                self.log(f"设置间隔区间: {min_val:.1f} - {max_val:.1f} 秒")
            except ValueError:
                # 如果输入不是数字，重置为默认值
                self.min_interval_var.set("0.5")
                self.max_interval_var.set("2.0")

        self.min_interval_var.trace_add("write", validate_interval)
        self.max_interval_var.trace_add("write", validate_interval)

        # 保存设置按钮
        save_settings_frame = ttk.Frame(self.settings_frame)
        save_settings_frame.pack(fill=tk.X, padx=10, pady=10)

        self.save_settings_button = ttk.Button(save_settings_frame, text="保存设置",
                                            command=self.save_settings_to_config,
                                            style="Success.TButton")
        self.save_settings_button.pack(side=tk.RIGHT, padx=5)

    def get_sound_cards(self):
        """获取系统声卡列表"""
        sound_cards = []

        # 先尝试使用pygame获取声卡列表
        try:
            # 初始化pygame混音器，使用更安全的参数
            if not pygame.mixer.get_init():
                try:
                    # 使用标准参数初始化
                    pygame.mixer.init(frequency=44100, size=-16, channels=2, buffer=4096)
                    self.log("Pygame混音器初始化成功（标准参数）")
                except Exception as init_error:
                    self.log(f"Pygame标准参数初始化失败: {str(init_error)}")
                    try:
                        # 尝试使用最简单的参数初始化
                        pygame.mixer.init(frequency=44100, size=-16, channels=1)
                        self.log("Pygame混音器初始化成功（简化参数）")
                    except Exception as simple_error:
                        self.log(f"Pygame简化参数初始化失败: {str(simple_error)}")
                        # 最后尝试无参数初始化
                        try:
                            pygame.mixer.init()
                            self.log("Pygame混音器初始化成功（无参数）")
                        except Exception as e:
                            self.log(f"Pygame无参数初始化失败: {str(e)}")
                            # 初始化失败，但继续执行

            # 检查是否成功初始化
            if pygame.mixer.get_init():
                try:
                    # 获取声卡数量
                    device_count = pygame.mixer.get_num_devices()
                    self.log(f"Pygame检测到 {device_count} 个音频设备")

                    # 获取声卡名称
                    for i in range(device_count):
                        try:
                            device_name = pygame.mixer.get_device_name(i)
                            sound_cards.append(device_name)
                            self.log(f"Pygame检测到设备: {device_name}")
                        except Exception as device_error:
                            self.log(f"获取设备 {i} 名称失败: {str(device_error)}")
                except Exception as count_error:
                    self.log(f"获取设备数量失败: {str(count_error)}")
            else:
                self.log("Pygame混音器初始化失败，无法获取声卡列表")
        except Exception as e:
            self.log(f"Pygame获取声卡失败: {str(e)}")

        # 如果没有找到声卡，尝试使用系统命令获取
        if not sound_cards:
            try:
                import subprocess
                import platform

                system = platform.system()
                self.log(f"当前操作系统: {system}")

                if system == "Windows":
                    # 在Windows上使用PowerShell获取声卡列表
                    cmd = "Get-WmiObject Win32_SoundDevice | Select-Object Name | Format-Table -HideTableHeaders"
                    result = subprocess.run(["powershell", "-Command", cmd], capture_output=True, text=True)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            line = line.strip()
                            if line:
                                sound_cards.append(line)
                                self.log(f"系统命令检测到设备: {line}")
                elif system == "Linux":
                    # 在Linux上使用aplay -l获取声卡列表
                    result = subprocess.run(["aplay", "-l"], capture_output=True, text=True)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line.startswith('card '):
                                parts = line.split(':')
                                if len(parts) > 1:
                                    sound_cards.append(parts[1].strip())
                                    self.log(f"系统命令检测到设备: {parts[1].strip()}")
                elif system == "Darwin":  # macOS
                    # 在macOS上使用system_profiler获取声卡列表
                    result = subprocess.run(["system_profiler", "SPAudioDataType"], capture_output=True, text=True)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for i, line in enumerate(lines):
                            if "Output" in line and i+1 < len(lines):
                                sound_cards.append(lines[i+1].strip())
                                self.log(f"系统命令检测到设备: {lines[i+1].strip()}")
            except Exception as e:
                self.log(f"系统命令获取声卡失败: {str(e)}")

        # 如果仍然没有找到声卡，添加默认声卡
        if not sound_cards:
            sound_cards = ["默认声卡"]
            self.log("未检测到声卡，使用默认声卡")

        # 添加默认声卡选项
        if "默认声卡" not in sound_cards:
            sound_cards.insert(0, "默认声卡")

        return sound_cards

    def create_playlist_area(self):
        """创建播放列表区"""
        # 创建播放列表区框架
        self.playlist_frame = ttk.LabelFrame(self.ai_host_tab, text="播放列表")
        self.playlist_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(5, 10))

        # 创建标题栏
        title_frame = ttk.Frame(self.playlist_frame)
        title_frame.pack(fill=tk.X, padx=10, pady=(5, 0))

        ttk.Label(title_frame, text="待播放语音列表", style="Title.TLabel").pack(side=tk.LEFT)

        # 添加分隔线
        separator = ttk.Separator(self.playlist_frame, orient="horizontal")
        separator.pack(fill=tk.X, padx=10, pady=(5, 5))

        # 创建播放列表显示区
        playlist_container = ttk.Frame(self.playlist_frame)
        playlist_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 设置行列权重，使列表可以自适应缩放
        playlist_container.grid_rowconfigure(0, weight=1)
        playlist_container.grid_columnconfigure(0, weight=1)

        # 创建播放列表框，使用固定高度并确保在窗口大小内完整显示
        self.playlist_listbox = tk.Listbox(playlist_container,
                                       font=("Microsoft YaHei UI", 9),  # 减小字体大小
                                       activestyle="none",
                                       selectbackground=self.primary_color,
                                       selectforeground="white",
                                       borderwidth=1,
                                       relief="solid",
                                       bg="white",
                                       height=6)  # 进一步减小高度
        self.playlist_listbox.grid(row=0, column=0, sticky="nsew")

        # 添加滚动条
        playlist_scrollbar = ttk.Scrollbar(playlist_container, orient=tk.VERTICAL, command=self.playlist_listbox.yview)
        self.playlist_listbox.configure(yscrollcommand=playlist_scrollbar.set)
        playlist_scrollbar.grid(row=0, column=1, sticky="ns")

        # 插入占位文本
        self.playlist_listbox.insert(tk.END, "播放列表为空...")

        # 添加列表操作按钮区域
        buttons_frame = ttk.Frame(self.playlist_frame)
        buttons_frame.pack(fill=tk.X, padx=10, pady=(2, 5))

        # 清空列表按钮
        clear_button = ttk.Button(buttons_frame, text="清空列表", command=self.clear_playlist)
        clear_button.pack(side=tk.RIGHT, padx=5)

        # 移除选中项按钮
        remove_button = ttk.Button(buttons_frame, text="移除选中项", command=self.remove_selected_item)
        remove_button.pack(side=tk.RIGHT, padx=5)

        # 绑定窗口大小变化事件，动态调整列表框大小
        self.root.bind("<Configure>", self.adjust_playlist_height)

    def clear_playlist(self):
        """清空播放列表"""
        self.playlist_listbox.delete(0, tk.END)
        self.playlist_listbox.insert(tk.END, "播放列表为空...")
        self.log("已清空播放列表")

    def remove_selected_item(self):
        """移除选中的播放列表项"""
        selected = self.playlist_listbox.curselection()
        if not selected:
            return

        # 获取选中项的内容
        item_text = self.playlist_listbox.get(selected[0])

        # 如果是占位文本，不删除
        if item_text == "播放列表为空...":
            return

        # 删除选中项
        self.playlist_listbox.delete(selected[0])

        # 如果列表为空，添加占位文本
        if self.playlist_listbox.size() == 0:
            self.playlist_listbox.insert(tk.END, "播放列表为空...")

        self.log(f"已移除播放列表项: {item_text[:30]}...")


    def on_obs_frame_configure(self, event):
        """当OBS内容框架大小变化时更新滚动区域"""
        # 更新滚动区域以包含所有内容
        self.obs_canvas.configure(scrollregion=self.obs_canvas.bbox("all"))

    def on_obs_canvas_configure(self, event):
        """当OBS画布大小变化时调整内容框架宽度"""
        # 调整内容框架宽度以匹配画布宽度
        self.obs_canvas.itemconfig(self.obs_canvas_window, width=event.width)

    def create_obs_control_area(self):
        """创建OBS控制区"""
        # 导入OBS API模块
        try:
            # 先检查模块是否存在
            import importlib.util
            obs_spec = importlib.util.find_spec("obs_media_api")

            if obs_spec is not None:
                # 模块存在，尝试导入
                from obs_media_api import OBSMediaAPI
                self.obs_api_available = True
                self.log("成功导入OBS API模块")
            else:
                # 模块不存在
                self.obs_api_available = False
                self.log("警告: OBS API模块未找到，OBS控制功能将不可用")
        except ImportError as e:
            self.obs_api_available = False
            self.log(f"警告: OBS API模块导入失败: {str(e)}，OBS控制功能将不可用")

        # 创建OBS控制区框架
        self.obs_frame = ttk.LabelFrame(self.obs_tab, text="OBS控制区")
        self.obs_frame.pack(fill=tk.BOTH, expand=True, padx=8, pady=8)

        # 创建标题栏
        title_frame = ttk.Frame(self.obs_frame)
        title_frame.pack(fill=tk.X, padx=10, pady=(5, 0))

        ttk.Label(title_frame, text="OBS直播控制", style="Title.TLabel").pack(side=tk.LEFT)

        # 添加分隔线
        separator = ttk.Separator(self.obs_frame, orient="horizontal")
        separator.pack(fill=tk.X, padx=10, pady=(5, 5))

        # 创建滚动区域容器，使得内容可以滚动
        self.obs_canvas = tk.Canvas(self.obs_frame, highlightthickness=0)
        self.obs_canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 添加垂直滚动条
        obs_scrollbar = ttk.Scrollbar(self.obs_frame, orient="vertical", command=self.obs_canvas.yview)
        obs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.obs_canvas.configure(yscrollcommand=obs_scrollbar.set)

        # 创建内容框架
        self.obs_content_frame = ttk.Frame(self.obs_canvas)
        self.obs_canvas_window = self.obs_canvas.create_window((0, 0), window=self.obs_content_frame, anchor="nw")

        # 绑定事件以更新滚动区域
        self.obs_content_frame.bind("<Configure>", self.on_obs_frame_configure)
        self.obs_canvas.bind("<Configure>", self.on_obs_canvas_configure)

        # 创建连接设置区域
        connection_frame = ttk.LabelFrame(self.obs_content_frame, text="连接设置")
        connection_frame.pack(fill=tk.X, padx=10, pady=5)

        # 创建连接设置表单
        settings_frame = ttk.Frame(connection_frame)
        settings_frame.pack(fill=tk.X, padx=10, pady=10)

        # 主机设置
        ttk.Label(settings_frame, text="主机:", width=8).grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.obs_host_var = tk.StringVar(value="localhost")
        ttk.Entry(settings_frame, textvariable=self.obs_host_var, width=15).grid(row=0, column=1, padx=5, pady=5, sticky="w")

        # 端口设置
        ttk.Label(settings_frame, text="端口:", width=8).grid(row=0, column=2, padx=5, pady=5, sticky="w")
        self.obs_port_var = tk.StringVar(value="4455")
        ttk.Entry(settings_frame, textvariable=self.obs_port_var, width=8).grid(row=0, column=3, padx=5, pady=5, sticky="w")

        # 密码设置
        ttk.Label(settings_frame, text="密码:", width=8).grid(row=0, column=4, padx=5, pady=5, sticky="w")
        self.obs_password_var = tk.StringVar(value="")
        ttk.Entry(settings_frame, textvariable=self.obs_password_var, width=15, show="*").grid(row=0, column=5, padx=5, pady=5, sticky="w")

        # 连接按钮
        button_frame = ttk.Frame(connection_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        self.obs_connect_button = ttk.Button(button_frame, text="连接OBS", command=self.connect_to_obs, style="Primary.TButton")
        self.obs_connect_button.pack(side=tk.LEFT, padx=5)

        self.obs_disconnect_button = ttk.Button(button_frame, text="断开连接", command=self.disconnect_from_obs)
        self.obs_disconnect_button.pack(side=tk.LEFT, padx=5)
        self.obs_disconnect_button.config(state="disabled")  # 初始状态为禁用

        # 创建状态显示
        status_frame = ttk.Frame(connection_frame)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Label(status_frame, text="连接状态:").pack(side=tk.LEFT, padx=(0, 5))
        self.obs_status_var = tk.StringVar(value="未连接")
        ttk.Label(status_frame, textvariable=self.obs_status_var, foreground="red").pack(side=tk.LEFT)

        # 创建媒体源控制区域
        self.media_frame = ttk.LabelFrame(self.obs_content_frame, text="媒体源控制")
        self.media_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 创建主副视频设置区域
        video_settings_frame = ttk.Frame(self.media_frame)
        video_settings_frame.pack(fill=tk.X, padx=10, pady=10)

        # 创建主视频设置
        main_video_frame = ttk.LabelFrame(video_settings_frame, text="主视频设置")
        main_video_frame.pack(fill=tk.X, padx=5, pady=5)

        # 主视频源A选择
        main_source_frame = ttk.Frame(main_video_frame)
        main_source_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(main_source_frame, text="主视频源A:").pack(side=tk.LEFT, padx=(0, 5))
        self.main_video_source_var = tk.StringVar()
        self.main_video_source_combobox = ttk.Combobox(main_source_frame, textvariable=self.main_video_source_var, state="readonly", width=30)
        self.main_video_source_combobox.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.main_video_source_combobox.bind("<<ComboboxSelected>>", self.on_main_video_source_selected)

        # 主视频源B选择
        main_source_b_frame = ttk.Frame(main_video_frame)
        main_source_b_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(main_source_b_frame, text="主视频源B:").pack(side=tk.LEFT, padx=(0, 5))
        self.main_video_source_b_var = tk.StringVar()
        self.main_video_source_b_combobox = ttk.Combobox(main_source_b_frame, textvariable=self.main_video_source_b_var, state="readonly", width=30)
        self.main_video_source_b_combobox.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.main_video_source_b_combobox.bind("<<ComboboxSelected>>", self.on_main_video_source_b_selected)

        # 启用双主视频源模式复选框
        self.use_dual_main_video_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(main_source_b_frame, text="启用双主视频源模式（避免变速黑屏）", variable=self.use_dual_main_video_var).pack(side=tk.LEFT, padx=5)

        # 主视频速度区间设置
        speed_range_frame = ttk.Frame(main_video_frame)
        speed_range_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(speed_range_frame, text="速度区间:").pack(side=tk.LEFT, padx=(0, 5))

        # 最小速度
        self.main_video_speed_min_var = tk.DoubleVar(value=0.8)
        ttk.Entry(speed_range_frame, textvariable=self.main_video_speed_min_var, width=5).pack(side=tk.LEFT, padx=2)

        ttk.Label(speed_range_frame, text="-").pack(side=tk.LEFT, padx=2)

        # 最大速度
        self.main_video_speed_max_var = tk.DoubleVar(value=1.2)
        ttk.Entry(speed_range_frame, textvariable=self.main_video_speed_max_var, width=5).pack(side=tk.LEFT, padx=2)

        # 当前速度显示
        ttk.Label(speed_range_frame, text="当前速度:").pack(side=tk.LEFT, padx=(10, 2))
        self.current_video_speed_var = tk.StringVar(value="1.0x")

        # 添加音频播放相关参数
        self.audio_preload_delay = 100  # 音频预加载延迟时间（毫秒）
        self.audio_buffer_size = 8192   # 音频缓冲区大小
        ttk.Label(speed_range_frame, textvariable=self.current_video_speed_var).pack(side=tk.LEFT, padx=2)

        # 主视频循环播放按钮
        main_video_control_frame = ttk.Frame(main_video_frame)
        main_video_control_frame.pack(fill=tk.X, padx=5, pady=5)

        self.start_main_video_button = ttk.Button(main_video_control_frame, text="开始循环播放", command=self.start_main_video_loop, style="Primary.TButton")
        self.start_main_video_button.pack(side=tk.LEFT, padx=5)
        # 初始状态为禁用，但在连接OBS后会启用
        self.start_main_video_button.config(state="disabled")

        self.stop_main_video_button = ttk.Button(main_video_control_frame, text="停止循环播放", command=self.stop_main_video_loop)
        self.stop_main_video_button.pack(side=tk.LEFT, padx=5)
        self.stop_main_video_button.config(state="disabled")  # 初始状态为禁用

        # 创建副视频设置
        sub_video_frame = ttk.LabelFrame(video_settings_frame, text="副视频设置")
        sub_video_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 副视频关键词和媒体源映射表
        sub_video_list_frame = ttk.Frame(sub_video_frame)
        sub_video_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 设置行列权重，使表格可以自适应缩放
        sub_video_list_frame.grid_rowconfigure(0, weight=1)
        sub_video_list_frame.grid_columnconfigure(0, weight=1)

        # 创建表格显示副视频映射，使用较小的高度确保完整显示
        columns = ("关键词", "媒体源", "播放时间")
        self.sub_video_treeview = ttk.Treeview(sub_video_list_frame, columns=columns, show="headings", height=3)
        self.sub_video_treeview.heading("关键词", text="关键词")
        self.sub_video_treeview.heading("媒体源", text="媒体源")
        self.sub_video_treeview.heading("播放时间", text="播放时间")
        self.sub_video_treeview.column("关键词", width=90)
        self.sub_video_treeview.column("媒体源", width=120)
        self.sub_video_treeview.column("播放时间", width=60)
        self.sub_video_treeview.grid(row=0, column=0, sticky="nsew")

        # 添加滚动条
        sub_video_scrollbar = ttk.Scrollbar(sub_video_list_frame, orient=tk.VERTICAL, command=self.sub_video_treeview.yview)
        sub_video_scrollbar.grid(row=0, column=1, sticky="ns")
        self.sub_video_treeview.configure(yscrollcommand=sub_video_scrollbar.set)

        # 副视频添加/删除按钮
        sub_video_buttons_frame = ttk.Frame(sub_video_frame)
        sub_video_buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        self.add_sub_video_button = ttk.Button(sub_video_buttons_frame, text="添加副视频", command=self.add_sub_video_mapping)
        self.add_sub_video_button.pack(side=tk.LEFT, padx=5)
        self.add_sub_video_button.config(state="disabled")  # 初始状态为禁用

        self.delete_sub_video_button = ttk.Button(sub_video_buttons_frame, text="删除副视频", command=self.delete_sub_video_mapping)
        self.delete_sub_video_button.pack(side=tk.LEFT, padx=5)
        self.delete_sub_video_button.config(state="disabled")  # 初始状态为禁用

        # 添加测试副视频触发按钮
        self.test_sub_video_button = ttk.Button(sub_video_buttons_frame, text="测试副视频触发", command=self.test_sub_video_trigger, style="Accent.TButton")
        self.test_sub_video_button.pack(side=tk.LEFT, padx=5)
        self.test_sub_video_button.config(state="disabled")  # 初始状态为禁用

        # 创建媒体源列表区域
        sources_frame = ttk.Frame(self.media_frame)
        sources_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(sources_frame, text="媒体源:").pack(side=tk.LEFT, padx=(0, 5))
        self.media_source_var = tk.StringVar()
        self.media_source_combobox = ttk.Combobox(sources_frame, textvariable=self.media_source_var, state="readonly", width=30)
        self.media_source_combobox.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.media_source_combobox.bind("<<ComboboxSelected>>", self.on_media_source_selected)

        # 刷新媒体源按钮
        self.refresh_sources_button = ttk.Button(sources_frame, text="刷新", command=self.refresh_media_sources)
        self.refresh_sources_button.pack(side=tk.LEFT, padx=5)
        self.refresh_sources_button.config(state="disabled")  # 初始状态为禁用

        # 创建媒体控制按钮区域
        controls_frame = ttk.Frame(self.media_frame)
        controls_frame.pack(fill=tk.X, padx=10, pady=10)

        # 播放按钮
        self.media_play_button = ttk.Button(controls_frame, text="播放", command=self.play_media, style="Success.TButton")
        self.media_play_button.pack(side=tk.LEFT, padx=5)
        self.media_play_button.config(state="disabled")  # 初始状态为禁用

        # 暂停按钮
        self.media_pause_button = ttk.Button(controls_frame, text="暂停", command=self.pause_media)
        self.media_pause_button.pack(side=tk.LEFT, padx=5)
        self.media_pause_button.config(state="disabled")  # 初始状态为禁用

        # 停止按钮
        self.media_stop_button = ttk.Button(controls_frame, text="停止", command=self.stop_media)
        self.media_stop_button.pack(side=tk.LEFT, padx=5)
        self.media_stop_button.config(state="disabled")  # 初始状态为禁用

        # 显示源按钮
        self.show_source_button = ttk.Button(controls_frame, text="显示源", command=self.show_selected_source, style="Primary.TButton")
        self.show_source_button.pack(side=tk.LEFT, padx=5)
        self.show_source_button.config(state="disabled")  # 初始状态为禁用

        # 创建媒体速度控制区域
        speed_frame = ttk.Frame(self.media_frame)
        speed_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(speed_frame, text="播放速度:").pack(side=tk.LEFT, padx=(0, 5))

        # 创建速度滑块
        self.media_speed_var = tk.DoubleVar(value=1.0)
        self.media_speed_scale = ttk.Scale(speed_frame, from_=0.5, to=2.0, orient=tk.HORIZONTAL,
                                   variable=self.media_speed_var, length=200)
        self.media_speed_scale.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.media_speed_scale.config(state="disabled")  # 初始状态为禁用

        # 添加速度值更新函数
        def update_speed_label(*_):
            value = self.media_speed_var.get()
            self.media_speed_label.config(text=f"{value:.1f}x")
            # 如果已连接，实时更新速度
            if hasattr(self, "obs_api") and self.obs_api:
                self.set_media_speed()

        self.media_speed_var.trace_add("write", update_speed_label)

        # 显示当前速度值
        self.media_speed_label = ttk.Label(speed_frame, text="1.0x", width=5)
        self.media_speed_label.pack(side=tk.LEFT, padx=5)

        # 应用速度按钮
        self.apply_speed_button = ttk.Button(speed_frame, text="应用速度", command=self.set_media_speed)
        self.apply_speed_button.pack(side=tk.LEFT, padx=5)
        self.apply_speed_button.config(state="disabled")  # 初始状态为禁用

        # 创建媒体状态显示区域
        status_frame = ttk.LabelFrame(self.media_frame, text="媒体状态")
        status_frame.pack(fill=tk.X, padx=10, pady=10)

        # 创建状态显示
        media_status_frame = ttk.Frame(status_frame)
        media_status_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(media_status_frame, text="播放状态:").grid(row=0, column=0, padx=5, pady=2, sticky="w")
        self.media_state_var = tk.StringVar(value="未知")
        ttk.Label(media_status_frame, textvariable=self.media_state_var).grid(row=0, column=1, padx=5, pady=2, sticky="w")

        ttk.Label(media_status_frame, text="总时长:").grid(row=1, column=0, padx=5, pady=2, sticky="w")
        self.media_duration_var = tk.StringVar(value="0:00")
        ttk.Label(media_status_frame, textvariable=self.media_duration_var).grid(row=1, column=1, padx=5, pady=2, sticky="w")

        ttk.Label(media_status_frame, text="当前位置:").grid(row=2, column=0, padx=5, pady=2, sticky="w")
        self.media_position_var = tk.StringVar(value="0:00")
        ttk.Label(media_status_frame, textvariable=self.media_position_var).grid(row=2, column=1, padx=5, pady=2, sticky="w")

        ttk.Label(media_status_frame, text="进度:").grid(row=3, column=0, padx=5, pady=2, sticky="w")
        self.media_progress_var = tk.DoubleVar(value=0)
        self.media_progress_bar = ttk.Progressbar(media_status_frame, variable=self.media_progress_var, length=200, mode="determinate")
        self.media_progress_bar.grid(row=3, column=1, padx=5, pady=2, sticky="w")

        # 创建刷新状态按钮
        self.refresh_status_button = ttk.Button(status_frame, text="刷新状态", command=self.refresh_media_status)
        self.refresh_status_button.pack(side=tk.RIGHT, padx=10, pady=(0, 10))
        self.refresh_status_button.config(state="disabled")  # 初始状态为禁用

        # 初始化OBS API对象为None
        self.obs_api = None
        self.current_media_source = None

        # 设置定时刷新媒体状态
        self.media_status_timer = None

    def connect_to_obs(self):
        """连接到OBS WebSocket服务器"""
        try:
            # 先检查模块是否存在
            import importlib.util

            # 检查 obs_media_api 模块
            obs_api_spec = importlib.util.find_spec("obs_media_api")
            if obs_api_spec is None:
                self.log("错误: OBS API模块未找到，无法连接到OBS", "error")
                messagebox.showerror("连接失败", "OBS API模块未找到，请确保已安装 obs_media_api 模块")
                return

            # 检查 obsws_python 模块
            obsws_spec = importlib.util.find_spec("obsws_python")
            if obsws_spec is None:
                self.log("错误: obsws_python 模块未找到，无法连接到OBS", "error")
                messagebox.showerror("连接失败", "obsws_python 模块未找到，请确保已安装 obsws_python 模块")
                return

            # 导入 OBS API 模块
            from obs_media_api import OBSMediaAPI

            # 获取连接参数
            host = self.obs_host_var.get()
            port = int(self.obs_port_var.get())
            password = self.obs_password_var.get()

            self.log(f"正在连接到OBS WebSocket服务器 {host}:{port}...", "obs")

            # 创建OBS API实例
            self.obs_api = OBSMediaAPI(host=host, port=port, password=password)

            # 更新UI状态
            self.obs_status_var.set("已连接")
            self.obs_connect_button.config(state="disabled")
            self.obs_disconnect_button.config(state="normal")
            self.refresh_sources_button.config(state="normal")
            self.add_sub_video_button.config(state="normal")
            self.delete_sub_video_button.config(state="normal")
            self.test_sub_video_button.config(state="normal")  # 启用测试副视频触发按钮

            # 如果已经选择了主视频源，启用开始循环播放按钮
            if hasattr(self, "main_video_source") and self.main_video_source:
                self.log(f"已有主视频源: {self.main_video_source}，启用开始循环播放按钮")
                self.start_main_video_button.config(state="normal")
            else:
                self.log("未选择主视频源，开始循环播放按钮保持禁用状态")

            # 刷新媒体源列表
            self.refresh_media_sources()

            # 如果有保存的副视频映射，显示在UI上
            if hasattr(self, "sub_video_sources") and self.sub_video_sources and hasattr(self, "sub_video_treeview"):
                # 清空表格
                self.sub_video_treeview.delete(*self.sub_video_treeview.get_children())

                # 添加映射
                for keyword, value in self.sub_video_sources.items():
                    if isinstance(value, dict):
                        # 新格式：字典包含媒体源和播放时间
                        source = value.get('source', '')
                        duration = value.get('duration', 5)
                        self.sub_video_treeview.insert("", tk.END, values=(keyword, source, f"{duration}秒"))
                    else:
                        # 兼容旧格式：直接存储媒体源
                        source = value
                        self.sub_video_treeview.insert("", tk.END, values=(keyword, source, "5秒"))

                        # 将旧格式转换为新格式
                        self.sub_video_sources[keyword] = {
                            "source": source,
                            "duration": 5  # 默认播放时间
                        }
                        self.log(f"已将副视频映射转换为新格式: {keyword} -> {self.sub_video_sources[keyword]}")

                self.log(f"已加载{len(self.sub_video_sources)}个副视频映射", "obs")

            # 启动定时刷新媒体状态
            self.start_media_status_timer()

            self.log("成功连接到OBS WebSocket服务器", "obs")
        except Exception as e:
            self.log(f"连接OBS WebSocket服务器失败: {str(e)}", "error")
            messagebox.showerror("连接失败", f"连接OBS WebSocket服务器失败: {str(e)}")

    def disconnect_from_obs(self):
        """断开与OBS WebSocket服务器的连接"""
        if self.obs_api:
            try:
                # 停止定时刷新
                self.stop_media_status_timer()

                # 停止主视频循环
                self.stop_main_video_loop()

                # 关闭连接
                self.obs_api.close()
                self.obs_api = None

                # 更新UI状态
                self.obs_status_var.set("未连接")
                self.obs_connect_button.config(state="normal")
                self.obs_disconnect_button.config(state="disabled")
                self.refresh_sources_button.config(state="disabled")
                self.media_play_button.config(state="disabled")
                self.media_pause_button.config(state="disabled")
                self.media_stop_button.config(state="disabled")
                self.show_source_button.config(state="disabled")
                self.media_speed_scale.config(state="disabled")
                self.apply_speed_button.config(state="disabled")
                self.refresh_status_button.config(state="disabled")
                self.add_sub_video_button.config(state="disabled")
                self.delete_sub_video_button.config(state="disabled")
                self.test_sub_video_button.config(state="disabled")  # 禁用测试副视频触发按钮
                self.start_main_video_button.config(state="disabled")
                self.stop_main_video_button.config(state="disabled")

                # 清空媒体源列表
                self.media_source_combobox['values'] = []
                self.media_source_var.set("")
                self.main_video_source_combobox['values'] = []
                self.main_video_source_var.set("")

                # 清空副视频映射表
                self.sub_video_treeview.delete(*self.sub_video_treeview.get_children())
                self.sub_video_sources = {}

                # 重置媒体状态
                self.media_state_var.set("未知")
                self.media_duration_var.set("0:00")
                self.media_position_var.set("0:00")
                self.media_progress_var.set(0)
                self.current_video_speed_var.set("1.0x")

                self.log("已断开与OBS WebSocket服务器的连接", "obs")
            except Exception as e:
                self.log(f"断开OBS WebSocket服务器连接时出错: {str(e)}", "error")

    def refresh_media_sources(self):
        """刷新媒体源列表"""
        if not self.obs_api:
            return

        try:
            # 获取媒体源列表
            media_sources = self.obs_api.get_media_sources()
            self.log(f"获取到媒体源列表，类型: {type(media_sources)}")

            # 检查media_sources是字符串列表还是字典列表
            if media_sources and isinstance(media_sources, list):
                if media_sources and isinstance(media_sources[0], dict):
                    # 如果是字典列表
                    source_names = [source.get('inputName', 'Unknown') for source in media_sources]
                    self.log(f"从字典列表提取媒体源名称")
                else:
                    # 如果是字符串列表
                    source_names = media_sources
                    self.log(f"直接使用字符串列表作为媒体源名称")

                self.log(f"媒体源名称: {source_names}")

                # 更新所有媒体源下拉框
                self.media_source_combobox['values'] = source_names
                self.main_video_source_combobox['values'] = source_names

                # 更新主视频源B下拉框
                if hasattr(self, "main_video_source_b_combobox"):
                    self.main_video_source_b_combobox['values'] = source_names
                    self.log("已更新主视频源B下拉框")

                # 如果有媒体源，选择第一个
                if source_names:
                    # 更新当前媒体源
                    self.media_source_combobox.current(0)
                    self.on_media_source_selected(None)

                    # 如果主视频源A为空，选择第一个
                    if not self.main_video_source:
                        self.main_video_source_combobox.current(0)
                        self.on_main_video_source_selected(None)
                        # 启用开始循环播放按钮
                        self.log(f"已自动选择主视频源A: {source_names[0]}，启用开始循环播放按钮")
                        self.start_main_video_button.config(state="normal")
                    else:
                        # 如果已经有主视频源，也启用开始循环播放按钮
                        self.log(f"已有主视频源: {self.main_video_source}，启用开始循环播放按钮")
                        self.start_main_video_button.config(state="normal")

                    # 如果主视频源B为空，选择第二个（如果有）或第一个
                    if hasattr(self, "main_video_source_b_var") and not self.main_video_source_b_var.get():
                        if len(source_names) > 1:
                            self.main_video_source_b_combobox.current(1)
                            self.log(f"已自动选择第二个媒体源作为主视频源B: {source_names[1]}")
                        else:
                            self.main_video_source_b_combobox.current(0)
                            self.log(f"只有一个媒体源，将其同时用作主视频源A和B: {source_names[0]}")

                self.log(f"成功获取到 {len(source_names)} 个媒体源")
            else:
                self.log(f"没有找到媒体源或格式不正确: {media_sources}")
        except Exception as e:
            self.log(f"获取媒体源列表失败: {str(e)}")
            import traceback
            self.log(traceback.format_exc())

    def on_media_source_selected(self, _):
        """当选择媒体源时触发"""
        if not self.obs_api:
            return

        selected_index = self.media_source_combobox.current()
        if selected_index >= 0:
            self.current_media_source = self.media_source_combobox.get()
            self.log(f"已选择媒体源: {self.current_media_source}")

            # 启用控制按钮
            self.media_play_button.config(state="normal")
            self.media_pause_button.config(state="normal")
            self.media_stop_button.config(state="normal")
            self.show_source_button.config(state="normal")
            self.media_speed_scale.config(state="normal")
            self.apply_speed_button.config(state="normal")
            self.refresh_status_button.config(state="normal")

            # 刷新媒体状态
            self.refresh_media_status()

    def play_media(self):
        """播放选中的媒体源"""
        if not self.obs_api or not self.current_media_source:
            return

        try:
            self.obs_api.play_media(self.current_media_source)
            self.log(f"正在播放媒体: {self.current_media_source}")
            # 刷新媒体状态
            self.refresh_media_status()
        except Exception as e:
            self.log(f"播放媒体失败: {str(e)}")

    def pause_media(self):
        """暂停选中的媒体源"""
        if not self.obs_api or not self.current_media_source:
            return

        try:
            self.obs_api.pause_media(self.current_media_source)
            self.log(f"已暂停媒体: {self.current_media_source}")
            # 刷新媒体状态
            self.refresh_media_status()
        except Exception as e:
            self.log(f"暂停媒体失败: {str(e)}")

    def stop_media(self):
        """停止选中的媒体源"""
        if not self.obs_api or not self.current_media_source:
            return

        try:
            self.obs_api.stop_media(self.current_media_source)
            self.log(f"已停止媒体: {self.current_media_source}")
            # 刷新媒体状态
            self.refresh_media_status()
        except Exception as e:
            self.log(f"停止媒体失败: {str(e)}")

    def show_selected_source(self):
        """显示选中的媒体源"""
        if not self.obs_api or not self.current_media_source:
            return

        try:
            self.obs_api.show_source(self.current_media_source)
            self.log(f"已显示媒体源: {self.current_media_source}")
        except Exception as e:
            self.log(f"显示媒体源失败: {str(e)}")

    def set_media_speed(self):
        """设置媒体播放速度"""
        if not self.obs_api or not self.current_media_source:
            return

        try:
            speed = self.media_speed_var.get()
            self.obs_api.set_media_speed(self.current_media_source, speed)
            self.log(f"已将媒体 {self.current_media_source} 的播放速度设置为 {speed}x")
        except Exception as e:
            self.log(f"设置媒体速度失败: {str(e)}")

    def refresh_media_status(self):
        """刷新媒体状态"""
        if not self.obs_api or not self.current_media_source:
            return

        try:
            status = self.obs_api.get_media_status(self.current_media_source)
            if status:
                # 更新状态显示
                self.media_state_var.set(status['state'])

                # 格式化时间显示
                duration_ms = status['duration']
                position_ms = status['position']
                duration_sec = duration_ms / 1000
                position_sec = position_ms / 1000

                duration_min = int(duration_sec // 60)
                duration_sec = int(duration_sec % 60)
                position_min = int(position_sec // 60)
                position_sec = int(position_sec % 60)

                self.media_duration_var.set(f"{duration_min}:{duration_sec:02d}")
                self.media_position_var.set(f"{position_min}:{position_sec:02d}")

                # 更新进度条
                self.media_progress_var.set(status['progress_percent'])

                # 更新当前速度显示
                try:
                    current_speed = self.obs_api.get_media_speed(self.current_media_source)
                    if current_speed and hasattr(self, "current_video_speed_var"):
                        self.current_video_speed_var.set(f"{current_speed:.2f}x")
                        self.log(f"更新当前速度显示: {current_speed:.2f}x", "obs")
                except Exception as speed_error:
                    self.log(f"获取当前速度出错: {str(speed_error)}", "error")
            else:
                # 重置状态显示
                self.media_state_var.set("未播放")
                self.media_duration_var.set("0:00")
                self.media_position_var.set("0:00")
                self.media_progress_var.set(0)
        except Exception as e:
            self.log(f"刷新媒体状态失败: {str(e)}", "error")

    def start_media_status_timer(self):
        """启动定时刷新媒体状态"""
        # 停止现有的定时器
        self.stop_media_status_timer()

        # 创建新的定时器
        def update_status():
            if self.obs_api and self.current_media_source:
                self.refresh_media_status()
            # 继续定时刷新
            self.media_status_timer = self.root.after(1000, update_status)

        # 启动定时器
        self.media_status_timer = self.root.after(1000, update_status)

    def stop_media_status_timer(self):
        """停止定时刷新媒体状态"""
        if self.media_status_timer:
            self.root.after_cancel(self.media_status_timer)
            self.media_status_timer = None

    def on_main_video_source_selected(self, _):
        """当选择主视频源A时触发"""
        if not self.obs_api:
            return

        selected_index = self.main_video_source_combobox.current()
        if selected_index >= 0:
            self.main_video_source = self.main_video_source_combobox.get()
            self.main_video_source_a = self.main_video_source  # 同时更新主视频源A
            self.log(f"已选择主视频源A: {self.main_video_source}")

            # 启用主视频循环播放按钮
            if hasattr(self, "start_main_video_button"):
                self.start_main_video_button.config(state="normal")

            # 保存设置
            self.save_settings_to_config()

    def on_main_video_source_b_selected(self, _):
        """当选择主视频源B时触发"""
        if not self.obs_api:
            return

        if hasattr(self, "main_video_source_b_combobox"):
            selected_index = self.main_video_source_b_combobox.current()
            if selected_index >= 0:
                self.main_video_source_b = self.main_video_source_b_combobox.get()
                self.log(f"已选择主视频源B: {self.main_video_source_b}")

                # 保存设置
                self.save_settings_to_config()

    def add_sub_video_mapping(self):
        """添加副视频关键词和媒体源映射"""
        if not self.obs_api:
            return

        # 创建添加副视频的对话框
        add_dialog = tk.Toplevel(self.root)
        add_dialog.title("添加副视频映射")
        add_dialog.geometry("400x250")
        add_dialog.transient(self.root)  # 设置为主窗口的子窗口
        add_dialog.grab_set()  # 模态对话框

        # 创建关键词输入框
        keyword_frame = ttk.Frame(add_dialog, padding=10)
        keyword_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(keyword_frame, text="关键词:").pack(side=tk.LEFT, padx=(0, 5))
        keyword_entry = ttk.Entry(keyword_frame, width=30)
        keyword_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        keyword_entry.focus_set()  # 设置焦点

        # 创建媒体源选择
        source_frame = ttk.Frame(add_dialog, padding=10)
        source_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(source_frame, text="媒体源:").pack(side=tk.LEFT, padx=(0, 5))
        source_var = tk.StringVar()
        source_combobox = ttk.Combobox(source_frame, textvariable=source_var, state="readonly", width=30)
        source_combobox.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # 创建播放时间输入框
        duration_frame = ttk.Frame(add_dialog, padding=10)
        duration_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(duration_frame, text="播放时间(秒):").pack(side=tk.LEFT, padx=(0, 5))
        duration_var = tk.StringVar(value="5")
        duration_entry = ttk.Entry(duration_frame, textvariable=duration_var, width=10)
        duration_entry.pack(side=tk.LEFT, padx=5)

        # 填充媒体源列表
        if self.obs_api:
            media_sources = self.obs_api.get_media_sources()
            self.log(f"获取到媒体源列表，类型: {type(media_sources)}")

            # 检查media_sources是字符串列表还是字典列表
            if media_sources and isinstance(media_sources, list):
                if media_sources and isinstance(media_sources[0], dict):
                    # 如果是字典列表
                    source_names = [source.get('inputName', 'Unknown') for source in media_sources]
                    self.log(f"从字典列表提取媒体源名称")
                else:
                    # 如果是字符串列表
                    source_names = media_sources
                    self.log(f"直接使用字符串列表作为媒体源名称")

                self.log(f"媒体源名称: {source_names}")
                source_combobox['values'] = source_names
                if source_names:
                    source_combobox.current(0)

        # 创建确认按钮
        def confirm_add():
            # 获取关键词、媒体源和播放时间
            keyword = keyword_entry.get().strip()
            source = source_var.get()

            # 获取播放时间
            try:
                duration = int(duration_var.get())
                if duration <= 0:
                    messagebox.showinfo("提示", "播放时间必须大于0")
                    return
            except ValueError:
                messagebox.showinfo("提示", "播放时间必须是整数")
                return

            # 验证输入
            if not keyword:
                messagebox.showinfo("提示", "关键词不能为空")
                return

            if not source:
                messagebox.showinfo("提示", "请选择媒体源")
                return

            # 检查关键词是否已存在
            if keyword in self.sub_video_sources:
                messagebox.showinfo("提示", f"关键词 '{keyword}' 已存在")
                return

            # 添加到映射表（新格式：字典包含媒体源和播放时间）
            self.sub_video_sources[keyword] = {
                "source": source,
                "duration": duration
            }

            # 添加到表格（显示关键词、媒体源和播放时间）
            self.sub_video_treeview.insert("", tk.END, values=(keyword, source, f"{duration}秒"))

            self.log(f"已添加副视频映射: 关键词='{keyword}', 媒体源='{source}', 播放时间={duration}秒")

            # 保存设置
            self.save_settings_to_config()

            # 关闭对话框
            add_dialog.destroy()

        # 创建按钮区域
        button_frame = ttk.Frame(add_dialog, padding=10)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        cancel_button = ttk.Button(button_frame, text="取消", command=add_dialog.destroy)
        cancel_button.pack(side=tk.RIGHT, padx=5)

        confirm_button = ttk.Button(button_frame, text="添加", command=confirm_add, style="Primary.TButton")
        confirm_button.pack(side=tk.RIGHT, padx=5)

        # 绑定Enter键到确认按钮
        add_dialog.bind("<Return>", lambda _: confirm_add())

        # 等待对话框关闭
        self.root.wait_window(add_dialog)

    def delete_sub_video_mapping(self):
        """删除选中的副视频映射"""
        selected_items = self.sub_video_treeview.selection()
        if not selected_items:
            self.log("请先选择要删除的副视频映射")
            return

        for item in selected_items:
            # 获取关键词
            values = self.sub_video_treeview.item(item, 'values')
            if values and len(values) > 0:
                keyword = values[0]

                # 从映射表中删除
                if keyword in self.sub_video_sources:
                    # 获取媒体源和播放时间（用于日志）
                    if isinstance(self.sub_video_sources[keyword], dict):
                        source = self.sub_video_sources[keyword].get('source', '')
                        duration = self.sub_video_sources[keyword].get('duration', 0)
                        self.log(f"删除副视频映射: 关键词='{keyword}', 媒体源='{source}', 播放时间={duration}秒")
                    else:
                        self.log(f"删除副视频映射: 关键词='{keyword}', 媒体源='{self.sub_video_sources[keyword]}'")

                    # 删除映射
                    del self.sub_video_sources[keyword]

                # 从表格中删除
                self.sub_video_treeview.delete(item)

                # 保存设置
                self.save_settings_to_config()

                self.log(f"副视频映射已删除并保存到配置文件")

    def start_main_video_loop(self):
        """开始主视频循环播放"""
        self.log("开始执行主视频循环播放函数")
        self.log(f"当前按钮状态: start_button={self.start_main_video_button['state']}, stop_button={self.stop_main_video_button['state']}")

        # 检查OBS API是否已连接
        if not self.obs_api:
            self.log("无法开始循环播放：OBS API未初始化", "error")
            messagebox.showerror("错误", "OBS API未初始化，请先连接OBS")
            return

        # 检查OBS连接状态
        if not self.obs_api.is_connected():
            self.log("无法开始循环播放：OBS未连接", "error")
            messagebox.showerror("错误", "OBS未连接，请先连接OBS")
            return

        # 检查主视频源是否已设置
        if not self.main_video_source:
            self.log("无法开始循环播放：主视频源未设置", "error")
            messagebox.showerror("错误", "主视频源未设置，请先选择主视频源")
            return

        # 停止当前循环
        self.log("停止当前循环")
        self.stop_main_video_loop()

        # 获取当前可用的媒体源
        self.log("获取OBS中的媒体源列表")
        sources = self.obs_api.get_media_sources()
        if not sources:
            self.log("无法开始循环播放：OBS中没有找到媒体源", "error")
            messagebox.showerror("错误", "OBS中没有找到媒体源")
            return

        self.log(f"找到 {len(sources)} 个媒体源: {sources}")

        # 初始化双主视频源
        self.log("初始化双主视频源")
        self.setup_dual_main_video_sources(sources)

        # 检查是否启用双主视频源模式
        if hasattr(self, "use_dual_main_video") and self.use_dual_main_video and hasattr(self, "main_video_source_b") and self.main_video_source_b:
            self.log("已启用双主视频源模式，变速时将不会黑屏")

            # 确保所有源都已经隐藏
            self.log(f"隐藏所有视频源")
            hide_all_result = self.obs_api.hide_all_sources()
            self.log(f"隐藏所有视频源结果: {hide_all_result}")

            # 为两个源都设置随机速度
            self.log(f"为主视频源A设置随机速度: {self.main_video_source_a}")
            speed_a = self.set_random_speed_for_source(self.main_video_source_a)
            self.log(f"主视频源A速度设置为: {speed_a}x")

            self.log(f"为主视频源B设置随机速度: {self.main_video_source_b}")
            speed_b = self.set_random_speed_for_source(self.main_video_source_b)
            self.log(f"主视频源B速度设置为: {speed_b}x")

            # 播放主视频源A（仍然是隐藏状态）
            self.log(f"播放主视频源A: {self.main_video_source_a}")
            play_result_a = self.obs_api.play_media(self.main_video_source_a)
            self.log(f"播放主视频源A结果: {play_result_a}")

            # 等待一小段时间确保播放已经开始并且速度已经设置好
            import time
            self.log(f"等待0.2秒确保播放已经开始并且速度已经设置好")
            time.sleep(0.2)

            # 显示主视频源A（此时速度已经设置好）
            self.log(f"显示主视频源A: {self.main_video_source_a}")
            show_result_a = self.obs_api.show_source(self.main_video_source_a)
            self.log(f"显示主视频源A结果: {show_result_a}")

            # 设置当前活动源为A
            self.main_video_source = self.main_video_source_a
            self.current_video_source = self.main_video_source_a
            self.log(f"当前活动源设置为: {self.main_video_source}")
        else:
            # 单一主视频源模式
            self.log("使用单一主视频源模式")

            # 确保所有源都已经隐藏
            self.log(f"隐藏所有视频源")
            hide_all_result = self.obs_api.hide_all_sources()
            self.log(f"隐藏所有视频源结果: {hide_all_result}")

            # 随机设置速度（在隐藏状态下设置）
            self.log(f"为主视频设置随机速度: {self.main_video_source}")
            speed = self.set_random_main_video_speed()
            self.log(f"主视频速度设置为: {speed}x")

            # 播放主视频（仍然是隐藏状态）
            self.log(f"播放主视频: {self.main_video_source}")
            play_result = self.obs_api.play_media(self.main_video_source)
            self.log(f"播放主视频结果: {play_result}")

            # 等待一小段时间确保播放已经开始并且速度已经设置好
            import time
            self.log(f"等待0.2秒确保播放已经开始并且速度已经设置好")
            time.sleep(0.2)

            # 显示主视频源（此时速度已经设置好）
            self.log(f"显示主视频源: {self.main_video_source}")
            show_result = self.obs_api.show_source(self.main_video_source)
            self.log(f"显示主视频源结果: {show_result}")

            self.current_video_source = self.main_video_source
            self.log(f"当前视频源设置为: {self.current_video_source}")

        # 更新UI状态
        self.start_main_video_button.config(state="disabled")
        self.stop_main_video_button.config(state="normal")

        # 更新状态显示
        self.log("更新媒体状态显示")
        self.refresh_media_status()

        # 启动定时器检查主视频状态
        self.log("启动定时器检查主视频状态")
        if hasattr(self, "main_video_timer") and self.main_video_timer:
            self.root.after_cancel(self.main_video_timer)
            self.main_video_timer = None
        self.main_video_timer = self.root.after(1000, self.check_main_video_status)

        self.log(f"已开始主视频 {self.main_video_source} 循环播放")

    def setup_dual_main_video_sources(self, sources):
        """设置双主视频源

        Args:
            sources (list): OBS中可用的媒体源列表
        """
        # 从UI获取主视频源A和B的选择
        main_source_a = self.main_video_source_var.get()
        main_source_b = self.main_video_source_b_var.get()

        # 检查是否启用双主视频源模式
        use_dual_mode = self.use_dual_main_video_var.get()

        # 如果主视频源A未设置，无法继续
        if not main_source_a:
            self.log("主视频源A未设置，无法启用双主视频源模式")
            self.use_dual_main_video = False
            return

        # 检查主视频源A是否存在
        if main_source_a not in sources:
            self.log(f"主视频源A '{main_source_a}' 不存在于OBS中")
            self.log(f"可用的媒体源: {sources}")

            # 尝试模糊匹配
            matched_source = None
            for source in sources:
                if main_source_a.lower() in source.lower():
                    matched_source = source
                    self.log(f"找到可能匹配的主视频源A: '{matched_source}'")
                    break

            if matched_source:
                main_source_a = matched_source
                self.main_video_source_var.set(main_source_a)
            else:
                self.log("无法找到匹配的主视频源A")
                self.use_dual_main_video = False
                return

        # 设置主视频源A
        self.main_video_source_a = main_source_a
        self.main_video_source = main_source_a  # 兼容旧代码

        # 如果启用双主视频源模式，检查主视频源B
        if use_dual_mode:
            # 如果主视频源B未设置，尝试自动查找
            if not main_source_b:
                self.log("主视频源B未设置，尝试自动查找")

                # 查找包含相同文件名但名称不同的媒体源作为第二个主视频源
                for source in sources:
                    if source != main_source_a and "主视频" in source:
                        main_source_b = source
                        self.main_video_source_b_var.set(main_source_b)
                        self.log(f"自动找到第二个主视频源: '{main_source_b}'")
                        break

            # 检查主视频源B是否存在
            if main_source_b and main_source_b in sources:
                # 设置主视频源B
                self.main_video_source_b = main_source_b
                self.use_dual_main_video = True

                self.log(f"已设置主视频源A: '{self.main_video_source_a}'")
                self.log(f"已设置主视频源B: '{self.main_video_source_b}'")
                self.log("已启用双主视频源模式，变速时将不会黑屏")
            else:
                self.log("主视频源B未设置或不存在，将使用单一主视频源模式")
                self.main_video_source_b = None
                self.use_dual_main_video = False
        else:
            self.log("未启用双主视频源模式，将使用单一主视频源模式")
            self.main_video_source_b = None
            self.use_dual_main_video = False

    def check_main_video_status(self):
        """检查主视频播放状态"""
        if not self.obs_api or not self.main_video_source or self.current_video_source != self.main_video_source:
            return

        try:
            # 获取当前状态
            status = self.obs_api.get_media_status(self.main_video_source)

            if status:
                # 记录当前状态
                current_state = status['state']
                progress = status['progress_percent']
                duration = status['duration']  # 总时长（毫秒）
                position = status['position']  # 当前位置（毫秒）

                # 保存上一次的进度和状态，用于检测循环完成
                if not hasattr(self, "last_video_progress"):
                    self.last_video_progress = progress
                    self.last_video_state = current_state

                # 如果进度从高变低，说明循环了一次
                progress_reset = (self.last_video_progress > 90 and progress < 10)

                # 记录日志，但不要太频繁
                if progress_reset or abs(progress - self.last_video_progress) > 10 or current_state != self.last_video_state:
                    self.log(f"主视频当前状态: {current_state}, 进度: {progress:.2f}%, 位置: {position/1000:.2f}秒, 总时长: {duration/1000:.2f}秒")

                    # 更新当前速度显示
                    try:
                        current_speed = self.obs_api.get_media_speed(self.main_video_source)
                        if current_speed and hasattr(self, "current_video_speed_var"):
                            self.current_video_speed_var.set(f"{current_speed:.2f}x")
                            self.log(f"更新当前速度显示: {current_speed:.2f}x")
                    except Exception as e:
                        self.log(f"获取当前速度出错: {str(e)}")

                # 检查是否接近视频结束（提前2秒准备下一个视频）
                remaining_time = (duration - position) / 1000  # 剩余时间（秒）
                prepare_next = (remaining_time <= 2 and remaining_time > 0 and not hasattr(self, "preparing_next_video"))

                if prepare_next:
                    self.log(f"视频即将结束，剩余 {remaining_time:.2f} 秒，开始准备下一个视频")
                    self.preparing_next_video = True

                # 如果接近视频结束（提前2秒准备下一个视频）
                if prepare_next:
                    try:
                        # 记录当前速度
                        old_speed = self.current_video_speed_var.get() if hasattr(self, "current_video_speed_var") else "1.00x"

                        # 使用双主视频源模式提前准备下一个视频
                        if hasattr(self, "use_dual_main_video") and self.use_dual_main_video and hasattr(self, "main_video_source_a") and hasattr(self, "main_video_source_b") and self.main_video_source_b:
                            # 确定当前活动的主视频源和非活动的主视频源
                            current_active = self.main_video_source
                            inactive_source = self.main_video_source_b if current_active == self.main_video_source_a else self.main_video_source_a

                            # 为非活动源设置随机速度（在隐藏状态下设置）
                            self.log(f"提前为非活动源 {inactive_source} 设置随机速度")
                            new_speed = self.set_random_speed_for_source(inactive_source)

                            # 设置非活动源的播放位置为0（从头开始）
                            self.log(f"设置非活动源 {inactive_source} 的播放位置为 0 毫秒")
                            self.obs_api.set_media_time(inactive_source, 0)

                            # 确保非活动源处于隐藏状态
                            self.log(f"确保非活动源 {inactive_source} 处于隐藏状态")
                            self.obs_api.hide_source(inactive_source)

                            # 播放非活动源（仍然是隐藏状态）
                            self.log(f"播放非活动源 {inactive_source}（仍然是隐藏状态）")
                            play_result = self.obs_api.play_media(inactive_source)
                            self.log(f"播放非活动源结果: {play_result}")

                            self.log(f"已提前准备好下一个视频源 {inactive_source}，速度: {new_speed}x")

                            # 标记下一个视频已准备好
                            self.next_video_prepared = True
                            self.next_video_source = inactive_source
                            self.next_video_speed = new_speed
                        else:
                            # 单一主视频源模式下，提前生成随机速度
                            self.log("单一主视频源模式下，提前生成随机速度")

                            # 获取速度区间
                            min_speed = self.main_video_speed_min_var.get()
                            max_speed = self.main_video_speed_max_var.get()

                            # 生成随机速度
                            import random
                            new_speed = random.uniform(float(min_speed), float(max_speed))
                            new_speed = round(new_speed, 2)  # 保留两位小数

                            self.log(f"已提前生成随机速度: {new_speed}x")

                            # 保存生成的速度
                            self.next_video_speed = new_speed
                            self.next_video_prepared = True

                            # 提前设置速度（但不改变当前播放状态）
                            self.log(f"提前为主视频源 {self.main_video_source} 设置随机速度 {new_speed}x")
                            self.obs_api.set_media_speed(self.main_video_source, new_speed, restart_media=False)
                    except Exception as e:
                        self.log(f"提前准备下一个视频出错: {str(e)}", "error")
                        import traceback
                        self.log(traceback.format_exc(), "error")

                # 如果播放结束或停止或进度重置（循环了一次）
                if current_state == "OBS_MEDIA_STATE_ENDED" or current_state == "OBS_MEDIA_STATE_STOPPED" or progress_reset:
                    # 重新播放并随机设置速度
                    if progress_reset:
                        self.log("检测到主视频进度重置，循环完成一次")
                    else:
                        self.log("主视频播放周期结束，重新设置速度并播放")

                    # 记录当前速度
                    old_speed = self.current_video_speed_var.get() if hasattr(self, "current_video_speed_var") else "1.00x"

                    # 使用双主视频源模式或直接变速
                    try:
                        if hasattr(self, "use_dual_main_video") and self.use_dual_main_video and hasattr(self, "main_video_source_a") and hasattr(self, "main_video_source_b") and self.main_video_source_b:
                            # 使用双主视频源模式，交替切换
                            self.log("使用双主视频源模式变化速度，避免黑屏")

                            # 检查是否已经提前准备好了下一个视频
                            if hasattr(self, "next_video_prepared") and self.next_video_prepared and hasattr(self, "next_video_source"):
                                # 使用已准备好的视频源
                                inactive_source = self.next_video_source
                                new_speed = self.next_video_speed
                                current_active = self.main_video_source
                                self.log(f"使用已提前准备好的视频源: {inactive_source}, 速度: {new_speed}x")

                                # 直接显示非活动源（此时已经设置好速度并开始播放）
                                self.log(f"直接切换显示到非活动源 {inactive_source}")
                                show_result = self.obs_api.show_source(inactive_source)
                                self.log(f"显示非活动源结果: {show_result}")

                                # 更新当前活动源
                                self.main_video_source = inactive_source
                                self.current_video_source = inactive_source

                                # 清除准备标记
                                self.next_video_prepared = False
                                delattr(self, "next_video_source")
                                delattr(self, "next_video_speed")
                                delattr(self, "preparing_next_video")
                            else:
                                # 如果没有提前准备好，使用常规方法
                                # 确定当前活动的主视频源和非活动的主视频源
                                current_active = self.main_video_source
                                inactive_source = self.main_video_source_b if current_active == self.main_video_source_a else self.main_video_source_a

                                # 获取当前播放位置
                                current_position = self.obs_api.get_media_time(current_active)
                                self.log(f"当前播放位置: {current_position} 毫秒")

                                # 确保所有源都已经隐藏
                                self.log(f"隐藏所有视频源")
                                self.obs_api.hide_all_sources()

                                # 为非活动源设置随机速度（在隐藏状态下设置）
                                self.log(f"为非活动源 {inactive_source} 设置随机速度")
                                new_speed = self.set_random_speed_for_source(inactive_source)

                                # 设置非活动源的播放位置为0（从头开始）
                                self.log(f"设置非活动源 {inactive_source} 的播放位置为 0 毫秒")
                                self.obs_api.set_media_time(inactive_source, 0)

                                # 播放非活动源（仍然是隐藏状态）
                                self.log(f"播放非活动源 {inactive_source}")
                                play_result = self.obs_api.play_media(inactive_source)
                                self.log(f"播放非活动源结果: {play_result}")

                                # 等待一小段时间确保播放已经开始并且速度已经设置好
                                import time
                                self.log(f"等待0.2秒确保播放已经开始并且速度已经设置好")
                                time.sleep(0.2)

                                # 切换到非活动源（此时速度已经设置好）
                                self.log(f"切换显示到非活动源 {inactive_source}")
                                show_result = self.obs_api.show_source(inactive_source)
                                self.log(f"显示非活动源结果: {show_result}")

                            # 更新状态显示
                            self.refresh_media_status()

                            self.log(f"主视频源已切换: {current_active} -> {inactive_source}, 速度已变化: {old_speed} -> {new_speed}x")
                        else:
                            # 使用单一主视频源模式，但优化变速逻辑
                            self.log("使用单一主视频源模式变化速度，优化变速逻辑避免黑屏")

                            # 检查是否已经提前准备好了速度
                            if hasattr(self, "next_video_speed") and hasattr(self, "next_video_prepared") and self.next_video_prepared:
                                new_speed = self.next_video_speed
                                self.log(f"使用已提前准备好的速度: {new_speed}x")

                                # 如果是播放结束或停止状态，需要重新播放
                                if current_state != "OBS_MEDIA_STATE_PLAYING":
                                    # 重新播放
                                    self.log(f"主视频重新开始播放: {self.main_video_source}")
                                    play_result = self.obs_api.play_media(self.main_video_source)
                                    self.log(f"主视频重新播放结果: {play_result}")
                                else:
                                    # 如果是播放状态，设置播放位置为0（从头开始）
                                    self.log(f"设置主视频播放位置为 0 毫秒")
                                    self.obs_api.set_media_time(self.main_video_source, 0)

                                # 清除准备标记
                                delattr(self, "next_video_speed")
                                delattr(self, "preparing_next_video")
                                if hasattr(self, "next_video_prepared"):
                                    delattr(self, "next_video_prepared")
                                if hasattr(self, "next_video_source"):
                                    delattr(self, "next_video_source")
                            else:
                                # 创建一个临时媒体源作为过渡
                                # 这里我们使用与副视频切换类似的方法，先准备好下一个状态，然后直接切换

                                # 隐藏当前源
                                self.log(f"隐藏当前视频源: {self.main_video_source}")
                                hide_result = self.obs_api.hide_source(self.main_video_source)
                                self.log(f"隐藏当前视频源结果: {hide_result}")

                                # 设置随机速度（在隐藏状态下设置）
                                self.log(f"为主视频设置随机速度: {self.main_video_source}")
                                new_speed = self.set_random_main_video_speed()
                                self.log(f"主视频速度设置为: {new_speed}x")

                                # 如果是播放结束或停止状态，需要重新播放
                                if current_state != "OBS_MEDIA_STATE_PLAYING":
                                    # 重新播放（仍然是隐藏状态）
                                    self.log(f"主视频重新开始播放: {self.main_video_source}")
                                    play_result = self.obs_api.play_media(self.main_video_source)
                                    self.log(f"主视频重新播放结果: {play_result}")
                                else:
                                    # 如果是播放状态，设置播放位置为0（从头开始）
                                    self.log(f"设置主视频播放位置为 0 毫秒")
                                    self.obs_api.set_media_time(self.main_video_source, 0)

                                # 等待一小段时间确保播放已经开始并且速度已经设置好
                                import time
                                self.log(f"等待0.1秒确保播放已经开始并且速度已经设置好")
                                time.sleep(0.1)

                            # 显示主视频源（此时速度已经设置好）
                            self.log(f"显示主视频源: {self.main_video_source}")
                            show_result = self.obs_api.show_source(self.main_video_source)
                            self.log(f"显示主视频源结果: {show_result}")

                            # 更新状态显示
                            self.refresh_media_status()

                            self.log(f"主视频速度已变化: {old_speed} -> {new_speed}x")
                    except Exception as e:
                        self.log(f"变化速度出错: {str(e)}", "error")
                        import traceback
                        self.log(traceback.format_exc(), "error")

                # 更新上一次的进度和状态
                self.last_video_progress = progress
                self.last_video_state = current_state
        except Exception as e:
            self.log(f"检查主视频状态出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc(), "error")

        # 继续定时检查，使用更短的间隔以更及时地检测循环完成
        if hasattr(self, "main_video_timer") and self.main_video_timer:
            self.root.after_cancel(self.main_video_timer)
        self.main_video_timer = self.root.after(1000, self.check_main_video_status)

    def stop_main_video_loop(self):
        """停止主视频循环播放"""
        # 停止定时器
        if hasattr(self, "main_video_timer") and self.main_video_timer:
            self.root.after_cancel(self.main_video_timer)
            self.main_video_timer = None

        # 停止副视频定时器
        if self.sub_video_timer:
            self.root.after_cancel(self.sub_video_timer)
            self.sub_video_timer = None

        # 更新UI状态
        if hasattr(self, "start_main_video_button") and self.main_video_source:
            self.start_main_video_button.config(state="normal")

        if hasattr(self, "stop_main_video_button"):
            self.stop_main_video_button.config(state="disabled")

        self.log("已停止主视频循环播放")

    def set_random_main_video_speed(self):
        """随机设置主视频播放速度"""
        if not self.obs_api or not self.main_video_source:
            return

        try:
            # 获取速度区间
            min_speed = self.main_video_speed_min_var.get()
            max_speed = self.main_video_speed_max_var.get()

            # 获取当前速度（如果有）
            current_speed = None
            if hasattr(self, "current_video_speed_var"):
                try:
                    # 从显示文本中提取数字
                    current_speed_text = self.current_video_speed_var.get()
                    if current_speed_text.endswith('x'):
                        current_speed_text = current_speed_text[:-1]  # 移除结尾的'x'
                    current_speed = float(current_speed_text)
                except (ValueError, AttributeError):
                    current_speed = None

            # 生成随机速度
            import random
            # 生成多个随机速度，确保与当前速度不同
            max_attempts = 10
            speed = None
            for _ in range(max_attempts):
                new_speed = random.uniform(min_speed, max_speed)
                new_speed = round(new_speed, 2)  # 保留两位小数

                # 如果没有当前速度或新速度与当前速度相差足够大
                if current_speed is None or abs(new_speed - current_speed) >= 0.1:
                    speed = new_speed
                    break

            # 如果没有生成合适的速度，使用最后一个生成的速度
            if speed is None:
                speed = new_speed

            # 设置速度
            self.obs_api.set_media_speed(self.main_video_source, speed)

            # 更新显示
            self.current_video_speed_var.set(f"{speed:.2f}x")

            self.log(f"已将主视频 {self.main_video_source} 的播放速度设置为 {speed:.2f}x")
            return speed
        except Exception as e:
            self.log(f"设置主视频速度出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc(), "error")
            return None

    def set_random_speed_for_source(self, source_name):
        """为指定媒体源设置随机播放速度

        Args:
            source_name (str): 媒体源名称

        Returns:
            float: 设置的速度值，如果失败则返回None
        """
        if not self.obs_api or not source_name:
            return None

        try:
            # 获取速度区间
            min_speed = self.main_video_speed_min_var.get()
            max_speed = self.main_video_speed_max_var.get()

            # 获取当前速度（如果有）
            current_speed = None
            try:
                current_speed = self.obs_api.get_media_speed(source_name)
            except:
                pass

            # 生成随机速度
            import random
            # 生成多个随机速度，确保与当前速度不同
            max_attempts = 10
            speed = None
            for _ in range(max_attempts):
                new_speed = random.uniform(min_speed, max_speed)
                new_speed = round(new_speed, 2)  # 保留两位小数

                # 如果没有当前速度或新速度与当前速度相差足够大
                if current_speed is None or abs(new_speed - current_speed) >= 0.1:
                    speed = new_speed
                    break

            # 如果没有生成合适的速度，使用最后一个生成的速度
            if speed is None:
                speed = new_speed

            # 设置速度，不重启媒体
            self.obs_api.set_media_speed(source_name, speed, restart_media=False)

            self.log(f"已将媒体源 {source_name} 的播放速度设置为 {speed:.2f}x")
            return speed
        except Exception as e:
            self.log(f"为媒体源 {source_name} 设置随机速度出错: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")
            return None

    def switch_to_sub_video(self, keyword, duration):
        """切换到副视频

        Args:
            keyword (str): 副视频关键词
            duration (int): 播放时间（秒）
        """
        self.log(f"开始切换副视频: 关键词='{keyword}', 时间={duration}秒")

        # 检查OBS API是否初始化
        if not hasattr(self, "obs_api") or not self.obs_api:
            self.log("无法切换副视频：OBS API未初始化")
            return False

        # 检查OBS是否连接
        if not self.obs_api.is_connected():
            self.log("无法切换副视频：OBS未连接")
            return False

        # 检查副视频映射是否初始化
        if not hasattr(self, "sub_video_sources"):
            self.sub_video_sources = {}
            self.log("初始化副视频映射")

        # 检查关键词是否存在
        if keyword not in self.sub_video_sources:
            self.log(f"副视频关键词 '{keyword}' 不存在于映射中")
            self.log(f"可用的副视频关键词: {list(self.sub_video_sources.keys())}")

            # 获取可用的媒体源
            try:
                sources = self.obs_api.get_media_sources()
                self.log(f"找到 {len(sources)} 个媒体源: {sources}")

                # 尝试查找与关键词匹配的媒体源
                matched_source = None
                for source in sources:
                    if keyword.lower() in source.lower():
                        matched_source = source
                        self.log(f"找到可能匹配的源: '{matched_source}'")
                        break

                if matched_source:
                    # 添加到映射中
                    self.sub_video_sources[keyword] = {
                        'source': matched_source,
                        'duration': duration
                    }
                    self.log(f"已将关键词 '{keyword}' 添加到副视频映射中，媒体源: '{matched_source}'")
                else:
                    # 如果没有找到匹配的源，使用第一个可用的源
                    if sources:
                        self.sub_video_sources[keyword] = {
                            'source': sources[0],
                            'duration': duration
                        }
                        self.log(f"未找到匹配的源，使用第一个可用的源 '{sources[0]}' 作为关键词 '{keyword}' 的媒体源")
                    else:
                        self.log("没有可用的媒体源")
                        return False
            except Exception as e:
                self.log(f"获取媒体源列表失败: {str(e)}")
                return False

        # 获取副视频源和播放时间
        if isinstance(self.sub_video_sources[keyword], dict):
            # 新格式：字典包含媒体源和播放时间
            sub_video_source = self.sub_video_sources[keyword]['source']
            # 如果提供了时间参数，使用提供的时间，否则使用映射中的时间
            if duration <= 0:
                duration = self.sub_video_sources[keyword]['duration']
            self.log(f"找到副视频源: '{sub_video_source}', 播放时间: {duration}秒")
        else:
            # 兼容旧格式：直接存储媒体源
            sub_video_source = self.sub_video_sources[keyword]
            self.log(f"找到副视频源: '{sub_video_source}'")

        # 停止当前副视频定时器
        if hasattr(self, "sub_video_timer") and self.sub_video_timer:
            self.log("取消当前副视频定时器")
            self.root.after_cancel(self.sub_video_timer)
            self.sub_video_timer = None

        try:
            # 获取当前可用的媒体源
            self.log("获取OBS中的媒体源列表...")
            sources = self.obs_api.get_media_sources()
            self.log(f"找到 {len(sources)} 个媒体源: {sources}")

            # 检查媒体源是否存在
            if sub_video_source not in sources:
                self.log(f"副视频源 '{sub_video_source}' 不存在于OBS中")
                self.log(f"可用的媒体源: {sources}")

                # 尝试模糊匹配
                matched_source = None
                for source in sources:
                    if sub_video_source.lower() in source.lower():
                        matched_source = source
                        self.log(f"找到可能匹配的源: '{matched_source}'")
                        break

                if matched_source:
                    self.log(f"使用模糊匹配的源: '{matched_source}' 替代 '{sub_video_source}'")
                    sub_video_source = matched_source
                    # 更新映射（兼容新旧格式）
                    if isinstance(self.sub_video_sources[keyword], dict):
                        # 新格式：更新字典中的媒体源
                        self.sub_video_sources[keyword]['source'] = matched_source
                        self.log(f"更新副视频映射(新格式): 关键词='{keyword}', 媒体源='{matched_source}'")
                    else:
                        # 旧格式：直接更新媒体源
                        self.sub_video_sources[keyword] = matched_source
                        self.log(f"更新副视频映射(旧格式): 关键词='{keyword}', 媒体源='{matched_source}'")
                else:
                    return False

            # 先隐藏所有源，然后再显示副视频源
            self.log(f"先隐藏所有视频源")
            hide_result = self.obs_api.hide_all_sources()
            self.log(f"隐藏所有视频源结果: {hide_result}")

            # 显示副视频源
            self.log(f"尝试显示副视频源: '{sub_video_source}'")
            result = self.obs_api.show_source(sub_video_source)
            if not result:
                self.log(f"显示副视频源 '{sub_video_source}' 失败")
                return False

            self.current_video_source = sub_video_source
            self.log(f"当前视频源已设置为: '{self.current_video_source}'")

            # 只有在播放状态下才通过WebSocket实时更新OBS源到服务器
            if self.is_playing and hasattr(self, "server_ws") and self.server_ws and self.server_ws.connected and self.server_ws.authenticated:
                try:
                    # 准备OBS源数据
                    import datetime
                    status_data = {
                        "obs_source": self.current_video_source,
                        "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "username": self.username if hasattr(self, "username") else ""
                    }

                    # 发送状态更新
                    self.server_ws.update_status(status_data)
                    self.log("已实时发送OBS源状态到服务器", "network")
                except Exception as e:
                    self.log(f"发送OBS源状态到服务器出错: {str(e)}", "error")

            # 播放副视频
            self.log(f"尝试播放副视频源: '{sub_video_source}'")
            result = self.obs_api.play_media(sub_video_source)
            if not result:
                self.log(f"播放副视频源 '{sub_video_source}' 失败")
                return False

            # 成功切换到副视频
            self.log(f"成功切换到副视频: 关键词='{keyword}', 媒体源='{sub_video_source}', 时间={duration}秒")

            # 设置定时器，在指定时间后切回主视频
            def switch_back_to_main_video():
                if not hasattr(self, "obs_api") or not self.obs_api or not hasattr(self, "main_video_source") or not self.main_video_source:
                    self.log("无法切回主视频：OBS未连接或主视频源未设置")
                    return

                try:
                    # 获取当前可用的媒体源
                    self.log("获取OBS中的媒体源列表...")
                    sources = self.obs_api.get_media_sources()
                    self.log(f"找到 {len(sources)} 个媒体源: {sources}")

                    # 检查主视频源是否存在
                    if self.main_video_source not in sources:
                        self.log(f"主视频源 '{self.main_video_source}' 不存在于OBS中")

                        # 尝试模糊匹配
                        matched_source = None
                        for source in sources:
                            if self.main_video_source.lower() in source.lower():
                                matched_source = source
                                self.log(f"找到可能匹配的主源: '{matched_source}'")
                                break

                        if matched_source:
                            self.log(f"使用模糊匹配的源: '{matched_source}' 替代 '{self.main_video_source}'")
                            self.main_video_source = matched_source
                        else:
                            self.log(f"无法找到匹配的主视频源")
                            return

                    # 先隐藏所有源，然后再显示主视频源
                    self.log(f"先隐藏所有视频源")
                    hide_result = self.obs_api.hide_all_sources()
                    self.log(f"隐藏所有视频源结果: {hide_result}")

                    # 显示主视频源
                    self.log(f"尝试切回主视频源: '{self.main_video_source}'")
                    result = self.obs_api.show_source(self.main_video_source)
                    if not result:
                        self.log(f"显示主视频源 '{self.main_video_source}' 失败")
                        return

                    self.current_video_source = self.main_video_source
                    self.log(f"当前视频源已设置为: '{self.current_video_source}'")

                    # 播放主视频
                    result = self.obs_api.play_media(self.main_video_source)
                    if not result:
                        self.log(f"播放主视频源 '{self.main_video_source}' 失败")
                        return

                    # 注意：速度已在切换到副视频前设置好，这里不需要再设置
                    # 记录日志
                    self.log(f"主视频已经在切换到副视频前设置好了速度，无需再次设置", "obs")

                    self.log(f"副视频播放完成，已切回主视频 '{self.main_video_source}'")
                except Exception as e:
                    self.log(f"切回主视频时出错: {str(e)}")
                    import traceback
                    self.log(traceback.format_exc())
                finally:
                    # 清除定时器
                    self.sub_video_timer = None

            # 设置定时器
            self.log(f"设置定时器 {duration} 秒后切回主视频")
            self.sub_video_timer = self.root.after(duration * 1000, switch_back_to_main_video)

            return True
        except Exception as e:
            self.log(f"切换副视频时出错: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            return False

    def test_sub_video_trigger(self):
        """测试副视频关键词触发功能"""
        # 检查OBS是否连接
        if not self.obs_api or not self.obs_api.is_connected():
            messagebox.showwarning("测试失败", "OBS未连接，请先连接OBS")
            return

        # 检查是否有副视频映射
        if not hasattr(self, "sub_video_sources") or not self.sub_video_sources:
            messagebox.showwarning("测试失败", "没有副视频映射，请先添加副视频映射")
            return

        # 创建测试对话框
        test_dialog = tk.Toplevel(self.root)
        test_dialog.title("测试副视频触发")
        test_dialog.geometry("400x300")
        test_dialog.transient(self.root)  # 设置为主窗口的子窗口
        test_dialog.grab_set()  # 模态对话框

        # 创建关键词选择
        keyword_frame = ttk.Frame(test_dialog, padding=10)
        keyword_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(keyword_frame, text="选择关键词:").pack(side=tk.LEFT, padx=(0, 5))
        keyword_var = tk.StringVar()
        keyword_combobox = ttk.Combobox(keyword_frame, textvariable=keyword_var, state="readonly", width=30)
        keyword_combobox.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # 填充关键词列表
        keywords = list(self.sub_video_sources.keys())
        keyword_combobox['values'] = keywords
        if keywords:
            keyword_combobox.current(0)

        # 创建播放时间输入框
        duration_frame = ttk.Frame(test_dialog, padding=10)
        duration_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(duration_frame, text="播放时间(秒):").pack(side=tk.LEFT, padx=(0, 5))
        duration_var = tk.StringVar(value="5")
        duration_entry = ttk.Entry(duration_frame, textvariable=duration_var, width=10)
        duration_entry.pack(side=tk.LEFT, padx=5)

        # 创建测试方式选择
        method_frame = ttk.LabelFrame(test_dialog, text="测试方式", padding=10)
        method_frame.pack(fill=tk.X, padx=10, pady=5)

        method_var = tk.StringVar(value="direct")
        direct_radio = ttk.Radiobutton(method_frame, text="直接触发副视频", variable=method_var, value="direct")
        direct_radio.pack(side=tk.LEFT, padx=5)

        text_radio = ttk.Radiobutton(method_frame, text="使用关键词触发副视频", variable=method_var, value="text")
        text_radio.pack(side=tk.LEFT, padx=5)

        # 创建测试按钮
        button_frame = ttk.Frame(test_dialog, padding=10)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        # 测试函数
        def run_test():
            # 获取关键词和时间
            keyword = keyword_var.get()
            try:
                duration = int(duration_var.get())
                if duration <= 0:
                    messagebox.showwarning("输入错误", "播放时间必须大于0")
                    return
            except ValueError:
                messagebox.showwarning("输入错误", "播放时间必须是整数")
                return

            # 关闭对话框
            test_dialog.destroy()

            # 根据选择的方式执行测试
            method = method_var.get()
            if method == "direct":
                # 直接触发
                self.log(f"开始测试直接触发副视频: 关键词='{keyword}', 时间={duration}秒")
                success = self.switch_to_sub_video(keyword, duration)
                if success:
                    # 获取媒体源名称（兼容新旧格式）
                    if isinstance(self.sub_video_sources[keyword], dict):
                        source_name = self.sub_video_sources[keyword].get('source', '未知')
                    else:
                        source_name = self.sub_video_sources[keyword]

                    self.log(f"测试成功: 已切换到副视频 '{source_name}'")
                    messagebox.showinfo("测试成功", f"已切换到副视频 '{source_name}'，将在 {duration} 秒后切回主视频")
                else:
                    self.log(f"测试失败: 无法切换到副视频")
                    messagebox.showerror("测试失败", "无法切换到副视频，请查看日志了解详细原因")
            else:
                # 模拟语音文本触发
                self.log(f"开始测试模拟语音文本触发副视频: 关键词='{keyword}', 时间={duration}秒")

                # 直接触发副视频切换
                self.log(f"使用文本中的副视频触发格式直接切换副视频")
                success = self.switch_to_sub_video(keyword, duration)

                if success:
                    # 获取媒体源名称（兼容新旧格式）
                    if isinstance(self.sub_video_sources[keyword], dict):
                        source_name = self.sub_video_sources[keyword].get('source', '未知')
                    else:
                        source_name = self.sub_video_sources[keyword]

                    self.log(f"测试成功: 已切换到副视频 '{source_name}'")
                    messagebox.showinfo("测试成功", f"已切换到副视频 '{source_name}'，将在 {duration} 秒后切回主视频")
                else:
                    self.log(f"测试失败: 无法切换到副视频")
                    messagebox.showerror("测试失败", "无法切换到副视频，请查看日志了解详细原因")

        # 创建按钮
        cancel_button = ttk.Button(button_frame, text="取消", command=test_dialog.destroy)
        cancel_button.pack(side=tk.RIGHT, padx=5)

        test_button = ttk.Button(button_frame, text="开始测试", command=run_test, style="Primary.TButton")
        test_button.pack(side=tk.RIGHT, padx=5)

        # 绑定Enter键到测试按钮
        test_dialog.bind("<Return>", lambda _: run_test())

        # 等待对话框关闭
        self.root.wait_window(test_dialog)

    def create_log_area(self):
        """创建操作日志区"""
        # 创建日志区框架
        self.log_frame = ttk.LabelFrame(self.log_tab, text="操作日志")
        self.log_frame.pack(fill=tk.BOTH, expand=True, padx=8, pady=8)

        # 创建标题栏
        title_frame = ttk.Frame(self.log_frame)
        title_frame.pack(fill=tk.X, padx=10, pady=(10, 0))

        ttk.Label(title_frame, text="系统操作日志", style="Title.TLabel").pack(side=tk.LEFT)

        # 添加分隔线
        separator = ttk.Separator(self.log_frame, orient="horizontal")
        separator.pack(fill=tk.X, padx=10, pady=(5, 10))

        # 创建日志显示区
        self.log_display = scrolledtext.ScrolledText(self.log_frame, wrap=tk.WORD, state="disabled")
        self.log_display.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建控制区
        control_frame = ttk.Frame(self.log_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        # 创建导出日志按钮
        self.export_log_button = ttk.Button(control_frame, text="导出日志", command=self.export_log, style="Accent.TButton")
        self.export_log_button.pack(side=tk.RIGHT, padx=5)

        # 创建清空日志按钮
        self.clear_log_button = ttk.Button(control_frame, text="清空日志", command=self.clear_log, style="Accent.TButton")
        self.clear_log_button.pack(side=tk.RIGHT, padx=5)

    def clear_log(self):
        """清空日志"""
        self.log_display.config(state="normal")
        self.log_display.delete(1.0, tk.END)
        self.log_display.config(state="disabled")
        self.log("日志已清空")

    def export_log(self):
        """导出日志到文件"""
        try:
            import datetime
            import os
            from tkinter import filedialog

            # 获取当前时间作为文件名
            now = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"client_log_{now}.txt"

            # 打开文件选择对话框
            file_path = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                initialfile=default_filename,
                title="导出日志"
            )

            if not file_path:
                # 用户取消了操作
                return

            # 获取日志内容
            self.log_display.config(state="normal")
            log_content = self.log_display.get(1.0, tk.END)
            self.log_display.config(state="disabled")

            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(log_content)

            self.log(f"日志已导出到: {file_path}")

            # 尝试打开文件所在目录
            try:
                import subprocess
                # 获取文件目录
                file_dir = os.path.dirname(os.path.abspath(file_path))
                # 打开文件目录
                if os.name == 'nt':  # Windows
                    os.startfile(file_dir)
                elif os.name == 'posix':  # macOS/Linux
                    subprocess.call(['open', file_dir])
            except Exception as e:
                self.log(f"打开文件目录失败: {str(e)}", "error")
        except Exception as e:
            self.log(f"导出日志失败: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")

    def log(self, message, log_type="system"):
        """在日志区添加日志消息

        Args:
            message (str): 日志消息
            log_type (str): 日志类型，可选值：
                - system: 系统日志（默认）
                - voice: 语音相关日志
                - danmaku: 弹幕相关日志
                - obs: OBS相关日志
                - network: 网络相关日志
                - error: 错误日志
        """
        # 检查日志类型是否有效
        valid_types = ["system", "voice", "danmaku", "obs", "network", "error"]
        if log_type not in valid_types:
            print(f"[日志警告] 日志类型 '{log_type}' 无效，将使用默认类型 'system'")
            log_type = "system"

        # 打印调试信息
        print(f"[日志生成] 消息: {message}, 类型: {log_type}")

        # 使用队列在主线程中更新UI
        try:
            # 将日志消息和类型作为字典传递
            log_data = {
                'data': message,
                'log_type': log_type
            }
            self.ui_queue.put({'type': 'log', 'data': log_data})
            print(f"[日志队列] 成功添加日志到UI队列")
        except Exception as e:
            # 如果队列不可用，尝试直接输出
            print(f"UI队列不可用，直接输出日志: {message} [类型: {log_type}]")
            print(f"UI队列错误: {str(e)}")

    def _log_impl(self, data):
        """日志实现方法，在主线程中调用"""
        try:
            import datetime
            print(f"[日志实现] 开始处理日志: {data}")

            # 解析日志数据
            message = data.get('data', '')
            log_type = data.get('log_type', 'system')
            print(f"[日志实现] 解析结果: message={message}, log_type={log_type}")

            # 获取当前时间
            now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 根据日志类型设置标记
            type_markers = {
                'system': '[SYS]',
                'voice': '[VOICE]',
                'danmaku': '[DANMAKU]',
                'obs': '[OBS]',
                'network': '[NET]',
                'error': '[ERROR]'
            }
            type_marker = type_markers.get(log_type, '[SYS]')

            # 格式化日志消息
            log_message = f"[{now}] {type_marker} {message}\n"
            timestamp = now
            print(f"[日志实现] 格式化日志: {log_message.strip()}")

            # 检查log_display是否存在
            if not hasattr(self, "log_display") or self.log_display is None:
                print("[日志实现] log_display不存在，无法显示日志")
                return

            # 在日志区添加消息
            try:
                self.log_display.config(state="normal")
                self.log_display.insert(tk.END, log_message)

                # 限制日志数量，避免内存溢出
                log_content = self.log_display.get(1.0, tk.END)
                if len(log_content) > 50000:  # 如果日志超过50000个字符
                    # 删除前半部分
                    self.log_display.delete(1.0, float(len(log_content) // 2))
            except Exception as e:
                print(f"[日志实现] 更新日志显示出错: {str(e)}")

            # 使用try-except包装滚动操作，防止KeyboardInterrupt导致程序崩溃
            try:
                # 使用root.after在主线程中安全地执行滚动操作
                self.root.after(0, lambda: self._safe_see_end())
            except Exception as e:
                print(f"调度滚动日志显示出错: {str(e)}")

            self.log_display.config(state="disabled")

            # WebSocket相关代码已删除

            # 只记录待播放列表中正在播放的语音话术文本和接收到的弹幕信息
            should_log_to_server = False

            # 检查是否是需要记录的日志类型
            if log_type == 'voice' and '正在播放' in message:
                # 记录正在播放的语音
                should_log_to_server = True
            elif log_type == 'danmaku':
                # 记录接收到的弹幕
                should_log_to_server = True

            # 日志服务器相关代码（WebSocket已删除）
            if should_log_to_server:
                try:
                    # 检查用户名和机器码
                    username = self.username if hasattr(self, "username") else ""
                    machine_code = self.machine_code if hasattr(self, "machine_code") else ""
                    print(f"[日志实现] 用户信息: username={username}, machine_code={machine_code}")

                    # 准备日志数据
                    log_data = {
                        "client_log": {
                            "timestamp": timestamp,
                            "message": message,
                            "log_type": log_type,
                            "username": username,
                            "machine_code": machine_code
                        }
                    }

                    # 打印调试信息
                    print(f"[日志实现] 日志数据准备完成: {log_data}")

                    # 可以在这里添加HTTP方式发送日志到服务器的代码
                    # 目前仅记录到本地
                except Exception as e:
                    print(f"[日志实现] 准备日志数据出错: {str(e)}")
                    import traceback
                    print(traceback.format_exc())
        except Exception as e:
            # 如果日志实现出错，直接打印到控制台
            print(f"[日志实现] 日志实现出错: {str(e)}")
            print(f"[日志实现] 原始日志数据: {data}")
            import traceback
            print(traceback.format_exc())

    def _safe_see_end(self):
        """安全地执行滚动到最后的操作"""
        try:
            if hasattr(self, "log_display") and self.log_display:
                self.log_display.see(tk.END)
        except Exception as e:
            print(f"安全滚动日志显示出错: {str(e)}")

    def process_voice_download_response(self, data):
        """处理语音下载响应 - 使用测试脚本中的成功代码"""
        try:
            response = data.get('response')
            voice = data.get('voice')
            text = data.get('text', '')

            if not response or not voice:
                self.log("处理语音下载响应失败: 缺少必要参数", "error")
                return

            # 获取输出文件路径
            output_file = voice.get("output_file")
            if not output_file:
                self.log("处理语音下载响应失败: 未指定输出文件路径", "error")
                return

            # 获取回调函数
            callback = voice.get("callback")

            # 检查响应状态码
            if response.status_code != 200:
                self.log(f"请求语音失败: HTTP {response.status_code}", "error")
                self._handle_voice_failure(callback)
                return

            # 检查响应内容
            content_type = response.headers.get('Content-Type', '')
            content_length = response.headers.get('Content-Length', 'unknown')
            self.log(f"响应内容类型: {content_type}, 内容长度: {content_length}")

            try:
                # 确保目录存在
                import os
                os.makedirs(os.path.dirname(output_file), exist_ok=True)

                # 保存文件
                with open(output_file, 'wb') as f:
                    f.write(response.content)

                self.log(f"成功保存语音文件: {output_file}")

                # 更新语音队列中的文件路径
                if hasattr(self, "voice_queue") and self.voice_queue:
                    found_item = False
                    for i, item in enumerate(self.voice_queue):
                        if item.get("text") == text and (
                            item.get("speaker_id") == voice.get("speaker_id") or
                            item.get("speaker") == voice.get("speaker")
                        ):
                            self.voice_queue[i]["file_path"] = output_file
                            self.log(f"已更新语音队列中的文件路径: {output_file}")
                            found_item = True

                            # 如果是队列中的第一个项目，并且没有正在播放，开始播放
                            if i == 0 and not self.is_playing:
                                self.log("这是队列中的第一个项目，开始播放")
                                self.play_next_voice()
                            break

                    # 如果没有找到匹配的项目，可能是因为队列已经被修改
                    if not found_item:
                        self.log(f"警告: 未找到匹配的语音项目，可能队列已被修改", "warning")
                        # 如果队列不为空且第一个项目没有文件路径，尝试使用当前文件
                        if self.voice_queue and not self.voice_queue[0].get("file_path"):
                            self.voice_queue[0]["file_path"] = output_file
                            self.log(f"已将文件路径分配给队列中的第一个项目: {output_file}")

                            # 如果没有正在播放，开始播放
                            if not self.is_playing:
                                self.log("队列中有项目且没有正在播放，开始播放")
                                self.play_next_voice()

                # 调用回调函数
                if callback:
                    callback(True, output_file)

                # 移除队列中的第一个项目（如果它已经有文件路径）
                if hasattr(self, "voice_queue") and self.voice_queue and self.voice_queue[0].get("file_path"):
                    # 只有在不是当前正在播放的项目时才移除
                    if not self.is_playing or (hasattr(self, "current_playing_item") and self.current_playing_item != self.voice_queue[0]):
                        self.log(f"下载完成，移除队列中的第一个项目")
                        self.voice_queue.pop(0)

                # 继续处理下一个语音
                if hasattr(self, "voice_queue") and self.voice_queue:
                    # 检查是否有待下载的语音
                    need_download = False
                    for item in self.voice_queue:
                        if not item.get("file_path") and not item.get("downloading", False):
                            need_download = True
                            break

                    if need_download:
                        self.log("队列中还有待下载的语音，继续处理")
                        self.ui_queue.put({'type': 'download_next', 'data': None})

            except Exception as e:
                self.log(f"保存语音文件失败: {str(e)}", "error")
                import traceback
                self.log(traceback.format_exc(), "error")
                self._handle_voice_failure(callback)

        except Exception as e:
            self.log(f"处理语音下载响应异常: {str(e)}", "error")
            import traceback
            self.log(traceback.format_exc(), "error")

    def handle_voice_download_error(self, data):
        """处理语音下载错误"""
        error_msg = data.get('error', '未知错误')
        voice = data.get('voice', None)

        self.log(f"语音下载错误: {error_msg}", "error")

        # 如果提供了具体的语音数据，使用它
        if voice:
            callback = voice.get("callback")
            self._handle_voice_failure(callback)
        # 否则使用队列中的第一个语音
        elif hasattr(self, "voice_queue") and self.voice_queue:
            voice_data = self.voice_queue[0]
            callback = voice_data.get("callback")
            self._handle_voice_failure(callback)

        # 继续处理下一个语音
        if hasattr(self, "voice_queue") and self.voice_queue:
            # 检查是否有待下载的语音
            need_download = False
            for item in self.voice_queue:
                if not item.get("file_path") and not item.get("downloading", False):
                    need_download = True
                    break

            if need_download:
                self.log("队列中还有待下载的语音，继续处理")
                self.ui_queue.put({'type': 'download_next', 'data': None})





    # 删除重复的test_sub_video_trigger方法

    def send_danmaku(self):
        """发送弹幕"""
        # 获取输入框中的内容
        danmaku_text = self.danmaku_input.get().strip()
        if not danmaku_text:
            return

        # 清空输入框
        self.danmaku_input.delete(0, tk.END)

        # 在弹幕显示区添加弹幕
        self.danmaku_display.config(state="normal")
        self.danmaku_display.insert(tk.END, f"用户: {danmaku_text}\n")
        self.danmaku_display.see(tk.END)  # 滚动到最后
        self.danmaku_display.config(state="disabled")

        # 处理弹幕，检查是否匹配关键词
        self.process_danmaku(danmaku_text, "测试用户")

    def process_danmaku(self, text, user_name="", gift_name=""):
        """处理弹幕，检查是否匹配关键词"""
        if not self.dialogue_data:
            return

        # 保存当前用户名和礼物名称，用于变量替换
        self.current_user_name = user_name
        self.current_gift_name = gift_name

        # 遍历所有关键词，检查是否匹配
        for _, item in enumerate(self.dialogue_data):
            if "gjc" in item:
                keyword = item["gjc"]
                # 检查关键词是否在文本中，或者是否为同义词
                if keyword in text or self.check_synonym(keyword, text):
                    # 找到匹配的关键词
                    self.log(f"弹幕匹配到关键词: '{keyword}'", "danmaku")
                    response = item.get("aidf", "")

                    # 处理回复中的变量并检查副视频触发
                    processed_response, sub_video_info = self.process_variables(response)

                    # 在弹幕显示区添加回复
                    self.danmaku_display.config(state="normal")
                    self.danmaku_display.insert(tk.END, f"AI: {processed_response}\n")
                    self.danmaku_display.see(tk.END)  # 滚动到最后
                    self.danmaku_display.config(state="disabled")

                    self.log(f"弹幕匹配关键词: {keyword}")

                    # 触发视频语音匹配系统的AI对话语音生成
                    if hasattr(self, "video_match_system") and self.video_match_system:
                        # 检查视频语音匹配系统是否已启动
                        if self.video_match_system.video_match_settings.get("is_running", False):
                            self.log(f"生成AI对话语音: {processed_response[:30]}...")
                            self.video_match_system.generate_ai_dialog_voice(processed_response, user_name)

                    # 处理副视频触发
                    if sub_video_info and hasattr(self, "obs_api") and self.obs_api:
                        source = sub_video_info.get("source", "")
                        duration = sub_video_info.get("duration", 5)
                        if source:
                            self.log(f"检测到副视频触发: 源={source}, 时间={duration}秒")
                            self.switch_to_sub_video(source, duration)

                    # 生成语音回复
                    if hasattr(self, "current_speaker") and self.current_speaker:
                        # 获取预备语音数量 - 这里只是为了日志记录，实际不使用这个变量
                        _prepared_count = 5  # 默认值
                        if hasattr(self, "prepared_count_var"):
                            try:
                                _prepared_count = int(self.prepared_count_var.get())
                                self.log(f"当前预备语音数量设置为: {_prepared_count}")
                            except Exception as ex:
                                self.log(f"获取预备语音数量出错: {str(ex)}")

                        # 检查队列中是否已经有相同关键词的回复
                        # AI对话触发的语音不受预备语音数量限制
                        keyword_exists = False
                        for voice_item in self.voice_queue:
                            # 如果当前正在播放的是这个关键词的回复，也算作已存在
                            if self.is_playing and voice_item == self.voice_queue[0] and keyword in voice_item.get("keyword", ""):
                                keyword_exists = True
                                break
                            # 检查队列中的其他项
                            if "keyword" in voice_item and voice_item["keyword"] == keyword:
                                keyword_exists = True
                                self.log(f"队列中已存在关键词 '{keyword}' 的回复，不重复生成")
                                break

                        if not keyword_exists:
                                # 创建新的语音条目，并记录关键词
                                new_voice_item = {
                                    "text": processed_response,
                                    "speaker_id": self.current_speaker["id"],
                                    "speed": self.speaker_speed,
                                    "file_path": None,  # 将在下载后设置
                                    "keyword": keyword,  # 记录触发这个语音的关键词
                                    "ai_triggered": True  # 标记为AI对话触发的语音
                                }

                                # 如果有副视频触发信息，添加到语音条目中
                                if sub_video_info:
                                    new_voice_item["sub_video_info"] = sub_video_info
                                    self.log(f"关键词 '{keyword}' 的回复包含副视频触发")

                                # 将新语音插入到队列的第二个位置（当前正在播放的之后）
                                if self.is_playing and len(self.voice_queue) > 0:
                                    # 如果正在播放，插入到第二个位置
                                    self.voice_queue.insert(1, new_voice_item)
                                    self.log(f"已生成关键词 '{keyword}' 的回复语音并插入到队列第二个位置 (队列总数: {len(self.voice_queue)})")
                                else:
                                    # 如果没有正在播放，直接添加到队列开头
                                    self.voice_queue.insert(0, new_voice_item)
                                    self.log(f"已生成关键词 '{keyword}' 的回复语音并插入到队列开头 (队列总数: {len(self.voice_queue)})")

                                # 更新播放列表显示
                                self.update_playlist()

                                # 开始下载语音
                                self.download_next_voice()

                                # 如果没有正在播放，开始播放
                                if not self.is_playing and self.voice_queue and self.voice_queue[0]["file_path"] is not None:
                                    self.play_next_voice()

                    # 只处理第一个匹配的关键词
                    break

    def process_variables(self, text):
        """处理文本中的变量"""
        import datetime

        # 初始化副视频触发信息
        sub_video_info = None

        # 替换日期变量
        if "{date}" in text:
            today = datetime.datetime.now().strftime("%Y年%m月%d日")
            text = text.replace("{date}", today)

        # 替换时间变量
        if "{time}" in text:
            now = datetime.datetime.now().strftime("%H时%M分")
            text = text.replace("{time}", now)

        # 替换昵称变量
        if "{nick}" in text and hasattr(self, "current_user_name") and self.current_user_name:
            text = text.replace("{nick}", self.current_user_name)
        elif "{nick}" in text:
            text = text.replace("{nick}", "观众")

        # 替换人数变量
        if "{people}" in text and hasattr(self, "online_count"):
            text = text.replace("{people}", str(self.online_count))

        # 替换游戏类型变量
        if "{gametype}" in text and hasattr(self, "game_type_var"):
            game_type = self.game_type_var.get()
            text = text.replace("{gametype}", game_type)

        # 替换游戏名称变量
        if "{gamename}" in text and hasattr(self, "game_name_var"):
            game_name = self.game_name_var.get()
            text = text.replace("{gamename}", game_name)

        # 替换礼物变量
        if "{gift}" in text and hasattr(self, "current_gift_name") and self.current_gift_name:
            text = text.replace("{gift}", self.current_gift_name)
        elif "{gift}" in text:
            text = text.replace("{gift}", "礼物")

        # 替换用户变量
        if "{user1}" in text and hasattr(self, "recent_users") and len(self.recent_users) > 0:
            text = text.replace("{user1}", self.recent_users[0] or "观众")

        if "{user2}" in text and hasattr(self, "recent_users") and len(self.recent_users) > 1:
            text = text.replace("{user2}", self.recent_users[1] or "观众")

        if "{user3}" in text and hasattr(self, "recent_users") and len(self.recent_users) > 2:
            text = text.replace("{user3}", self.recent_users[2] or "观众")

        # 检查是否有副视频关键词
        if hasattr(self, "sub_video_sources") and self.sub_video_sources:
            for keyword, info in self.sub_video_sources.items():
                if keyword in text:
                    if isinstance(info, dict):
                        sub_video_info = info
                    else:
                        # 兼容旧格式
                        sub_video_info = {"source": info, "duration": 5}
                    self.log(f"检测到副视频关键词: '{keyword}'")
                    break

        return text, sub_video_info

    def check_synonym(self, keyword, text):
        """检查关键词的同义词是否在文本中"""
        # 定义同义词字典
        synonyms = {
            "进入直播间": ["进入了直播间", "来到直播间", "加入直播间", "进入房间", "进房间"],
            "点赞": ["点了赞", "点了个赞", "给主播点赞", "赞了", "点了个赞"],
            "关注": ["关注了主播", "成为粉丝", "成为关注者", "关注了", "已关注"]
        }

        # 检查关键词是否有同义词
        if keyword in synonyms:
            # 检查同义词是否在文本中
            for synonym in synonyms[keyword]:
                if synonym in text:
                    self.log(f"关键词 '{keyword}' 的同义词 '{synonym}' 匹配成功", "danmaku")
                    return True

        return False

    def api_request(self, url, method="GET", data=None, json_data=None):
        """统一的API请求方法，带有错误处理和日志"""
        try:
            if method.upper() == "GET":
                response = requests.get(url, params=data, timeout=10)
            else:
                response = requests.post(url, data=data, json=json_data, timeout=10)

            # 尝试解析JSON响应
            try:
                result = response.json()
                return response.status_code, result
            except:
                return response.status_code, response.text
        except Exception as e:
            self.log(f"API请求错误: {str(e)}")
            return None, str(e)

    def insert_variable(self, widget, code, desc=""):
        """在文本控件的光标位置插入变量代码"""
        try:
            # 插入变量代码
            widget.insert(tk.INSERT, code)

            # 重新获取焦点
            widget.focus_set()

            # 记录日志
            self.log(f"已插入变量: {desc if desc else code}")
        except Exception as e:
            self.log(f"插入变量失败: {str(e)}")

    def create_new_dialogue(self):
        """创建新AI对话"""
        # 创建新建对话的对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("新建AI对话")
        dialog.geometry("400x150")
        dialog.transient(self.root)  # 设置为主窗口的子窗口
        dialog.grab_set()  # 模态对话框

        # 创建对话名称输入框
        name_frame = ttk.Frame(dialog, padding=10)
        name_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(name_frame, text="对话名称:").pack(side=tk.LEFT, padx=(0, 5))
        name_entry = ttk.Entry(name_frame, width=30)
        name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        name_entry.focus_set()  # 设置焦点

        # 创建确认按钮
        def confirm_create():
            dialogue_name = name_entry.get().strip()
            if not dialogue_name:
                messagebox.showwarning("新建AI对话", "对话名称不能为空")
                return

            # 检查名称是否已存在
            if dialogue_name in self.dialogue_list:
                messagebox.showwarning("新建AI对话", f"对话 '{dialogue_name}' 已存在")
                return

            # 发送请求创建新对话
            try:
                import requests
                import json

                # 发送请求创建新对话
                payload = {
                    "类型": "新建ai对话",
                    "ai对话名": dialogue_name  # 使用"ai对话名"参数以与其他API保持一致
                }

                # 使用本地服务器
                response = requests.post("http://localhost:12456/", json=payload, timeout=10)

                if response.status_code == 200:
                    # 添加到对话列表
                    self.dialogue_list.append(dialogue_name)
                    self.dialogue_combobox['values'] = self.dialogue_list
                    self.dialogue_combobox.set(dialogue_name)
                    self.current_dialogue = dialogue_name

                    # 清空关键词列表和回复内容
                    self.keyword_listbox.delete(0, tk.END)
                    self.response_text.delete(1.0, tk.END)
                    self.dialogue_data = []

                    # 添加默认关键词
                    self.dialogue_data.append({"gjc": "关键词1", "aidf": "这是关键词1的回复内容"})
                    self.dialogue_data.append({"gjc": "关键词2", "aidf": "这是关键词2的回复内容"})

                    # 更新关键词列表
                    for item in self.dialogue_data:
                        self.keyword_listbox.insert(tk.END, item["gjc"])

                    # 选中第一个关键词
                    if self.dialogue_data:
                        self.keyword_listbox.selection_set(0)
                        self.on_keyword_selected(None)

                    self.log(f"已创建新AI对话: {dialogue_name}")
                    dialog.destroy()
                else:
                    messagebox.showerror("错误", f"创建AI对话失败: HTTP {response.status_code}")
            except Exception as e:
                self.log(f"创建AI对话出错: {str(e)}")
                messagebox.showerror("错误", f"创建AI对话时发生错误: {str(e)}")

        # 创建按钮区域
        button_frame = ttk.Frame(dialog, padding=10)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        cancel_button = ttk.Button(button_frame, text="取消", command=dialog.destroy)
        cancel_button.pack(side=tk.RIGHT, padx=5)

        confirm_button = ttk.Button(button_frame, text="创建", command=confirm_create, style="Primary.TButton")
        confirm_button.pack(side=tk.RIGHT, padx=5)

        # 绑定Enter键到确认按钮
        dialog.bind("<Return>", lambda _: confirm_create())

        # 等待对话框关闭
        self.root.wait_window(dialog)

    def create_tooltip(self, widget, text):
        """为控件创建悬停提示"""
        def enter(_):
            # 创建提示窗口
            x = y = 0
            x, y, _, _ = widget.bbox("insert")
            x += widget.winfo_rootx() + 25
            y += widget.winfo_rooty() + 25

            # 创建提示窗口
            self.tooltip = tk.Toplevel(widget)
            self.tooltip.wm_overrideredirect(True)  # 无边框窗口
            self.tooltip.wm_geometry(f"+{x}+{y}")

            # 创建提示标签
            label = ttk.Label(self.tooltip, text=text, background="#ffffe0", relief="solid", borderwidth=1)
            label.pack()

        def leave(_):
            # 销毁提示窗口
            if hasattr(self, "tooltip"):
                self.tooltip.destroy()

        # 绑定鼠标进入和离开事件
        widget.bind("<Enter>", enter)
        widget.bind("<Leave>", leave)

    def load_gift_id_map(self):
        """加载礼物ID映射"""
        self.gift_id_map = {}  # 初始化礼物ID映射字典
        try:
            gift_map_file = "礼物ID转礼物名称.txt"
            if os.path.exists(gift_map_file):
                with open(gift_map_file, "r", encoding="utf-8") as f:
                    lines = f.readlines()
                    for line in lines:
                        line = line.strip()
                        # 支持两种分隔符："|" 和 "="
                        if line and ("|" in line or "=" in line):
                            if "|" in line:
                                parts = line.split("|", 1)
                            else:
                                parts = line.split("=", 1)

                            if len(parts) == 2:
                                gift_id = parts[0].strip()
                                gift_name = parts[1].strip()
                                self.gift_id_map[gift_id] = gift_name
                self.log(f"已加载{len(self.gift_id_map)}个礼物ID映射")
                # 输出部分礼物ID映射信息供调试
                sample_gifts = list(self.gift_id_map.items())[:5]  # 取前5个礼物作为示例
                self.log(f"礼物ID映射示例: {sample_gifts}", "system")
            else:
                self.log("礼物ID映射文件不存在")
                # 如果文件不存在，创建一个模板文件
                with open(gift_map_file, "w", encoding="utf-8") as f:
                    f.write("# 礼物ID=礼物名称\n")
                    f.write("1=点赞\n")
                    f.write("2=爱心\n")
                    f.write("3=鲜花\n")
                self.log("已创建礼物ID映射文件模板")
                # 初始化一些默认的礼物映射
                self.gift_id_map = {"1": "点赞", "2": "爱心", "3": "鲜花"}
        except Exception as e:
            self.log(f"加载礼物ID映射出错: {str(e)}")
            # 出错时使用默认映射
            self.gift_id_map = {"1": "点赞", "2": "爱心", "3": "鲜花"}

    def connect_to_danmaku(self):
        """连接到WebSocket弹幕服务器"""
        if self.ws_connected:
            self.log("已经连接到弹幕服务器")
            return

        try:
            # 导入websocket模块
            import websocket
            import threading
            import queue

            # 创建消息队列，用于在线程之间传递消息
            if not hasattr(self, 'ws_message_queue'):
                self.ws_message_queue = queue.Queue()

            # 更新UI状态
            self.ws_status_var.set("正在连接...")
            self.ws_connect_button.config(state="disabled")
            self.root.update()

            # 创建WebSocket连接
            danmaku_server_url = "ws://127.0.0.1:9999"  # 弹幕服务器地址

            # 只在详细或正常日志级别下输出连接信息
            if self.danmaku_log_level < 2:
                print(f"正在连接到弹幕服务器: {danmaku_server_url}")
            self.log(f"正在连接到弹幕服务器: {danmaku_server_url}")

            # 根据测试模式决定是否启动测试服务器
            if hasattr(self, 'use_test_server') and self.use_test_server:
                print(f"测试模式: 正在连接到弹幕测试服务器: {danmaku_server_url}")
                self.log(f"测试模式: 正在连接到弹幕测试服务器: {danmaku_server_url}")
                # 启动测试服务器
                try:
                    import danmaku_test
                    self.test_server_thread = danmaku_test.start_test_server()
                    self.log("弹幕测试服务器已启动")
                    # 等待服务器启动
                    time.sleep(1)
                except Exception as e:
                    print(f"启动弹幕测试服务器出错: {str(e)}")
                    self.log(f"启动弹幕测试服务器出错: {str(e)}", "error")
            else:
                print(f"正在连接到外部弹幕服务器: {danmaku_server_url}")
                self.log(f"正在连接到外部弹幕服务器: {danmaku_server_url}")

            # 定义WebSocket回调函数
            def on_message(ws, message):
                # 不打印每条消息内容，减少无用输出
                # 将消息放入队列，由主线程处理
                self.ws_message_queue.put(('message', message))

            def on_error(ws, error):
                # 只在详细日志级别下打印错误
                if self.danmaku_log_level == 0:
                    print(f"连接错误: {error}")
                # 将错误放入队列，由主线程处理
                self.ws_message_queue.put(('error', str(error)))

            def on_close(ws, close_status_code, close_msg):
                # 只在详细日志级别下打印关闭信息
                if self.danmaku_log_level == 0:
                    print(f"连接已关闭: {close_status_code} {close_msg}")
                # 将关闭信息放入队列，由主线程处理
                self.ws_message_queue.put(('close', (close_status_code, close_msg)))

            def on_open(ws):
                # 只在详细或正常日志级别下打印连接成功信息
                if self.danmaku_log_level < 2:
                    print("已成功连接到弹幕服务器")
                # 将连接成功信息放入队列，由主线程处理
                self.ws_message_queue.put(('open', None))
                # 直接在主线程中更新状态，确保状态能够正确更新
                self.root.after(0, self.update_ws_status_connected)

            # 创建WebSocket连接
            self.ws = websocket.WebSocketApp(danmaku_server_url,
                                          on_open=on_open,
                                          on_message=on_message,
                                          on_error=on_error,
                                          on_close=on_close)

            # 在新线程中运行WebSocket连接
            def run_websocket():
                try:
                    # 禁用调试输出
                    websocket.enableTrace(False)  # 禁用跟踪以减少无用的输出
                    # 只在详细或正常日志级别下打印连接信息
                    if self.danmaku_log_level < 2:
                        print("开始运行WebSocket连接...")
                    # 使用与测试脚本相同的方式运行WebSocket连接
                    # 添加ping_interval和ping_timeout参数，启用心跳机制保持连接活跃
                    # ping_interval: 发送ping的间隔时间（秒）
                    # ping_timeout: 等待pong响应的超时时间（秒）
                    self.ws.run_forever(ping_interval=30, ping_timeout=10)
                    # 只在详细或正常日志级别下打印连接信息
                    if self.danmaku_log_level < 2:
                        print("WebSocket连接已结束")
                except Exception as e:
                    # 只在详细或正常日志级别下打印错误信息
                    if self.danmaku_log_level < 2:
                        print(f"WebSocket连接线程出错: {str(e)}")
                        import traceback
                        print(traceback.format_exc())
                    # 将错误放入队列，由主线程处理
                    self.ws_message_queue.put(('thread_error', str(e)))

            # 创建并启动WebSocket线程
            self.ws_thread = threading.Thread(target=run_websocket, daemon=True)
            self.ws_thread.start()
            # 只在详细或正常日志级别下打印线程启动信息
            if self.danmaku_log_level < 2:
                print("弹幕连接线程已启动")
            self.log("弹幕连接线程已启动")

            # 启动消息处理定时器
            self.start_message_processing()

            # 设置一个超时定时器，如果10秒内没有收到连接成功消息，则直接更新状态
            def check_connection_status():
                if self.ws_status_var.get() == "正在连接...":
                    print("连接超时，直接更新状态")
                    self.update_ws_status_connected()

            # 10秒后检查连接状态
            self.root.after(10000, check_connection_status)

            # 启动连接状态监控定时器，定期检查WebSocket连接状态
            self.start_connection_monitor()

        except Exception as e:
            print(f"连接弹幕服务器出错: {str(e)}")
            import traceback
            print(traceback.format_exc())
            self.log(f"连接弹幕服务器出错: {str(e)}")
            self.update_ws_status_disconnected()

    def start_message_processing(self):
        """启动消息处理定时器"""
        # 每100毫秒检查一次消息队列
        self.process_messages()

    def process_messages(self):
        """处理消息队列中的消息"""
        try:
            # 如果消息队列不存在，则返回
            if not hasattr(self, 'ws_message_queue'):
                return

            # 每次处理最多5条消息，减少每批处理数量以减少卡顿
            message_count = 0
            max_messages_per_batch = 5

            # 处理队列中的部分消息
            while not self.ws_message_queue.empty() and message_count < max_messages_per_batch:
                try:
                    # 获取消息
                    msg_type, msg_data = self.ws_message_queue.get_nowait()
                    message_count += 1

                    # 根据消息类型处理
                    if msg_type == 'message':
                        # 处理弹幕消息
                        self.safe_handle_danmaku(msg_data)
                    elif msg_type == 'error':
                        # 处理错误
                        if self.danmaku_log_level < 2:  # 只在详细和正常日志级别下输出错误
                            self.safe_log(f"弹幕连接错误: {msg_data}")
                        self.update_ws_status_disconnected()
                    elif msg_type == 'close':
                        # 处理连接关闭
                        code_str, msg_str = msg_data if msg_data else (None, None)
                        if self.danmaku_log_level < 2:  # 只在详细和正常日志级别下输出错误
                            self.safe_log(f"弹幕连接已关闭: {code_str} {msg_str}")
                        self.update_ws_status_disconnected()
                    elif msg_type == 'open':
                        # 处理连接成功
                        if self.danmaku_log_level < 2:  # 只在详细和正常日志级别下输出日志
                            print("主线程处理连接成功消息")
                            self.safe_log("已成功连接到弹幕服务器")
                        # 直接更新状态，而不是通过safe_update_ui
                        self.ws_connected = True
                        self.ws_status_var.set("已连接")
                        self.ws_connect_button.config(state="disabled")
                        self.ws_disconnect_button.config(state="normal")
                        self.log("弹幕连接状态: 已连接")
                    elif msg_type == 'thread_error':
                        # 处理线程错误
                        if self.danmaku_log_level < 2:  # 只在详细和正常日志级别下输出错误
                            self.safe_log(f"WebSocket连接线程出错: {msg_data}", "error")
                        self.update_ws_status_disconnected()
                except Exception as e:
                    if self.danmaku_log_level < 2:  # 只在详细和正常日志级别下输出错误
                        print(f"处理消息出错: {str(e)}")
                        import traceback
                        print(traceback.format_exc())
        except Exception as e:
            if self.danmaku_log_level < 2:  # 只在详细和正常日志级别下输出错误
                print(f"消息处理定时器出错: {str(e)}")
                import traceback
                print(traceback.format_exc())
        finally:
            # 重新调度定时器，增加延迟时间以减少CPU占用
            if hasattr(self, 'root') and self.root:
                # 如果队列中还有消息，则快速处理，否则增加延迟
                # 增加延迟时间，减少CPU占用
                delay = 100 if not self.ws_message_queue.empty() else 300
                self.root.after(delay, self.process_messages)

    def update_ws_status_connected(self):
        """更新WebSocket连接状态为已连接"""
        self.ws_connected = True
        self.ws_status_var.set("已连接")
        self.ws_connect_button.config(state="disabled")
        self.ws_disconnect_button.config(state="normal")
        self.log("弹幕连接状态: 已连接")

    def update_ws_status_disconnected(self):
        """更新WebSocket连接状态为未连接"""
        self.ws_connected = False
        self.ws_status_var.set("未连接")
        self.ws_connect_button.config(state="normal")
        self.ws_disconnect_button.config(state="disabled")
        self.log("弹幕连接状态: 未连接")

    def start_connection_monitor(self):
        """启动WebSocket连接状态监控定时器"""
        self.monitor_connection_status()

    def monitor_connection_status(self):
        """监控WebSocket连接状态，如果连接已断开但状态仍显示为已连接，则自动更新状态"""
        try:
            # 检查WebSocket连接状态
            # 添加一个时间戳检查，如果超过60秒没有收到消息，则认为连接已断开
            current_time = time.time()

            # 初始化最后收到消息的时间
            if not hasattr(self, "last_message_time"):
                self.last_message_time = current_time

            # 如果连接状态为已连接，但WebSocket对象不存在，则更新状态
            if self.ws_connected and (not hasattr(self, "ws") or self.ws is None):
                if self.danmaku_log_level < 2:
                    print("检测到WebSocket对象不存在，更新状态")
                self.log("检测到WebSocket对象不存在，更新状态", "system")
                self.update_ws_status_disconnected()
            # 如果连接状态为已连接，但长时间没有收到消息，则认为连接已断开
            elif self.ws_connected and hasattr(self, "last_message_time") and (current_time - self.last_message_time) > 60:
                if self.danmaku_log_level < 2:
                    print(f"检测到长时间没有收到消息（{int(current_time - self.last_message_time)}秒），认为连接已断开")
                self.log(f"检测到长时间没有收到消息（{int(current_time - self.last_message_time)}秒），认为连接已断开", "system")
                # 尝试发送ping消息检测连接是否真正断开
                try:
                    if hasattr(self, "ws") and self.ws and hasattr(self.ws, "sock") and self.ws.sock:
                        self.ws.sock.ping()
                        if self.danmaku_log_level < 2:
                            print("发送ping消息成功，连接仍然活跃")
                        # 更新最后收到消息的时间，避免重复检测
                        self.last_message_time = current_time
                    else:
                        # WebSocket对象不存在或无法发送ping，更新状态
                        self.update_ws_status_disconnected()
                except Exception as e:
                    # 发送ping消息出错，认为连接已断开
                    if self.danmaku_log_level < 2:
                        print(f"发送ping消息出错: {str(e)}，认为连接已断开")
                    self.log(f"发送ping消息出错，认为连接已断开", "system")
                    self.update_ws_status_disconnected()
            # 如果连接状态为未连接，但仍然在接收弹幕，则更新状态为已连接
            elif not self.ws_connected and hasattr(self, "last_message_time") and (current_time - self.last_message_time) < 10:
                if self.danmaku_log_level < 2:
                    print("检测到正在接收弹幕，但状态显示为未连接，更新状态")
                self.log("检测到正在接收弹幕，但状态显示为未连接，更新状态", "system")
                self.update_ws_status_connected()

            # 尝试重新连接
            if not self.ws_connected and hasattr(self, "auto_reconnect") and self.auto_reconnect:
                self.log("尝试自动重新连接...", "system")
                self.root.after(1000, self.connect_to_danmaku)
        except Exception as e:
            if self.danmaku_log_level < 2:
                print(f"监控WebSocket连接状态出错: {str(e)}")
                import traceback
                print(traceback.format_exc())
        finally:
            # 每10秒检查一次连接状态
            if hasattr(self, "root") and self.root:
                self.root.after(10000, self.monitor_connection_status)

    def initialize_danmaku_queue(self):
        """初始化弹幕消息队列"""
        # 初始化弹幕相关变量
        self.danmaku_queue = queue.Queue()  # 弹幕消息队列
        self.danmaku_list = []  # 弹幕列表，最多保存20条
        self.current_user_name = ""  # 当前用户名
        self.current_gift_name = ""  # 当前礼物名称
        self.current_people_count = 0  # 当前人数
        self.last_people_count = 0  # 上次人数，初始化为整数类型
        self.allow_danmaku_voice = False  # 是否允许弹幕触发语音生成，默认为关闭

        # 初始化最近进入直播间的用户列表
        self.recent_users = ["", "", ""]  # 存储3个最近进入的用户名

        # 初始化WebSocket相关变量
        self.ws = None  # 弹幕WebSocket连接
        self.ws_connected = False  # 是否已连接
        self.ws_thread = None  # WebSocket线程
        self.use_test_server = False  # 是否使用测试服务器
        self.test_server_thread = None  # 测试服务器线程
        self.auto_reconnect = False  # 是否自动重连

        # 添加弹幕处理限流变量
        self.last_danmaku_process_time = 0  # 上次处理弹幕的时间
        self.danmaku_process_interval = 0.3  # 处理弹幕的最小间隔时间（秒），增加到300毫秒减少CPU占用
        self.danmaku_batch_size = 3  # 每批处理的弹幕数量，减少每批处理数量以减少卡顿
        self.danmaku_processing = False  # 是否正在处理弹幕
        self.danmaku_log_level = 1  # 弹幕日志级别：0=详细，1=正常，2=简单

        self.log("弹幕消息队列初始化完成")
        self.log(f"弹幕触发语音生成功能初始状态: allow_danmaku_voice={self.allow_danmaku_voice}", "system")

    def toggle_test_mode(self):
        """切换测试模式"""
        self.use_test_server = self.test_mode_var.get()
        if self.use_test_server:
            self.log("已开启弹幕测试模式，连接时将启动测试服务器")
        else:
            self.log("已关闭弹幕测试模式，将连接到外部弹幕服务器")

        # 如果当前已连接，则断开连接并提示用户重新连接
        if self.ws_connected:
            self.log("模式已切换，请断开当前连接并重新连接")
            messagebox.showinfo("模式切换", "测试模式已切换，请断开当前连接并重新连接以生效")

    def disconnect_from_danmaku(self):
        """断开与WebSocket弹幕服务器的连接"""
        if not self.ws_connected:
            print("未连接到弹幕服务器")
            self.log("未连接到弹幕服务器")
            return

        try:
            # 更新UI状态
            self.ws_status_var.set("正在断开...")
            self.ws_disconnect_button.config(state="disabled")
            self.root.update()

            print("正在断开与弹幕服务器的连接...")

            # 关闭WebSocket连接
            if self.ws:
                print("关闭WebSocket连接...")
                try:
                    self.ws.close()
                    print("WebSocket连接已关闭")
                except Exception as e:
                    print(f"WebSocket关闭出错: {str(e)}")
                finally:
                    self.ws = None

            # 清空消息队列
            if hasattr(self, 'ws_message_queue'):
                try:
                    while not self.ws_message_queue.empty():
                        self.ws_message_queue.get_nowait()
                    print("消息队列已清空")
                except Exception as e:
                    print(f"清空消息队列出错: {str(e)}")

            # 如果是测试模式，关闭测试服务器
            if hasattr(self, 'use_test_server') and self.use_test_server and hasattr(self, 'test_server_thread') and self.test_server_thread:
                print("关闭弹幕测试服务器...")
                self.log("关闭弹幕测试服务器...")
                try:
                    import danmaku_test
                    danmaku_test.shutdown()
                    print("弹幕测试服务器已关闭")
                    self.log("弹幕测试服务器已关闭")
                except Exception as e:
                    print(f"关闭弹幕测试服务器出错: {str(e)}")
                    self.log(f"关闭弹幕测试服务器出错: {str(e)}", "error")
                finally:
                    self.test_server_thread = None

            # 直接更新状态，而不是通过消息队列
            self.ws_connected = False
            self.ws_status_var.set("未连接")
            self.ws_connect_button.config(state="normal")
            self.ws_disconnect_button.config(state="disabled")
            print("已断开与弹幕服务器的连接")
            self.log("已断开与弹幕服务器的连接")

        except Exception as e:
            print(f"断开弹幕服务器连接出错: {str(e)}")
            import traceback
            print(traceback.format_exc())
            self.log(f"断开弹幕服务器连接出错: {str(e)}")
            # 直接更新状态，而不是通过消息队列
            self.ws_connected = False
            self.ws_status_var.set("未连接")
            self.ws_connect_button.config(state="normal")
            self.ws_disconnect_button.config(state="disabled")

    def handle_danmaku_message(self, message):
        """处理接收到的弹幕消息"""
        try:
            # 防止处理过多弹幕导致卡死
            # 如果消息为空或者过长，直接返回
            if not message or len(message) > 10000:  # 设置消息长度上限
                if self.danmaku_log_level == 0:  # 只在详细日志级别下输出
                    self.safe_log(f"弹幕消息过长或为空，已跳过处理", "danmaku")
                return

            # 解析JSON消息
            try:
                data = json.loads(message)
            except json.JSONDecodeError as e:
                if self.danmaku_log_level < 2:  # 只在详细和正常日志级别下输出错误
                    print(f"JSON解析错误: {str(e)}")
                    print(f"原始消息: {message[:100]}...")
                return

            message_type = data.get("type", "")

            # 根据消息类型处理
            if message_type == "ChatMessage":
                # 聊天消息
                name = data.get("name", "")
                content = data.get("content", "")

                # 限制内容长度，防止过长弹幕导致卡死
                if len(content) > 100:
                    content = content[:100] + "..."

                # 限制名称长度
                if len(name) > 20:
                    name = name[:20] + "..."

                # 只在详细或正常日志级别下输出弹幕日志
                if self.danmaku_log_level < 2:
                    self.safe_log(f"收到聊天消息: {name} 说: {content}", "danmaku")

                # 在弹幕显示区添加弹幕，使用延迟执行减少阻塞
                # 增加延迟时间，减少UI更新频率
                self.root.after(20, lambda n=name, c=content: self.add_danmaku_to_display(f"{n}: {c}"))

                # 处理弹幕，检查是否匹配关键词
                # 增加延迟时间，防止事件堆积
                self.root.after(50, lambda c=content, n=name: self.process_danmaku(c, n))

            elif message_type == "GiftMessage":
                # 礼物消息
                name = data.get("name", "")
                gift_id = str(data.get("giftId", ""))
                gift_name = self.gift_id_map.get(gift_id, f"未知礼物({gift_id})")

                # 限制名称长度
                if len(name) > 20:
                    name = name[:20] + "..."
                if len(gift_name) > 20:
                    gift_name = gift_name[:20] + "..."

                # 只在详细或正常日志级别下输出弹幕日志
                if self.danmaku_log_level < 2:
                    self.safe_log(f"收到礼物消息: {name} 送出了 {gift_name}", "danmaku")

                # 在弹幕显示区添加礼物信息，使用延迟执行减少阻塞
                # 增加延迟时间，减少UI更新频率
                self.root.after(20, lambda n=name, g=gift_name: self.add_danmaku_to_display(f"{n} 送出了 {g}"))

                # 处理礼物关键词
                # 增加延迟时间，防止事件堆积
                self.root.after(50, lambda g=gift_name, n=name: self.process_danmaku(g, n, g))

            elif message_type == "MemberMessage":
                # 进入直播间消息
                name = data.get("name", "")

                # 限制名称长度
                if len(name) > 20:
                    display_name = name[:20] + "..."
                else:
                    display_name = name

                # 只在详细或正常日志级别下输出弹幕日志
                if self.danmaku_log_level < 2:
                    self.safe_log(f"用户进入直播间: {display_name}", "danmaku")

                # 在弹幕显示区添加进入信息，使用延迟执行减少阻塞
                # 增加延迟时间，减少UI更新频率
                self.root.after(20, lambda dn=display_name: self.add_danmaku_to_display(f"{dn} 进入了直播间"))

                # 更新最近进入直播间的用户列表
                if name and hasattr(self, "recent_users"):
                    # 如果recent_users不存在或长度不足，初始化它
                    if not hasattr(self, "recent_users") or len(self.recent_users) < 3:
                        self.recent_users = ["", "", ""]

                    # 如果用户名不在列表中，才更新列表
                    if name not in self.recent_users:
                        # 将所有用户后移一位
                        self.recent_users[2] = self.recent_users[1]
                        self.recent_users[1] = self.recent_users[0]
                        self.recent_users[0] = name  # 最新用户放在第一位

                        # 只在详细日志级别下输出用户列表更新日志
                        if self.danmaku_log_level == 0:
                            self.safe_log(f"更新最近进入用户列表: {self.recent_users}", "system")

                # 处理进入直播间关键词，使用延迟执行减少阻塞
                # 增加延迟时间，防止事件堆积
                self.root.after(50, lambda n=name: self.process_danmaku("进入直播间", n))

            elif message_type == "live_like":
                # 点赞消息
                name = data.get("name", "")

                # 限制名称长度
                if len(name) > 20:
                    display_name = name[:20] + "..."
                else:
                    display_name = name

                # 只在详细或正常日志级别下输出弹幕日志
                if self.danmaku_log_level < 2:
                    self.safe_log(f"用户点赞: {display_name}", "danmaku")

                # 在弹幕显示区添加点赞信息，使用延迟执行减少阻塞
                # 增加延迟时间，减少UI更新频率
                self.root.after(20, lambda dn=display_name: self.add_danmaku_to_display(f"{dn} 点赞了直播间"))

                # 处理点赞关键词，使用延迟执行减少阻塞
                # 增加延迟时间，防止事件堆积
                self.root.after(50, lambda n=name: self.process_danmaku("点赞", n))

            elif message_type == "SocialMessage":
                # 关注消息
                name = data.get("name", "")

                # 限制名称长度
                if len(name) > 20:
                    display_name = name[:20] + "..."
                else:
                    display_name = name

                # 只在详细或正常日志级别下输出弹幕日志
                if self.danmaku_log_level < 2:
                    self.safe_log(f"用户关注: {display_name}", "danmaku")

                # 在弹幕显示区添加关注信息，使用延迟执行减少阻塞
                # 增加延迟时间，减少UI更新频率
                self.root.after(20, lambda dn=display_name: self.add_danmaku_to_display(f"{dn} 关注了主播"))

                # 处理关注关键词，使用延迟执行减少阻塞
                # 增加延迟时间，防止事件堆积
                self.root.after(50, lambda n=name: self.process_danmaku("关注", n))

            elif message_type == "RoomUserSeqMessage":
                # 直播间人数消息
                try:
                    # 将total转换为整数类型，防止字符串相减错误
                    total = int(data.get("total", 0))
                except (ValueError, TypeError):
                    # 如果转换失败，使用默认值0
                    total = 0
                    if self.danmaku_log_level < 2:
                        self.safe_log(f"人数数据格式错误，使用默认值0", "danmaku")

                # 只在详细或正常日志级别下输出弹幕日志
                if self.danmaku_log_level < 2:
                    self.safe_log(f"直播间当前人数: {total}", "danmaku")

                # 更新人数变量，供变量替换使用
                self.current_people_count = total

                # 确保last_people_count也是整数类型
                if hasattr(self, "last_people_count"):
                    try:
                        last_count = int(self.last_people_count)
                    except (ValueError, TypeError):
                        last_count = 0
                else:
                    last_count = 0
                    self.last_people_count = 0

                # 在弹幕显示区添加人数信息，使用延迟执行减少阻塞
                # 人数更新信息不需要每次都显示，可以减少弹幕量
                # 增加人数变化阈值，只有变化超过20人才显示
                if abs(total - last_count) > 20:
                    # 增加延迟时间，减少UI更新频率
                    self.root.after(20, lambda t=total: self.add_danmaku_to_display(f"直播间当前人数: {t}"))
                    self.last_people_count = total

            else:
                # 其他类型消息
                # 只在详细日志级别下输出未知类型消息
                if self.danmaku_log_level == 0:
                    self.safe_log(f"收到未知类型消息: {message_type}", "danmaku")

        except Exception as e:
            print(f"处理弹幕消息出错: {str(e)}")
            import traceback
            print(traceback.format_exc())

    def add_danmaku_to_display(self, text):
        """在弹幕显示区添加弹幕"""
        try:
            # 使用root.after确保在主线程中执行
            def update_display():
                try:
                    if hasattr(self, "danmaku_display") and self.danmaku_display:
                        # 设置弹幕显示区的最大行数
                        max_lines = 50  # 减少到最多显示50行弹幕，减少内存占用

                        # 每10次更新才检查一次行数，减少计算开销
                        if not hasattr(self, "danmaku_update_count"):
                            self.danmaku_update_count = 0
                        self.danmaku_update_count += 1

                        self.danmaku_display.config(state="normal")

                        # 添加新弹幕
                        self.danmaku_display.insert(tk.END, f"{text}\n")

                        # 每10次更新才检查一次行数并清理
                        if self.danmaku_update_count >= 10:
                            self.danmaku_update_count = 0

                            # 直接删除前半部分内容，而不是精确计算行数
                            content = self.danmaku_display.get("1.0", tk.END)
                            line_count = content.count('\n')

                            if line_count > max_lines:
                                # 直接删除前半部分内容，减少计算开销
                                lines_to_keep = max_lines // 2
                                lines_to_delete = line_count - lines_to_keep
                                self.danmaku_display.delete("1.0", f"{lines_to_delete + 1}.0")

                        # 滚动到最后
                        self.danmaku_display.see(tk.END)
                        self.danmaku_display.config(state="disabled")

                        # 仅在必要时强制更新UI，减少UI阻塞
                        if self.danmaku_update_count == 0:
                            self.danmaku_display.update_idletasks()
                except Exception as e:
                    if self.danmaku_log_level < 2:  # 只在详细和正常日志级别下输出错误
                        print(f"添加弹幕到显示区出错: {str(e)}")

            # 在主线程中执行更新，使用低优先级
            if hasattr(self, "root") and self.root:
                # 使用较长的延迟时间，减少UI阻塞
                self.root.after(30, update_display)  # 增加延迟时间到30毫秒
            else:
                if self.danmaku_log_level < 2:
                    print("无法更新弹幕显示，root对象不存在")
        except Exception as e:
            if self.danmaku_log_level < 2:  # 只在详细和正常日志级别下输出错误
                print(f"添加弹幕到显示区出错: {str(e)}")


    def safe_handle_danmaku(self, message):
        """在主线程中安全地处理弹幕消息"""
        try:
            # 防止处理过多弹幕导致卡死
            # 如果消息为空或者过长，直接返回
            if not message or (isinstance(message, str) and len(message) > 10000):
                return

            # 更新最后收到消息的时间
            self.last_message_time = time.time()

            # 如果连接状态显示为未连接，但正在接收弹幕，则更新状态
            if not self.ws_connected:
                if self.danmaku_log_level < 2:
                    print("正在接收弹幕，但状态显示为未连接，更新状态")
                self.log("正在接收弹幕，但状态显示为未连接，更新状态", "system")
                self.update_ws_status_connected()

            # 将消息添加到队列中，而不是直接处理
            self.danmaku_queue.put(message)

            # 如果当前没有正在处理弹幕，启动处理
            if not self.danmaku_processing and hasattr(self, "root") and self.root:
                self.danmaku_processing = True
                self.root.after(10, self.process_danmaku_queue)

        except Exception as e:
            print(f"处理弹幕消息出错: {str(e)}")
            if isinstance(message, str):
                print(f"原始消息: {message[:100]}...")
            import traceback
            print(traceback.format_exc())

    def process_danmaku_queue(self):
        """批量处理弹幕队列中的消息"""
        try:
            # 检查是否有消息需要处理
            if self.danmaku_queue.empty():
                self.danmaku_processing = False
                return

            # 检查是否过了限流时间
            current_time = time.time()
            time_since_last = current_time - self.last_danmaku_process_time

            if time_since_last < self.danmaku_process_interval:
                # 如果还没到处理时间，延迟一会再处理
                delay = int((self.danmaku_process_interval - time_since_last) * 1000)
                self.root.after(delay, self.process_danmaku_queue)
                return

            # 更新上次处理时间
            self.last_danmaku_process_time = current_time

            # 获取队列大小，用于日志
            queue_size = self.danmaku_queue.qsize()

            # 批量处理消息
            processed = 0
            while not self.danmaku_queue.empty() and processed < self.danmaku_batch_size:
                message = self.danmaku_queue.get()
                try:
                    self.handle_danmaku_message(message)
                except Exception as e:
                    if self.danmaku_log_level < 2:  # 只在详细和正常日志级别下输出错误
                        print(f"处理弹幕消息出错: {str(e)}")
                processed += 1

            # 如果还有消息，安排下一批处理
            remaining = self.danmaku_queue.qsize()
            if remaining > 0:
                # 使用较长的延迟，给UI线程更多响应时间
                # 根据队列大小动态调整延迟，队列越大延迟越短
                delay = max(50, int(self.danmaku_process_interval * 1000 / (1 + remaining / 10)))
                self.root.after(delay, self.process_danmaku_queue)

                # 只在队列大小变化较大时记录日志，减少日志输出
                if self.danmaku_log_level == 0 or (self.danmaku_log_level == 1 and abs(remaining - queue_size) > 5):
                    self.log(f"弹幕队列状态: 已处理{processed}条，剩余{remaining}条", "system")
            else:
                self.danmaku_processing = False
                if self.danmaku_log_level == 0:  # 只在详细日志级别下输出
                    self.log("弹幕队列已清空", "system")

        except Exception as e:
            print(f"处理弹幕队列出错: {str(e)}")
            if self.danmaku_log_level < 2:  # 只在详细和正常日志级别下输出详细错误
                import traceback
                print(traceback.format_exc())
            # 出错后重置处理状态，并尝试继续处理
            self.danmaku_processing = False
            if hasattr(self, "root") and self.root:
                self.root.after(1000, self.process_danmaku_queue)

    def safe_log(self, message, log_type="system"):
        """在主线程中安全地记录日志"""
        try:
            self.log(message, log_type)
        except Exception as e:
            print(f"记录日志出错: {str(e)}")
            print(f"原始日志消息: {message}")

    def safe_update_ui(self, func, *args, **kwargs):
        """线程安全的UI更新方法"""
        try:
            if hasattr(self, 'root') and self.root:
                try:
                    # 尝试使用after方法在主线程中执行函数
                    self.root.after(0, lambda: func(*args, **kwargs))
                except Exception as e:
                    # 如果出现'main thread is not in main loop'错误，则直接执行函数
                    # 这可能在某些情况下工作，但不是所有情况
                    print(f"UI更新错误，尝试直接执行: {str(e)}")
                    try:
                        func(*args, **kwargs)
                    except Exception as e2:
                        print(f"直接执行UI更新失败: {str(e2)}")
        except Exception as e:
            print(f"安全UI更新方法出错: {str(e)}")

    def load_config(self):
        """加载配置文件"""
        import os

        # 优先使用 _internal/config.json
        internal_config_file = os.path.join("_internal", "config.json")
        config_file = "config.json"  # 备用配置文件路径

        default_config = {
            "voice": {
                "prepared_count": 5,
                "loop_mode": "random",
                "volume": 1.0,
                "pitch": 1.0,
                "interval": 1.0,
                "min_interval": 0.5,
                "max_interval": 2.0,
                "sound_card": "默认声卡",
                "speed": 1.0,
                "equalizer": "标准"
            },
            "game": {
                "type": "",
                "name": ""
            },
            "last_selected": {
                "speaker": 0,
                "script": 0,
                "dialogue": 0
            }
        }

        try:
            # 首先尝试从 _internal 目录加载配置
            if os.path.exists(internal_config_file):
                with open(internal_config_file, "r", encoding="utf-8") as f:
                    config = json.load(f)
                self.log("从 _internal 目录加载配置文件成功")
                return config
            # 如果 _internal 目录中没有配置文件，尝试从根目录加载
            elif os.path.exists(config_file):
                with open(config_file, "r", encoding="utf-8") as f:
                    config = json.load(f)
                self.log("从根目录加载配置文件成功")

                # 确保 _internal 目录存在
                os.makedirs("_internal", exist_ok=True)

                # 将配置保存到 _internal 目录
                with open(internal_config_file, "w", encoding="utf-8") as f:
                    json.dump(config, f, ensure_ascii=False, indent=4)
                self.log("配置文件已复制到 _internal 目录")

                # 删除根目录的配置文件
                try:
                    os.remove(config_file)
                    self.log("已删除根目录的配置文件")
                except Exception as e:
                    self.log(f"删除根目录配置文件失败: {str(e)}")

                return config
            else:
                self.log("配置文件不存在，使用默认配置")
                return default_config
        except Exception as e:
            self.log(f"加载配置文件出错: {str(e)}")
            return default_config

    def save_config(self, config=None):
        """保存配置文件"""
        import os

        if config is None:
            config = self.config

        try:
            # 确保 _internal 目录存在
            os.makedirs("_internal", exist_ok=True)

            # 保存到 _internal/config.json
            internal_config_file = os.path.join("_internal", "config.json")
            with open(internal_config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            self.log("配置文件保存到 _internal 目录成功")

            # 如果根目录存在 config.json，删除它
            config_file = "config.json"
            if os.path.exists(config_file):
                try:
                    os.remove(config_file)
                    self.log("已删除根目录的配置文件")
                except Exception as e:
                    self.log(f"删除根目录配置文件失败: {str(e)}")

            return True
        except Exception as e:
            self.log(f"保存配置文件出错: {str(e)}")
            return False




if __name__ == "__main__":
    try:
        # 导入登录窗口模块
        from login_window import show_login_window

        # 导入更新检测器
        from update_checker import UpdateChecker

        # 显示登录窗口
        login_result = show_login_window()

        # 如果登录成功，显示主窗口
        if login_result.get("success"):
            root = tk.Tk()
            app = MainWindow(root)

            # 设置登录信息
            app.username = login_result.get("username")
            app.token = login_result.get("token")
            app.expiry = login_result.get("expiry")

            # 显示到期时间
            if app.expiry:
                app.log(f"登录成功，用户: {app.username}，到期时间: {app.expiry}")

                # 在状态栏显示到期时间
                if hasattr(app, "status_bar"):
                    app.status_bar.config(text=f"到期时间: {app.expiry}")

            # 检查更新
            def check_for_updates():
                try:
                    # 获取当前版本
                    import config
                    current_version = getattr(config, "CLIENT_VERSION", "1.8")

                    # 创建更新检测器，使用config.py中的版本
                    updater = UpdateChecker()

                    # 检查更新
                    app.log(f"正在检查更新: {current_version}")
                    update_info = updater.check_for_updates()

                    if update_info:
                        # 发现更新
                        version = update_info.get("version", "未知")
                        description = update_info.get("description", "无更新说明")

                        app.log(f"发现新版本: {version}")

                        # 询问用户是否下载更新
                        if messagebox.askyesno("发现新版本", f"发现新版本: {version}\n\n{description}\n\n是否下载并安装更新?"):
                            # 下载更新
                            app.log("正在下载更新...")

                            # 创建进度条窗口
                            progress_window = tk.Toplevel(root)
                            progress_window.title("下载更新")
                            progress_window.geometry("400x150")
                            progress_window.resizable(False, False)
                            progress_window.transient(root)
                            progress_window.grab_set()

                            # 创建进度条
                            progress_frame = ttk.Frame(progress_window, padding=20)
                            progress_frame.pack(fill=tk.BOTH, expand=True)

                            progress_label = ttk.Label(progress_frame, text=f"正在下载 {version} 版本更新...")
                            progress_label.pack(pady=(0, 10))

                            progress_var = tk.DoubleVar()
                            progress_bar = ttk.Progressbar(progress_frame, variable=progress_var, maximum=100, length=300)
                            progress_bar.pack(pady=10)

                            progress_text = ttk.Label(progress_frame, text="0%")
                            progress_text.pack()

                            # 下载更新
                            def update_progress(downloaded, total):
                                if total > 0:
                                    progress = downloaded / total * 100
                                    progress_var.set(progress)
                                    progress_text.config(text=f"{progress:.1f}%")
                                    progress_window.update()

                            # 在新线程中下载更新
                            import threading

                            def download_thread():
                                nonlocal updater
                                download_path = updater.download_update(update_info, progress_callback=update_progress)

                                # 关闭进度条窗口
                                progress_window.destroy()

                                if download_path:
                                    # 下载成功
                                    app.log(f"更新下载完成: {download_path}")

                                    # 询问用户是否安装更新
                                    # 自动安装更新，无需用户再次确认
                                app.log("下载完成，正在自动安装更新...")

                                # 显示安装中的提示
                                installing_window = tk.Toplevel(root)
                                installing_window.title("安装更新")
                                installing_window.geometry("400x150")
                                installing_window.resizable(False, False)
                                installing_window.transient(root)
                                installing_window.grab_set()

                                # 创建安装提示
                                install_frame = ttk.Frame(installing_window, padding=20)
                                install_frame.pack(fill=tk.BOTH, expand=True)

                                install_label = ttk.Label(install_frame, text=f"正在安装更新 {version}，请稍候...")
                                install_label.pack(pady=(0, 10))

                                # 创建进度条
                                install_progress = ttk.Progressbar(install_frame, mode="indeterminate", length=300)
                                install_progress.pack(pady=10)
                                install_progress.start(10)

                                # 更新UI
                                installing_window.update()

                                # 安装更新
                                success = updater.install_update(download_path)

                                # 停止进度条
                                install_progress.stop()

                                if success:
                                    # 安装成功
                                    app.log("更新安装成功")
                                    install_label.config(text="更新已成功安装，程序将自动重启。")
                                    installing_window.update()

                                    # 延迟一会儿再重启，让用户看到成功消息
                                    # 使用main.py重启程序，而不是当前脚本
                                    def restart_with_main():
                                        try:
                                            # 获取main.py的路径
                                            main_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "main.py")
                                            if os.path.exists(main_path):
                                                # 使用main.py重启
                                                os.execl(sys.executable, sys.executable, main_path)
                                            else:
                                                # 如果找不到main.py，使用当前脚本重启
                                                os.execl(sys.executable, sys.executable, *sys.argv)
                                        except Exception as e:
                                            app.log(f"重启程序失败: {str(e)}")
                                            # 如果重启失败，使用当前脚本重启
                                            os.execl(sys.executable, sys.executable, *sys.argv)

                                    root.after(2000, restart_with_main)
                                else:
                                    # 安装失败
                                    app.log("更新安装失败")
                                    install_label.config(text="更新安装失败，请稍后重试。")
                                    installing_window.update()

                                    # 延迟关闭安装窗口
                                    root.after(3000, installing_window.destroy)

                                    # 继续初始化应用
                                    app.initialize_app()

                            # 启动下载线程
                            threading.Thread(target=download_thread).start()
                        else:
                            # 用户取消下载
                            app.log("用户取消下载更新")
                    else:
                        # 没有更新
                        app.log("当前已是最新版本")
                except Exception as e:
                    # 检查更新出错
                    app.log(f"检查更新出错: {str(e)}")
                    messagebox.showerror("检查更新", f"检查更新出错: {str(e)}")

            # 在主窗口加载完成后检查更新
            root.after(2000, check_for_updates)

            root.mainloop()
    except Exception as e:
        print(f"\n\n程序运行出错: {str(e)}")
        import traceback
        print(traceback.format_exc())
        input("按回车键退出...")