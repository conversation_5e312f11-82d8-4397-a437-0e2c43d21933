# 🎬 副视频最低层排序最终修复

## 🎯 修复目标

根据用户最新要求，在切换副视频之前：

1. **先将副视频移至最低层**：通过OBS操作把副视频的视频源排序移至最低层
2. **再进行其他操作**：暂停主视频、调整位置、显示副视频

## 🔧 最终修复方案

### 主视频 → 副视频切换流程（最终版）

```
步骤1: 暂停双主视频自动切换
步骤2: 保存主视频当前播放状态（位置、状态）
步骤3: 在隐藏状态下启动副视频播放（预加载）
步骤4: 等待副视频启动（0.1秒）
步骤5: 🔥 先将副视频移至最低层
  - 获取场景中所有项目数量
  - 将副视频移到最低层（最大索引值）
步骤6: 暂停主视频（只暂停，不隐藏）
步骤7: 调整副视频位置并显示
  - 调整副视频位置到画面上方
  - 显示副视频源
```

**关键修复点**：
- ✅ **最低层排序**：副视频移到最低层而不是最顶层
- ✅ **动态索引计算**：根据场景项目数量动态计算最低层索引
- ✅ **备用方法**：使用大索引值作为备用方法

## 📝 代码修改详情

### 修改：副视频移至最低层

**位置**：`run_gui_qt5.py` 第4057-4099行

**修改前**：
```python
# 将副视频移到最顶层
obs_controller.send_request_sync("SetSceneItemIndex", {
    "sceneName": scene_name,
    "sceneItemId": sub_video_id,
    "sceneItemIndex": 0  # 0表示最顶层
})
```

**修改后**：
```python
# 🔥 步骤5.1：首先将副视频移到最低层（在其他操作之前）
sub_video_id = obs_controller._get_source_id(sub_video_source, scene_name)

# 获取场景中的所有项目数量，确定最低层索引
scene_items_response = obs_controller.send_request_sync("GetSceneItemList", {
    "sceneName": scene_name
})

if scene_items_response and "sceneItems" in scene_items_response:
    scene_items = scene_items_response["sceneItems"]
    max_index = len(scene_items) - 1  # 最低层索引
    
    obs_controller.send_request_sync("SetSceneItemIndex", {
        "sceneName": scene_name,
        "sceneItemId": sub_video_id,
        "sceneItemIndex": max_index  # 移到最低层
    })
    print(f"📋 副视频已移到最低层: {sub_video_source} (索引: {max_index})")
else:
    # 备用方法：使用一个较大的索引值
    obs_controller.send_request_sync("SetSceneItemIndex", {
        "sceneName": scene_name,
        "sceneItemId": sub_video_id,
        "sceneItemIndex": 999  # 使用大索引值移到最低层
    })
    print(f"📋 副视频已移到最低层: {sub_video_source} (备用方法)")
```

### 技术实现详解

#### 1. **动态索引计算**
```python
# 获取场景项目列表
scene_items_response = obs_controller.send_request_sync("GetSceneItemList", {
    "sceneName": scene_name
})

# 计算最低层索引
scene_items = scene_items_response["sceneItems"]
max_index = len(scene_items) - 1  # 最低层索引
```

#### 2. **备用方法**
```python
# 如果无法获取场景项目列表，使用大索引值
obs_controller.send_request_sync("SetSceneItemIndex", {
    "sceneName": scene_name,
    "sceneItemId": sub_video_id,
    "sceneItemIndex": 999  # 使用大索引值移到最低层
})
```

#### 3. **OBS图层索引说明**
- **索引0**：最顶层（最前面）
- **索引1**：第二层
- **索引2**：第三层
- **...以此类推**
- **最大索引**：最低层（最后面）

## 🔄 完整工作流程

### 【主视频 → 副视频】最终流程
1. **暂停双主视频自动切换** ✅
2. **保存主视频状态** ✅
3. **预加载副视频** ✅
4. **🔥 将副视频移至最低层**：
   - 获取场景项目数量 ✅
   - 计算最低层索引 ✅
   - 移动副视频到最低层 ✅
5. **暂停主视频** ✅
6. **调整位置并显示副视频** ✅

### 【副视频 → 主视频】流程保持不变
1. **继续播放主视频** ✅
2. **隐藏副视频** ✅
3. **停止副视频播放** ✅
4. **恢复双主视频自动切换** ✅

## 🎬 技术特点

### ✅ 最低层排序
- **动态计算**：根据场景项目数量动态计算最低层索引
- **备用机制**：提供备用方法确保操作成功
- **操作优先**：在所有其他操作之前完成图层排序

### ✅ OBS API使用
- **GetSceneItemList**：获取场景中所有项目列表
- **SetSceneItemIndex**：设置项目的图层索引
- **错误处理**：完善的错误处理和备用方案

### ✅ 逻辑合理性
- **最低层预设**：副视频先移到最低层，为后续显示做准备
- **无干扰操作**：图层排序不影响当前显示状态
- **状态管理**：确保操作顺序的逻辑性

## 🧪 测试建议

现在您可以在主程序中测试修复后的副视频功能：

1. **设置视频源**：配置视频源A（如2222）和B（如2223）
2. **开始播放**：点击播放按钮开始播放
3. **触发副视频**：发送包含关键词"代"的测试弹幕
4. **观察切换效果**：
   - 副视频应该先移到最低层
   - 然后暂停主视频
   - 最后调整位置并显示副视频

### 📋 预期日志输出

```
🔄 开始副视频切换: 2222 -> 1111
⏸️ 已暂停双主视频自动切换
💾 保存主视频状态: 2222 位置=XXXXXms
▶️ 预加载副视频: 1111 (隐藏状态)
📋 副视频已移到最低层: 1111 (索引: X)
🔄 副视频图层排序完成: 1111(最低层)
⏸️ 主视频已暂停: 2222
📐 副视频位置已调整到画面上方: 1111
👁️ 副视频源已显示: 1111
✅ 副视频切换完成: 1111
```

### 🔍 验证要点

1. **图层排序**：确认副视频移到最低层
2. **操作顺序**：图层排序在暂停主视频之前
3. **索引计算**：动态计算的索引值是否正确
4. **备用方法**：如果动态计算失败，备用方法是否生效

## 🎉 总结

副视频功能现在已经完全按照您的最新要求修复：

1. ✅ **最低层排序**：在切换副视频之前先将副视频移至最低层
2. ✅ **动态索引计算**：根据场景项目数量动态计算最低层索引
3. ✅ **备用机制**：提供备用方法确保操作成功
4. ✅ **操作顺序正确**：图层排序在所有其他操作之前完成

所有修改都已完成，副视频功能现在应该能够实现您要求的最低层排序效果！

---

## 🔧 OBS图层管理参考

### 图层索引规则
- **索引0**：最顶层（最前面显示）
- **索引1**：第二层
- **索引2**：第三层
- **索引N-1**：最低层（最后面显示，N为项目总数）

### 相关API
- **GetSceneItemList**：获取场景项目列表
- **SetSceneItemIndex**：设置项目图层索引
- **GetSceneItemIndex**：获取项目当前图层索引

### 使用场景
- **最低层排序**：适用于需要在后台准备的元素
- **最顶层排序**：适用于需要覆盖显示的元素
- **动态排序**：根据实际需求动态调整图层顺序
