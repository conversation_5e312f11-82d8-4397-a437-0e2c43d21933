#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试状态更新功能
"""

import hashlib
import json
from pathlib import Path

def test_status_update():
    """测试状态更新功能"""
    print("🧪 测试状态更新功能...")
    
    # 读取播放列表
    playlist_file = Path("data/playlist.json")
    if not playlist_file.exists():
        print("❌ 播放列表文件不存在")
        return False
    
    with open(playlist_file, 'r', encoding='utf-8') as f:
        playlist_data = json.load(f)
    
    voices_dir = Path("voices")
    
    print(f"📋 播放列表中有 {len(playlist_data)} 个项目")
    
    # 统计状态
    status_count = {}
    for item in playlist_data:
        status = item.get('status', '未知')
        status_count[status] = status_count.get(status, 0) + 1
    
    print("📊 状态统计:")
    for status, count in status_count.items():
        print(f"  - {status}: {count} 个")
    
    # 检查未下载项目
    undownloaded_items = [item for item in playlist_data if item['status'] == '未下载']
    print(f"\n🔍 检查 {len(undownloaded_items)} 个未下载项目:")
    
    fixed_count = 0
    for i, item in enumerate(undownloaded_items[:10]):  # 只检查前10个
        content = item['content']
        
        # 生成可能的文件名
        text_hash = hashlib.md5(content.encode('utf-8')).hexdigest()[:8]
        
        # 检查不同参数组合的文件
        possible_files = [
            f"{text_hash}_5_120.wav",  # 主播ID=5, 速度=120
            f"{text_hash}_0_100.wav",  # 主播ID=0, 速度=100
            f"{text_hash}_0_1.wav",    # 其他可能的组合
        ]
        
        existing_file = None
        for filename in possible_files:
            filepath = voices_dir / filename
            if filepath.exists():
                existing_file = filename
                break
        
        print(f"  {i+1}. {content[:40]}...")
        print(f"     哈希: {text_hash}")
        
        if existing_file:
            print(f"     ✅ 找到文件: {existing_file}")
            fixed_count += 1
        else:
            print(f"     ❌ 文件不存在")
    
    print(f"\n📈 结果:")
    print(f"  - 检查了 {min(10, len(undownloaded_items))} 个未下载项目")
    print(f"  - 其中 {fixed_count} 个实际上文件已存在")
    print(f"  - 需要修复状态的项目: {fixed_count} 个")
    
    return True

def main():
    """主函数"""
    print("🔧 状态更新测试")
    print("=" * 60)
    
    try:
        success = test_status_update()
        
        print("=" * 60)
        if success:
            print("✅ 测试完成")
        else:
            print("❌ 测试失败")
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
