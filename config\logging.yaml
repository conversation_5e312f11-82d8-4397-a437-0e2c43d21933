version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  colored:
    (): colorlog.ColoredFormatter
    format: '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s%(reset)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
    log_colors:
      DEBUG: cyan
      INFO: green
      WARNING: yellow
      ERROR: red
      CRITICAL: bold_red

  detailed:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: colored
    stream: ext://sys.stdout

  file_info:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: standard
    filename: logs/app.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8

  file_error:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: detailed
    filename: logs/error.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8

  file_debug:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/debug.log
    maxBytes: 10485760  # 10MB
    backupCount: 3
    encoding: utf8

loggers:
  ai_broadcaster:
    level: DEBUG
    handlers: [console, file_info, file_error, file_debug]
    propagate: false

  audio_processor:
    level: DEBUG
    handlers: [console, file_info, file_error]
    propagate: false

  ai_engine:
    level: DEBUG
    handlers: [console, file_info, file_error]
    propagate: false

root:
  level: INFO
  handlers: [console, file_info, file_error]
