"""
AI Broadcaster v2 - 错误处理服务
统一的错误处理和异常管理
"""

import traceback
import sys
from typing import Optional, Callable, Any
from functools import wraps

from .logging_service import create_logger


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        self.logger = create_logger("error_handler")
        self.error_callbacks = []
    
    def add_error_callback(self, callback: Callable[[Exception, str], None]):
        """添加错误回调函数"""
        self.error_callbacks.append(callback)
    
    def handle_exception(self, exc: Exception, context: str = "Unknown") -> None:
        """
        处理异常
        
        Args:
            exc: 异常对象
            context: 异常上下文
        """
        error_msg = f"异常发生在 {context}: {str(exc)}"
        self.logger.error(error_msg)
        self.logger.exception("异常详情")
        
        # 调用错误回调
        for callback in self.error_callbacks:
            try:
                callback(exc, context)
            except Exception as callback_exc:
                self.logger.error(f"错误回调执行失败: {callback_exc}")
    
    def handle_critical_error(self, exc: Exception, context: str = "Critical") -> None:
        """
        处理严重错误
        
        Args:
            exc: 异常对象
            context: 异常上下文
        """
        error_msg = f"严重错误发生在 {context}: {str(exc)}"
        self.logger.critical(error_msg)
        self.logger.exception("严重错误详情")
        
        # 调用错误回调
        for callback in self.error_callbacks:
            try:
                callback(exc, context)
            except Exception as callback_exc:
                self.logger.error(f"错误回调执行失败: {callback_exc}")


# 全局错误处理器
_error_handler = ErrorHandler()


def get_error_handler() -> ErrorHandler:
    """获取全局错误处理器"""
    return _error_handler


def handle_exceptions(context: str = "Unknown"):
    """
    异常处理装饰器
    
    Args:
        context: 异常上下文描述
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                _error_handler.handle_exception(e, f"{context}.{func.__name__}")
                return None
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                _error_handler.handle_exception(e, f"{context}.{func.__name__}")
                return None
        
        # 根据函数类型返回相应的包装器
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return wrapper
    
    return decorator


def handle_critical_exceptions(context: str = "Critical"):
    """
    严重异常处理装饰器
    
    Args:
        context: 异常上下文描述
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                _error_handler.handle_critical_error(e, f"{context}.{func.__name__}")
                raise  # 重新抛出严重异常
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                _error_handler.handle_critical_error(e, f"{context}.{func.__name__}")
                raise  # 重新抛出严重异常
        
        # 根据函数类型返回相应的包装器
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return wrapper
    
    return decorator


class SafeExecutor:
    """安全执行器"""
    
    def __init__(self, error_handler: Optional[ErrorHandler] = None):
        self.error_handler = error_handler or _error_handler
    
    def execute(self, func: Callable, *args, context: str = "SafeExecutor", **kwargs) -> Any:
        """
        安全执行函数
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            context: 执行上下文
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果，如果出错则返回None
        """
        try:
            return func(*args, **kwargs)
        except Exception as e:
            self.error_handler.handle_exception(e, context)
            return None
    
    async def execute_async(self, func: Callable, *args, context: str = "SafeExecutor", **kwargs) -> Any:
        """
        安全执行异步函数
        
        Args:
            func: 要执行的异步函数
            *args: 函数参数
            context: 执行上下文
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果，如果出错则返回None
        """
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            self.error_handler.handle_exception(e, context)
            return None
