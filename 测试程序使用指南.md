# AI直播系统测试程序使用指南

## 🚀 测试程序概述

我已经创建了一个和主程序差不多的测试程序 `ai_broadcaster_test.py`，它模拟了AI直播系统的所有核心功能，可以用来验证：

1. **播放功能** - 播放列表初始化和语音播放
2. **生成新语音** - 播放完成后自动补充新语音
3. **播放顺序（优先级）** - 弹幕>报时>主视频话术的优先级
4. **时间段匹配** - 根据主视频位置选择对应时间段的语音
5. **播放带副视频的AI对话** - 弹幕触发AI对话并切换副视频

---

## 🎮 测试程序界面

### 控制面板
- **当前话术**: 选择要测试的话术（默认：测试话术1）
- **预备语音数量**: 设置每个时间段的预备语音数量（默认：3个）
- **主视频位置**: 滑动条模拟主视频播放位置（0-60秒）
- **开始播放**: 启动播放测试
- **停止播放**: 停止播放测试
- **添加弹幕**: 模拟弹幕消息
- **添加报时**: 模拟报时功能

### 播放列表
显示所有生成的播放项目，包括：
- ID、类型、内容、时间段、状态、文件名、副视频

### 测试日志
实时显示所有测试过程和结果

---

## 🧪 详细测试步骤

### 测试1: 播放列表初始化功能

**目标**: 验证每个时间段都能生成足够的预备语音

**步骤**:
1. 启动测试程序
2. 设置预备语音数量为3
3. 点击"开始播放"
4. 观察播放列表是否生成了6个项目（2个时间段 × 3个语音）

**预期结果**:
```
📋 初始化播放列表...
📝 时间段 '10秒 - 20秒': 找到 4 条话术
  - 生成语音 1/3: 欢迎来到直播间！
  - 生成语音 2/3: 各位好，正在玩王者荣耀
  - 生成语音 3/3: 感谢测试用户的关注
📝 时间段 '40秒 - 50秒': 找到 4 条话术
  - 生成语音 1/3: 感谢大家的观看
  - 生成语音 2/3: 谢谢大家的支持
  - 生成语音 3/3: 我们继续游戏
✅ 播放列表初始化完成，共 6 个项目
```

**验证点**:
- ✅ 每个时间段都生成了3个语音
- ✅ 随机文本选择正常工作（【大家好|各位好|hello】）
- ✅ 变量替换正常工作（{gamename} → 王者荣耀）

---

### 测试2: 时间段匹配功能

**目标**: 验证根据主视频位置正确选择对应时间段的语音

**步骤**:
1. 确保播放已启动
2. 拖动主视频位置滑动条到15秒
3. 观察是否选择10秒-20秒时间段的语音
4. 拖动滑动条到45秒
5. 观察是否选择40秒-50秒时间段的语音
6. 拖动滑动条到30秒（不在任何时间段）
7. 观察是否不播放主视频话术

**预期结果**:
```
🕐 主视频位置: 15秒, 时间段: 10秒 - 20秒
🎯 选择播放: 主视频话术 - 时间段:10秒 - 20秒 - 欢迎来到直播间！...

🕐 主视频位置: 45秒, 时间段: 40秒 - 50秒
🎯 选择播放: 主视频话术 - 时间段:40秒 - 50秒 - 感谢大家的观看...

🕐 主视频位置: 30秒, 时间段: 不在时间段内
⚠️ 当前不在任何时间段内，不播放主视频话术
```

**验证点**:
- ✅ 15秒位置选择10秒-20秒时间段的语音
- ✅ 45秒位置选择40秒-50秒时间段的语音
- ✅ 30秒位置不播放主视频话术

---

### 测试3: 播放优先级功能

**目标**: 验证弹幕话术 > 报时话术 > 主视频话术的优先级

**步骤**:
1. 确保播放已启动
2. 点击"添加弹幕"按钮
3. 观察是否立即选择弹幕话术播放
4. 等待弹幕播放完成
5. 点击"添加报时"按钮
6. 观察是否选择报时话术播放
7. 等待报时播放完成
8. 观察是否回到主视频话术播放

**预期结果**:
```
💬 收到弹幕: 主播在玩什么游戏？
🤖 生成AI回复: 正在玩王者荣耀，很有趣的游戏
🎬 关联副视频: 游戏画面源
🎯 选择播放: 弹幕话术 - 正在玩王者荣耀，很有趣的游戏... (弹幕优先)

⏰ 添加报时: 现在时间是14:30
🎯 选择播放: 报时话术 - 现在时间是14:30... (报时优先)

🎯 选择播放: 主视频话术 - 时间段:10秒 - 20秒 - 欢迎来到直播间！...
```

**验证点**:
- ✅ 弹幕话术优先级最高
- ✅ 报时话术优先级次之
- ✅ 主视频话术按时间段匹配

---

### 测试4: 新语音生成功能

**目标**: 验证播放完成后自动补充新的同时间段语音

**步骤**:
1. 确保播放已启动
2. 观察主视频话术播放完成
3. 检查是否自动补充了新的同时间段语音
4. 观察播放列表中是否增加了新项目

**预期结果**:
```
🎵 播放完成: 欢迎来到直播间！...
🔄 补充主视频话术: 10秒 - 20秒
✅ 补充新语音: 各位好，正在玩王者荣耀...
```

**验证点**:
- ✅ 播放完成后自动触发补充
- ✅ 补充的语音属于相同时间段
- ✅ 新语音正确处理随机文本和变量

---

### 测试5: 带副视频的AI对话功能

**目标**: 验证弹幕触发AI对话并正确切换副视频

**步骤**:
1. 确保播放已启动
2. 多次点击"添加弹幕"按钮
3. 观察不同类型的弹幕是否触发不同的AI回复
4. 检查是否正确关联了副视频源

**预期结果**:
```
💬 收到弹幕: 主播在玩什么游戏？
🤖 生成AI回复: 正在玩王者荣耀，很有趣的游戏
🎬 关联副视频: 游戏画面源

💬 收到弹幕: 感谢主播的精彩直播
🤖 生成AI回复: 谢谢测试用户的支持
🎬 关联副视频: 感谢画面源

💬 收到弹幕: 666，主播很厉害
（无匹配的AI对话）
```

**验证点**:
- ✅ 关键词匹配准确（游戏、感谢等）
- ✅ AI回复内容正确
- ✅ 副视频源选择正确
- ✅ 无匹配时的处理正确

---

## 🎯 测试数据说明

### 话术时间段数据
```
测试话术1:
  10秒 - 20秒:
    - 欢迎来到直播间！
    - 【大家好|各位好|hello】，正在玩{gamename}
    - 感谢{nick}的关注
    - 现在时间是{time}
  
  40秒 - 50秒:
    - 感谢大家的观看
    - 谢谢【大家|各位】的支持
    - 我们继续游戏
    - {gamename}很有趣
```

### AI对话数据
```
关键词 → 回复:
  游戏 → 正在玩{gamename}，很有趣的游戏
  感谢 → 谢谢{nick}的支持
  问候 → 【大家好|各位好】，欢迎来到直播间
  时间 → 现在时间是{time}
```

### 副视频设置
```
游戏画面: 关键词[游戏, 王者, 英雄] → 游戏画面源
感谢画面: 关键词[感谢, 谢谢, 支持] → 感谢画面源
```

---

## ✅ 测试成功标准

### 全部功能正常的标志：

1. **播放列表初始化** ✅
   - 每个时间段都生成了预设数量的语音
   - 随机文本选择和变量替换正常工作

2. **时间段匹配** ✅
   - 不同位置正确匹配对应时间段
   - 不在时间段内时不播放主视频话术

3. **播放优先级** ✅
   - 弹幕话术立即播放
   - 报时话术优先于主视频话术
   - 主视频话术按时间段匹配

4. **新语音生成** ✅
   - 播放完成后自动补充新语音
   - 新语音属于相同时间段

5. **副视频AI对话** ✅
   - 弹幕正确触发AI回复
   - 副视频源正确关联

---

## 🚀 使用建议

1. **按顺序测试**: 建议按照上述顺序逐一测试各个功能
2. **观察日志**: 重点关注测试日志中的详细信息
3. **多次测试**: 可以多次运行测试以验证随机性功能
4. **调整参数**: 可以修改预备语音数量、主视频位置等参数进行测试

**测试程序已经启动并运行正常！** 🎉

您可以按照上述指南逐一测试各个功能，验证AI直播系统的所有核心功能是否正常工作。
