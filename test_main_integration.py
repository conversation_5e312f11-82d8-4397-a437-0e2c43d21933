#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试主程序集成功能
验证语音下载和播放状态更新是否正常工作
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_main_program_integration():
    """测试主程序集成功能"""
    try:
        print("🧪 开始测试主程序集成功能...")
        
        # 导入主程序
        from run_gui_qt5 import AIBroadcasterApp
        from PyQt5.QtWidgets import QApplication
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主程序实例
        main_app = AIBroadcasterApp()
        
        print("✅ 主程序实例创建成功")
        
        # 测试语音下载功能
        test_voice_download(main_app)
        
        # 测试播放列表功能
        test_playlist_functionality(main_app)
        
        # 测试状态更新功能
        test_status_update(main_app)
        
        print("🎉 所有测试通过！主程序集成功能正常")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_voice_download(main_app):
    """测试语音下载功能"""
    try:
        print("\n📥 测试语音下载功能...")
        
        # 测试下载单个语音
        test_text = "这是一个测试语音"
        speaker_id = 0
        speed = 1
        
        filename = main_app.download_voice(test_text, speaker_id, speed)
        
        if filename:
            print(f"✅ 语音下载成功: {filename}")
            
            # 检查文件是否存在
            voices_dir = Path("voices")
            filepath = voices_dir / filename
            
            if filepath.exists():
                print(f"✅ 语音文件确实存在: {filepath}")
                print(f"📊 文件大小: {filepath.stat().st_size} 字节")
            else:
                print(f"❌ 语音文件不存在: {filepath}")
        else:
            print("❌ 语音下载失败")
            
    except Exception as e:
        print(f"❌ 语音下载测试失败: {e}")

def test_playlist_functionality(main_app):
    """测试播放列表功能"""
    try:
        print("\n📋 测试播放列表功能...")
        
        # 测试创建播放列表
        if hasattr(main_app, 'create_test_playlist_data'):
            main_app.create_test_playlist_data()
            print("✅ 测试播放列表创建成功")
        else:
            print("⚠️ 主程序没有测试播放列表功能")
            
        # 测试播放列表保存
        if hasattr(main_app, 'save_playlist_to_file'):
            main_app.save_playlist_to_file([])
            print("✅ 播放列表保存功能正常")
        else:
            print("⚠️ 主程序没有播放列表保存功能")
            
        # 测试播放列表加载
        if hasattr(main_app, 'load_playlist_from_file'):
            loaded_items = main_app.load_playlist_from_file()
            print(f"✅ 播放列表加载功能正常，加载了 {len(loaded_items)} 个项目")
        else:
            print("⚠️ 主程序没有播放列表加载功能")
            
    except Exception as e:
        print(f"❌ 播放列表功能测试失败: {e}")

def test_status_update(main_app):
    """测试状态更新功能"""
    try:
        print("\n📝 测试状态更新功能...")
        
        # 创建测试项目
        test_item = {
            'id': 1,
            'voice_type': '测试话术',
            'content': '这是一个测试项目',
            'time_segment': '测试时间段',
            'status': '未下载',
            'filename': '',
            'sub_video': '无',
            'row_index': 0
        }
        
        # 测试状态更新功能
        if hasattr(main_app, 'update_item_status'):
            # 测试更新为已下载
            main_app.update_item_status(test_item, '已下载')
            if test_item['status'] == '已下载':
                print("✅ 状态更新为'已下载'成功")
            
            # 测试更新为播放中
            main_app.update_item_status(test_item, '播放中')
            if test_item['status'] == '播放中':
                print("✅ 状态更新为'播放中'成功")
            
            # 测试更新为已播放
            main_app.update_item_status(test_item, '已播放')
            if test_item['status'] == '已播放':
                print("✅ 状态更新为'已播放'成功")
                
            print("✅ 状态更新功能完全正常")
        else:
            print("⚠️ 主程序没有状态更新功能")
            
    except Exception as e:
        print(f"❌ 状态更新功能测试失败: {e}")

def test_download_integration():
    """测试下载集成功能"""
    try:
        print("\n🔄 测试下载集成功能...")
        
        # 检查voices目录
        voices_dir = Path("voices")
        if voices_dir.exists():
            voice_files = list(voices_dir.glob("*.wav"))
            print(f"✅ voices目录存在，包含 {len(voice_files)} 个语音文件")
            
            # 显示最近的几个文件
            if voice_files:
                print("📁 最近的语音文件:")
                for i, file in enumerate(sorted(voice_files, key=lambda x: x.stat().st_mtime, reverse=True)[:5]):
                    size = file.stat().st_size
                    print(f"  {i+1}. {file.name} ({size} 字节)")
        else:
            print("⚠️ voices目录不存在")
            
    except Exception as e:
        print(f"❌ 下载集成功能测试失败: {e}")

if __name__ == "__main__":
    print("🚀 AI直播系统主程序集成测试")
    print("=" * 50)
    
    # 测试下载集成
    test_download_integration()
    
    # 测试主程序集成
    test_main_program_integration()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成！")
