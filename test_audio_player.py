#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频播放器测试文件
用于诊断播放器连续播放问题
"""

import os
import sys
import time
import threading
from pathlib import Path

class TestAudioPlayer:
    """测试音频播放器类"""
    
    def __init__(self):
        self.is_playing = False
        self.current_file = None
        self.player_type = "pygame"  # 默认使用pygame
        self.on_playback_end = None
        self.playback_count = 0
        
    def play(self, file_path):
        """播放音频文件"""
        if self.is_playing:
            print(f"⚠️ 正在播放其他音频，跳过: {Path(file_path).name}")
            return False
            
        if not os.path.exists(file_path):
            print(f"❌ 音频文件不存在: {file_path}")
            return False
            
        self.current_file = file_path
        self.is_playing = True
        self.playback_count += 1
        
        print(f"🎵 开始播放第{self.playback_count}个音频: {Path(file_path).name}")
        
        # 在新线程中播放
        play_thread = threading.Thread(target=self._play_worker, args=(file_path,))
        play_thread.daemon = True
        play_thread.start()
        
        return True
        
    def _play_worker(self, file_path):
        """播放工作线程"""
        try:
            print(f"🎵 音频播放线程开始: {Path(file_path).name}")
            
            # 根据播放器类型选择播放方式
            if self.player_type == "pygame":
                self._play_with_pygame(file_path)
            elif self.player_type == "winsound":
                self._play_with_winsound(file_path)
            elif self.player_type == "vlc":
                self._play_with_vlc(file_path)
            elif self.player_type == "system":
                self._play_with_system_player(file_path)
            else:
                self._play_with_pygame(file_path)
                
        except Exception as e:
            print(f"❌ 播放音频异常: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 🔥 关键：无论如何都要重置播放状态
            file_to_callback = self.current_file
            self.is_playing = False
            self.current_file = None
            print(f"🎵🎵🎵 音频播放线程结束: {Path(file_to_callback).name}")
            
            # 触发播放完成回调
            if self.on_playback_end:
                print(f"🔄🔄🔄 触发播放完成回调: {Path(file_to_callback).name}")
                try:
                    self.on_playback_end(file_to_callback)
                    print(f"✅ 播放完成回调已执行")
                except Exception as callback_error:
                    print(f"❌ 播放完成回调异常: {callback_error}")
                    import traceback
                    traceback.print_exc()
            else:
                print(f"⚠️⚠️⚠️ 没有设置播放完成回调")
                
    def _play_with_pygame(self, file_path):
        """使用pygame播放"""
        try:
            import pygame
            print(f"🎵 使用pygame播放: {Path(file_path).name}")
            
            # 初始化pygame mixer
            try:
                mixer_initialized = pygame.mixer.get_init() is not None
            except:
                mixer_initialized = False
                
            if not mixer_initialized:
                print("🔧 初始化pygame mixer...")
                pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=1024)
                pygame.mixer.init()
                print("✅ pygame mixer初始化成功")
            else:
                try:
                    pygame.mixer.music.stop()
                except:
                    pass
                    
            # 加载并播放
            pygame.mixer.music.load(file_path)
            pygame.mixer.music.play()
            
            # 获取音频时长
            try:
                import wave
                with wave.open(file_path, 'rb') as wav_file:
                    frames = wav_file.getnframes()
                    sample_rate = wav_file.getframerate()
                    duration = frames / float(sample_rate)
                    print(f"🎵 检测到音频时长: {duration:.2f}秒")
            except:
                duration = 5.0  # 默认5秒
                print(f"🎵 无法检测时长，使用默认: {duration}秒")
                
            start_time = time.time()
            max_duration = duration + 2  # 实际时长+2秒缓冲
            
            # 监控播放状态
            while self.is_playing and time.time() - start_time < max_duration:
                if not pygame.mixer.music.get_busy():
                    elapsed = time.time() - start_time
                    print(f"🎵 pygame播放完成: {Path(file_path).name} (播放了{elapsed:.2f}秒)")
                    break
                time.sleep(0.1)
                
            # 强制停止
            pygame.mixer.music.stop()
            
        except ImportError:
            print(f"❌ pygame不可用，模拟播放: {Path(file_path).name}")
            time.sleep(3)  # 模拟播放3秒
            print(f"🎵 模拟播放完成: {Path(file_path).name}")
            
    def _play_with_winsound(self, file_path):
        """使用winsound播放"""
        try:
            import winsound
            print(f"🎵 使用winsound播放: {Path(file_path).name}")
            winsound.PlaySound(file_path, winsound.SND_FILENAME)
            print(f"🎵 winsound播放完成: {Path(file_path).name}")
        except Exception as e:
            print(f"❌ winsound播放失败: {e}")
            self._play_with_pygame(file_path)
            
    def _play_with_vlc(self, file_path):
        """使用VLC播放"""
        try:
            import subprocess
            print(f"🎵 使用VLC播放: {Path(file_path).name}")
            
            vlc_commands = [
                'vlc',
                'C:\\Program Files\\VideoLAN\\VLC\\vlc.exe',
                'C:\\Program Files (x86)\\VideoLAN\\VLC\\vlc.exe'
            ]
            
            vlc_found = False
            for vlc_cmd in vlc_commands:
                try:
                    subprocess.run([vlc_cmd, '--intf', 'dummy', '--play-and-exit', file_path], 
                                 check=True, timeout=30)
                    vlc_found = True
                    break
                except (FileNotFoundError, subprocess.TimeoutExpired):
                    continue
                    
            if not vlc_found:
                print("⚠️ VLC未找到，回退到pygame")
                self._play_with_pygame(file_path)
            else:
                print(f"🎵 VLC播放完成: {Path(file_path).name}")
                
        except Exception as e:
            print(f"❌ VLC播放失败: {e}")
            self._play_with_pygame(file_path)
            
    def _play_with_system_player(self, file_path):
        """使用系统播放器"""
        try:
            import subprocess
            import platform
            print(f"🎵 使用系统播放器播放: {Path(file_path).name}")
            
            system = platform.system()
            if system == "Windows":
                subprocess.Popen(['start', '', file_path], shell=True)
            elif system == "Darwin":  # macOS
                subprocess.Popen(['open', file_path])
            else:  # Linux
                subprocess.Popen(['xdg-open', file_path])
                
            # 估算播放时长并等待
            try:
                import wave
                with wave.open(file_path, 'rb') as wav_file:
                    frames = wav_file.getnframes()
                    sample_rate = wav_file.getframerate()
                    duration = frames / float(sample_rate)
            except:
                duration = 5.0
                
            time.sleep(duration)
            print(f"🎵 系统播放器播放完成: {Path(file_path).name}")
            
        except Exception as e:
            print(f"❌ 系统播放器播放失败: {e}")
            self._play_with_pygame(file_path)
            
    def stop(self):
        """停止播放"""
        if self.is_playing:
            print(f"🛑 停止播放: {Path(self.current_file).name if self.current_file else '未知'}")
            self.is_playing = False
            try:
                import pygame
                pygame.mixer.music.stop()
            except:
                pass
                
    def set_player_type(self, player_type):
        """设置播放器类型"""
        self.player_type = player_type
        print(f"🔧 切换播放器类型: {player_type}")


class PlaylistTester:
    """播放列表测试器"""
    
    def __init__(self):
        self.player = TestAudioPlayer()
        self.playlist = []
        self.current_index = 0
        self.is_running = False
        
        # 设置播放完成回调
        self.player.on_playback_end = self.on_audio_finished
        
    def add_audio_file(self, file_path):
        """添加音频文件到播放列表"""
        if os.path.exists(file_path):
            self.playlist.append(file_path)
            print(f"✅ 添加到播放列表: {Path(file_path).name}")
        else:
            print(f"❌ 文件不存在: {file_path}")
            
    def start_playlist(self):
        """开始播放列表"""
        if not self.playlist:
            print("❌ 播放列表为空")
            return
            
        self.is_running = True
        self.current_index = 0
        print(f"🎬 开始播放列表，共 {len(self.playlist)} 个文件")
        self.play_next()
        
    def play_next(self):
        """播放下一个音频"""
        if not self.is_running:
            print("🛑 播放列表已停止")
            return
            
        if self.current_index >= len(self.playlist):
            print("🎉 播放列表播放完成")
            self.is_running = False
            return
            
        file_path = self.playlist[self.current_index]
        print(f"📋 播放列表进度: {self.current_index + 1}/{len(self.playlist)}")
        
        success = self.player.play(file_path)
        if not success:
            print(f"❌ 播放失败，跳到下一个")
            self.current_index += 1
            # 延迟一点再播放下一个
            threading.Timer(1.0, self.play_next).start()
            
    def on_audio_finished(self, file_path):
        """音频播放完成回调"""
        print(f"🔔 播放完成回调触发: {Path(file_path).name}")
        self.current_index += 1
        
        # 🔥 关键：添加延迟再播放下一个
        if self.is_running:
            print(f"⏰ 等待2秒后播放下一个...")
            threading.Timer(2.0, self.play_next).start()
        
    def stop_playlist(self):
        """停止播放列表"""
        self.is_running = False
        self.player.stop()
        print("🛑 播放列表已停止")


def main():
    """主测试函数"""
    print("🧪 音频播放器测试开始")
    print("=" * 50)
    
    # 创建测试器
    tester = PlaylistTester()
    
    # 查找测试音频文件
    voice_dir = Path("voices")
    if voice_dir.exists():
        wav_files = list(voice_dir.glob("*.wav"))
        if wav_files:
            print(f"🔍 找到 {len(wav_files)} 个WAV文件")
            # 只取前5个文件进行测试
            for wav_file in wav_files[:5]:
                tester.add_audio_file(str(wav_file))
        else:
            print("❌ voice目录中没有找到WAV文件")
            return
    else:
        print("❌ voice目录不存在")
        return
    
    # 测试不同播放器
    player_types = ["pygame", "winsound", "vlc", "system"]
    
    for player_type in player_types:
        print(f"\n🎯 测试播放器: {player_type}")
        print("-" * 30)
        
        tester.player.set_player_type(player_type)
        tester.start_playlist()
        
        # 等待播放完成
        while tester.is_running:
            time.sleep(1)
            
        print(f"✅ {player_type} 播放器测试完成")
        time.sleep(3)  # 播放器之间的间隔
        
    print("\n🎉 所有测试完成")


if __name__ == "__main__":
    main()
