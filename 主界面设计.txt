# 🎯 AI主播系统描述示例

### 🎨 第一步：描述主界面

#### 主界面布局
```
AI主播系统 v1.0                               [_][□][×]
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━─━━━━━
[▶️播放] [⏸️暂停] [⏹️停止] 
播放进度：-------------------- 5s/60s   当前处于0-15时间段
┌─ 待播放列表：────────────────────────────────────────────┐
 编号   语音类型  语音内容 所在时间段  语音状态 语音文件名

└──────────────────────────────────────────────────────────┘
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
┌─ AI主播        ─┐ ┌─ 话术管理 ─┐ ┌AI对话管理┐ ┌─副视频设置─┐ ┌─      OBS控制  ──┐ ┌─报时设置     ─┐ ┌─弹幕设置             ─┐ ┌─系统设置        ─┐
│                 │ │            │ │          │ │            │ │                  │ │               │ │                       │ │ 游戏名称/游戏类型│
│AI主播选择       │ │  话术列表  │ │AI对话列表│ │副视频列表  │ │obs连接设置       │ │ 报时时间间隔  │ │ WebSocket弹幕连接设置 │ │                  │ 
│AI主播设置       │ │   [添加]   │ │[添加]    │ │[添加]      │ │设置主视频A       │ │ 报时话术      │ │     弹幕内容          │ │预备语音数量      │
│                 │ │   [编辑]   │ │[编辑]    │ │[编辑]      │ │设置主视频B       │ │ [变量词]      │ │     发送测试弹幕      │ │设置播放间隔区间  │ 
│                 │ │   [删除]   │ │[删除]    │ │[删除]      │ │设置主视频变速范围│ │               │ │                       │ │选择播放声卡      │  
│                 │ │  [变量词]  │ │ [变量词] │ │ [变量词]   │ │                  │ │               │ │                       │ │[🔊音量━━━━]       │
└─────────────────┘ └────────────┘ └──────────┘ └────────────┘ └──────────────────┘ └───────────────┘ └───────────────────────┘ └──────────────────┘ 
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
[用户:Admin]           到期时间2025年6月1日14:30:25 
```

#### 界面元素说明
- **顶部工具栏**：播放控制按钮、播放进度显示、待播放列表
- **中央工作区**：8个主要功能标签页
- **底部状态栏**：用户状态、到期时间

### 🔧 第二步：描述核心功能

###AI主播功能
**功能描述**：显示现有AI主播，并可以选中一个作为使用的主播
*主要操作**：
1.设置AI主播语速，一般在0.8-1.2
2.选择AI主播，把获取主播列表api里面的主播显示出来，并选择一个使用其中的AI主播id作为后续下载语音的参数

#### 话术管理功能
**功能描述**：获取所有的话术列表，用户选择一个话术然后可以设置特定时间段，在该时间段内自动取时间段内的话术下载成语音放到待播放里面。类型为主视频语音

**主要操作**：
1. **新建话术**
   - 点击"新建话术"按钮 → 打开设置对话框
   - 输入：话术名称
   - 刷新显示
   
2. **刷新话术**
   - 访问话术列表api，更新最新话术列表内容

3. **保存话术**
   - 点击"保存话术"按钮 → 确认保存

4. **添加时间段话术**
   - 点击"添加"按钮 → 打开设置对话框
   - 输入：开始时间、结束时间
   - 保存时间段配置

5. **编辑时间段**
   - 选中时间段 → 双击或点击"编辑"
   - 修改时间段话术，可以加入变量词，可以多行，每次随机取不重复话术，直至取完之后再重置后再取不重复话术。
   - 更新话术项目列表，用话术管理API保存服务器
   - 保存修改

6. **删除时间段**
   - 选中时间段 → 点击"删除"
   - 确认删除操作
   - 清理相关文件

#### AI对话功能
**功能描述**：弹幕里面的词出发了AI对话里面的关键词后，取对应关键词回复内容，下载成语音放到待播放列表里面。类型为AI对话语音

**主要操作**：
1. **选择获取AI对话列表返回的ai对话**，选择某一个AI对话需要从服务器通过

2. **新建AI对话**
   - 点击"新建AI对话"按钮 → 打开对话框
   - 输入：AI对话名字
   - 点击创建创建新的AI对话
   -重新获取AI对话列表，刷新最新的AI对话列表
   
3. **刷新AI对话**
   - 点击"刷新AI对话"按钮 → 重新获取→ 刷新AI对话列表

4. **保存AI对话**
   - 点击"保存AI对话"按钮 → 把编辑好的AI对话关键词和回复内容通过对话操作提交给服务器
   - 输入：开始时间、结束时间
   - 保存时间段配置

5. **新增关键词**
   - 点击"新增关键词"按钮 →打开对话框
   - 输入：关键词
   - 点击创建
   - 保存新增关键词的修改
   - 刷新AI对话关键词列表
   
6. **编辑关键词回复内容**
   - 单击选中关键词 → 在右边出现关键词对应的回复内容
   - 输入回复内容，可以加入变量词，"【】"中间的内容为选取随机内容，用“|”分隔。每次随机选取一个内容作为关键词的回复内容
   - 输入的内容临时保存，下回从其他关键词点回来的时候还显示本次输入的内容。

7. **删除关键词**
   - 选中关键词 → 右键点击"删除"
   - 确认删除操作
   - 清理关键词和关键词对饮的回复内容

#### 副视频设置功能
**功能描述**：用于当话术和ai对话触发了副视频关键词的时候，就会下载触发的副视频话术语音，添加到待播放列表里面，当播放到该语音的时候就会切换到语音对应的副视频上。类型为副视频语音

**主要操作**：
1. **添加副视频**
   - 点击"添加副视频"按钮 → 打开对话框
   - 输入：添加副视频触发关键词，选择obs中获取到的视频源作为触发副视频的视频源
   - 确认创建操作
   -刷新副视频列表

2. **编辑副视频**
   - 选中副视频* → 双击或点击"编辑副视频"
   - 输入：输入播放该副视频的时候需要播放的话术，一行一个，可以加入变量词，可以多行，每次随机取不重复话术，直至取完之后再重置后再取不重复话术。
   -保存修改的副视频
   
3. **删除副视频**
   - 选中副视频* → 点击"编辑副视频"
   - 确认删除操作
   - 清理删除，刷新副视频列表

#### 播放控制功能
**功能描述**：控制语音内容的播放，支持播放列表管理和播放状态控制。现在主视频源的进度，显示当前位置，显示位置所处的时间段。

**主要操作**：
1. **播放控制**
   - 开始播放：点击播放按钮，开始播放当前列表
   - 暂停播放：点击暂停按钮，暂停当前播放
   - 停止播放：点击停止按钮，停止播放并重置
   - 播放前准备：点击开始播放后，每个时间段都会生成系统设置里面设置的预备语音数量的语音作为备用语音。生成完成再播放。比如预备语音数量为5，每个时间段都取5条不重复话术来下载5条语音。
2. **播放列表管理**
   - 删除项目：清空待播放列表
   - 播放顺序：按照播放语音优先级播放语音，副视频语音>AI对话语音>报时语音>主视频语音，按照这个播放优先级来播放语音
   - 播放模式：获取系统设置里面的循环模式播放主视频语音，其他的视频语音按照下载顺序播放

   -播放逻辑：播放主视频语音的时候先读取当前主视频所在的时间段再选取对应时间段的语音播放。当播放副视频语音的时候，需要暂停主视频，切换到副视频后播放副视频语音，播放副视频语音完成之后，切回主视频，继续播放主视频，继续播放主视频语音。如果有AI对话语音播放或者报时语音播放的时候，播放完成后视频处于什么位置，就选取当前所处的时间段的主视频语音播放。

#### OBS控制功能
**功能描述**：与OBS Studio集成，控制直播场景和媒体播放。

**主要操作**：
1. **连接OBS**
   - 配置OBS连接参数（地址、端口、密码）
   - 建立WebSocket连接
   - 验证连接状态

2. **主视频源设置**
   - 获取场景列表
   - 设置视频源A的视频源，设置视频源B的视频源。用于视频源A变速播放完成前提前把视频源B变速播放，显示视频源B，然后再隐藏视频源A，当视频源B要播放完成的时候也把视频源A变速播放，显示视频源A，然后再应酬视频源B。作用为无黑屏切换视频。每次只有一个最上面显示的视频源作为播放控制中主视频源的进度

3. **主视频源速度设置**
   - 每次开始播放前设置主视频源速度区间，最小速度和最大速度
   
#### 报时设置功能
**功能描述**：定时播报规定的报时话术

**主要操作**：
1. **启用报时功能**
   - 勾选“启用报时功能”，输入报时间隔（分钟）
   - 输入报时话术，一行一个，可以加入变量词，可以多行，每次随机取不重复话术
   - 保存报时设置

#### 弹幕功能
**功能描述**：定时播报规定的报时话术

**主要操作**：
1. **连接弹幕服务器**
   - 点击“连接弹幕”，就连接websocket弹幕服务器“ws://127.0.0.1:9999”
   - 显示连接状态

2. **断开弹幕服务器**
   - 点击“断开弹幕”，就断开websocket弹幕服务器“ws://127.0.0.1:9999”
   - 显示连接状态

3. **弹幕列表管理**
   - 显示连接弹幕成功后返回的所有弹幕数据。当弹幕触发AI对话之后需要下载AI对话语音放到待播放语音列表里面
   - 输入模拟弹幕，点击“发送弹幕”，可以模拟用户发送弹幕，享受到同样触发AI对话的效果

#### 系统设置功能
**功能描述**：设置一些变量参数，播放模式，预备语音数量，语音播放间隔。声卡和声音。

**主要操作**：
1. **选择输入所有设置**
   - 输入游戏名称，游戏类型作为AI对话，话术，副视频，报时里面的变量词
   - 输入预备语音数量
   - 设置每次播放完成语音停顿时间区间
   - 选择播放使用的声卡
   - 设置播放语音的音量

### 🔗 第三步：描述功能联系

#### 核心业务流程：自动播放
```
 → 时间段匹配 → 下载语音 →    OBS控制 →    播放执行
    ↓               ↓             ↓           ↓           
 找到匹配的      生成所需的   切换场景和  按优先级顺序播放
 时间段话术配置   语音文件     视频播放     语音内容
```

#### 详细流程说明
2. **发现匹配的时间段**时，获取该时间段的话术信息
3. **检查语音文件**是否已下载，如未生成则调用语音API
4. **如果有关联视频**，通知OBS切换场景并播放视频
5. **开始播放语音**，按照语音类型优先级播放语音，主视频语音按照时间段配置的顺序播放语音内容
6. **播放完成后**，等待间隔时间，再去读视频播放位置处于哪个时间段。更新状态

#### 数据流向
```
用户配置时间段 → 保存到配置文件 → 时间监控读取配置
                                        ↓
用户界面更新 ← 状态事件通知 ← 播放控制器 ← 语音下载器
    ↑                                    ↑
界面状态同步 ← OBS状态更新 ← OBS控制器 ← 事件分发
```

#### 模块间通信
- **事件驱动**：各模块通过事件总线进行通信
- **异步处理**：语音生成和网络操作使用异步处理
- **状态同步**：所有状态变化都会通知界面更新
