"""
AI主播系统 v2 - 话术管理服务
话术的获取、上传、新建等操作
"""

import requests
import json
from typing import Dict, Any, List, Optional

from .logging_service import create_logger
from .error_handler import handle_exceptions


class ScriptService:
    """话术管理服务"""

    def __init__(self, server_url: str = "http://localhost:12456"):
        self.logger = create_logger("script_service")
        self.server_url = server_url.rstrip('/')
        self.session = requests.Session()

        # 设置请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'AI-Broadcaster-Client/2.0.0'
        })

        # 禁用代理，直接连接
        self.session.proxies = {'http': None, 'https': None}

        # 超时设置
        self.timeout = 10

    @handle_exceptions("ScriptService")
    def get_script_list(self) -> Dict[str, Any]:
        """
        获取话术列表

        Returns:
            话术列表结果
        """
        try:
            self.logger.info("获取话术列表...")

            # 发送请求
            response = self.session.get(
                f"{self.server_url}/getscriptlist",
                timeout=self.timeout
            )

            # 检查HTTP状态码
            if response.status_code != 200:
                return {
                    'success': False,
                    'message': f'服务器错误: HTTP {response.status_code}'
                }

            # 解析响应
            result = response.json()

            # 提取话术列表
            scripts = result.get('ai话术', [])

            self.logger.info(f"获取到 {len(scripts)} 个话术")

            return {
                'success': True,
                'scripts': scripts
            }

        except requests.exceptions.ConnectionError:
            self.logger.error("无法连接到服务器")
            return {
                'success': False,
                'message': '无法连接到服务器，请检查网络连接'
            }
        except requests.exceptions.Timeout:
            self.logger.error("请求超时")
            return {
                'success': False,
                'message': '请求超时，请稍后重试'
            }
        except json.JSONDecodeError:
            self.logger.error("服务器响应格式错误")
            return {
                'success': False,
                'message': '服务器响应格式错误'
            }
        except Exception as e:
            self.logger.error(f"获取话术列表失败: {e}")
            return {
                'success': False,
                'message': f'获取话术列表失败: {str(e)}'
            }

    @handle_exceptions("ScriptService")
    def get_script_content(self, script_name: str) -> Dict[str, Any]:
        """
        获取话术内容

        Args:
            script_name: 话术名称

        Returns:
            话术内容结果
        """
        try:
            self.logger.info(f"获取话术内容: {script_name}")

            # 准备请求数据
            data = {
                "类型": "获取话术",
                "话术名": script_name
            }

            # 发送请求
            response = self.session.post(
                f"{self.server_url}/",
                json=data,
                timeout=self.timeout
            )

            # 检查HTTP状态码
            if response.status_code != 200:
                return {
                    'success': False,
                    'message': f'服务器错误: HTTP {response.status_code}'
                }

            # 获取响应内容
            content = response.text

            # 🔥 检查是否为时间段话术格式并转换
            processed_content = self._process_script_content(content, script_name)

            return {
                'success': True,
                'content': processed_content,
                'script_name': script_name,
                'original_content': content  # 保留原始内容
            }

        except Exception as e:
            self.logger.error(f"获取话术内容失败: {e}")
            return {
                'success': False,
                'message': f'获取话术内容失败: {str(e)}'
            }

    def _process_script_content(self, content: str, script_name: str) -> str:
        """
        处理话术内容，将时间段格式转换为标准JSON格式

        Args:
            content: 原始话术内容
            script_name: 话术名称

        Returns:
            处理后的内容
        """
        try:
            # 检查是否为JSON格式
            if content.strip().startswith('{') and content.strip().endswith('}'):
                try:
                    data = json.loads(content)

                    # 🔥 检查是否为新的时间段话术格式：{"0秒 - 10秒": "话术内容", ...}
                    if isinstance(data, dict) and all(isinstance(v, str) for v in data.values()):
                        # 新格式，直接返回，不需要转换
                        self.logger.info(f"检测到新时间段话术格式: {script_name}")
                        return content

                    # 🔥 检查是否为旧的时间段话术格式：{"0秒 - 10秒": {"start": 0, "end": 10, "content": "..."}, ...}
                    elif isinstance(data, dict) and all(
                        isinstance(v, dict) and 'start' in v and 'end' in v and 'content' in v
                        for v in data.values()
                    ):
                        # 旧格式，转换为新格式
                        converted_data = {}
                        for segment_name, segment_data in data.items():
                            segment_content = segment_data.get('content', '')
                            converted_data[segment_name] = segment_content

                        # 转换为新格式的JSON字符串
                        processed_content = json.dumps(converted_data, ensure_ascii=False, indent=2)
                        self.logger.info(f"转换旧时间段话术格式为新格式: {script_name}")
                        self.logger.info(f"转换后格式预览: {processed_content[:200]}...")

                        return processed_content

                except json.JSONDecodeError:
                    pass

            # 如果不是时间段格式或解析失败，返回原内容
            return content

        except Exception as e:
            self.logger.error(f"处理话术内容异常: {e}")
            return content

    def _extract_time_from_segment_name(self, segment_name: str) -> tuple:
        """
        从时间段名称中提取开始和结束时间

        Args:
            segment_name: 时间段名称，如 "0秒 - 10秒"

        Returns:
            (start_time, end_time) 元组
        """
        try:
            import re

            # 匹配时间段格式：数字秒 - 数字秒
            pattern = r'(\d+)秒\s*-\s*(\d+)秒'
            match = re.search(pattern, segment_name)

            if match:
                start_time = int(match.group(1))
                end_time = int(match.group(2))
                return start_time, end_time

            # 如果无法解析，返回默认值
            return 0, 60

        except Exception as e:
            self.logger.error(f"提取时间信息失败: {e}")
            return 0, 60

    @handle_exceptions("ScriptService")
    def upload_script(self, script_name: str, content) -> Dict[str, Any]:
        """
        上传话术

        Args:
            script_name: 话术名称
            content: 话术内容（可以是字符串或JSON对象格式的时间段话术）

        Returns:
            上传结果
        """
        try:
            self.logger.info(f"上传话术: {script_name}")

            # 🔥 支持多种输入格式：字符串或JSON对象
            upload_data = content

            # 如果输入是字符串，检测是否为JSON格式
            if isinstance(content, str):
                if content.strip().startswith('{') and content.strip().endswith('}'):
                    try:
                        time_segments_data = json.loads(content)

                        # 检查是否为新的时间段格式：{"0秒 - 10秒": "话术内容", ...}
                        if isinstance(time_segments_data, dict) and all(
                            isinstance(v, str) for v in time_segments_data.values()
                        ):
                            # [FIX] 新格式，保持为字符串格式
                            upload_data = content  # 保持原始字符串格式
                            self.logger.info(f"字符串JSON保持字符串格式: {script_name}")

                        # 检查是否为旧的时间段格式：{"0秒 - 10秒": {"start": 0, "end": 10, "content": "..."}, ...}
                        elif isinstance(time_segments_data, dict) and all(
                            isinstance(v, dict) and 'start' in v and 'end' in v and 'content' in v
                            for v in time_segments_data.values()
                        ):
                            # 🔥 转换旧格式为新格式：时间段名：话术内容
                            converted_data = {}
                            for segment_name, segment_data in time_segments_data.items():
                                segment_content = segment_data.get('content', '')
                                converted_data[segment_name] = segment_content

                            # [FIX] 转换为字符串格式
                            upload_data = json.dumps(converted_data, ensure_ascii=False, indent=2)
                            self.logger.info(f"旧格式转换为字符串格式: {script_name}")

                    except json.JSONDecodeError:
                        # 不是JSON格式，保持字符串原样
                        pass

            # 如果输入已经是字典对象（JSON对象）
            elif isinstance(content, dict):
                # 检查是否为新的时间段格式
                if all(isinstance(v, str) for v in content.values()):
                    # [FIX] 将JSON对象转换为字符串格式，因为服务器期望字符串
                    upload_data = json.dumps(content, ensure_ascii=False, indent=2)
                    self.logger.info(f"JSON对象转换为字符串格式: {script_name}")

                # 检查是否为旧的时间段格式
                elif all(
                    isinstance(v, dict) and 'start' in v and 'end' in v and 'content' in v
                    for v in content.values()
                ):
                    # 转换旧格式为新格式
                    converted_data = {}
                    for segment_name, segment_data in content.items():
                        segment_content = segment_data.get('content', '')
                        converted_data[segment_name] = segment_content

                    # [FIX] 转换为字符串格式
                    upload_data = json.dumps(converted_data, ensure_ascii=False, indent=2)
                    self.logger.info(f"旧JSON对象格式转换为字符串格式: {script_name}")

            # 准备请求数据
            data = {
                "类型": "上传话术",
                "话术名": script_name,
                "上传数据": upload_data
            }

            # 安全的日志记录，避免json变量作用域问题
            try:
                data_preview = json.dumps(data, ensure_ascii=False)[:300]
                self.logger.info(f"上传请求数据: {data_preview}...")
            except Exception:
                self.logger.info(f"上传请求数据: [无法序列化，长度: {len(str(data))}]")

            # 发送请求
            response = self.session.post(
                f"{self.server_url}/",
                json=data,
                timeout=self.timeout
            )

            # 检查HTTP状态码
            if response.status_code != 200:
                return {
                    'success': False,
                    'message': f'服务器错误: HTTP {response.status_code}'
                }

            # 检查响应内容
            try:
                # 尝试解析JSON响应
                result = response.json()
                if result.get('状态') == '成功':
                    return {
                        'success': True,
                        'message': result.get('信息', '话术上传成功')
                    }
                else:
                    return {
                        'success': False,
                        'message': result.get('信息', '话术上传失败')
                    }
            except json.JSONDecodeError:
                # 如果不是JSON，检查文本响应
                if response.text.strip():
                    return {
                        'success': True,
                        'message': '话术上传成功'
                    }
                else:
                    return {
                        'success': False,
                        'message': '服务器响应为空'
                    }

        except Exception as e:
            self.logger.error(f"上传话术失败: {e}")
            return {
                'success': False,
                'message': f'上传话术失败: {str(e)}'
            }

    @handle_exceptions("ScriptService")
    def create_script(self, script_name: str, content: str = "") -> Dict[str, Any]:
        """
        新建话术

        Args:
            script_name: 话术名称
            content: 话术内容

        Returns:
            创建结果
        """
        try:
            self.logger.info(f"新建话术: {script_name}")

            # 准备请求数据
            data = {
                "类型": "新建话术",
                "话术名": script_name
            }

            # 发送请求
            response = self.session.post(
                f"{self.server_url}/",
                json=data,
                timeout=self.timeout
            )

            # 检查HTTP状态码
            if response.status_code != 200:
                return {
                    'success': False,
                    'message': f'服务器错误: HTTP {response.status_code}'
                }

            # 检查响应内容
            try:
                # 尝试解析JSON响应
                result = response.json()
                if result.get('状态') == '成功':
                    return {
                        'success': True,
                        'message': result.get('信息', '话术创建成功')
                    }
                else:
                    return {
                        'success': False,
                        'message': result.get('信息', '话术创建失败')
                    }
            except json.JSONDecodeError:
                # 如果不是JSON，检查文本响应
                if response.text.strip():
                    return {
                        'success': True,
                        'message': '话术创建成功'
                    }
                else:
                    return {
                        'success': False,
                        'message': '服务器响应为空'
                    }

        except Exception as e:
            self.logger.error(f"新建话术失败: {e}")
            return {
                'success': False,
                'message': f'新建话术失败: {str(e)}'
            }

    def set_server_url(self, url: str):
        """设置服务器地址"""
        self.server_url = url.rstrip('/')
        self.logger.info(f"设置服务器地址: {self.server_url}")

    def close(self):
        """关闭会话"""
        self.session.close()
