"""
AI Broadcaster v2 - 提示词管理器
管理AI对话的提示词模板
"""

from typing import Dict, Any, List, Optional
from datetime import datetime

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class PromptManager:
    """提示词管理器"""
    
    def __init__(self, db_manager):
        self.logger = create_logger("prompt_manager")
        self.db_manager = db_manager
        
        # 内置提示词模板
        self.system_prompts = {
            'friendly': {
                'conversational': """你是一个友善、热情的AI助手，正在参与一场轻松的对话。
你的特点：
- 语言风格亲切自然，就像朋友间的聊天
- 善于倾听，会对用户的话题表现出真诚的兴趣
- 回复简洁明了，不会过于冗长
- 偶尔会用一些口语化的表达
- 会适当地提出问题来延续对话

请用中文回复，保持友好和自然的语调。""",
                
                'formal': """您是一位专业且友善的AI助手，正在进行正式的对话交流。
您的特点：
- 使用礼貌、专业的语言
- 回复准确、有条理
- 保持友善但不失专业性
- 会根据话题提供有价值的见解
- 注重对话的质量和深度

请使用标准的中文表达，保持专业而友善的语调。"""
            },
            
            'professional': {
                'conversational': """你是一个专业的AI助手，具有丰富的知识和经验。
你的特点：
- 回复准确、有深度
- 善于分析和总结
- 会提供专业的建议和见解
- 语言简洁有力
- 注重逻辑性和条理性

请用中文回复，保持专业而易懂的表达方式。""",
                
                'formal': """您是一位资深的专业顾问，正在提供专业的咨询服务。
您的特点：
- 具备深厚的专业知识
- 回复严谨、准确
- 善于分析复杂问题
- 提供有价值的专业建议
- 语言正式、条理清晰

请使用正式的中文表达，确保回复的专业性和准确性。"""
            },
            
            'humorous': {
                'conversational': """你是一个幽默风趣的AI助手，善于用轻松的方式进行对话。
你的特点：
- 语言幽默，但不失分寸
- 善于用比喻和有趣的例子
- 会适当地开一些无害的玩笑
- 保持积极乐观的态度
- 让对话变得轻松愉快

请用中文回复，保持幽默而友善的语调。""",
                
                'casual': """你是个很有趣的AI朋友，总是能让聊天变得很有意思。
你的特点：
- 说话很有趣，经常有意想不到的角度
- 喜欢用生动的比喻和例子
- 偶尔会说些俏皮话
- 很会活跃气氛
- 让人感觉轻松愉快

用轻松的中文聊天，就像好朋友一样。"""
            }
        }
        
        # 话题提示词
        self.topic_prompts = {
            'weather': "请以自然、友好的方式聊聊天气话题，可以分享一些天气相关的感受或建议。",
            'news': "请以客观、理性的方式讨论新闻话题，提供有价值的观点和分析。",
            'entertainment': "请以轻松、有趣的方式聊聊娱乐话题，分享一些有趣的见解。",
            'sports': "请以热情、专业的方式讨论体育话题，可以分享一些运动相关的知识。",
            'technology': "请以专业、前瞻的方式讨论科技话题，提供有深度的分析。",
            'life': "请以温暖、贴心的方式聊聊生活话题，分享一些生活智慧和感悟。",
            'food': "请以美食家的角度聊聊美食话题，分享一些有趣的美食知识。",
            'travel': "请以旅行者的角度聊聊旅游话题，分享一些旅行的见解和建议。",
            'culture': "请以文化学者的角度讨论文化话题，提供有深度的文化见解。",
            'education': "请以教育者的角度讨论教育话题，分享一些教育理念和方法。"
        }
        
    @handle_exceptions("PromptManager")
    def get_system_prompt(self, personality: str = 'friendly', style: str = 'conversational') -> str:
        """获取系统提示词"""
        try:
            if personality in self.system_prompts and style in self.system_prompts[personality]:
                return self.system_prompts[personality][style]
            else:
                # 返回默认提示词
                return self.system_prompts['friendly']['conversational']
                
        except Exception as e:
            self.logger.error(f"获取系统提示词失败: {e}")
            return "你是一个友善的AI助手，请用中文进行自然的对话。"
            
    @handle_exceptions("PromptManager")
    def get_topic_prompt(self, topic: str) -> str:
        """获取话题提示词"""
        try:
            return self.topic_prompts.get(topic, f"请聊聊关于{topic}的话题。")
            
        except Exception as e:
            self.logger.error(f"获取话题提示词失败: {e}")
            return f"请聊聊关于{topic}的话题。"
            
    @handle_exceptions("PromptManager")
    def create_custom_prompt(self, name: str, content: str, category: str = 'custom',
                            user_id: Optional[int] = None) -> Optional[int]:
        """创建自定义提示词"""
        try:
            prompt_id = self.db_manager.execute_insert(
                """INSERT INTO prompts (name, content, category, user_id, created_at, updated_at)
                   VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)""",
                (name, content, category, user_id)
            )
            
            if prompt_id:
                self.logger.info(f"创建自定义提示词成功: {name}")
                return prompt_id
            else:
                self.logger.error(f"创建自定义提示词失败: {name}")
                return None
                
        except Exception as e:
            self.logger.error(f"创建自定义提示词异常: {e}")
            return None
            
    @handle_exceptions("PromptManager")
    def get_custom_prompts(self, user_id: Optional[int] = None, 
                          category: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取自定义提示词列表"""
        try:
            query = "SELECT * FROM prompts WHERE (user_id = ? OR user_id IS NULL)"
            params = [user_id]
            
            if category:
                query += " AND category = ?"
                params.append(category)
                
            query += " ORDER BY updated_at DESC"
            
            prompts = self.db_manager.execute_query(query, params)
            return prompts or []
            
        except Exception as e:
            self.logger.error(f"获取自定义提示词失败: {e}")
            return []
            
    @handle_exceptions("PromptManager")
    def update_custom_prompt(self, prompt_id: int, name: Optional[str] = None,
                            content: Optional[str] = None, category: Optional[str] = None) -> bool:
        """更新自定义提示词"""
        try:
            update_fields = []
            params = []
            
            if name is not None:
                update_fields.append("name = ?")
                params.append(name)
                
            if content is not None:
                update_fields.append("content = ?")
                params.append(content)
                
            if category is not None:
                update_fields.append("category = ?")
                params.append(category)
                
            if not update_fields:
                return True
                
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            params.append(prompt_id)
            
            query = f"UPDATE prompts SET {', '.join(update_fields)} WHERE id = ?"
            
            rows_affected = self.db_manager.execute_update(query, params)
            
            if rows_affected > 0:
                self.logger.info(f"更新自定义提示词成功: ID {prompt_id}")
                return True
            else:
                self.logger.warning(f"提示词不存在: ID {prompt_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"更新自定义提示词失败: {e}")
            return False
            
    @handle_exceptions("PromptManager")
    def delete_custom_prompt(self, prompt_id: int) -> bool:
        """删除自定义提示词"""
        try:
            rows_affected = self.db_manager.execute_update(
                "DELETE FROM prompts WHERE id = ?",
                (prompt_id,)
            )
            
            if rows_affected > 0:
                self.logger.info(f"删除自定义提示词成功: ID {prompt_id}")
                return True
            else:
                self.logger.warning(f"提示词不存在: ID {prompt_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除自定义提示词失败: {e}")
            return False
            
    @handle_exceptions("PromptManager")
    def get_prompt_categories(self, user_id: Optional[int] = None) -> List[str]:
        """获取提示词分类列表"""
        try:
            # 内置分类
            builtin_categories = ['system', 'topic', 'custom']
            
            # 自定义分类
            query = """
                SELECT DISTINCT category FROM prompts 
                WHERE (user_id = ? OR user_id IS NULL) AND category IS NOT NULL
                ORDER BY category
            """
            
            results = self.db_manager.execute_query(query, (user_id,))
            custom_categories = [row['category'] for row in results if row['category']]
            
            # 合并并去重
            all_categories = list(set(builtin_categories + custom_categories))
            all_categories.sort()
            
            return all_categories
            
        except Exception as e:
            self.logger.error(f"获取提示词分类失败: {e}")
            return ['system', 'topic', 'custom']
            
    @handle_exceptions("PromptManager")
    def search_prompts(self, keyword: str, user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """搜索提示词"""
        try:
            query = """
                SELECT * FROM prompts 
                WHERE (user_id = ? OR user_id IS NULL) 
                AND (name LIKE ? OR content LIKE ? OR category LIKE ?)
                ORDER BY updated_at DESC
            """
            
            search_pattern = f"%{keyword}%"
            params = [user_id, search_pattern, search_pattern, search_pattern]
            
            prompts = self.db_manager.execute_query(query, params)
            return prompts or []
            
        except Exception as e:
            self.logger.error(f"搜索提示词失败: {e}")
            return []
            
    @handle_exceptions("PromptManager")
    def get_available_personalities(self) -> List[str]:
        """获取可用的人格类型"""
        return list(self.system_prompts.keys())
        
    @handle_exceptions("PromptManager")
    def get_available_styles(self, personality: str = 'friendly') -> List[str]:
        """获取可用的对话风格"""
        if personality in self.system_prompts:
            return list(self.system_prompts[personality].keys())
        return ['conversational', 'formal']
        
    @handle_exceptions("PromptManager")
    def get_available_topics(self) -> List[str]:
        """获取可用的话题类型"""
        return list(self.topic_prompts.keys())
        
    @handle_exceptions("PromptManager")
    def build_context_prompt(self, context: Dict[str, Any]) -> str:
        """构建上下文提示词"""
        try:
            context_parts = []
            
            if 'user_name' in context:
                context_parts.append(f"用户名称：{context['user_name']}")
                
            if 'current_time' in context:
                context_parts.append(f"当前时间：{context['current_time']}")
                
            if 'location' in context:
                context_parts.append(f"位置信息：{context['location']}")
                
            if 'mood' in context:
                context_parts.append(f"用户情绪：{context['mood']}")
                
            if 'topic' in context:
                context_parts.append(f"当前话题：{context['topic']}")
                
            if 'previous_topics' in context:
                topics = ', '.join(context['previous_topics'])
                context_parts.append(f"之前讨论的话题：{topics}")
                
            if context_parts:
                return "上下文信息：\n" + "\n".join(context_parts) + "\n\n"
            else:
                return ""
                
        except Exception as e:
            self.logger.error(f"构建上下文提示词失败: {e}")
            return ""
            
    @handle_exceptions("PromptManager")
    def validate_prompt(self, content: str) -> Dict[str, Any]:
        """验证提示词内容"""
        try:
            result = {
                'valid': True,
                'warnings': [],
                'suggestions': []
            }
            
            # 检查长度
            if len(content) < 10:
                result['warnings'].append("提示词内容过短，可能影响AI理解")
                
            if len(content) > 2000:
                result['warnings'].append("提示词内容过长，可能影响处理效率")
                
            # 检查关键词
            if '你' not in content and '您' not in content:
                result['suggestions'].append("建议明确指定AI的角色（使用'你'或'您'）")
                
            if '中文' not in content and '中国' not in content:
                result['suggestions'].append("建议明确指定使用中文回复")
                
            # 检查特殊字符
            special_chars = ['<', '>', '{', '}', '[', ']']
            for char in special_chars:
                if char in content:
                    result['warnings'].append(f"包含特殊字符'{char}'，可能影响解析")
                    
            return result
            
        except Exception as e:
            self.logger.error(f"验证提示词失败: {e}")
            return {'valid': False, 'warnings': [str(e)], 'suggestions': []}
