#!/usr/bin/env python3
"""
测试按钮视觉反馈效果
"""

import sys
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QFont


class ButtonTestWindow(QMainWindow):
    """按钮测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("按钮视觉反馈测试")
        self.setGeometry(100, 100, 600, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 标题
        title_label = QLabel("🎮 按钮视觉反馈测试")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #333; margin: 20px; text-align: center;")
        layout.addWidget(title_label)
        
        # 状态标签
        self.status_label = QLabel("准备测试按钮效果")
        self.status_label.setFont(QFont("Arial", 12))
        self.status_label.setStyleSheet("color: #666; margin: 10px; text-align: center;")
        layout.addWidget(self.status_label)
        
        # 按钮容器
        button_container = QWidget()
        button_layout = QHBoxLayout()
        button_container.setLayout(button_layout)
        
        # 创建测试按钮
        self.create_test_buttons(button_layout)
        
        layout.addWidget(button_container)
        
        # 说明文字
        info_label = QLabel(
            "点击按钮测试视觉反馈效果：\n"
            "• 播放按钮：绿色主题，点击后显示'播放中'\n"
            "• 暂停按钮：橙色主题，点击后切换状态\n"
            "• 停止按钮：红色主题，点击后显示'停止中'\n"
            "• 清空按钮：灰色主题，点击后显示'清空中'"
        )
        info_label.setFont(QFont("Arial", 10))
        info_label.setStyleSheet("color: #888; margin: 20px; line-height: 1.5;")
        layout.addWidget(info_label)
        
    def create_test_buttons(self, layout):
        """创建测试按钮"""
        button_width = 100
        button_height = 45
        
        # 播放按钮
        self.play_button = QPushButton("▶ 播放")
        self.play_button.setFixedSize(button_width, button_height)
        self.play_button.clicked.connect(self.on_play_clicked)
        self.play_button.setStyleSheet(self.get_button_style("play"))
        layout.addWidget(self.play_button)
        
        # 暂停按钮
        self.pause_button = QPushButton("⏸ 暂停")
        self.pause_button.setFixedSize(button_width, button_height)
        self.pause_button.clicked.connect(self.on_pause_clicked)
        self.pause_button.setStyleSheet(self.get_button_style("pause"))
        layout.addWidget(self.pause_button)
        
        # 停止按钮
        self.stop_button = QPushButton("⏹ 停止")
        self.stop_button.setFixedSize(button_width, button_height)
        self.stop_button.clicked.connect(self.on_stop_clicked)
        self.stop_button.setStyleSheet(self.get_button_style("stop"))
        layout.addWidget(self.stop_button)
        
        # 清空按钮
        self.clear_button = QPushButton("🗑 清空")
        self.clear_button.setFixedSize(button_width, button_height)
        self.clear_button.clicked.connect(self.on_clear_clicked)
        self.clear_button.setStyleSheet(self.get_button_style("clear"))
        layout.addWidget(self.clear_button)
        
        # 初始化状态
        self.is_paused = False
        
    def get_button_style(self, button_type):
        """获取按钮样式"""
        base_style = """
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                border: 2px solid;
                border-radius: 8px;
                padding: 8px 12px;
                text-align: center;
            }
            QPushButton:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }
            QPushButton:pressed {
                transform: translateY(1px);
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }
            QPushButton:disabled {
                opacity: 0.6;
            }
        """
        
        if button_type == "play":
            return base_style + """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4CAF50, stop:1 #45a049);
                    border-color: #4CAF50;
                    color: white;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #5CBF60, stop:1 #4CAF50);
                }
            """
        elif button_type == "pause":
            return base_style + """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #FF9800, stop:1 #F57C00);
                    border-color: #FF9800;
                    color: white;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #FFB74D, stop:1 #FF9800);
                }
            """
        elif button_type == "stop":
            return base_style + """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #F44336, stop:1 #D32F2F);
                    border-color: #F44336;
                    color: white;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #EF5350, stop:1 #F44336);
                }
            """
        elif button_type == "clear":
            return base_style + """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #9E9E9E, stop:1 #757575);
                    border-color: #9E9E9E;
                    color: white;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #BDBDBD, stop:1 #9E9E9E);
                }
            """
        return base_style
        
    def on_play_clicked(self):
        """播放按钮点击"""
        print("🎵 播放按钮被点击")
        self.status_label.setText("🎵 播放中...")
        self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        
        self.play_button.setText("🎵 播放中")
        self.play_button.setEnabled(False)
        
        # 2秒后恢复
        QTimer.singleShot(2000, self.restore_play_button)
        
    def on_pause_clicked(self):
        """暂停按钮点击"""
        if self.is_paused:
            print("▶ 继续播放")
            self.status_label.setText("▶ 继续播放...")
            self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
            self.pause_button.setText("▶ 继续中")
            self.pause_button.setStyleSheet(self.get_button_style("play"))
        else:
            print("⏸ 暂停播放")
            self.status_label.setText("⏸ 暂停中...")
            self.status_label.setStyleSheet("color: #FF9800; font-weight: bold;")
            self.pause_button.setText("⏸ 暂停中")
            
        self.is_paused = not self.is_paused
        
        # 1秒后恢复
        QTimer.singleShot(1000, self.restore_pause_button)
        
    def on_stop_clicked(self):
        """停止按钮点击"""
        print("⏹ 停止播放")
        self.status_label.setText("⏹ 停止中...")
        self.status_label.setStyleSheet("color: #F44336; font-weight: bold;")
        
        self.stop_button.setText("⏹ 停止中")
        self.stop_button.setEnabled(False)
        
        # 重置暂停状态
        self.is_paused = False
        
        # 1.5秒后恢复
        QTimer.singleShot(1500, self.restore_stop_button)
        
    def on_clear_clicked(self):
        """清空按钮点击"""
        print("🗑 清空列表")
        self.status_label.setText("🗑 清空中...")
        self.status_label.setStyleSheet("color: #9E9E9E; font-weight: bold;")
        
        self.clear_button.setText("🗑 清空中")
        self.clear_button.setEnabled(False)
        
        # 2秒后恢复
        QTimer.singleShot(2000, self.restore_clear_button)
        
    def restore_play_button(self):
        """恢复播放按钮"""
        self.play_button.setText("▶ 播放")
        self.play_button.setEnabled(True)
        self.status_label.setText("播放系统已启动")
        self.status_label.setStyleSheet("color: #666; font-weight: normal;")
        
    def restore_pause_button(self):
        """恢复暂停按钮"""
        if self.is_paused:
            self.pause_button.setText("▶ 继续")
            self.pause_button.setStyleSheet(self.get_button_style("play"))
            self.status_label.setText("已暂停")
        else:
            self.pause_button.setText("⏸ 暂停")
            self.pause_button.setStyleSheet(self.get_button_style("pause"))
            self.status_label.setText("播放中")
        self.status_label.setStyleSheet("color: #666; font-weight: normal;")
        
    def restore_stop_button(self):
        """恢复停止按钮"""
        self.stop_button.setText("⏹ 停止")
        self.stop_button.setEnabled(True)
        self.status_label.setText("已停止")
        self.status_label.setStyleSheet("color: #666; font-weight: normal;")
        
        # 同时恢复暂停按钮状态
        self.pause_button.setText("⏸ 暂停")
        self.pause_button.setStyleSheet(self.get_button_style("pause"))
        
    def restore_clear_button(self):
        """恢复清空按钮"""
        self.clear_button.setText("🗑 清空")
        self.clear_button.setEnabled(True)
        self.status_label.setText("列表已清空")
        self.status_label.setStyleSheet("color: #666; font-weight: normal;")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f5f5f5;
        }
        QWidget {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
    """)
    
    window = ButtonTestWindow()
    window.show()
    
    print("🎮 按钮视觉反馈测试程序启动")
    print("请点击不同的按钮测试视觉效果")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
