#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试话术上传和获取功能
验证服务器API的完整流程
"""

import json
import requests
import re
from datetime import datetime

class ScriptAPITester:
    def __init__(self):
        self.base_url = "http://**************:12456/"
        self.test_script_name = "xy-kaer"
        
    def test_get_script(self, script_name):
        """测试获取话术功能"""
        print(f"\n🔍 测试获取话术: {script_name}")
        print("=" * 60)
        
        try:
            # 构造请求
            request_data = {
                "类型": "获取话术",
                "话术名": script_name
            }
            
            print(f"📤 发送请求: {request_data}")
            
            # 发送请求
            response = requests.post(
                self.base_url,
                json=request_data,
                timeout=10
            )
            
            print(f"📥 响应状态码: {response.status_code}")
            print(f"📥 响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                # 检查响应内容
                raw_content = response.content
                text_content = response.text
                
                print(f"📊 原始内容长度: {len(raw_content)} 字节")
                print(f"📊 文本内容长度: {len(text_content)} 字符")
                
                # 检查BOM
                if raw_content.startswith(b'\xef\xbb\xbf'):
                    print("⚠️  检测到UTF-8 BOM标记")
                    # 移除BOM
                    clean_content = raw_content[3:].decode('utf-8')
                    print(f"🧹 移除BOM后长度: {len(clean_content)} 字符")
                else:
                    print("✅ 没有BOM标记")
                    clean_content = text_content

                # 额外检查：字符串开头的BOM字符
                if clean_content.startswith('\ufeff'):
                    print("⚠️  检测到字符串BOM标记")
                    clean_content = clean_content[1:]
                    print(f"🧹 移除字符串BOM后长度: {len(clean_content)} 字符")
                
                print(f"📝 内容预览: {clean_content[:200]}...")
                
                # 尝试解析JSON
                try:
                    parsed_data = json.loads(clean_content)
                    print(f"✅ JSON解析成功!")
                    print(f"📊 数据类型: {type(parsed_data)}")
                    
                    if isinstance(parsed_data, dict):
                        print(f"📊 包含 {len(parsed_data)} 个键")
                        print(f"🔑 键列表: {list(parsed_data.keys())}")
                        
                        # 检查是否为时间段格式
                        is_time_segment_format = all(
                            isinstance(v, str) and '秒' in k 
                            for k, v in parsed_data.items()
                        )
                        
                        if is_time_segment_format:
                            print("🎯 这是时间段格式的话术!")
                            return {
                                'success': True,
                                'content': parsed_data,
                                'format': 'time_segments',
                                'clean_content': clean_content
                            }
                        else:
                            print("📄 这是普通格式的话术")
                            return {
                                'success': True,
                                'content': parsed_data,
                                'format': 'normal',
                                'clean_content': clean_content
                            }
                    else:
                        print(f"📄 数据不是字典格式: {type(parsed_data)}")
                        return {
                            'success': True,
                            'content': clean_content,
                            'format': 'text',
                            'clean_content': clean_content
                        }
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    return {
                        'success': False,
                        'error': f"JSON解析失败: {e}",
                        'raw_content': clean_content
                    }
            else:
                print(f"❌ 请求失败: HTTP {response.status_code}")
                print(f"📄 错误内容: {response.text}")
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}"
                }
                
        except requests.RequestException as e:
            print(f"❌ 网络请求异常: {e}")
            return {
                'success': False,
                'error': f"网络异常: {e}"
            }
    
    def test_upload_script(self, script_name, script_data):
        """测试上传话术功能"""
        print(f"\n📤 测试上传话术: {script_name}")
        print("=" * 60)
        
        try:
            # [FIX] 根据测试结果，服务器期望字符串格式
            if isinstance(script_data, dict):
                upload_data = json.dumps(script_data, ensure_ascii=False, indent=2)
                print(f"📤 转换字典为JSON字符串")
            else:
                upload_data = script_data

            # 构造上传请求
            upload_request = {
                "类型": "上传话术",
                "话术名": script_name,
                "上传数据": upload_data  # 使用字符串格式
            }

            print(f"📤 上传数据预览: {str(upload_data)[:200]}...")
            print(f"📊 数据类型: {type(upload_data)}")

            if isinstance(script_data, dict):
                print(f"📊 原始数据包含 {len(script_data)} 个键")
            
            # 发送请求
            response = requests.post(
                self.base_url,
                json=upload_request,
                timeout=10
            )
            
            print(f"📥 响应状态码: {response.status_code}")
            print(f"📥 响应内容: {response.text}")
            
            if response.status_code == 200:
                print("✅ 上传成功!")
                return {'success': True, 'response': response.text}
            else:
                print(f"❌ 上传失败: HTTP {response.status_code}")
                return {'success': False, 'error': f"HTTP {response.status_code}: {response.text}"}
                
        except requests.RequestException as e:
            print(f"❌ 网络请求异常: {e}")
            return {'success': False, 'error': f"网络异常: {e}"}
    
    def parse_time_segments(self, data):
        """解析时间段数据"""
        print(f"\n🔧 解析时间段数据")
        print("=" * 60)
        
        if not isinstance(data, dict):
            print("❌ 数据不是字典格式")
            return {}
        
        time_segments = {}
        
        for time_key, content in data.items():
            print(f"📝 处理时间段: {time_key}")
            
            # 从时间段名称中提取时间信息
            pattern = r'(\d+)秒\s*-\s*(\d+)秒'
            match = re.search(pattern, time_key)
            
            if match:
                start_time = int(match.group(1))
                end_time = int(match.group(2))
                print(f"   ⏰ 时间范围: {start_time}-{end_time}秒")
            else:
                # 使用默认时间
                start_time = 0
                end_time = 60
                print(f"   ⚠️  无法解析时间，使用默认: {start_time}-{end_time}秒")
            
            # 统计话术行数
            if isinstance(content, str):
                lines = content.split('\\n') if '\\n' in content else content.split('\n')
                script_lines = [line for line in lines if line.strip() and '***' in line]
                print(f"   📊 总行数: {len(lines)}, 话术行数: {len(script_lines)}")
                print(f"   📝 内容预览: {content[:50]}...")
            
            time_segments[time_key] = {
                'start': start_time,
                'end': end_time,
                'content': content
            }
        
        print(f"✅ 解析完成，共 {len(time_segments)} 个时间段")
        return time_segments
    
    def run_full_test(self):
        """运行完整测试"""
        print("🧪 开始完整的话术API测试")
        print("=" * 80)
        
        # 测试1：获取现有话术
        print(f"\n📋 测试1：获取现有话术 '{self.test_script_name}'")
        get_result = self.test_get_script(self.test_script_name)
        
        if get_result['success']:
            print("✅ 获取话术成功!")
            
            if get_result['format'] == 'time_segments':
                print("🎯 检测到时间段格式，开始解析...")
                time_segments = self.parse_time_segments(get_result['content'])
                
                # 测试2：重新上传相同的数据
                print(f"\n📋 测试2：重新上传时间段数据")
                upload_result = self.test_upload_script(
                    f"{self.test_script_name}_test", 
                    get_result['content']
                )
                
                if upload_result['success']:
                    print("✅ 上传测试成功!")
                    
                    # 测试3：获取刚上传的数据进行验证
                    print(f"\n📋 测试3：验证上传的数据")
                    verify_result = self.test_get_script(f"{self.test_script_name}_test")
                    
                    if verify_result['success']:
                        print("✅ 验证成功!")
                        
                        # 比较数据
                        if verify_result['content'] == get_result['content']:
                            print("🎉 数据完全一致，测试通过!")
                        else:
                            print("⚠️  数据有差异，需要检查")
                            print(f"原始数据键: {list(get_result['content'].keys())}")
                            print(f"验证数据键: {list(verify_result['content'].keys())}")
                    else:
                        print("❌ 验证失败")
                else:
                    print("❌ 上传测试失败")
            else:
                print("📄 不是时间段格式，跳过上传测试")
        else:
            print("❌ 获取话术失败")
        
        print("\n" + "=" * 80)
        print("🏁 测试完成")

def main():
    """主函数"""
    tester = ScriptAPITester()
    tester.run_full_test()

if __name__ == "__main__":
    main()
