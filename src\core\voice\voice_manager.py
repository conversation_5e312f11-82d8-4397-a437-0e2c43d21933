"""
AI Broadcaster v2 - 语音管理器
统一的语音生成、缓存和管理
"""

import random
import threading
from typing import Optional, Dict, Any, List
from pathlib import Path

from ...data.database_manager import DatabaseManager
from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions
from .speaker_manager import SpeakerManager
from .voice_downloader import VoiceDownloader


class VoiceManager:
    """语音管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.logger = create_logger("voice_manager")
        self.db = db_manager
        self.speaker_manager = SpeakerManager(db_manager)
        self.voice_downloader = VoiceDownloader(db_manager)
        
        # 语音生成队列
        self._generation_queue = []
        self._queue_lock = threading.Lock()
        self._is_generating = False
        
        # 当前设置
        self.current_speaker_id = 1
        self.default_speed_range = (0.8, 1.2)
        self.default_volume = 0.8
    
    @handle_exceptions("VoiceManager")
    def generate_voice(self, text: str, speaker_id: Optional[int] = None, 
                      speed: Optional[float] = None, use_cache: bool = True) -> Optional[str]:
        """
        生成语音文件
        
        Args:
            text: 要转换的文本
            speaker_id: 主播ID，None则使用当前主播
            speed: 语速，None则使用随机语速
            use_cache: 是否使用缓存
            
        Returns:
            语音文件路径
        """
        try:
            # 参数处理
            if speaker_id is None:
                speaker_id = self.current_speaker_id
            
            if speed is None:
                speed = self.speaker_manager.get_random_speed(speaker_id)
            
            # 文本预处理
            processed_text = self._preprocess_text(text)
            if not processed_text:
                self.logger.warning("文本为空，跳过语音生成")
                return None
            
            self.logger.info(f"生成语音: 主播={speaker_id}, 语速={speed}, 文本={processed_text[:50]}...")
            
            # 检查缓存
            if use_cache:
                cached_file = self.voice_downloader.check_cache(processed_text, speaker_id, speed)
                if cached_file:
                    return cached_file
            
            # 下载语音
            voice_file = self.voice_downloader.download_voice(processed_text, speaker_id, speed)
            
            if voice_file:
                # 记录播放历史
                self._record_play_history("generated", processed_text, voice_file)
                return voice_file
            else:
                self.logger.error("语音生成失败")
                return None
                
        except Exception as e:
            self.logger.error(f"生成语音失败: {e}")
            return None
    
    @handle_exceptions("VoiceManager")
    def generate_voice_mock(self, text: str, speaker_id: Optional[int] = None, 
                           speed: Optional[float] = None) -> Optional[str]:
        """
        生成模拟语音文件（用于测试）
        """
        try:
            if speaker_id is None:
                speaker_id = self.current_speaker_id
            
            if speed is None:
                speed = self.speaker_manager.get_random_speed(speaker_id)
            
            processed_text = self._preprocess_text(text)
            if not processed_text:
                return None
            
            voice_file = self.voice_downloader.download_voice_mock(processed_text, speaker_id, speed)
            
            if voice_file:
                self._record_play_history("mock", processed_text, voice_file)
                return voice_file
            
            return None
            
        except Exception as e:
            self.logger.error(f"生成模拟语音失败: {e}")
            return None
    
    def _preprocess_text(self, text: str) -> str:
        """文本预处理"""
        if not text:
            return ""
        
        # 移除多余空白
        text = ' '.join(text.split())
        
        # 限制长度
        max_length = 500
        if len(text) > max_length:
            text = text[:max_length] + "..."
            self.logger.warning(f"文本过长，已截断到{max_length}字符")
        
        return text
    
    @handle_exceptions("VoiceManager")
    def _record_play_history(self, voice_type: str, content: str, file_path: str):
        """记录播放历史"""
        try:
            # 这里需要当前用户ID，暂时使用None
            user_id = None  # TODO: 从用户管理器获取当前用户ID
            
            self.db.execute_insert(
                """INSERT INTO play_history (user_id, voice_type, content, file_path) 
                   VALUES (?, ?, ?, ?)""",
                (user_id, voice_type, content, file_path)
            )
        except Exception as e:
            self.logger.warning(f"记录播放历史失败: {e}")
    
    def set_current_speaker(self, speaker_id: int) -> bool:
        """设置当前主播"""
        speaker = self.speaker_manager.get_speaker_by_id(speaker_id)
        if speaker:
            self.current_speaker_id = speaker_id
            self.logger.info(f"当前主播已设置为: {speaker['name']}")
            return True
        else:
            self.logger.error(f"主播不存在: ID={speaker_id}")
            return False
    
    def get_current_speaker(self) -> Optional[Dict[str, Any]]:
        """获取当前主播信息"""
        return self.speaker_manager.get_speaker_by_id(self.current_speaker_id)
    
    def get_all_speakers(self) -> List[Dict[str, Any]]:
        """获取所有主播"""
        return self.speaker_manager.get_all_speakers()
    
    def get_random_speaker_id(self) -> int:
        """获取随机主播ID"""
        speakers = self.get_all_speakers()
        if speakers:
            return random.choice(speakers)['id']
        return 1
    
    def get_random_speed(self, speaker_id: Optional[int] = None) -> float:
        """获取随机语速"""
        if speaker_id is None:
            speaker_id = self.current_speaker_id
        return self.speaker_manager.get_random_speed(speaker_id)
    
    @handle_exceptions("VoiceManager")
    def batch_generate_voices(self, texts: List[str], speaker_id: Optional[int] = None) -> List[Optional[str]]:
        """
        批量生成语音
        
        Args:
            texts: 文本列表
            speaker_id: 主播ID
            
        Returns:
            语音文件路径列表
        """
        results = []
        
        for text in texts:
            voice_file = self.generate_voice(text, speaker_id)
            results.append(voice_file)
        
        success_count = sum(1 for result in results if result is not None)
        self.logger.info(f"批量生成语音完成: {success_count}/{len(texts)} 成功")
        
        return results
    
    @handle_exceptions("VoiceManager")
    def prepare_voices(self, texts: List[str], count: int = 5) -> List[str]:
        """
        预生成语音文件
        
        Args:
            texts: 文本列表
            count: 生成数量
            
        Returns:
            成功生成的语音文件路径列表
        """
        if not texts:
            return []
        
        # 随机选择文本
        selected_texts = random.choices(texts, k=min(count, len(texts)))
        
        voice_files = []
        for text in selected_texts:
            # 使用随机主播和语速
            speaker_id = self.get_random_speaker_id()
            speed = self.get_random_speed(speaker_id)
            
            voice_file = self.generate_voice(text, speaker_id, speed)
            if voice_file:
                voice_files.append(voice_file)
        
        self.logger.info(f"预生成语音完成: {len(voice_files)}/{count} 成功")
        return voice_files
    
    def clear_voice_cache(self, days_old: int = 30) -> int:
        """清理语音缓存"""
        return self.voice_downloader.clear_cache(days_old)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return self.voice_downloader.get_cache_stats()
    
    @handle_exceptions("VoiceManager")
    def get_play_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取播放历史"""
        try:
            history = self.db.execute_query(
                """SELECT voice_type, content, file_path, played_at 
                   FROM play_history 
                   ORDER BY played_at DESC 
                   LIMIT ?""",
                (limit,)
            )
            return [dict(record) for record in history]
        except Exception as e:
            self.logger.error(f"获取播放历史失败: {e}")
            return []
    
    def set_api_config(self, base_url: str, api_key: str = ""):
        """设置语音API配置"""
        self.voice_downloader.set_api_config(base_url, api_key)
    
    def test_api_connection(self) -> tuple[bool, str]:
        """测试API连接"""
        return self.voice_downloader.test_api_connection()
    
    def get_voice_manager_stats(self) -> Dict[str, Any]:
        """获取语音管理器统计信息"""
        current_speaker = self.get_current_speaker()
        cache_stats = self.get_cache_stats()
        
        return {
            'current_speaker': current_speaker,
            'total_speakers': len(self.get_all_speakers()),
            'cache_stats': cache_stats,
            'generation_queue_size': len(self._generation_queue),
            'is_generating': self._is_generating
        }
