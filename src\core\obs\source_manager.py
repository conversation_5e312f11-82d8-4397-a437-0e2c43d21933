"""
AI直播系统 v2 - 源管理器
OBS源的管理和控制
"""

from typing import Dict, Any, List, Optional, Callable
from datetime import datetime

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class SourceManager:
    """源管理器"""

    def __init__(self, obs_controller):
        self.logger = create_logger("source_manager")
        self.obs_controller = obs_controller

        # 源配置
        self.source_configs = {}

        # 回调函数
        self.on_source_visibility_changed: Optional[Callable] = None

    @handle_exceptions("SourceManager")
    def get_scene_sources(self, scene_name: str) -> List[Dict[str, Any]]:
        """获取场景中的源列表"""
        try:
            if not self.obs_controller.is_connected:
                return []

            result = self.obs_controller.send_request_sync("GetSceneItemList", {
                "sceneName": scene_name
            })

            if result and "sceneItems" in result:
                return result["sceneItems"]
            else:
                return []

        except Exception as e:
            self.logger.error(f"获取场景源列表失败: {e}")
            return []

    @handle_exceptions("SourceManager")
    def set_source_visibility(self, scene_name: str, source_name: str, visible: bool) -> bool:
        """设置源的可见性"""
        try:
            if not self.obs_controller.is_connected:
                return False

            # 先获取源的ID
            sources = self.get_scene_sources(scene_name)
            source_id = None

            for source in sources:
                if source.get("sourceName") == source_name:
                    source_id = source.get("sceneItemId")
                    break

            if source_id is None:
                self.logger.error(f"在场景 {scene_name} 中找不到源 {source_name}")
                return False

            result = self.obs_controller.send_request_sync("SetSceneItemEnabled", {
                "sceneName": scene_name,
                "sceneItemId": source_id,
                "sceneItemEnabled": visible
            })

            if result is not None:
                self.logger.info(f"设置源可见性: {source_name} = {'显示' if visible else '隐藏'}")

                if self.on_source_visibility_changed:
                    self.on_source_visibility_changed(scene_name, source_name, visible)

                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"设置源可见性失败: {e}")
            return False

    @handle_exceptions("SourceManager")
    def set_source_transform(self, scene_name: str, source_name: str,
                           transform: Dict[str, Any]) -> bool:
        """设置源的变换属性"""
        try:
            if not self.obs_controller.is_connected:
                return False

            # 获取源ID
            sources = self.get_scene_sources(scene_name)
            source_id = None

            for source in sources:
                if source.get("sourceName") == source_name:
                    source_id = source.get("sceneItemId")
                    break

            if source_id is None:
                self.logger.error(f"在场景 {scene_name} 中找不到源 {source_name}")
                return False

            result = self.obs_controller.send_request_sync("SetSceneItemTransform", {
                "sceneName": scene_name,
                "sceneItemId": source_id,
                "sceneItemTransform": transform
            })

            if result is not None:
                self.logger.info(f"设置源变换: {source_name}")
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"设置源变换失败: {e}")
            return False

    @handle_exceptions("SourceManager")
    def get_source_transform(self, scene_name: str, source_name: str) -> Optional[Dict[str, Any]]:
        """获取源的变换属性"""
        try:
            if not self.obs_controller.is_connected:
                return None

            # 获取源ID
            sources = self.get_scene_sources(scene_name)
            source_id = None

            for source in sources:
                if source.get("sourceName") == source_name:
                    source_id = source.get("sceneItemId")
                    break

            if source_id is None:
                return None

            result = self.obs_controller.send_request_sync("GetSceneItemTransform", {
                "sceneName": scene_name,
                "sceneItemId": source_id
            })

            if result and "sceneItemTransform" in result:
                return result["sceneItemTransform"]
            else:
                return None

        except Exception as e:
            self.logger.error(f"获取源变换失败: {e}")
            return None

    @handle_exceptions("SourceManager")
    def set_text_source_content(self, source_name: str, text: str) -> bool:
        """设置文本源的内容"""
        try:
            if not self.obs_controller.is_connected:
                return False

            result = self.obs_controller.send_request_sync("SetInputSettings", {
                "inputName": source_name,
                "inputSettings": {
                    "text": text
                }
            })

            if result is not None:
                self.logger.info(f"设置文本源内容: {source_name}")
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"设置文本源内容失败: {e}")
            return False

    @handle_exceptions("SourceManager")
    def set_image_source_file(self, source_name: str, file_path: str) -> bool:
        """设置图像源的文件"""
        try:
            if not self.obs_controller.is_connected:
                return False

            result = self.obs_controller.send_request_sync("SetInputSettings", {
                "inputName": source_name,
                "inputSettings": {
                    "file": file_path
                }
            })

            if result is not None:
                self.logger.info(f"设置图像源文件: {source_name} -> {file_path}")
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"设置图像源文件失败: {e}")
            return False

    @handle_exceptions("SourceManager")
    def set_media_source_file(self, source_name: str, file_path: str) -> bool:
        """设置媒体源的文件"""
        try:
            if not self.obs_controller.is_connected:
                return False

            result = self.obs_controller.send_request_sync("SetInputSettings", {
                "inputName": source_name,
                "inputSettings": {
                    "local_file": file_path
                }
            })

            if result is not None:
                self.logger.info(f"设置媒体源文件: {source_name} -> {file_path}")
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"设置媒体源文件失败: {e}")
            return False

    @handle_exceptions("SourceManager")
    def get_source_settings(self, source_name: str) -> Optional[Dict[str, Any]]:
        """获取源的设置"""
        try:
            if not self.obs_controller.is_connected:
                return None

            result = self.obs_controller.send_request_sync("GetInputSettings", {
                "inputName": source_name
            })

            if result and "inputSettings" in result:
                return result["inputSettings"]
            else:
                return None

        except Exception as e:
            self.logger.error(f"获取源设置失败: {e}")
            return None

    @handle_exceptions("SourceManager")
    def create_source_preset(self, name: str, sources: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        创建源预设

        Args:
            name: 预设名称
            sources: 源配置列表
                - source_name: 源名称
                - visible: 是否可见
                - transform: 变换属性
                - settings: 源设置

        Returns:
            预设配置
        """
        preset = {
            'name': name,
            'sources': sources,
            'created_at': datetime.now()
        }

        self.logger.info(f"创建源预设: {name}")
        return preset

    @handle_exceptions("SourceManager")
    def apply_source_preset(self, scene_name: str, preset: Dict[str, Any]) -> bool:
        """应用源预设"""
        try:
            success_count = 0
            total_count = len(preset['sources'])

            for source_config in preset['sources']:
                source_name = source_config['source_name']

                # 设置可见性
                if 'visible' in source_config:
                    if self.set_source_visibility(scene_name, source_name, source_config['visible']):
                        success_count += 1

                # 设置变换
                if 'transform' in source_config:
                    self.set_source_transform(scene_name, source_name, source_config['transform'])

                # 设置源设置
                if 'settings' in source_config:
                    self.obs_controller.send_request_sync("SetInputSettings", {
                        "inputName": source_name,
                        "inputSettings": source_config['settings']
                    })

            self.logger.info(f"应用源预设: {preset['name']} ({success_count}/{total_count})")
            return success_count == total_count

        except Exception as e:
            self.logger.error(f"应用源预设失败: {e}")
            return False

    @handle_exceptions("SourceManager")
    def get_all_sources(self) -> List[str]:
        """获取所有源的名称"""
        try:
            if not self.obs_controller.is_connected:
                return []

            result = self.obs_controller.send_request_sync("GetInputList")

            if result and "inputs" in result:
                return [input_item.get("inputName") for input_item in result["inputs"]]
            else:
                return []

        except Exception as e:
            self.logger.error(f"获取所有源失败: {e}")
            return []

    @handle_exceptions("SourceManager")
    def get_source_filters(self, source_name: str) -> List[Dict[str, Any]]:
        """获取源的滤镜列表"""
        try:
            if not self.obs_controller.is_connected:
                return []

            result = self.obs_controller.send_request_sync("GetSourceFilterList", {
                "sourceName": source_name
            })

            if result and "filters" in result:
                return result["filters"]
            else:
                return []

        except Exception as e:
            self.logger.error(f"获取源滤镜失败: {e}")
            return []

    @handle_exceptions("SourceManager")
    def set_filter_enabled(self, source_name: str, filter_name: str, enabled: bool) -> bool:
        """设置滤镜启用状态"""
        try:
            if not self.obs_controller.is_connected:
                return False

            result = self.obs_controller.send_request_sync("SetSourceFilterEnabled", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterEnabled": enabled
            })

            if result is not None:
                self.logger.info(f"设置滤镜状态: {source_name}.{filter_name} = {'启用' if enabled else '禁用'}")
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"设置滤镜状态失败: {e}")
            return False
