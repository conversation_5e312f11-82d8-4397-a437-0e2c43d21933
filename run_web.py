#!/usr/bin/env python3
"""
AI直播系统 v2 - Web界面启动程序
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from flask import Flask, render_template_string, request, jsonify, session
    from src.services.logging_service import setup_logging
    from src.data.database_manager import DatabaseManager
    from src.core.user import User<PERSON><PERSON><PERSON>, AuthManager, PaymentManager
    
    app = Flask(__name__)
    app.secret_key = 'your-secret-key-change-this'
    
    # 初始化管理器
    db = DatabaseManager("data/web_app.db")
    user_manager = UserManager(db)
    auth_manager = AuthManager(db)
    payment_manager = PaymentManager(db)
    
    # HTML模板
    HTML_TEMPLATE = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>AI直播系统 v2 - 用户管理</title>
        <style>
            body {
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
                border-radius: 10px;
                padding: 30px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
            }
            .header h1 {
                color: #333;
                margin: 0;
                font-size: 2.5em;
            }
            .header p {
                color: #666;
                margin: 10px 0;
                font-size: 1.2em;
            }
            .tabs {
                display: flex;
                margin-bottom: 20px;
                border-bottom: 2px solid #eee;
            }
            .tab {
                flex: 1;
                padding: 15px;
                text-align: center;
                cursor: pointer;
                background: #f8f9fa;
                border: none;
                font-size: 16px;
                transition: all 0.3s;
            }
            .tab.active {
                background: #667eea;
                color: white;
            }
            .tab-content {
                display: none;
                padding: 20px 0;
            }
            .tab-content.active {
                display: block;
            }
            .form-group {
                margin-bottom: 20px;
            }
            .form-group label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
                color: #333;
            }
            .form-group input, .form-group select {
                width: 100%;
                padding: 12px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 16px;
                box-sizing: border-box;
            }
            .form-group input:focus, .form-group select:focus {
                border-color: #667eea;
                outline: none;
            }
            .btn {
                background: #667eea;
                color: white;
                padding: 12px 30px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                transition: all 0.3s;
                width: 100%;
            }
            .btn:hover {
                background: #5a6fd8;
                transform: translateY(-2px);
            }
            .message {
                padding: 15px;
                margin: 20px 0;
                border-radius: 5px;
                text-align: center;
            }
            .message.success {
                background: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            .message.error {
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            .packages {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin: 20px 0;
            }
            .package {
                border: 2px solid #ddd;
                border-radius: 10px;
                padding: 20px;
                text-align: center;
                cursor: pointer;
                transition: all 0.3s;
            }
            .package:hover, .package.selected {
                border-color: #667eea;
                background: #f8f9ff;
            }
            .package h3 {
                color: #333;
                margin: 0 0 10px 0;
            }
            .package .price {
                font-size: 2em;
                color: #667eea;
                font-weight: bold;
            }
            .package .description {
                color: #666;
                margin: 10px 0;
            }
            .footer {
                text-align: center;
                margin-top: 30px;
                color: #666;
                font-size: 14px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎬 AI直播系统 v2</h1>
                <p>用户管理系统</p>
            </div>
            
            <div class="tabs">
                <button class="tab active" onclick="showTab('login')">用户登录</button>
                <button class="tab" onclick="showTab('register')">用户注册</button>
                <button class="tab" onclick="showTab('recharge')">充值中心</button>
            </div>
            
            <!-- 登录标签页 -->
            <div id="login" class="tab-content active">
                <form onsubmit="login(event)">
                    <div class="form-group">
                        <label>用户名或邮箱:</label>
                        <input type="text" id="login-username" required>
                    </div>
                    <div class="form-group">
                        <label>密码:</label>
                        <input type="password" id="login-password" required>
                    </div>
                    <button type="submit" class="btn">登录</button>
                </form>
            </div>
            
            <!-- 注册标签页 -->
            <div id="register" class="tab-content">
                <form onsubmit="register(event)">
                    <div class="form-group">
                        <label>用户名:</label>
                        <input type="text" id="register-username" required>
                    </div>
                    <div class="form-group">
                        <label>密码:</label>
                        <input type="password" id="register-password" required>
                    </div>
                    <div class="form-group">
                        <label>邮箱 (可选):</label>
                        <input type="email" id="register-email">
                    </div>
                    <div class="form-group">
                        <label>手机号 (可选):</label>
                        <input type="tel" id="register-phone">
                    </div>
                    <button type="submit" class="btn">注册</button>
                </form>
            </div>
            
            <!-- 充值标签页 -->
            <div id="recharge" class="tab-content">
                <div class="packages">
                    <div class="package" onclick="selectPackage('basic')">
                        <h3>基础套餐</h3>
                        <div class="price">¥30</div>
                        <div class="description">30天VIP会员</div>
                    </div>
                    <div class="package" onclick="selectPackage('premium')">
                        <h3>高级套餐</h3>
                        <div class="price">¥88</div>
                        <div class="description">90天高级会员</div>
                    </div>
                    <div class="package" onclick="selectPackage('yearly')">
                        <h3>年度套餐</h3>
                        <div class="price">¥298</div>
                        <div class="description">365天高级会员</div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>支付方式:</label>
                    <select id="payment-method">
                        <option value="alipay">支付宝</option>
                        <option value="wechat">微信支付</option>
                        <option value="bank">银行卡</option>
                    </select>
                </div>
                
                <button class="btn" onclick="createOrder()">立即充值</button>
            </div>
            
            <div id="message"></div>
            
            <div class="footer">
                <p>AI直播系统 v2.0.0 | 用户管理系统演示</p>
            </div>
        </div>
        
        <script>
            let selectedPackage = null;
            
            function showTab(tabName) {
                // 隐藏所有标签页
                document.querySelectorAll('.tab-content').forEach(tab => {
                    tab.classList.remove('active');
                });
                document.querySelectorAll('.tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                
                // 显示选中的标签页
                document.getElementById(tabName).classList.add('active');
                event.target.classList.add('active');
            }
            
            function showMessage(text, type = 'success') {
                const messageDiv = document.getElementById('message');
                messageDiv.innerHTML = `<div class="message ${type}">${text}</div>`;
                setTimeout(() => {
                    messageDiv.innerHTML = '';
                }, 5000);
            }
            
            async function login(event) {
                event.preventDefault();
                const username = document.getElementById('login-username').value;
                const password = document.getElementById('login-password').value;
                
                try {
                    const response = await fetch('/api/login', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({username, password})
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        showMessage(`登录成功！欢迎 ${result.user_info.username}`);
                        showTab('recharge');
                    } else {
                        showMessage(result.message, 'error');
                    }
                } catch (error) {
                    showMessage('登录失败：' + error.message, 'error');
                }
            }
            
            async function register(event) {
                event.preventDefault();
                const username = document.getElementById('register-username').value;
                const password = document.getElementById('register-password').value;
                const email = document.getElementById('register-email').value;
                const phone = document.getElementById('register-phone').value;
                
                try {
                    const response = await fetch('/api/register', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({username, password, email, phone})
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        showMessage(`注册成功！用户ID: ${result.user_id}`);
                        showTab('login');
                    } else {
                        showMessage(result.message, 'error');
                    }
                } catch (error) {
                    showMessage('注册失败：' + error.message, 'error');
                }
            }
            
            function selectPackage(packageId) {
                // 移除之前的选择
                document.querySelectorAll('.package').forEach(pkg => {
                    pkg.classList.remove('selected');
                });
                
                // 选择当前套餐
                event.target.classList.add('selected');
                selectedPackage = packageId;
            }
            
            async function createOrder() {
                if (!selectedPackage) {
                    showMessage('请选择充值套餐', 'error');
                    return;
                }
                
                const paymentMethod = document.getElementById('payment-method').value;
                
                try {
                    const response = await fetch('/api/create_order', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({
                            package_id: selectedPackage,
                            payment_method: paymentMethod
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        showMessage(`订单创建成功！订单号: ${result.order_no}`);
                        
                        // 模拟支付
                        setTimeout(async () => {
                            const payResponse = await fetch('/api/process_payment', {
                                method: 'POST',
                                headers: {'Content-Type': 'application/json'},
                                body: JSON.stringify({order_no: result.order_no})
                            });
                            
                            const payResult = await payResponse.json();
                            
                            if (payResult.success) {
                                showMessage('支付成功！充值已到账');
                            } else {
                                showMessage('支付失败：' + payResult.message, 'error');
                            }
                        }, 2000);
                    } else {
                        showMessage(result.message, 'error');
                    }
                } catch (error) {
                    showMessage('创建订单失败：' + error.message, 'error');
                }
            }
        </script>
    </body>
    </html>
    """
    
    @app.route('/')
    def index():
        """主页"""
        return render_template_string(HTML_TEMPLATE)
    
    @app.route('/api/register', methods=['POST'])
    def api_register():
        """用户注册API"""
        data = request.json
        result = user_manager.register_user(
            data['username'],
            data['password'],
            data.get('email'),
            data.get('phone')
        )
        return jsonify(result)
    
    @app.route('/api/login', methods=['POST'])
    def api_login():
        """用户登录API"""
        data = request.json
        result = user_manager.login_user(data['username'], data['password'])
        if result['success']:
            session['user_id'] = result['user_info']['id']
            session['username'] = result['user_info']['username']
        return jsonify(result)
    
    @app.route('/api/create_order', methods=['POST'])
    def api_create_order():
        """创建充值订单API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})
        
        data = request.json
        result = payment_manager.create_recharge_order(
            session['user_id'],
            data['package_id'],
            data['payment_method']
        )
        return jsonify(result)
    
    @app.route('/api/process_payment', methods=['POST'])
    def api_process_payment():
        """处理支付API"""
        data = request.json
        result = payment_manager.process_payment(data['order_no'])
        return jsonify(result)
    
    def main():
        """主函数"""
        setup_logging()
        print("🌐 启动Web界面...")
        print("📱 请在浏览器中访问: http://localhost:5000")
        print("⏹️  按 Ctrl+C 停止服务")
        
        app.run(host='0.0.0.0', port=5000, debug=True)
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print("❌ Flask未安装")
    print(f"错误信息: {e}")
    print("\n请安装Flask:")
    print("pip install flask")
    print("\n或者使用命令行版本:")
    print("python demo_user_system.py")
    sys.exit(1)
