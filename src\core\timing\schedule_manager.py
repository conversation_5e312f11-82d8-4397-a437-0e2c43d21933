"""
AI直播系统 v2 - 调度管理器
管理各种定时任务和调度
"""

import threading
import time
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


@dataclass
class ScheduleTask:
    """调度任务"""
    id: str
    name: str
    task_type: str  # 'interval', 'cron', 'once'
    action: str     # 'announce', 'script', 'scene_switch', 'custom'
    params: Dict[str, Any]
    enabled: bool = True
    next_run: Optional[datetime] = None
    last_run: Optional[datetime] = None
    run_count: int = 0

    # 间隔任务参数
    interval_seconds: Optional[int] = None

    # Cron任务参数
    cron_expression: Optional[str] = None

    # 一次性任务参数
    run_at: Optional[datetime] = None


class ScheduleManager:
    """调度管理器"""

    def __init__(self):
        self.logger = create_logger("schedule_manager")

        # 任务列表
        self.tasks: Dict[str, ScheduleTask] = {}

        # 运行状态
        self.is_running = False
        self.scheduler_thread = None
        self.stop_event = threading.Event()

        # 任务执行器
        self.task_executors = {
            'announce': self._execute_announce_task,
            'script': self._execute_script_task,
            'scene_switch': self._execute_scene_switch_task,
            'custom': self._execute_custom_task
        }

        # 回调函数
        self.on_task_executed: Optional[Callable] = None
        self.on_task_error: Optional[Callable] = None

        # 外部管理器引用
        self.time_announcer = None
        self.script_scheduler = None
        self.scene_manager = None

        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'executed_tasks': 0,
            'failed_tasks': 0,
            'last_execution_time': None,
            'start_time': None
        }

    @handle_exceptions("ScheduleManager")
    def start(self) -> bool:
        """启动调度器"""
        try:
            if self.is_running:
                self.logger.warning("调度器已启动")
                return True

            self.is_running = True
            self.stop_event.clear()
            self.stats['start_time'] = datetime.now()

            self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
            self.scheduler_thread.start()

            self.logger.info("调度器已启动")
            return True

        except Exception as e:
            self.logger.error(f"启动调度器失败: {e}")
            return False

    def stop(self):
        """停止调度器"""
        try:
            if not self.is_running:
                return

            self.is_running = False
            self.stop_event.set()

            if self.scheduler_thread and self.scheduler_thread.is_alive():
                self.scheduler_thread.join(timeout=3.0)

            self.logger.info("调度器已停止")

        except Exception as e:
            self.logger.error(f"停止调度器失败: {e}")

    def _scheduler_loop(self):
        """调度循环"""
        while not self.stop_event.is_set():
            try:
                current_time = datetime.now()

                # 检查所有任务
                for task in list(self.tasks.values()):
                    if not task.enabled:
                        continue

                    # 检查是否需要执行
                    if self._should_execute_task(task, current_time):
                        self._execute_task(task, current_time)

                # 等待下次检查（每10秒检查一次）
                self.stop_event.wait(10)

            except Exception as e:
                self.logger.error(f"调度循环异常: {e}")
                self.stop_event.wait(30)  # 出错后等待30秒

    def _should_execute_task(self, task: ScheduleTask, current_time: datetime) -> bool:
        """判断任务是否应该执行"""
        try:
            if task.task_type == 'interval':
                # 间隔任务
                if task.next_run is None:
                    # 首次运行
                    task.next_run = current_time
                    return True
                elif current_time >= task.next_run:
                    return True

            elif task.task_type == 'once':
                # 一次性任务
                if task.run_at and current_time >= task.run_at and task.run_count == 0:
                    return True

            elif task.task_type == 'cron':
                # Cron任务（简化实现）
                if task.next_run is None or current_time >= task.next_run:
                    return True

            return False

        except Exception as e:
            self.logger.error(f"判断任务执行时间失败: {e}")
            return False

    def _execute_task(self, task: ScheduleTask, current_time: datetime):
        """执行任务"""
        try:
            self.logger.info(f"执行任务: {task.name}")

            # 更新任务状态
            task.last_run = current_time
            task.run_count += 1

            # 计算下次运行时间
            self._calculate_next_run(task, current_time)

            # 执行任务
            executor = self.task_executors.get(task.action)
            if executor:
                success = executor(task)

                if success:
                    self.stats['executed_tasks'] += 1
                    self.stats['last_execution_time'] = current_time

                    if self.on_task_executed:
                        self.on_task_executed(task)
                else:
                    self.stats['failed_tasks'] += 1

                    if self.on_task_error:
                        self.on_task_error(task, "任务执行失败")
            else:
                self.logger.error(f"未知的任务动作: {task.action}")
                self.stats['failed_tasks'] += 1

        except Exception as e:
            self.logger.error(f"执行任务失败: {e}")
            self.stats['failed_tasks'] += 1

            if self.on_task_error:
                self.on_task_error(task, str(e))

    def _calculate_next_run(self, task: ScheduleTask, current_time: datetime):
        """计算下次运行时间"""
        try:
            if task.task_type == 'interval' and task.interval_seconds:
                task.next_run = current_time + timedelta(seconds=task.interval_seconds)

            elif task.task_type == 'once':
                # 一次性任务执行后禁用
                task.enabled = False
                task.next_run = None

            elif task.task_type == 'cron':
                # 简化的Cron实现，这里只支持每小时、每天等
                if task.cron_expression == '@hourly':
                    next_hour = current_time.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                    task.next_run = next_hour
                elif task.cron_expression == '@daily':
                    next_day = current_time.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
                    task.next_run = next_day
                else:
                    # 默认1小时后
                    task.next_run = current_time + timedelta(hours=1)

        except Exception as e:
            self.logger.error(f"计算下次运行时间失败: {e}")

    def _execute_announce_task(self, task: ScheduleTask) -> bool:
        """执行播报任务"""
        try:
            if not self.time_announcer:
                return False

            message = task.params.get('message')
            return self.time_announcer.announce_now(message)

        except Exception as e:
            self.logger.error(f"执行播报任务失败: {e}")
            return False

    def _execute_script_task(self, task: ScheduleTask) -> bool:
        """执行话术任务"""
        try:
            if not self.script_scheduler:
                return False

            script_id = task.params.get('script_id')
            if script_id:
                # 切换到指定话术
                return True  # 简化实现
            else:
                # 获取随机话术行
                return True  # 简化实现

        except Exception as e:
            self.logger.error(f"执行话术任务失败: {e}")
            return False

    def _execute_scene_switch_task(self, task: ScheduleTask) -> bool:
        """执行场景切换任务"""
        try:
            if not self.scene_manager:
                return False

            scene_name = task.params.get('scene_name')
            if scene_name:
                return self.scene_manager.switch_to_scene(scene_name, auto_switch=True)

            return False

        except Exception as e:
            self.logger.error(f"执行场景切换任务失败: {e}")
            return False

    def _execute_custom_task(self, task: ScheduleTask) -> bool:
        """执行自定义任务"""
        try:
            # 这里可以扩展自定义任务的执行逻辑
            self.logger.info(f"执行自定义任务: {task.name}")
            return True

        except Exception as e:
            self.logger.error(f"执行自定义任务失败: {e}")
            return False

    @handle_exceptions("ScheduleManager")
    def add_task(self, task: ScheduleTask) -> bool:
        """添加任务"""
        try:
            if task.id in self.tasks:
                self.logger.warning(f"任务ID已存在: {task.id}")
                return False

            # 验证任务参数
            if not self._validate_task(task):
                return False

            self.tasks[task.id] = task
            self.stats['total_tasks'] = len(self.tasks)

            self.logger.info(f"添加任务: {task.name} ({task.task_type})")
            return True

        except Exception as e:
            self.logger.error(f"添加任务失败: {e}")
            return False

    def _validate_task(self, task: ScheduleTask) -> bool:
        """验证任务参数"""
        try:
            if task.task_type == 'interval':
                if not task.interval_seconds or task.interval_seconds <= 0:
                    self.logger.error("间隔任务需要有效的间隔时间")
                    return False

            elif task.task_type == 'once':
                if not task.run_at:
                    self.logger.error("一次性任务需要指定运行时间")
                    return False

            elif task.task_type == 'cron':
                if not task.cron_expression:
                    self.logger.error("Cron任务需要指定表达式")
                    return False

            if task.action not in self.task_executors:
                self.logger.error(f"不支持的任务动作: {task.action}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"验证任务失败: {e}")
            return False

    @handle_exceptions("ScheduleManager")
    def remove_task(self, task_id: str) -> bool:
        """移除任务"""
        if task_id in self.tasks:
            del self.tasks[task_id]
            self.stats['total_tasks'] = len(self.tasks)
            self.logger.info(f"移除任务: {task_id}")
            return True
        else:
            self.logger.warning(f"任务不存在: {task_id}")
            return False

    @handle_exceptions("ScheduleManager")
    def get_all_tasks(self) -> List[ScheduleTask]:
        """获取所有任务"""
        return list(self.tasks.values())

    @handle_exceptions("ScheduleManager")
    def set_managers(self, time_announcer=None, script_scheduler=None, scene_manager=None):
        """设置外部管理器引用"""
        if time_announcer:
            self.time_announcer = time_announcer
        if script_scheduler:
            self.script_scheduler = script_scheduler
        if scene_manager:
            self.scene_manager = scene_manager

        self.logger.info("外部管理器引用已设置")

    @handle_exceptions("ScheduleManager")
    def create_interval_task(self, task_id: str, name: str, action: str,
                           interval_seconds: int, params: Dict[str, Any]) -> ScheduleTask:
        """创建间隔任务"""
        return ScheduleTask(
            id=task_id,
            name=name,
            task_type='interval',
            action=action,
            params=params,
            interval_seconds=interval_seconds
        )

    @handle_exceptions("ScheduleManager")
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()

        # 计算运行时间
        if stats['start_time']:
            runtime = datetime.now() - stats['start_time']
            stats['runtime_seconds'] = runtime.total_seconds()
            stats['runtime_formatted'] = str(runtime).split('.')[0]
        else:
            stats['runtime_seconds'] = 0
            stats['runtime_formatted'] = "00:00:00"

        stats['is_running'] = self.is_running
        stats['enabled_tasks'] = len([task for task in self.tasks.values() if task.enabled])
        stats['disabled_tasks'] = len([task for task in self.tasks.values() if not task.enabled])

        return stats
