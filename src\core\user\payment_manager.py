"""
AI直播系统 v2 - 支付管理器
用户充值和支付管理
"""

import uuid
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from decimal import Decimal

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class PaymentManager:
    """支付管理器"""

    def __init__(self, db_manager):
        self.logger = create_logger("payment_manager")
        self.db_manager = db_manager

        # 支付状态
        self.payment_status = {
            'PENDING': 0,    # 待支付
            'SUCCESS': 1,    # 支付成功
            'FAILED': 2,     # 支付失败
            'CANCELLED': 3,  # 已取消
            'REFUNDED': 4    # 已退款
        }

        # 支付方式
        self.payment_methods = {
            'ALIPAY': 'alipay',      # 支付宝
            'WECHAT': 'wechat',      # 微信支付
            'BANK': 'bank',          # 银行卡
            'BALANCE': 'balance'     # 余额支付
        }

        # 充值套餐
        self.recharge_packages = {
            'basic': {
                'name': '基础套餐',
                'amount': 30.0,
                'days': 30,
                'user_type': 1,  # VIP
                'description': '30天VIP会员'
            },
            'premium': {
                'name': '高级套餐',
                'amount': 88.0,
                'days': 90,
                'user_type': 2,  # PREMIUM
                'description': '90天高级会员'
            },
            'yearly': {
                'name': '年度套餐',
                'amount': 298.0,
                'days': 365,
                'user_type': 2,  # PREMIUM
                'description': '365天高级会员'
            }
        }

    @handle_exceptions("PaymentManager")
    def create_recharge_order(self, user_id: int, package_id: str,
                             payment_method: str) -> Dict[str, Any]:
        """
        创建充值订单

        Args:
            user_id: 用户ID
            package_id: 套餐ID
            payment_method: 支付方式

        Returns:
            订单信息
        """
        try:
            # 验证套餐
            if package_id not in self.recharge_packages:
                return {
                    'success': False,
                    'message': '无效的充值套餐'
                }

            package = self.recharge_packages[package_id]

            # 验证支付方式
            if payment_method not in self.payment_methods.values():
                return {
                    'success': False,
                    'message': '不支持的支付方式'
                }

            # 生成订单号
            order_no = f"RC{datetime.now().strftime('%Y%m%d%H%M%S')}{user_id:06d}"

            # 创建订单
            order_id = self.db_manager.execute_insert(
                """INSERT INTO payment_orders
                   (order_no, user_id, package_id, amount, payment_method,
                    status, package_info, created_at, updated_at)
                   VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)""",
                (order_no, user_id, package_id, package['amount'], payment_method,
                 self.payment_status['PENDING'], json.dumps(package, ensure_ascii=False))
            )

            if order_id:
                self.logger.info(f"创建充值订单成功: {order_no}")

                # 根据支付方式生成支付信息
                payment_info = self._generate_payment_info(order_no, package['amount'], payment_method)

                return {
                    'success': True,
                    'order_id': order_id,
                    'order_no': order_no,
                    'amount': package['amount'],
                    'package_info': package,
                    'payment_info': payment_info
                }
            else:
                return {
                    'success': False,
                    'message': '创建订单失败'
                }

        except Exception as e:
            self.logger.error(f"创建充值订单失败: {e}")
            return {
                'success': False,
                'message': f'创建订单失败: {str(e)}'
            }

    @handle_exceptions("PaymentManager")
    def process_payment(self, order_no: str, payment_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理支付

        Args:
            order_no: 订单号
            payment_data: 支付数据

        Returns:
            处理结果
        """
        try:
            # 获取订单信息
            orders = self.db_manager.execute_query(
                "SELECT * FROM payment_orders WHERE order_no = ?",
                (order_no,)
            )

            if not orders:
                return {
                    'success': False,
                    'message': '订单不存在'
                }

            order = dict(orders[0])

            # 检查订单状态
            if order['status'] != self.payment_status['PENDING']:
                return {
                    'success': False,
                    'message': '订单状态异常'
                }

            # 模拟支付处理（实际项目中需要对接真实支付接口）
            payment_success = self._simulate_payment(order, payment_data)

            if payment_success:
                # 更新订单状态
                self.db_manager.execute_update(
                    """UPDATE payment_orders
                       SET status = ?, paid_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                       WHERE order_no = ?""",
                    (self.payment_status['SUCCESS'], order_no)
                )

                # 处理充值
                recharge_result = self._process_recharge(order)

                if recharge_result['success']:
                    self.logger.info(f"支付处理成功: {order_no}")
                    return {
                        'success': True,
                        'message': '支付成功',
                        'order_info': order,
                        'recharge_info': recharge_result
                    }
                else:
                    # 充值失败，回滚订单状态
                    self.db_manager.execute_update(
                        "UPDATE payment_orders SET status = ? WHERE order_no = ?",
                        (self.payment_status['FAILED'], order_no)
                    )
                    return recharge_result
            else:
                # 支付失败
                self.db_manager.execute_update(
                    "UPDATE payment_orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE order_no = ?",
                    (self.payment_status['FAILED'], order_no)
                )

                return {
                    'success': False,
                    'message': '支付失败'
                }

        except Exception as e:
            self.logger.error(f"处理支付失败: {e}")
            return {
                'success': False,
                'message': f'支付处理失败: {str(e)}'
            }

    def _generate_payment_info(self, order_no: str, amount: float, payment_method: str) -> Dict[str, Any]:
        """生成支付信息"""
        payment_info = {
            'order_no': order_no,
            'amount': amount,
            'payment_method': payment_method
        }

        if payment_method == self.payment_methods['ALIPAY']:
            # 支付宝支付信息
            payment_info.update({
                'qr_code': f"alipay://pay?order_no={order_no}&amount={amount}",
                'pay_url': f"https://pay.alipay.com/order/{order_no}",
                'expire_time': (datetime.now() + timedelta(minutes=15)).isoformat()
            })
        elif payment_method == self.payment_methods['WECHAT']:
            # 微信支付信息
            payment_info.update({
                'qr_code': f"weixin://wxpay/bizpayurl?order_no={order_no}&amount={amount}",
                'pay_url': f"https://pay.weixin.qq.com/order/{order_no}",
                'expire_time': (datetime.now() + timedelta(minutes=15)).isoformat()
            })
        elif payment_method == self.payment_methods['BANK']:
            # 银行卡支付信息
            payment_info.update({
                'bank_info': {
                    'account_name': 'AI直播系统',
                    'account_number': '6222021234567890123',
                    'bank_name': '中国工商银行',
                    'remark': f'充值订单:{order_no}'
                }
            })

        return payment_info

    def _simulate_payment(self, order: Dict[str, Any], payment_data: Dict[str, Any] = None) -> bool:
        """模拟支付处理"""
        # 在实际项目中，这里需要对接真实的支付接口
        # 现在只是模拟支付成功
        import random
        return random.random() > 0.05  # 95%成功率

    def _process_recharge(self, order: Dict[str, Any]) -> Dict[str, Any]:
        """处理充值"""
        try:
            user_id = order['user_id']
            package_info = json.loads(order['package_info'])

            # 获取用户当前信息
            users = self.db_manager.execute_query(
                "SELECT * FROM users WHERE id = ?",
                (user_id,)
            )

            if not users:
                return {
                    'success': False,
                    'message': '用户不存在'
                }

            user = dict(users[0])

            # 计算新的到期时间
            current_expire = user.get('expire_at')
            if current_expire:
                try:
                    expire_date = datetime.fromisoformat(current_expire)
                    if expire_date > datetime.now():
                        # 当前还未过期，在现有基础上延长
                        new_expire = expire_date + timedelta(days=package_info['days'])
                    else:
                        # 已过期，从现在开始计算
                        new_expire = datetime.now() + timedelta(days=package_info['days'])
                except:
                    new_expire = datetime.now() + timedelta(days=package_info['days'])
            else:
                new_expire = datetime.now() + timedelta(days=package_info['days'])

            # 更新用户信息
            self.db_manager.execute_update(
                """UPDATE users
                   SET user_type = ?, status = ?, expire_at = ?, updated_at = CURRENT_TIMESTAMP
                   WHERE id = ?""",
                (package_info['user_type'], 1, new_expire.isoformat(), user_id)  # status=1表示激活
            )

            # 记录充值历史
            self.db_manager.execute_insert(
                """INSERT INTO recharge_history
                   (user_id, order_no, package_id, amount, days, user_type_before, user_type_after,
                    expire_before, expire_after, created_at)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)""",
                (user_id, order['order_no'], order['package_id'], order['amount'],
                 package_info['days'], user['user_type'], package_info['user_type'],
                 current_expire, new_expire.isoformat())
            )

            self.logger.info(f"用户充值成功: 用户ID {user_id}, 套餐 {package_info['name']}")

            return {
                'success': True,
                'message': '充值成功',
                'new_user_type': package_info['user_type'],
                'expire_at': new_expire.isoformat(),
                'days_added': package_info['days']
            }

        except Exception as e:
            self.logger.error(f"处理充值失败: {e}")
            return {
                'success': False,
                'message': f'充值处理失败: {str(e)}'
            }

    @handle_exceptions("PaymentManager")
    def get_order_status(self, order_no: str) -> Dict[str, Any]:
        """获取订单状态"""
        try:
            orders = self.db_manager.execute_query(
                "SELECT * FROM payment_orders WHERE order_no = ?",
                (order_no,)
            )

            if orders:
                order = dict(orders[0])
                order['package_info'] = json.loads(order['package_info'])
                return {
                    'success': True,
                    'order': order
                }
            else:
                return {
                    'success': False,
                    'message': '订单不存在'
                }

        except Exception as e:
            self.logger.error(f"获取订单状态失败: {e}")
            return {
                'success': False,
                'message': f'获取订单状态失败: {str(e)}'
            }

    @handle_exceptions("PaymentManager")
    def get_user_orders(self, user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """获取用户订单列表"""
        try:
            orders = self.db_manager.execute_query(
                """SELECT * FROM payment_orders
                   WHERE user_id = ?
                   ORDER BY created_at DESC
                   LIMIT ?""",
                (user_id, limit)
            )

            result = []
            for order in orders:
                order_dict = dict(order)
                order_dict['package_info'] = json.loads(order_dict['package_info'])
                result.append(order_dict)

            return result

        except Exception as e:
            self.logger.error(f"获取用户订单失败: {e}")
            return []

    @handle_exceptions("PaymentManager")
    def get_recharge_packages(self) -> Dict[str, Any]:
        """获取充值套餐列表"""
        return self.recharge_packages.copy()

    @handle_exceptions("PaymentManager")
    def get_payment_stats(self) -> Dict[str, Any]:
        """获取支付统计"""
        try:
            # 今日收入
            today_income = self.db_manager.execute_query(
                """SELECT COALESCE(SUM(amount), 0) as income
                   FROM payment_orders
                   WHERE status = ? AND DATE(paid_at) = DATE('now')""",
                (self.payment_status['SUCCESS'],)
            )[0]['income']

            # 总收入
            total_income = self.db_manager.execute_query(
                """SELECT COALESCE(SUM(amount), 0) as income
                   FROM payment_orders
                   WHERE status = ?""",
                (self.payment_status['SUCCESS'],)
            )[0]['income']

            # 订单统计
            order_stats = self.db_manager.execute_query(
                """SELECT status, COUNT(*) as count
                   FROM payment_orders
                   GROUP BY status"""
            )

            status_counts = {status: 0 for status in self.payment_status.values()}
            for stat in order_stats:
                status_name = [k for k, v in self.payment_status.items() if v == stat['status']][0]
                status_counts[status_name] = stat['count']

            return {
                'today_income': float(today_income),
                'total_income': float(total_income),
                'order_counts': status_counts
            }

        except Exception as e:
            self.logger.error(f"获取支付统计失败: {e}")
            return {
                'today_income': 0.0,
                'total_income': 0.0,
                'order_counts': {}
            }
