"""
AI Broadcaster v2 - 核心业务逻辑包
核心业务逻辑和功能模块
"""

# 认证模块
from .auth import UserManager, MachineCodeGenerator

# 语音模块
from .voice import VoiceManager, SpeakerManager, VoiceDownloader

# 播放控制模块（可选导入）
try:
    from .playback import AudioPlayer, PlaybackController, PlaylistManager
    PLAYBACK_AVAILABLE = True
except ImportError:
    AudioPlayer = None
    PlaybackController = None
    PlaylistManager = None
    PLAYBACK_AVAILABLE = False

__all__ = [
    # 认证模块
    'UserManager',
    'MachineCodeGenerator',

    # 语音模块
    'VoiceManager',
    'SpeakerManager',
    'VoiceDownloader'
]

# 如果播放模块可用，添加到导出列表
if PLAYBACK_AVAILABLE:
    __all__.extend(['AudioPlayer', 'PlaybackController', 'PlaylistManager'])
