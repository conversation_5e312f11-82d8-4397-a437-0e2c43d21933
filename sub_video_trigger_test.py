#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
副视频触发测试程序
专门测试副视频关键词触发功能
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def load_sub_video_config():
    """加载副视频配置"""
    config_file = Path("data/sub_videos.json")
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载副视频配置失败: {e}")
            return {}
    else:
        print("❌ 副视频配置文件不存在")
        return {}

def check_sub_video_trigger_simple(content, sub_videos_config):
    """简单的副视频触发检测"""
    if not sub_videos_config:
        return None
    
    for keyword, config in sub_videos_config.items():
        if keyword and keyword.lower() in content.lower():
            video_source = config.get('video_source', '')
            if video_source:
                return {
                    'keyword': keyword,
                    'video_source': video_source,
                    'scripts': config.get('scripts', [])
                }
    return None

def test_sub_video_trigger():
    """测试副视频触发功能"""
    print("🎬 副视频触发测试程序")
    print("=" * 60)
    
    # 加载副视频配置
    print("\n📋 步骤1：加载副视频配置...")
    sub_videos_config = load_sub_video_config()
    
    if sub_videos_config:
        print(f"✅ 成功加载 {len(sub_videos_config)} 个副视频配置")
        for keyword, config in sub_videos_config.items():
            video_source = config.get('video_source', '未知')
            scripts_count = len(config.get('scripts', []))
            print(f"  • {keyword} → {video_source} ({scripts_count}个话术)")
    else:
        print("❌ 没有副视频配置，无法进行测试")
        return
    
    # 测试弹幕列表
    print(f"\n🧪 步骤2：测试副视频触发...")
    test_messages = [
        # 应该触发副视频的弹幕
        "感谢老板的火箭，太给力了！",
        "哇，火箭来了！",
        "送个火箭给主播",
        "火箭火箭火箭",
        "谢谢大家的礼物支持",
        "这个礼物不错",
        "收到礼物了",
        "主播666，继续加油！",
        "666666666",
        "太666了",
        "666啊",
        
        # 不应该触发副视频的弹幕
        "普通弹幕，没有关键词",
        "主播加油",
        "继续播放",
        "好看好看",
        "支持支持",
        "来了来了",
        "哈哈哈哈",
        "不错不错",
    ]
    
    print(f"📝 测试弹幕数量: {len(test_messages)} 条")
    print(f"🎯 预期触发副视频: {len([msg for msg in test_messages if any(keyword in msg.lower() for keyword in sub_videos_config.keys())])} 条")
    
    # 执行测试
    triggered_count = 0
    not_triggered_count = 0
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n🎬 测试 {i:2d}: {message}")
        
        # 检测副视频触发
        result = check_sub_video_trigger_simple(message, sub_videos_config)
        
        if result:
            triggered_count += 1
            keyword = result['keyword']
            video_source = result['video_source']
            scripts = result['scripts']
            
            print(f"  ✅ 触发副视频")
            print(f"     关键词: {keyword}")
            print(f"     视频源: {video_source}")
            print(f"     话术数: {len(scripts)} 个")
            if scripts:
                print(f"     示例话术: {scripts[0]}")
        else:
            not_triggered_count += 1
            print(f"  ⚪ 无副视频触发")
    
    # 测试结果统计
    print(f"\n📊 步骤3：测试结果统计...")
    print(f"  总测试弹幕: {len(test_messages)} 条")
    print(f"  触发副视频: {triggered_count} 条")
    print(f"  未触发副视频: {not_triggered_count} 条")
    print(f"  触发率: {triggered_count/len(test_messages)*100:.1f}%")
    
    # 详细分析
    print(f"\n🔍 步骤4：详细分析...")
    
    # 按关键词分组统计
    keyword_stats = {}
    for keyword in sub_videos_config.keys():
        keyword_stats[keyword] = 0
    
    for message in test_messages:
        result = check_sub_video_trigger_simple(message, sub_videos_config)
        if result:
            keyword = result['keyword']
            keyword_stats[keyword] += 1
    
    print(f"  各关键词触发次数:")
    for keyword, count in keyword_stats.items():
        print(f"    • {keyword}: {count} 次")
    
    # 交互式测试
    print(f"\n🎮 步骤5：交互式测试...")
    print(f"您可以输入自定义弹幕来测试副视频触发功能")
    print(f"输入 'quit' 或 'exit' 退出测试")
    
    while True:
        try:
            user_input = input(f"\n请输入测试弹幕: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出', 'q']:
                break
            
            if not user_input:
                continue
            
            print(f"🎬 测试弹幕: {user_input}")
            result = check_sub_video_trigger_simple(user_input, sub_videos_config)
            
            if result:
                keyword = result['keyword']
                video_source = result['video_source']
                scripts = result['scripts']
                
                print(f"  ✅ 触发副视频!")
                print(f"     关键词: {keyword}")
                print(f"     视频源: {video_source}")
                print(f"     话术数: {len(scripts)} 个")
                if scripts:
                    import random
                    selected_script = random.choice(scripts)
                    print(f"     随机话术: {selected_script}")
            else:
                print(f"  ⚪ 无副视频触发")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 测试出错: {e}")
    
    print(f"\n🏁 副视频触发测试完成!")
    print(f"💡 提示: 如需修改副视频配置，请编辑 data/sub_videos.json 文件")

def test_with_main_program():
    """使用主程序的副视频检测方法进行测试"""
    print(f"\n🔧 使用主程序方法测试...")
    
    try:
        from run_gui_qt5 import MainWindow
        from PyQt5.QtWidgets import QApplication
        
        # 创建应用（不显示界面）
        app = QApplication([])
        
        # 创建主窗口实例
        user_info = {
            'username': 'test_user',
            'user_id': 1,
            'expire_time': '2025-12-31'
        }
        window = MainWindow(user_info)
        
        # 测试弹幕
        test_messages = [
            "感谢老板的火箭，太给力了！",
            "谢谢大家的礼物支持",
            "主播666，继续加油！",
            "普通弹幕，没有关键词"
        ]
        
        print(f"📝 使用主程序的 check_sub_video_trigger 方法测试:")
        
        for message in test_messages:
            if hasattr(window, 'check_sub_video_trigger'):
                result = window.check_sub_video_trigger(message)
                print(f"  弹幕: {message}")
                print(f"  结果: {result if result else '无'}")
            else:
                print(f"❌ 主程序中没有 check_sub_video_trigger 方法")
                break
        
        app.quit()
        
    except Exception as e:
        print(f"❌ 主程序测试失败: {e}")

if __name__ == "__main__":
    test_sub_video_trigger()
    
    # 可选：也测试主程序的方法
    print(f"\n" + "="*60)
    test_with_main_program()
