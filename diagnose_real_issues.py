"""
诊断实际问题：
1. 检查OBS连接状态
2. 检查视频源是否存在
3. 检查监控是否启动
4. 检查切换逻辑是否被调用
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def diagnose_obs_connection():
    """诊断OBS连接问题"""
    print("🔧 诊断OBS连接问题")
    print("=" * 40)
    
    try:
        # 检查OBS是否运行
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 4455))
        sock.close()
        
        if result != 0:
            print("❌ OBS WebSocket服务器未运行 (localhost:4455)")
            print("💡 解决方案:")
            print("  1. 启动OBS Studio")
            print("  2. 在OBS中启用WebSocket服务器")
            print("  3. 工具 -> WebSocket服务器设置")
            print("  4. 启用WebSocket服务器，端口4455")
            return False
        else:
            print("✅ OBS WebSocket服务器正在运行")
            return True
            
    except Exception as e:
        print(f"❌ 检查OBS连接异常: {e}")
        return False


def diagnose_video_sources():
    """诊断视频源问题"""
    print("\n🔧 诊断视频源问题")
    print("=" * 40)
    
    try:
        # 尝试连接OBS并获取源列表
        from src.core.obs_controller import OBSController
        
        obs = OBSController()
        obs.host = "localhost"
        obs.port = 4455
        obs.password = ""
        obs.websocket_url = "ws://localhost:4455"
        
        success = obs.connect()
        if not success:
            print("❌ 无法连接到OBS")
            return False
        
        print("✅ 成功连接到OBS")
        
        # 获取当前场景
        scene_result = obs.send_request_sync("GetCurrentProgramScene")
        if not scene_result:
            print("❌ 无法获取当前场景")
            return False
        
        current_scene = scene_result.get("currentProgramSceneName")
        print(f"📋 当前场景: {current_scene}")
        
        # 获取场景中的源列表
        sources_result = obs.send_request_sync("GetSceneItemList", {
            "sceneName": current_scene
        })
        
        if not sources_result or "sceneItems" not in sources_result:
            print("❌ 无法获取场景源列表")
            return False
        
        scene_items = sources_result["sceneItems"]
        print(f"📋 场景中的源数量: {len(scene_items)}")
        
        # 列出所有源
        media_sources = []
        for item in scene_items:
            source_name = item.get("sourceName", "")
            source_type = item.get("sourceType", "")
            is_enabled = item.get("sceneItemEnabled", False)
            
            print(f"  - {source_name} (类型: {source_type}, 可见: {is_enabled})")
            
            # 检查是否是媒体源
            if "media" in source_type.lower() or source_name.isdigit():
                media_sources.append(source_name)
        
        print(f"\n📋 检测到的媒体源: {media_sources}")
        
        # 检查常见的视频源名称
        common_names = ["2223", "2222", "视频A", "视频B", "主视频A", "主视频B"]
        found_sources = []
        
        for name in common_names:
            for item in scene_items:
                if item.get("sourceName") == name:
                    found_sources.append(name)
                    break
        
        print(f"📋 找到的常见视频源: {found_sources}")
        
        if len(found_sources) >= 2:
            print("✅ 找到足够的视频源进行双主视频切换")
            return True
        else:
            print("❌ 视频源不足，需要至少2个媒体源")
            print("💡 解决方案:")
            print("  1. 在OBS中添加媒体源")
            print("  2. 命名为数字（如2223, 2222）或描述性名称")
            print("  3. 确保媒体源包含视频文件")
            return False
        
    except Exception as e:
        print(f"❌ 诊断视频源异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def diagnose_monitoring_status():
    """诊断监控状态问题"""
    print("\n🔧 诊断监控状态问题")
    print("=" * 40)
    
    try:
        # 检查是否可以创建播放控制器
        from src.core.playback_controller import PlaybackController
        
        controller = PlaybackController()
        print("✅ 播放控制器创建成功")
        
        # 检查双主视频管理器
        dual_manager = controller.dual_video_manager
        if not dual_manager:
            print("❌ 双主视频管理器未初始化")
            return False
        
        print("✅ 双主视频管理器已初始化")
        
        # 检查监控状态
        status = dual_manager.get_manager_status()
        print(f"📋 管理器状态:")
        print(f"  - 视频源A: {status.get('video_source_a', '未设置')}")
        print(f"  - 视频源B: {status.get('video_source_b', '未设置')}")
        print(f"  - 当前激活: {status.get('current_active_source', '未设置')}")
        print(f"  - 下一个源: {status.get('next_source', '未设置')}")
        print(f"  - 监控状态: {status.get('monitoring', False)}")
        print(f"  - OBS连接: {status.get('obs_connected', False)}")
        print(f"  - 切换阈值: {status.get('switch_threshold_seconds', 0)}秒")
        print(f"  - 变速范围: {status.get('speed_range', (1.0, 1.0))}")
        
        # 检查是否设置了视频源
        if not status.get('video_source_a') or not status.get('video_source_b'):
            print("❌ 双主视频源未设置")
            print("💡 解决方案:")
            print("  1. 在GUI中选择视频源A和视频源B")
            print("  2. 点击'应用OBS设置'")
            print("  3. 确认启动自动监控")
            return False
        
        # 检查监控是否启动
        if not status.get('monitoring'):
            print("❌ 自动监控未启动")
            print("💡 解决方案:")
            print("  1. 点击'开始自动监控'按钮")
            print("  2. 或在应用OBS设置时选择立即启动监控")
            return False
        
        print("✅ 监控状态正常")
        return True
        
    except Exception as e:
        print(f"❌ 诊断监控状态异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def diagnose_switch_logic():
    """诊断切换逻辑问题"""
    print("\n🔧 诊断切换逻辑问题")
    print("=" * 40)
    
    try:
        # 检查切换方法是否存在
        from src.core.playback.dual_video_manager import DualVideoManager
        
        # 检查关键方法
        methods_to_check = [
            '_switch_to_next_source',
            'manual_switch',
            '_play_source',
            '_show_source',
            '_hide_source',
            '_stop_source'
        ]
        
        for method_name in methods_to_check:
            if hasattr(DualVideoManager, method_name):
                print(f"✅ 方法存在: {method_name}")
            else:
                print(f"❌ 方法缺失: {method_name}")
                return False
        
        # 检查修复标记
        import inspect
        
        # 检查_play_source方法是否包含修复
        play_source_code = inspect.getsource(DualVideoManager._play_source)
        if "修复黑屏问题" in play_source_code:
            print("✅ _play_source方法包含黑屏修复")
        else:
            print("❌ _play_source方法缺少黑屏修复")
        
        # 检查_switch_to_next_source方法是否包含修复
        switch_code = inspect.getsource(DualVideoManager._switch_to_next_source)
        if "修复黑屏问题：优化切换顺序" in switch_code:
            print("✅ _switch_to_next_source方法包含切换顺序修复")
        else:
            print("❌ _switch_to_next_source方法缺少切换顺序修复")
        
        # 检查监控循环是否包含自动切换逻辑
        check_code = inspect.getsource(DualVideoManager._check_active_source_status)
        if "🎬 检测到视频播放结束(ENDED)" in check_code:
            print("✅ 监控循环包含自动切换逻辑")
        else:
            print("❌ 监控循环缺少自动切换逻辑")
        
        print("✅ 切换逻辑检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 诊断切换逻辑异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def provide_solutions():
    """提供解决方案"""
    print("\n💡 问题解决方案")
    print("=" * 40)
    
    print("🔧 问题1: 切换视频黑屏")
    print("  原因: 切换顺序问题或OBS源设置问题")
    print("  解决方案:")
    print("    1. 确保OBS中的媒体源设置正确")
    print("    2. 检查媒体源是否包含有效的视频文件")
    print("    3. 尝试手动切换测试")
    print("    4. 查看控制台日志确认切换步骤")
    
    print("\n🔧 问题2: 播放完成后没有自动切换")
    print("  原因: 监控未启动或播放状态检测问题")
    print("  解决方案:")
    print("    1. 确保点击了'开始自动监控'")
    print("    2. 检查OBS连接状态")
    print("    3. 确认视频源A和B都已设置")
    print("    4. 查看控制台日志确认监控状态")
    print("    5. 检查视频文件是否正常播放")
    
    print("\n🚀 推荐调试步骤:")
    print("  1. 启动OBS并连接WebSocket")
    print("  2. 添加两个媒体源（命名为2223和2222）")
    print("  3. 在GUI中设置双主视频源")
    print("  4. 应用OBS设置并启动监控")
    print("  5. 观察控制台日志输出")
    print("  6. 尝试手动切换测试")
    print("  7. 等待视频播放完成观察自动切换")


def main():
    """主诊断函数"""
    print("🚀 实际问题诊断程序")
    print("=" * 50)
    
    # 诊断各个组件
    obs_ok = diagnose_obs_connection()
    sources_ok = diagnose_video_sources() if obs_ok else False
    monitoring_ok = diagnose_monitoring_status()
    logic_ok = diagnose_switch_logic()
    
    # 总结诊断结果
    print("\n📊 诊断结果总结:")
    print(f"  OBS连接: {'✅ 正常' if obs_ok else '❌ 异常'}")
    print(f"  视频源: {'✅ 正常' if sources_ok else '❌ 异常'}")
    print(f"  监控状态: {'✅ 正常' if monitoring_ok else '❌ 异常'}")
    print(f"  切换逻辑: {'✅ 正常' if logic_ok else '❌ 异常'}")
    
    # 提供解决方案
    provide_solutions()
    
    # 总体评估
    all_ok = obs_ok and sources_ok and monitoring_ok and logic_ok
    
    if all_ok:
        print(f"\n🎉 所有组件诊断正常！")
        print(f"如果仍有问题，请:")
        print(f"  1. 查看控制台详细日志")
        print(f"  2. 确认视频文件正常播放")
        print(f"  3. 测试手动切换功能")
    else:
        print(f"\n⚠️ 发现问题，请按照上述解决方案处理")
    
    return all_ok


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
