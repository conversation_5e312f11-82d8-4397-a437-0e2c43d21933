"""
AI Broadcaster v2 - 事件总线
应用程序内部事件通信系统
"""

import asyncio
from typing import Dict, List, Callable, Any, Optional
from collections import defaultdict

from ..services.logging_service import create_logger
from ..services.error_handler import handle_exceptions


class EventBus:
    """事件总线"""
    
    def __init__(self):
        self.logger = create_logger("event_bus")
        self._subscribers: Dict[str, List[Callable]] = defaultdict(list)
        self._event_history: List[Dict[str, Any]] = []
        self._max_history = 1000
    
    def subscribe(self, event_type: str, callback: Callable) -> None:
        """
        订阅事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        self._subscribers[event_type].append(callback)
        self.logger.debug(f"订阅事件: {event_type}")
    
    def unsubscribe(self, event_type: str, callback: Callable) -> bool:
        """
        取消订阅事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数
            
        Returns:
            是否取消成功
        """
        if event_type in self._subscribers:
            try:
                self._subscribers[event_type].remove(callback)
                self.logger.debug(f"取消订阅事件: {event_type}")
                return True
            except ValueError:
                self.logger.warning(f"回调函数不在订阅列表中: {event_type}")
                return False
        return False
    
    @handle_exceptions("EventBus")
    async def publish(self, event_type: str, data: Optional[Dict[str, Any]] = None) -> None:
        """
        发布事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        if data is None:
            data = {}
        
        # 记录事件历史
        event_record = {
            'type': event_type,
            'data': data,
            'timestamp': asyncio.get_event_loop().time()
        }
        self._event_history.append(event_record)
        
        # 限制历史记录数量
        if len(self._event_history) > self._max_history:
            self._event_history.pop(0)
        
        self.logger.debug(f"发布事件: {event_type}")
        
        # 通知所有订阅者
        if event_type in self._subscribers:
            for callback in self._subscribers[event_type]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    self.logger.error(f"事件回调执行失败 {event_type}: {e}")
    
    def get_subscribers(self, event_type: str) -> List[Callable]:
        """
        获取事件订阅者列表
        
        Args:
            event_type: 事件类型
            
        Returns:
            订阅者列表
        """
        return self._subscribers.get(event_type, []).copy()
    
    def get_event_types(self) -> List[str]:
        """
        获取所有事件类型
        
        Returns:
            事件类型列表
        """
        return list(self._subscribers.keys())
    
    def get_event_history(self, event_type: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取事件历史
        
        Args:
            event_type: 事件类型过滤器
            limit: 返回数量限制
            
        Returns:
            事件历史列表
        """
        if event_type:
            filtered_history = [
                event for event in self._event_history 
                if event['type'] == event_type
            ]
        else:
            filtered_history = self._event_history
        
        return filtered_history[-limit:]
    
    def clear_history(self):
        """清除事件历史"""
        self._event_history.clear()
        self.logger.info("事件历史已清除")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取事件总线统计信息
        
        Returns:
            统计信息字典
        """
        event_counts = defaultdict(int)
        for event in self._event_history:
            event_counts[event['type']] += 1
        
        return {
            'total_events': len(self._event_history),
            'event_types_count': len(self._subscribers),
            'subscribers_count': sum(len(subs) for subs in self._subscribers.values()),
            'event_counts': dict(event_counts),
            'max_history': self._max_history
        }


class EventEmitter:
    """事件发射器基类"""
    
    def __init__(self, event_bus: Optional[EventBus] = None):
        self.event_bus = event_bus or EventBus()
        self.logger = create_logger(f"event_emitter.{self.__class__.__name__}")
    
    async def emit(self, event_type: str, data: Optional[Dict[str, Any]] = None):
        """
        发射事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        await self.event_bus.publish(event_type, data)
    
    def on(self, event_type: str, callback: Callable):
        """
        监听事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        self.event_bus.subscribe(event_type, callback)
    
    def off(self, event_type: str, callback: Callable):
        """
        取消监听事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        self.event_bus.unsubscribe(event_type, callback)


class EventListener:
    """事件监听器装饰器"""
    
    def __init__(self, event_bus: EventBus):
        self.event_bus = event_bus
    
    def listen(self, event_type: str):
        """
        事件监听装饰器
        
        Args:
            event_type: 事件类型
        """
        def decorator(func):
            self.event_bus.subscribe(event_type, func)
            return func
        return decorator


# 全局事件总线实例
_global_event_bus = EventBus()


def get_global_event_bus() -> EventBus:
    """获取全局事件总线"""
    return _global_event_bus


def subscribe_global(event_type: str, callback: Callable):
    """订阅全局事件"""
    _global_event_bus.subscribe(event_type, callback)


async def publish_global(event_type: str, data: Optional[Dict[str, Any]] = None):
    """发布全局事件"""
    await _global_event_bus.publish(event_type, data)
