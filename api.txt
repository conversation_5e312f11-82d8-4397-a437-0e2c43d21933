# 🌐 AI主播系统API功能总结

## 📋 概述

本文档总结了AI主播系统中所有API请求功能，包括HTTP API、WebSocket连接、语音生成API等所有网络请求相关的功能。

## 🔐 用户认证API

### 1. 用户登录
- **接口地址**: `POST /user/login`
- **服务器**: `http://localhost:12456`
- **请求参数**:
  ```json
  {
    "username": "用户名",
    "password": "密码",
    "machine_code": "机器码",
    "ip": "客户端IP"
  }
  ```
- **响应格式**:
  ```json
  {
    "状态": "成功/失败",
    "信息": "登录结果信息",
    "token": "JWT令牌",
    "过期时间": "2025-01-20 10:30:00"
  }
  ```

### 2. 用户注册
- **接口地址**: `POST /user/register`
- **请求参数**:
  ```json
  {
    "username": "用户名",
    "password": "密码",
    "phone": "手机号",
    "machine_code": "机器码",
    "ip": "客户端IP"
  }
  ```
- **响应格式**:
  ```json
  {
    "状态": "成功/失败",
    "信息": "注册结果信息",
    "用户ID": "用户ID"
  }
  ```

### 3. 用户充值
- **接口地址**: `POST /user/recharge`
- **请求参数**:
  ```json
  {
    "username": "用户名",
    "password": "密码",
    "card_code": "充值卡密",
    "machine_code": "机器码",
    "ip": "客户端IP"
  }
  ```
- **响应格式**:
  ```json
  {
    "状态": "成功/失败",
    "信息": "充值结果信息",
    "天数": "增加天数",
    "到期时间": "新的到期时间"
  }
  ```

## 🎤 语音下载API

### 1. 语音下载
- **接口地址**: `GET http://ct.scjanelife.com/voice/bert-vits2`
- **请求参数**:
  - `id`: 语音模型ID (默认0)
  - `text`: 要转换的文本
  - `length`: 语速参数
- **响应**: 直接返回音频文件流
- **使用示例**:
  ```python
  url = f"http://ct.scjanelife.com/voice/bert-vits2?id={speaker_id}&text={encoded_text}&length={speed}"
  response = requests.get(url, timeout=10)
  ```

### 2. 获取主播列表
- **接口地址**: `GET http://ct.scjanelife.com/voice/speakers`
- **响应格式**:
  ```json
  [
    {
      "id": 0,
      "name": "主播名称",
      "description": "主播描述"
    }
  ]
  ```

## 📝 话术管理API

### 1. 获取话术列表
- **接口地址**: `GET /getscriptlist`
- **响应格式**:
  ```json
  {
    "ai话术": [
      {"name": "话术名称1"},
      {"name": "话术名称2"}
    ]
  }
  ```

### 2. 话术操作
- **接口地址**: `POST /`
- **请求参数**:
  ```json
  {
    "类型": "获取话术/上传话术/新建话术",
    "话术名称": "话术文件名",
    "话术内容": "话术内容（上传时）"
  }
  ```

## 💬 AI对话管理API

### 1. 获取对话列表
- **接口地址**: `GET /dialoguelist`
- **响应格式**:
  ```json
  {
    "ai对话": [
      {"name": "对话名称1"},
      {"name": "对话名称2"}
    ]
  }
  ```

### 2. 对话操作
- **接口地址**: `POST /`
- **请求参数**:
  ```json
  {
    "类型": "获取ai对话/上传ai对话/新建ai对话",
    "对话名称": "对话文件名",
    "对话内容": "对话内容（上传时）"
  }
  ```
###获取AI对话详细内容的API：
请求地址：http://localhost:12456/
请求方法：POST
请求参数：JSON格式，包含两个字段
类型: "获取ai对话"
对话名: 需要获取的对话名称
超时设置：10秒



## 🔄 更新检查API

### 1. 检查更新
- **接口地址**: `POST /api/check-update`
- **请求参数**:
  ```json
  {
    "version": "客户端版本",
    "machine_code": "机器码"
  }
  ```
- **响应格式**:
  ```json
  {
    "status": "success/error",
    "has_update": true/false,
    "latest_version": "最新版本号",
    "download_url": "下载地址",
    "update_info": "更新说明"
  }
  ```

## 🌐 WebSocket连接

### 1. 弹幕WebSocket
- **连接地址**: `ws://127.0.0.1:9999`
- **功能**: 接收弹幕消息
- **消息格式**:
  ```json
  {
    "type": "ChatMessage",
    "user": "用户名",
    "message": "弹幕内容",
    "timestamp": "时间戳"
  }
  ```


## 🎥 OBS WebSocket API

### 1. 连接配置
- **默认地址**: `ws://localhost:4455`
- **认证**: 密码认证
- **功能**:
  - 获取场景列表
  - 切换场景
  - 控制媒体源
  - 获取媒体状态

### 2. 主要操作
- **获取版本信息**: `get_version()`
- **获取输入源列表**: `get_input_list()`
- **获取当前场景**: `get_current_program_scene()`
- **场景项目控制**: `SetSceneItemEnabled`
- **媒体状态查询**: `get_media_input_status()`


## 🔍 详细API实现分析

### 机器码生成
```python
def get_machine_code():
    """获取机器码"""
    import platform
    import hashlib

    # 获取系统信息
    system_info = f"{platform.machine()}-{platform.processor()}"

    # 生成MD5哈希
    machine_code = hashlib.md5(system_info.encode()).hexdigest()
    return machine_code
```

### 配置文件管理
```python
# 配置文件优先级
1. _internal/config.json  # 最高优先级
2. config.json           # 次优先级
3. 默认配置              # 最低优先级

# 配置结构
{
    "client_version": "1.0.0",
    "username": "用户名",
    "password": "密码",
    "token": "JWT令牌",
    "token_expiry": "过期时间",
    "api_base_url": "http://localhost:12456"
}
```

### 语音文件处理
```python
# 语音文件保存路径
voice_dir = "voices/"
filename = f"{text_hash}_{speaker_id}_{speed}.wav"

# 文件格式支持
- WAV: 默认格式，高质量
```

### WebSocket消息类型
```python
# 弹幕消息类型
{
    "type": "danmaku",
    "user": "用户名",
    "message": "弹幕内容",
    "color": "#FFFFFF",
    "timestamp": 1642678800
}


### 机器码验证
- 每次登录验证机器码
- 机器码变更需要重新认证

## 📊 API监控和日志

### 请求日志格式
```
[2025-01-20 10:30:00] INFO - API请求: POST /user/login
[2025-01-20 10:30:00] INFO - 请求参数: {"username": "test", "machine_code": "xxx"}
[2025-01-20 10:30:01] INFO - 响应状态: 200, 耗时: 200ms
```