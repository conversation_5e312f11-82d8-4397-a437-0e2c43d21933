"""
AI Broadcaster v2 - 系统设置面板
系统配置和用户设置管理
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QSpinBox, QDoubleSpinBox, QCheckBox, QComboBox,
    QGroupBox, QMessageBox, QTabWidget, QSlider, QTextEdit,
    QFileDialog, QProgressBar
)
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot, QThread
from PyQt5.QtGui import QFont

from ...services.logging_service import create_logger


class SystemPanel(QWidget):
    """系统设置面板"""

    def __init__(self, db_manager, user_info):
        super().__init__()
        self.logger = create_logger("system_panel")
        self.db_manager = db_manager
        self.user_info = user_info

        self.init_ui()
        self.setup_connections()
        self.load_settings()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 创建选项卡
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 5px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                padding: 10px 15px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                min-width: 80px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #007bff;
            }
            QTabBar::tab:hover {
                background-color: #e9ecef;
            }
        """)

        # 基本设置选项卡
        self.create_basic_settings_tab()

        # 语音设置选项卡
        self.create_voice_settings_tab()

        # 播放设置选项卡
        self.create_playback_settings_tab()

        # 高级设置选项卡
        self.create_advanced_settings_tab()

        layout.addWidget(self.tab_widget)

        # 底部按钮
        self.create_bottom_buttons(layout)

    def create_basic_settings_tab(self):
        """创建基本设置选项卡"""
        basic_widget = QWidget()
        basic_layout = QVBoxLayout(basic_widget)
        basic_layout.setContentsMargins(20, 20, 20, 20)
        basic_layout.setSpacing(15)

        # 用户信息组
        user_group = QGroupBox("用户信息")
        user_group.setStyleSheet(self.get_group_style())
        user_layout = QVBoxLayout(user_group)

        # 用户名（只读）
        username_layout = QHBoxLayout()
        username_layout.addWidget(QLabel("用户名:"))
        self.username_label = QLabel(self.user_info['username'] if self.user_info else "未登录")
        self.username_label.setStyleSheet("font-weight: bold; color: #007bff;")
        username_layout.addWidget(self.username_label)
        username_layout.addStretch()
        user_layout.addLayout(username_layout)

        # 手机号
        phone_layout = QHBoxLayout()
        phone_layout.addWidget(QLabel("手机号:"))
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("请输入手机号")
        self.phone_input.setStyleSheet(self.get_input_style())
        phone_layout.addWidget(self.phone_input)
        user_layout.addLayout(phone_layout)

        # 修改密码按钮
        change_password_layout = QHBoxLayout()
        self.change_password_button = QPushButton("修改密码")
        self.change_password_button.setStyleSheet(self.get_button_style("#ffc107", "#212529"))
        change_password_layout.addWidget(self.change_password_button)
        change_password_layout.addStretch()
        user_layout.addLayout(change_password_layout)

        basic_layout.addWidget(user_group)

        # 应用设置组
        app_group = QGroupBox("应用设置")
        app_group.setStyleSheet(self.get_group_style())
        app_layout = QVBoxLayout(app_group)

        # 自动启动
        self.auto_start_checkbox = QCheckBox("开机自动启动")
        app_layout.addWidget(self.auto_start_checkbox)

        # 最小化到托盘
        self.minimize_to_tray_checkbox = QCheckBox("最小化到系统托盘")
        app_layout.addWidget(self.minimize_to_tray_checkbox)

        # 启动时自动播放
        self.auto_play_checkbox = QCheckBox("启动时自动开始播放")
        app_layout.addWidget(self.auto_play_checkbox)

        # 日志级别
        log_level_layout = QHBoxLayout()
        log_level_layout.addWidget(QLabel("日志级别:"))
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.log_level_combo.setCurrentText("INFO")
        self.log_level_combo.setStyleSheet(self.get_input_style())
        log_level_layout.addWidget(self.log_level_combo)
        log_level_layout.addStretch()
        app_layout.addLayout(log_level_layout)

        basic_layout.addWidget(app_group)

        # 添加弹性空间
        basic_layout.addStretch()

        self.tab_widget.addTab(basic_widget, "基本设置")

    def create_voice_settings_tab(self):
        """创建语音设置选项卡"""
        voice_widget = QWidget()
        voice_layout = QVBoxLayout(voice_widget)
        voice_layout.setContentsMargins(20, 20, 20, 20)
        voice_layout.setSpacing(15)

        # 语音引擎组
        engine_group = QGroupBox("语音引擎")
        engine_group.setStyleSheet(self.get_group_style())
        engine_layout = QVBoxLayout(engine_group)

        # 引擎选择
        engine_select_layout = QHBoxLayout()
        engine_select_layout.addWidget(QLabel("语音引擎:"))
        self.voice_engine_combo = QComboBox()
        self.voice_engine_combo.addItems(["模拟引擎", "百度语音", "阿里云语音", "腾讯云语音"])
        self.voice_engine_combo.setStyleSheet(self.get_input_style())
        engine_select_layout.addWidget(self.voice_engine_combo)
        engine_select_layout.addStretch()
        engine_layout.addLayout(engine_select_layout)

        # API配置
        api_layout = QHBoxLayout()
        api_layout.addWidget(QLabel("API密钥:"))
        self.api_key_input = QLineEdit()
        self.api_key_input.setPlaceholderText("请输入API密钥")
        self.api_key_input.setEchoMode(QLineEdit.Password)
        self.api_key_input.setStyleSheet(self.get_input_style())
        api_layout.addWidget(self.api_key_input)

        self.test_api_button = QPushButton("测试")
        self.test_api_button.setStyleSheet(self.get_button_style("#17a2b8"))
        api_layout.addWidget(self.test_api_button)
        engine_layout.addLayout(api_layout)

        voice_layout.addWidget(engine_group)

        # 语音参数组
        params_group = QGroupBox("默认语音参数")
        params_group.setStyleSheet(self.get_group_style())
        params_layout = QVBoxLayout(params_group)

        # 默认语速
        speed_layout = QHBoxLayout()
        speed_layout.addWidget(QLabel("默认语速:"))
        self.default_speed_slider = QSlider(Qt.Horizontal)
        self.default_speed_slider.setRange(50, 200)
        self.default_speed_slider.setValue(100)
        self.default_speed_slider.setStyleSheet(self.get_slider_style())
        speed_layout.addWidget(self.default_speed_slider)
        self.default_speed_label = QLabel("1.0x")
        self.default_speed_label.setMinimumWidth(40)
        speed_layout.addWidget(self.default_speed_label)
        params_layout.addLayout(speed_layout)

        # 音量
        volume_layout = QHBoxLayout()
        volume_layout.addWidget(QLabel("默认音量:"))
        self.default_volume_slider = QSlider(Qt.Horizontal)
        self.default_volume_slider.setRange(0, 100)
        self.default_volume_slider.setValue(80)
        self.default_volume_slider.setStyleSheet(self.get_slider_style())
        volume_layout.addWidget(self.default_volume_slider)
        self.default_volume_label = QLabel("80%")
        self.default_volume_label.setMinimumWidth(40)
        volume_layout.addWidget(self.default_volume_label)
        params_layout.addLayout(volume_layout)

        voice_layout.addWidget(params_group)

        # 缓存设置组
        cache_group = QGroupBox("语音缓存")
        cache_group.setStyleSheet(self.get_group_style())
        cache_layout = QVBoxLayout(cache_group)

        # 启用缓存
        self.enable_cache_checkbox = QCheckBox("启用语音缓存")
        self.enable_cache_checkbox.setChecked(True)
        cache_layout.addWidget(self.enable_cache_checkbox)

        # 缓存大小限制
        cache_size_layout = QHBoxLayout()
        cache_size_layout.addWidget(QLabel("缓存大小限制:"))
        self.cache_size_spin = QSpinBox()
        self.cache_size_spin.setRange(100, 10000)
        self.cache_size_spin.setValue(1000)
        self.cache_size_spin.setSuffix(" MB")
        self.cache_size_spin.setStyleSheet(self.get_input_style())
        cache_size_layout.addWidget(self.cache_size_spin)
        cache_size_layout.addStretch()
        cache_layout.addLayout(cache_size_layout)

        # 清理缓存按钮
        clear_cache_layout = QHBoxLayout()
        self.clear_cache_button = QPushButton("清理缓存")
        self.clear_cache_button.setStyleSheet(self.get_button_style("#dc3545"))
        clear_cache_layout.addWidget(self.clear_cache_button)
        clear_cache_layout.addStretch()
        cache_layout.addLayout(clear_cache_layout)

        voice_layout.addWidget(cache_group)

        # 添加弹性空间
        voice_layout.addStretch()

        self.tab_widget.addTab(voice_widget, "语音设置")

    def create_playback_settings_tab(self):
        """创建播放设置选项卡"""
        playback_widget = QWidget()
        playback_layout = QVBoxLayout(playback_widget)
        playback_layout.setContentsMargins(20, 20, 20, 20)
        playback_layout.setSpacing(15)

        # 播放控制组
        control_group = QGroupBox("播放控制")
        control_group.setStyleSheet(self.get_group_style())
        control_layout = QVBoxLayout(control_group)

        # 播放间隔
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("播放间隔:"))
        self.min_interval_spin = QDoubleSpinBox()
        self.min_interval_spin.setRange(0.5, 60.0)
        self.min_interval_spin.setValue(2.0)
        self.min_interval_spin.setSuffix(" 秒")
        self.min_interval_spin.setStyleSheet(self.get_input_style())
        interval_layout.addWidget(self.min_interval_spin)

        interval_layout.addWidget(QLabel("到"))

        self.max_interval_spin = QDoubleSpinBox()
        self.max_interval_spin.setRange(0.5, 60.0)
        self.max_interval_spin.setValue(5.0)
        self.max_interval_spin.setSuffix(" 秒")
        self.max_interval_spin.setStyleSheet(self.get_input_style())
        interval_layout.addWidget(self.max_interval_spin)

        interval_layout.addStretch()
        control_layout.addLayout(interval_layout)

        # 随机播放
        self.random_play_checkbox = QCheckBox("随机播放话术")
        control_layout.addWidget(self.random_play_checkbox)

        # 重复播放
        self.repeat_play_checkbox = QCheckBox("重复播放列表")
        control_layout.addWidget(self.repeat_play_checkbox)

        playback_layout.addWidget(control_group)

        # 音频设备组
        device_group = QGroupBox("音频设备")
        device_group.setStyleSheet(self.get_group_style())
        device_layout = QVBoxLayout(device_group)

        # 输出设备选择
        device_select_layout = QHBoxLayout()
        device_select_layout.addWidget(QLabel("输出设备:"))
        self.output_device_combo = QComboBox()
        self.output_device_combo.addItem("默认设备", None)
        self.output_device_combo.setStyleSheet(self.get_input_style())
        device_select_layout.addWidget(self.output_device_combo)

        self.refresh_devices_button = QPushButton("刷新")
        self.refresh_devices_button.setStyleSheet(self.get_button_style("#6c757d"))
        device_select_layout.addWidget(self.refresh_devices_button)
        device_layout.addLayout(device_select_layout)

        playback_layout.addWidget(device_group)

        # 添加弹性空间
        playback_layout.addStretch()

        self.tab_widget.addTab(playback_widget, "播放设置")

    def create_advanced_settings_tab(self):
        """创建高级设置选项卡"""
        advanced_widget = QWidget()
        advanced_layout = QVBoxLayout(advanced_widget)
        advanced_layout.setContentsMargins(20, 20, 20, 20)
        advanced_layout.setSpacing(15)

        # 数据管理组
        data_group = QGroupBox("数据管理")
        data_group.setStyleSheet(self.get_group_style())
        data_layout = QVBoxLayout(data_group)

        # 数据库路径
        db_path_layout = QHBoxLayout()
        db_path_layout.addWidget(QLabel("数据库路径:"))
        self.db_path_input = QLineEdit()
        self.db_path_input.setReadOnly(True)
        self.db_path_input.setText("data/ai_broadcaster.db")
        self.db_path_input.setStyleSheet(self.get_input_style())
        db_path_layout.addWidget(self.db_path_input)
        data_layout.addLayout(db_path_layout)

        # 数据库操作按钮
        db_button_layout = QHBoxLayout()

        self.backup_db_button = QPushButton("备份数据库")
        self.backup_db_button.setStyleSheet(self.get_button_style("#007bff"))
        db_button_layout.addWidget(self.backup_db_button)

        self.restore_db_button = QPushButton("恢复数据库")
        self.restore_db_button.setStyleSheet(self.get_button_style("#28a745"))
        db_button_layout.addWidget(self.restore_db_button)

        self.reset_db_button = QPushButton("重置数据库")
        self.reset_db_button.setStyleSheet(self.get_button_style("#dc3545"))
        db_button_layout.addWidget(self.reset_db_button)

        db_button_layout.addStretch()
        data_layout.addLayout(db_button_layout)

        advanced_layout.addWidget(data_group)

        # 导入导出组
        import_export_group = QGroupBox("配置导入导出")
        import_export_group.setStyleSheet(self.get_group_style())
        ie_layout = QVBoxLayout(import_export_group)

        # 导入导出按钮
        ie_button_layout = QHBoxLayout()

        self.export_config_button = QPushButton("导出配置")
        self.export_config_button.setStyleSheet(self.get_button_style("#17a2b8"))
        ie_button_layout.addWidget(self.export_config_button)

        self.import_config_button = QPushButton("导入配置")
        self.import_config_button.setStyleSheet(self.get_button_style("#ffc107", "#212529"))
        ie_button_layout.addWidget(self.import_config_button)

        ie_button_layout.addStretch()
        ie_layout.addLayout(ie_button_layout)

        advanced_layout.addWidget(import_export_group)

        # 系统信息组
        system_group = QGroupBox("系统信息")
        system_group.setStyleSheet(self.get_group_style())
        system_layout = QVBoxLayout(system_group)

        # 系统信息文本
        self.system_info_text = QTextEdit()
        self.system_info_text.setReadOnly(True)
        self.system_info_text.setMaximumHeight(150)
        self.system_info_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
            }
        """)
        system_layout.addWidget(self.system_info_text)

        # 刷新系统信息按钮
        refresh_info_layout = QHBoxLayout()
        self.refresh_info_button = QPushButton("刷新信息")
        self.refresh_info_button.setStyleSheet(self.get_button_style("#6c757d"))
        refresh_info_layout.addWidget(self.refresh_info_button)
        refresh_info_layout.addStretch()
        system_layout.addLayout(refresh_info_layout)

        advanced_layout.addWidget(system_group)

        # 添加弹性空间
        advanced_layout.addStretch()

        self.tab_widget.addTab(advanced_widget, "高级设置")

    def create_bottom_buttons(self, layout):
        """创建底部按钮"""
        button_layout = QHBoxLayout()

        # 应用按钮
        self.apply_button = QPushButton("应用")
        self.apply_button.setStyleSheet(self.get_button_style("#007bff"))
        self.apply_button.setMinimumWidth(100)
        button_layout.addWidget(self.apply_button)

        # 重置按钮
        self.reset_button = QPushButton("重置")
        self.reset_button.setStyleSheet(self.get_button_style("#6c757d"))
        self.reset_button.setMinimumWidth(100)
        button_layout.addWidget(self.reset_button)

        # 弹性空间
        button_layout.addStretch()

        # 关于按钮
        self.about_button = QPushButton("关于")
        self.about_button.setStyleSheet(self.get_button_style("#17a2b8"))
        self.about_button.setMinimumWidth(100)
        button_layout.addWidget(self.about_button)

        layout.addLayout(button_layout)

    def get_group_style(self):
        """获取组框样式"""
        return """
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """

    def get_input_style(self):
        """获取输入框样式"""
        return """
            QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                padding: 5px;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                background-color: white;
            }
            QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                border-color: #007bff;
            }
        """

    def get_button_style(self, bg_color, text_color="white"):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: {text_color};
                border: none;
                border-radius: 3px;
                padding: 8px 15px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(bg_color)};
            }}
            QPushButton:disabled {{
                background-color: #6c757d;
                color: #adb5bd;
            }}
        """

    def get_slider_style(self):
        """获取滑块样式"""
        return """
            QSlider::groove:horizontal {
                border: 1px solid #dee2e6;
                height: 8px;
                background: #f8f9fa;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #007bff;
                border: 1px solid #007bff;
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
            QSlider::handle:horizontal:hover {
                background: #0056b3;
            }
        """

    def darken_color(self, color):
        """使颜色变暗"""
        color_map = {
            "#007bff": "#0056b3",
            "#28a745": "#218838",
            "#dc3545": "#c82333",
            "#ffc107": "#e0a800",
            "#17a2b8": "#138496",
            "#6c757d": "#5a6268"
        }
        return color_map.get(color, color)

    def setup_connections(self):
        """设置信号连接"""
        # 滑块变化事件
        self.default_speed_slider.valueChanged.connect(self.on_speed_changed)
        self.default_volume_slider.valueChanged.connect(self.on_volume_changed)

        # 按钮点击事件
        self.change_password_button.clicked.connect(self.change_password)
        self.test_api_button.clicked.connect(self.test_api)
        self.clear_cache_button.clicked.connect(self.clear_cache)
        self.refresh_devices_button.clicked.connect(self.refresh_devices)
        self.backup_db_button.clicked.connect(self.backup_database)
        self.restore_db_button.clicked.connect(self.restore_database)
        self.reset_db_button.clicked.connect(self.reset_database)
        self.export_config_button.clicked.connect(self.export_config)
        self.import_config_button.clicked.connect(self.import_config)
        self.refresh_info_button.clicked.connect(self.refresh_system_info)
        self.apply_button.clicked.connect(self.apply_settings)
        self.reset_button.clicked.connect(self.reset_settings)
        self.about_button.clicked.connect(self.show_about)

    def load_settings(self):
        """加载设置"""
        try:
            # 加载用户信息
            if self.user_info:
                self.phone_input.setText(self.user_info.get('phone', ''))

            # 刷新系统信息
            self.refresh_system_info()

            self.logger.info("设置加载完成")

        except Exception as e:
            self.logger.error(f"加载设置失败: {e}")

    @pyqtSlot(int)
    def on_speed_changed(self, value):
        """语速变化事件"""
        speed = value / 100.0
        self.default_speed_label.setText(f"{speed:.1f}x")

    @pyqtSlot(int)
    def on_volume_changed(self, value):
        """音量变化事件"""
        self.default_volume_label.setText(f"{value}%")

    @pyqtSlot()
    def change_password(self):
        """修改密码"""
        QMessageBox.information(self, "提示", "修改密码功能待实现")

    @pyqtSlot()
    def test_api(self):
        """测试API"""
        api_key = self.api_key_input.text().strip()
        if not api_key:
            QMessageBox.warning(self, "提示", "请输入API密钥")
            return

        QMessageBox.information(self, "提示", "API测试功能待实现")

    @pyqtSlot()
    def clear_cache(self):
        """清理缓存"""
        reply = QMessageBox.question(
            self, "确认", "确定要清理语音缓存吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            QMessageBox.information(self, "成功", "缓存清理完成")

    @pyqtSlot()
    def refresh_devices(self):
        """刷新音频设备"""
        try:
            # 清空现有设备
            self.output_device_combo.clear()
            self.output_device_combo.addItem("默认设备", None)

            # TODO: 获取实际音频设备列表
            # 这里添加一些模拟设备
            devices = [
                "扬声器 (Realtek High Definition Audio)",
                "耳机 (USB Audio Device)",
                "蓝牙音箱 (Bluetooth Audio)"
            ]

            for i, device in enumerate(devices):
                self.output_device_combo.addItem(device, i)

            QMessageBox.information(self, "成功", f"已刷新 {len(devices)} 个音频设备")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"刷新设备失败: {str(e)}")

    @pyqtSlot()
    def backup_database(self):
        """备份数据库"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "备份数据库", "ai_broadcaster_backup.db", "数据库文件 (*.db)"
            )

            if file_path:
                # TODO: 实现数据库备份
                QMessageBox.information(self, "成功", f"数据库备份完成\n路径: {file_path}")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"备份失败: {str(e)}")

    @pyqtSlot()
    def restore_database(self):
        """恢复数据库"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "恢复数据库", "", "数据库文件 (*.db)"
            )

            if file_path:
                reply = QMessageBox.question(
                    self, "确认", "恢复数据库将覆盖当前所有数据，确定继续吗？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )

                if reply == QMessageBox.StandardButton.Yes:
                    # TODO: 实现数据库恢复
                    QMessageBox.information(self, "成功", "数据库恢复完成")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"恢复失败: {str(e)}")

    @pyqtSlot()
    def reset_database(self):
        """重置数据库"""
        reply = QMessageBox.question(
            self, "危险操作", "重置数据库将删除所有数据，此操作不可恢复！\n确定要继续吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 二次确认
            reply2 = QMessageBox.question(
                self, "最终确认", "请再次确认：真的要重置数据库吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply2 == QMessageBox.StandardButton.Yes:
                try:
                    # TODO: 实现数据库重置
                    QMessageBox.information(self, "完成", "数据库重置完成")
                except Exception as e:
                    QMessageBox.warning(self, "错误", f"重置失败: {str(e)}")

    @pyqtSlot()
    def export_config(self):
        """导出配置"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出配置", "ai_broadcaster_config.json", "配置文件 (*.json)"
            )

            if file_path:
                # TODO: 实现配置导出
                QMessageBox.information(self, "成功", f"配置导出完成\n路径: {file_path}")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"导出失败: {str(e)}")

    @pyqtSlot()
    def import_config(self):
        """导入配置"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "导入配置", "", "配置文件 (*.json)"
            )

            if file_path:
                reply = QMessageBox.question(
                    self, "确认", "导入配置将覆盖当前设置，确定继续吗？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )

                if reply == QMessageBox.StandardButton.Yes:
                    # TODO: 实现配置导入
                    QMessageBox.information(self, "成功", "配置导入完成")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"导入失败: {str(e)}")

    @pyqtSlot()
    def refresh_system_info(self):
        """刷新系统信息"""
        try:
            import platform
            import psutil
            from datetime import datetime

            info_lines = [
                f"系统: {platform.system()} {platform.release()}",
                f"架构: {platform.machine()}",
                f"Python版本: {platform.python_version()}",
                f"CPU核心数: {psutil.cpu_count()}",
                f"内存: {psutil.virtual_memory().total // (1024**3)} GB",
                f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                "",
                "数据库信息:",
                f"  - 连接状态: {'已连接' if self.db_manager else '未连接'}",
                f"  - 数据库路径: {self.db_path_input.text()}",
            ]

            if self.db_manager:
                try:
                    db_info = self.db_manager.get_database_info()
                    info_lines.extend([
                        f"  - 表数量: {db_info.get('table_count', 0)}",
                        f"  - 数据库大小: {db_info.get('size_mb', 0):.2f} MB"
                    ])
                except:
                    info_lines.append("  - 无法获取数据库详细信息")

            self.system_info_text.setPlainText("\n".join(info_lines))

        except Exception as e:
            self.system_info_text.setPlainText(f"获取系统信息失败: {str(e)}")

    @pyqtSlot()
    def apply_settings(self):
        """应用设置"""
        try:
            # TODO: 保存所有设置到配置文件或数据库
            QMessageBox.information(self, "成功", "设置已保存")
            self.logger.info("设置已应用")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存设置失败: {str(e)}")

    @pyqtSlot()
    def reset_settings(self):
        """重置设置"""
        reply = QMessageBox.question(
            self, "确认", "确定要重置所有设置为默认值吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 重置所有控件为默认值
                self.auto_start_checkbox.setChecked(False)
                self.minimize_to_tray_checkbox.setChecked(False)
                self.auto_play_checkbox.setChecked(False)
                self.log_level_combo.setCurrentText("INFO")
                self.voice_engine_combo.setCurrentIndex(0)
                self.api_key_input.clear()
                self.default_speed_slider.setValue(100)
                self.default_volume_slider.setValue(80)
                self.enable_cache_checkbox.setChecked(True)
                self.cache_size_spin.setValue(1000)
                self.min_interval_spin.setValue(2.0)
                self.max_interval_spin.setValue(5.0)
                self.random_play_checkbox.setChecked(False)
                self.repeat_play_checkbox.setChecked(False)
                self.output_device_combo.setCurrentIndex(0)

                QMessageBox.information(self, "完成", "设置已重置为默认值")
                self.logger.info("设置已重置")

            except Exception as e:
                QMessageBox.warning(self, "错误", f"重置设置失败: {str(e)}")

    @pyqtSlot()
    def show_about(self):
        """显示关于信息"""
        about_text = """
AI Broadcaster v2
智能AI广播系统

版本: 2.0.0
开发语言: Python 3.12
UI框架: PyQt6
数据库: SQLite

功能特性:
• 多主播语音合成
• 智能话术管理
• 自动播放控制
• 用户权限管理
• 数据备份恢复

© 2024 AI Broadcaster Team
        """
        QMessageBox.about(self, "关于 AI Broadcaster v2", about_text.strip())
