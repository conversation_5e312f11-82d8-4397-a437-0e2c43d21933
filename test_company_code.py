#!/usr/bin/env python3
"""
测试公司代码功能的脚本
"""

def test_extract_company_code():
    """测试提取公司代码功能"""
    print("=== 测试提取公司代码功能 ===")
    
    def extract_company_code(username: str) -> str:
        """从用户名中提取公司代码"""
        try:
            if '-' in username:
                return username.split('-', 1)[0]
            return ""
        except Exception as e:
            print(f"[ERROR] 提取公司代码失败: {e}")
            return ""
    
    test_cases = [
        ("jane-1", "jane"),
        ("abc-user123", "abc"),
        ("company-test-user", "company"),
        ("nocompanycode", ""),
        ("", ""),
        ("a-b", "a"),
    ]
    
    for username, expected in test_cases:
        result = extract_company_code(username)
        status = "✓" if result == expected else "✗"
        print(f"{status} 用户名: '{username}' -> 公司代码: '{result}' (期望: '{expected}')")

def test_filter_by_company_code():
    """测试按公司代码过滤功能"""
    print("\n=== 测试按公司代码过滤功能 ===")
    
    # 模拟AI主播数据
    speakers_data = [
        {"id": 1, "name": "jane-kaka"},
        {"id": 2, "name": "jane-bukeai"},
        {"id": 3, "name": "xy-speaker1"},
        {"id": 4, "name": "abc-voice1"},
        {"id": 5, "name": "jane-voice2"},
        {"id": 6, "name": "other-speaker"},
    ]
    
    # 模拟话术数据
    scripts_data = [
        {"name": "jane-script1"},
        {"name": "jane-script2"},
        {"name": "xy-script1"},
        {"name": "abc-script1"},
        {"name": "jane-test"},
        {"name": "other-script"},
    ]
    
    # 模拟AI对话数据
    dialogues_data = [
        {"name": "jane-dialogue1"},
        {"name": "jane-dialogue2"},
        {"name": "xy-dialogue1"},
        {"name": "abc-dialogue1"},
        {"name": "jane-chat"},
        {"name": "other-dialogue"},
    ]
    
    company_code = "jane"
    
    def filter_and_display(data_list, data_type):
        """过滤并显示数据"""
        print(f"\n--- {data_type} ---")
        filtered_data = []
        
        for item in data_list:
            item_name = item["name"]
            
            # 只显示包含当前公司代码的项目
            if company_code and company_code in item_name:
                # 显示时隐藏公司代码前缀
                if item_name.startswith(f"{company_code}-"):
                    display_name = item_name[len(company_code)+1:]  # 去掉"公司代码-"前缀
                else:
                    display_name = item_name  # 如果不是标准格式，显示完整名称
                
                filtered_data.append({
                    "original_name": item_name,
                    "display_name": display_name
                })
                print(f"  ✓ 显示: '{display_name}' (原名: '{item_name}')")
        
        print(f"  总计: {len(filtered_data)} 个符合条件的{data_type}（原始数据: {len(data_list)} 个）")
        return filtered_data
    
    # 测试过滤功能
    filtered_speakers = filter_and_display(speakers_data, "AI主播")
    filtered_scripts = filter_and_display(scripts_data, "话术")
    filtered_dialogues = filter_and_display(dialogues_data, "AI对话")

def test_add_company_prefix():
    """测试添加公司代码前缀功能"""
    print("\n=== 测试添加公司代码前缀功能 ===")
    
    company_code = "jane"
    
    def add_company_prefix(name: str, company_code: str) -> str:
        """为名称添加公司代码前缀"""
        if company_code and not name.startswith(f"{company_code}-"):
            return f"{company_code}-{name}"
        return name
    
    test_cases = [
        ("script1", "jane-script1"),
        ("dialogue1", "jane-dialogue1"),
        ("jane-existing", "jane-existing"),  # 已有前缀的不重复添加
        ("", "jane-"),  # 空名称
        ("test-name", "jane-test-name"),  # 包含其他连字符的
    ]
    
    for input_name, expected in test_cases:
        result = add_company_prefix(input_name, company_code)
        status = "✓" if result == expected else "✗"
        print(f"{status} 输入: '{input_name}' -> 输出: '{result}' (期望: '{expected}')")

def test_username_validation():
    """测试用户名格式验证"""
    print("\n=== 测试用户名格式验证 ===")
    
    def validate_username(username: str) -> tuple:
        """验证用户名格式"""
        if not username:
            return False, "请输入用户名"
        
        # 验证用户名格式必须为"公司代码-用户名"
        if '-' not in username:
            return False, "用户名格式错误，必须为"公司代码-用户名"格式，如：jane-1"
        
        # 检查公司代码和用户名部分
        parts = username.split('-', 1)  # 只分割第一个'-'
        if len(parts) != 2 or not parts[0] or not parts[1]:
            return False, "用户名格式错误，必须为"公司代码-用户名"格式，如：jane-1"
        
        company_code = parts[0]
        user_part = parts[1]
        
        # 验证公司代码和用户名部分的长度
        if len(company_code) < 2 or len(user_part) < 1:
            return False, "公司代码至少2个字符，用户名部分至少1个字符"
        
        if len(username) < 3 or len(username) > 20:
            return False, "用户名总长度应为3-20个字符"
        
        return True, f"有效用户名，公司代码: {company_code}, 用户部分: {user_part}"
    
    test_cases = [
        "jane-1",           # 有效
        "abc-user123",      # 有效
        "company-test",     # 有效
        "a-b",              # 有效（最小长度）
        "verylongcompany-verylonguser",  # 可能超长
        "jane1",            # 无效：没有连字符
        "jane-",            # 无效：用户部分为空
        "-user",            # 无效：公司代码为空
        "a-",               # 无效：用户部分为空
        "-",                # 无效：两部分都为空
        "",                 # 无效：空字符串
        "j-u",              # 有效（最小有效长度）
    ]
    
    for username in test_cases:
        is_valid, message = validate_username(username)
        status = "✓" if is_valid else "✗"
        print(f"{status} '{username}': {message}")

if __name__ == "__main__":
    test_extract_company_code()
    test_filter_by_company_code()
    test_add_company_prefix()
    test_username_validation()
    
    print("\n=== 测试完成 ===")
    print("所有功能测试已完成，请检查上述结果确认功能正常。")
