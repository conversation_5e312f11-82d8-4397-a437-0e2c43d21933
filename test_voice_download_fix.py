#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试语音下载修复
验证语速参数和代理问题的修复
"""

import hashlib
import time
import requests
from urllib.parse import quote

def test_api_parameters():
    """测试API参数修复"""
    print("🎤 测试API参数修复")
    print("=" * 50)
    
    # 测试数据
    text = "测试语音下载修复"
    speaker_id = 5
    slider_speed = 110  # 滑块值 (80-120)
    
    print(f"测试文本: {text}")
    print(f"主播ID: {speaker_id}")
    print(f"滑块速度值: {slider_speed}")
    
    # 🔥 修复：将滑块值转换为API速度值
    api_speed = slider_speed / 100.0
    print(f"API速度值: {api_speed}")
    
    # 构建API请求
    api_url = "http://ct.scjanelife.com/voice/bert-vits2"
    params = {
        'id': speaker_id,
        'speed': api_speed,  # 使用speed参数
        'text': text
    }
    
    print(f"\nAPI URL: {api_url}")
    print(f"请求参数: {params}")
    
    # 构建完整URL用于测试
    encoded_text = quote(text)
    full_url = f"{api_url}?id={speaker_id}&speed={api_speed}&text={encoded_text}"
    print(f"完整URL: {full_url}")

def test_proxy_fix():
    """测试代理修复"""
    print("\n🌐 测试代理修复")
    print("=" * 50)
    
    # 测试不使用代理的请求
    test_url = "http://ct.scjanelife.com/voice/speakers"
    
    print(f"测试URL: {test_url}")
    print("测试代理设置...")
    
    try:
        # 🔥 修复：禁用代理
        response = requests.get(
            test_url, 
            timeout=10,
            proxies={'http': None, 'https': None}
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 代理修复成功，可以直接访问API")
        else:
            print(f"⚠️ API返回状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_filename_generation():
    """测试新文件名生成"""
    print("\n📁 测试新文件名生成")
    print("=" * 50)
    
    text = "测试新文件名格式"
    
    # 🔥 新格式：语音内容+时间戳的哈希值
    timestamp = int(time.time() * 1000)
    content_for_hash = f"{text}_{timestamp}"
    text_hash = hashlib.md5(content_for_hash.encode('utf-8')).hexdigest()[:16]
    filename = f"{text_hash}.wav"
    
    print(f"文本内容: {text}")
    print(f"时间戳: {timestamp}")
    print(f"哈希内容: {content_for_hash}")
    print(f"生成的哈希: {text_hash}")
    print(f"最终文件名: {filename}")
    
    # 测试唯一性
    print("\n测试文件名唯一性:")
    filenames = []
    for i in range(3):
        timestamp = int(time.time() * 1000)
        content_for_hash = f"{text}_{timestamp}"
        text_hash = hashlib.md5(content_for_hash.encode('utf-8')).hexdigest()[:16]
        filename = f"{text_hash}.wav"
        filenames.append(filename)
        print(f"  {i+1}. {filename}")
        time.sleep(0.001)  # 确保时间戳不同
    
    unique_count = len(set(filenames))
    print(f"\n生成了 {len(filenames)} 个文件名，唯一文件名: {unique_count} 个")
    print(f"唯一性: {'✅ 通过' if len(filenames) == unique_count else '❌ 失败'}")

def test_speed_conversion():
    """测试语速转换"""
    print("\n⚡ 测试语速转换")
    print("=" * 50)
    
    # 测试不同滑块值的转换
    test_speeds = [80, 90, 100, 110, 120]
    
    print("滑块值 → API速度值")
    print("-" * 20)
    for slider_value in test_speeds:
        api_value = slider_value / 100.0
        print(f"{slider_value:3d}    → {api_value:.1f}")
    
    print("\n✅ 语速转换公式: API速度 = 滑块值 / 100.0")

def main():
    """主函数"""
    print("🔧 语音下载修复测试")
    print("=" * 60)
    
    # 运行所有测试
    test_api_parameters()
    test_proxy_fix()
    test_filename_generation()
    test_speed_conversion()
    
    print("\n" + "=" * 60)
    print("📋 修复总结:")
    print("1. ✅ 语速参数: 滑块值(80-120) → API速度值(0.8-1.2)")
    print("2. ✅ API参数: 使用 'speed' 参数而不是 'lang'")
    print("3. ✅ 代理设置: 禁用代理，直接访问API")
    print("4. ✅ 文件名格式: 语音内容+时间戳的哈希值")
    print("5. ✅ 文件名唯一性: 时间戳确保不重复")

if __name__ == "__main__":
    main()
