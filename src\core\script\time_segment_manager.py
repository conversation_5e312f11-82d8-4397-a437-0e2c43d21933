"""
AI Broadcaster v2 - 时间段管理器
管理话术的时间段设置
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, time

from ...services.logging_service import create_logger
from ...services.error_handler import handle_exceptions


class TimeSegmentManager:
    """时间段管理器"""
    
    def __init__(self, db_manager):
        self.logger = create_logger("time_segment_manager")
        self.db_manager = db_manager
        
    @handle_exceptions("TimeSegmentManager")
    def create_time_segment(self, script_id: int, start_time: str, end_time: str,
                           days_of_week: str = "1,2,3,4,5,6,7", priority: int = 1,
                           is_active: bool = True) -> Optional[int]:
        """
        创建时间段
        
        Args:
            script_id: 话术ID
            start_time: 开始时间 (HH:MM格式)
            end_time: 结束时间 (HH:MM格式)
            days_of_week: 星期几 (1-7，逗号分隔，1=周一)
            priority: 优先级 (数字越大优先级越高)
            is_active: 是否激活
            
        Returns:
            时间段ID，失败返回None
        """
        try:
            # 验证时间格式
            if not self._validate_time_format(start_time) or not self._validate_time_format(end_time):
                self.logger.error(f"时间格式错误: {start_time} - {end_time}")
                return None
                
            # 验证星期格式
            if not self._validate_days_format(days_of_week):
                self.logger.error(f"星期格式错误: {days_of_week}")
                return None
                
            segment_id = self.db_manager.execute_insert(
                """INSERT INTO time_segments 
                   (script_id, start_time, end_time, days_of_week, priority, is_active, created_at, updated_at)
                   VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)""",
                (script_id, start_time, end_time, days_of_week, priority, is_active)
            )
            
            if segment_id:
                self.logger.info(f"创建时间段成功: {start_time}-{end_time} (ID: {segment_id})")
                return segment_id
            else:
                self.logger.error("创建时间段失败")
                return None
                
        except Exception as e:
            self.logger.error(f"创建时间段异常: {e}")
            return None
            
    @handle_exceptions("TimeSegmentManager")
    def get_time_segments_by_script(self, script_id: int) -> List[Dict[str, Any]]:
        """获取话术的所有时间段"""
        try:
            segments = self.db_manager.execute_query(
                "SELECT * FROM time_segments WHERE script_id = ? ORDER BY priority DESC, start_time",
                (script_id,)
            )
            return segments or []
            
        except Exception as e:
            self.logger.error(f"获取时间段失败: {e}")
            return []
            
    @handle_exceptions("TimeSegmentManager")
    def get_active_scripts_for_time(self, check_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        获取指定时间应该播放的话术
        
        Args:
            check_time: 检查时间，None表示当前时间
            
        Returns:
            符合条件的话术列表，按优先级排序
        """
        try:
            if check_time is None:
                check_time = datetime.now()
                
            current_time = check_time.strftime("%H:%M")
            current_weekday = check_time.isoweekday()  # 1=周一, 7=周日
            
            # 查询符合时间条件的话术
            query = """
                SELECT s.*, ts.priority, ts.start_time, ts.end_time, ts.days_of_week
                FROM scripts s
                JOIN time_segments ts ON s.id = ts.script_id
                WHERE ts.is_active = 1
                AND ts.start_time <= ?
                AND ts.end_time >= ?
                AND ts.days_of_week LIKE ?
                ORDER BY ts.priority DESC, s.updated_at DESC
            """
            
            weekday_pattern = f"%{current_weekday}%"
            
            scripts = self.db_manager.execute_query(
                query, (current_time, current_time, weekday_pattern)
            )
            
            # 进一步过滤星期几
            filtered_scripts = []
            for script in scripts:
                days = [int(d.strip()) for d in script['days_of_week'].split(',') if d.strip().isdigit()]
                if current_weekday in days:
                    filtered_scripts.append(script)
                    
            self.logger.debug(f"找到 {len(filtered_scripts)} 个符合时间条件的话术")
            return filtered_scripts
            
        except Exception as e:
            self.logger.error(f"获取时间段话术失败: {e}")
            return []
            
    @handle_exceptions("TimeSegmentManager")
    def update_time_segment(self, segment_id: int, start_time: Optional[str] = None,
                           end_time: Optional[str] = None, days_of_week: Optional[str] = None,
                           priority: Optional[int] = None, is_active: Optional[bool] = None) -> bool:
        """更新时间段"""
        try:
            update_fields = []
            params = []
            
            if start_time is not None:
                if not self._validate_time_format(start_time):
                    self.logger.error(f"开始时间格式错误: {start_time}")
                    return False
                update_fields.append("start_time = ?")
                params.append(start_time)
                
            if end_time is not None:
                if not self._validate_time_format(end_time):
                    self.logger.error(f"结束时间格式错误: {end_time}")
                    return False
                update_fields.append("end_time = ?")
                params.append(end_time)
                
            if days_of_week is not None:
                if not self._validate_days_format(days_of_week):
                    self.logger.error(f"星期格式错误: {days_of_week}")
                    return False
                update_fields.append("days_of_week = ?")
                params.append(days_of_week)
                
            if priority is not None:
                update_fields.append("priority = ?")
                params.append(priority)
                
            if is_active is not None:
                update_fields.append("is_active = ?")
                params.append(is_active)
                
            if not update_fields:
                return True
                
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            params.append(segment_id)
            
            query = f"UPDATE time_segments SET {', '.join(update_fields)} WHERE id = ?"
            
            rows_affected = self.db_manager.execute_update(query, params)
            
            if rows_affected > 0:
                self.logger.info(f"更新时间段成功: ID {segment_id}")
                return True
            else:
                self.logger.warning(f"时间段不存在: ID {segment_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"更新时间段失败: {e}")
            return False
            
    @handle_exceptions("TimeSegmentManager")
    def delete_time_segment(self, segment_id: int) -> bool:
        """删除时间段"""
        try:
            rows_affected = self.db_manager.execute_update(
                "DELETE FROM time_segments WHERE id = ?",
                (segment_id,)
            )
            
            if rows_affected > 0:
                self.logger.info(f"删除时间段成功: ID {segment_id}")
                return True
            else:
                self.logger.warning(f"时间段不存在: ID {segment_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除时间段失败: {e}")
            return False
            
    @handle_exceptions("TimeSegmentManager")
    def get_time_conflicts(self, script_id: int, start_time: str, end_time: str,
                          days_of_week: str, exclude_segment_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """检查时间冲突"""
        try:
            query = """
                SELECT ts.*, s.name as script_name
                FROM time_segments ts
                JOIN scripts s ON ts.script_id = s.id
                WHERE ts.script_id = ?
                AND ts.is_active = 1
                AND ((ts.start_time <= ? AND ts.end_time >= ?) OR 
                     (ts.start_time <= ? AND ts.end_time >= ?) OR
                     (ts.start_time >= ? AND ts.end_time <= ?))
            """
            
            params = [script_id, start_time, start_time, end_time, end_time, start_time, end_time]
            
            if exclude_segment_id:
                query += " AND ts.id != ?"
                params.append(exclude_segment_id)
                
            conflicts = self.db_manager.execute_query(query, params)
            
            # 进一步检查星期冲突
            filtered_conflicts = []
            if conflicts:
                input_days = set(int(d.strip()) for d in days_of_week.split(',') if d.strip().isdigit())
                
                for conflict in conflicts:
                    conflict_days = set(int(d.strip()) for d in conflict['days_of_week'].split(',') if d.strip().isdigit())
                    if input_days & conflict_days:  # 有交集
                        filtered_conflicts.append(conflict)
                        
            return filtered_conflicts
            
        except Exception as e:
            self.logger.error(f"检查时间冲突失败: {e}")
            return []
            
    @handle_exceptions("TimeSegmentManager")
    def get_schedule_overview(self, days: int = 7) -> Dict[str, List[Dict[str, Any]]]:
        """获取时间表概览"""
        try:
            # 获取所有激活的时间段
            query = """
                SELECT ts.*, s.name as script_name, s.category
                FROM time_segments ts
                JOIN scripts s ON ts.script_id = s.id
                WHERE ts.is_active = 1
                ORDER BY ts.start_time, ts.priority DESC
            """
            
            segments = self.db_manager.execute_query(query)
            
            # 按星期分组
            schedule = {}
            weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            
            for i in range(1, 8):  # 1-7对应周一到周日
                day_name = weekdays[i-1]
                schedule[day_name] = []
                
                for segment in segments:
                    days_list = [int(d.strip()) for d in segment['days_of_week'].split(',') if d.strip().isdigit()]
                    if i in days_list:
                        schedule[day_name].append({
                            'time_range': f"{segment['start_time']}-{segment['end_time']}",
                            'script_name': segment['script_name'],
                            'category': segment['category'],
                            'priority': segment['priority'],
                            'segment_id': segment['id']
                        })
                        
            return schedule
            
        except Exception as e:
            self.logger.error(f"获取时间表概览失败: {e}")
            return {}
            
    def _validate_time_format(self, time_str: str) -> bool:
        """验证时间格式 (HH:MM)"""
        try:
            time.fromisoformat(time_str)
            return True
        except ValueError:
            return False
            
    def _validate_days_format(self, days_str: str) -> bool:
        """验证星期格式 (1,2,3,4,5,6,7)"""
        try:
            days = [int(d.strip()) for d in days_str.split(',') if d.strip()]
            return all(1 <= day <= 7 for day in days)
        except (ValueError, AttributeError):
            return False
            
    @handle_exceptions("TimeSegmentManager")
    def get_next_scheduled_script(self, current_time: Optional[datetime] = None) -> Optional[Dict[str, Any]]:
        """获取下一个计划播放的话术"""
        try:
            if current_time is None:
                current_time = datetime.now()
                
            # 先尝试获取当前时间的话术
            current_scripts = self.get_active_scripts_for_time(current_time)
            if current_scripts:
                return current_scripts[0]  # 返回优先级最高的
                
            # 如果当前时间没有话术，查找下一个时间段
            current_time_str = current_time.strftime("%H:%M")
            current_weekday = current_time.isoweekday()
            
            query = """
                SELECT ts.*, s.name as script_name, s.content
                FROM time_segments ts
                JOIN scripts s ON ts.script_id = s.id
                WHERE ts.is_active = 1
                AND ts.start_time > ?
                AND ts.days_of_week LIKE ?
                ORDER BY ts.start_time, ts.priority DESC
                LIMIT 1
            """
            
            weekday_pattern = f"%{current_weekday}%"
            results = self.db_manager.execute_query(query, (current_time_str, weekday_pattern))
            
            if results:
                # 进一步验证星期
                result = results[0]
                days = [int(d.strip()) for d in result['days_of_week'].split(',') if d.strip().isdigit()]
                if current_weekday in days:
                    return result
                    
            return None
            
        except Exception as e:
            self.logger.error(f"获取下一个计划话术失败: {e}")
            return None
