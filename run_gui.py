#!/usr/bin/env python3
"""
AI直播系统 v2 - 图形界面启动程序
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    
    from src.services.logging_service import setup_logging
    from src.data.database_manager import DatabaseManager
    from src.ui.user_ui import UserLoginWidget, UserRegisterWidget, UserRechargeWidget
    
    class MainWindow(QMainWindow):
        """主窗口"""
        
        def __init__(self):
            super().__init__()
            self.db = DatabaseManager("data/gui_app.db")
            self.current_user = None
            self.init_ui()
            
        def init_ui(self):
            """初始化界面"""
            self.setWindowTitle("AI直播系统 v2 - 用户管理")
            self.setGeometry(100, 100, 800, 600)
            
            # 创建中央窗口
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # 创建布局
            layout = QVBoxLayout()
            central_widget.setLayout(layout)
            
            # 标题
            title_label = QLabel("🎬 AI直播系统 v2")
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title_font = QFont()
            title_font.setPointSize(24)
            title_font.setBold(True)
            title_label.setFont(title_font)
            layout.addWidget(title_label)
            
            # 副标题
            subtitle_label = QLabel("用户管理系统")
            subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            subtitle_font = QFont()
            subtitle_font.setPointSize(16)
            subtitle_label.setFont(subtitle_font)
            layout.addWidget(subtitle_label)
            
            # 间距
            layout.addSpacing(50)
            
            # 登录按钮
            self.login_button = QPushButton("用户登录")
            self.login_button.setMinimumHeight(50)
            self.login_button.clicked.connect(self.show_login)
            layout.addWidget(self.login_button)
            
            # 注册按钮
            self.register_button = QPushButton("用户注册")
            self.register_button.setMinimumHeight(50)
            self.register_button.clicked.connect(self.show_register)
            layout.addWidget(self.register_button)
            
            # 状态标签
            self.status_label = QLabel("欢迎使用AI直播系统！")
            self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(self.status_label)
            
            # 间距
            layout.addStretch()
            
            # 版本信息
            version_label = QLabel("版本: v2.0.0 | 状态: 生产就绪")
            version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            version_label.setStyleSheet("color: gray;")
            layout.addWidget(version_label)
            
        def show_login(self):
            """显示登录界面"""
            self.login_widget = UserLoginWidget(self.db)
            self.login_widget.login_success.connect(self.on_login_success)
            self.login_widget.show()
            
        def show_register(self):
            """显示注册界面"""
            self.register_widget = UserRegisterWidget(self.db)
            self.register_widget.register_success.connect(self.on_register_success)
            self.register_widget.show()
            
        def on_login_success(self, login_data):
            """登录成功回调"""
            self.current_user = login_data['user_info']
            self.status_label.setText(f"欢迎，{self.current_user['username']}！")
            
            # 显示充值界面
            self.show_recharge()
            
        def on_register_success(self, user_info):
            """注册成功回调"""
            self.status_label.setText(f"注册成功！欢迎 {user_info['username']}")
            
        def show_recharge(self):
            """显示充值界面"""
            if self.current_user:
                self.recharge_widget = UserRechargeWidget(self.db, self.current_user)
                self.recharge_widget.show()
                
        def closeEvent(self, event):
            """关闭事件"""
            if hasattr(self, 'db'):
                self.db.close()
            event.accept()
    
    def main():
        """主函数"""
        # 初始化日志系统
        setup_logging()
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 设置应用程序信息
        app.setApplicationName("AI直播系统 v2")
        app.setApplicationVersion("2.0.0")
        app.setOrganizationName("AI直播系统")
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        # 运行应用程序
        sys.exit(app.exec())
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print("❌ PyQt6未安装或导入失败")
    print(f"错误信息: {e}")
    print("\n请安装PyQt6:")
    print("pip install PyQt6")
    print("\n或者使用命令行版本:")
    print("python demo_user_system.py")
    sys.exit(1)
except Exception as e:
    print(f"❌ 启动图形界面失败: {e}")
    print("\n请尝试使用命令行版本:")
    print("python demo_user_system.py")
    sys.exit(1)
